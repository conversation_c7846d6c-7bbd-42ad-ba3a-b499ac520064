<div data-widget="disputeWorkOrder" style="overflow: auto; height: 100%;">
    <p class="vita-data">{"data":$options}</p>
    <div class="box-maincont">
        <div class="homenofood">
            <div class="page_main">
                <!--填单start-->
                <div class="col-lg-12">
                    <div class="box-item">
                        <div class="container-fluid row">
                            <div class="form_title">
                                <div>
                                    <i class="bot"></i>查询条件
                                </div>
                            </div>
                            <div class="wmin_content row ">
                                <form class=" form-bordered">

                                    <div class="form-group col-md-6 ">
                                        <label class="col-md-4 control-label lablep">受理工号</label>
                                        <div class="col-md-8">
                                            <input type="text"  class="form-control" placeholder="">
                                            <p class="vita-bind" model="staffCode"></p>
                                        </div>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="col-md-4 control-label lablep">受理渠道</label>
                                        <div class="col-md-8">
                                            <input  type="text"  class="form-control" placeholder="">
                                            <p class="vita-bind" model="channelId"></p>
                                        </div>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="col-md-4 control-label lablep">受理日期</label>
                                        <div class="col-md-8">
                                            <input name="createDate" id="createDate" type="text" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="col-md-4 control-label lablep">客户名称</label>
                                        <div class="col-md-8">
                                            <input  type="text"  class="form-control" placeholder="">
                                            <p class="vita-bind" model="custName"></p>
                                        </div>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="col-md-4 control-label lablep">客户证件号</label>
                                        <div class="col-md-8">
                                            <input  type="text"  class="form-control" placeholder="">
                                            <p class="vita-bind" model="certNum"></p>
                                        </div>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="col-md-4 control-label lablep">争议单状态</label>
                                        <div class="col-md-8">
                                            <select class="form-control"  id="disputeWorkOrderStatus">
                                                <option value="">-请选择-</option>
                                                <option value="1001">已发起争议单</option>
                                                <option value="1000">争议单处理成功</option>
                                                <option value="2000">争议单退单</option>
                                                <option value="3000">待处理</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group col-md-5">
                                        <label class="col-md-2 control-label lablep"></label>
                                        <div class="col-md-5">
                                            <button  type="button"
                                                     class="btn btn-primary" id="searchBtn">查询</button>
                                        </div>
                                        <div id="disputeUploadBtn" class="input-group-btn">
											<button name="disputeUpload" class="btn btn-green" type="button">发起争议单</button>
										</div>
                                    </div>

                                </form>
                            </div>
                        </div>

                        <div class="container-fluid row">
                            <div class="form_title">
                                <div>
                                    <i class="bot"></i>查询结果
                                </div>
                            </div>
                            <div class="container-fluid row" id="disputeWorkOrder">

                                <div class="wmin_content" >
                                    <div class="col-lg-12 mart10" >
                                        <table class="table table-hover" >
                                            <thead>
                                            <tr>
                                                <th>编号</th>
                                                <th>发起工号</th>
                                                <th>发起渠道</th>
                                                <th>发起时间</th>
                                                <th>错误类型</th>
                                                <th>客户名称</th>
                                                <th>证件类型</th>
                                                <th>证件号</th>
                                                <th>状态</th>
                                                <th>处理结果</th>
                                                <th>响应时间</th>
                                            </tr>
                                            </thead>
                                            <tbody id="table">
                                            #if($options != "null"  && $options.disputeWorkOrders != "null" && $options.disputeWorkOrders.size() > 0)
                                            #foreach($disputeWorkOrder in $options.disputeWorkOrders)
                                            <tr >
                                                <td>$!disputeWorkOrder.workOrderNbr</td>
                                                <td>$!disputeWorkOrder.staffCode</td>
                                                <td>$!disputeWorkOrder.channelId</td>
                                                <td>$!disputeWorkOrder.createDate</td>
                                                <td>
                                                    #if($disputeWorkOrder.disputeErrorType=="0")
                                                    统一社会信用代码错误
                                                    #elseif($disputeWorkOrder.disputeErrorType=="1")
                                                    经营期限字段信息错误
                                                    #elseif($disputeWorkOrder.disputeErrorType=="2")
                                                    客户名称字段错误
                                                    #elseif($disputeWorkOrder.disputeErrorType=="3")
                                                    客户实际存在，但是查询不到
                                                    #elseif($disputeWorkOrder.disputeErrorType=="4")
                                                    经营状态错误
                                                    #elseif($disputeWorkOrder.disputeErrorType=="5")
                                                    其他（请求集团时必填）
                                                    #end
                                                </td>
                                                <td>$!disputeWorkOrder.custName </td>
                                                <td>$!disputeWorkOrder.certName</td>
                                                <td>$!disputeWorkOrder.certNum</td>
                                                <td>
                                                    #if($disputeWorkOrder.statusCd=="1001")
                                                    已发起争议单
                                                    #elseif($disputeWorkOrder.statusCd=="1000")
                                                    争议单处理成功
                                                    #elseif($disputeWorkOrder.statusCd=="2000")
                                                    争议单退单
                                                    #elseif($disputeWorkOrder.statusCd=="3000")
                                                    待处理
                                                    #end
                                                </td>
                                                <td>$!disputeWorkOrder.workOrderDesc</td>
                                                <td>$!disputeWorkOrder.updateDate</td>

                                            </tr>
                                            #end
                                            #end
                                            </tbody>
                                        </table>
                                        <div class="page-box" id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--填单end-->
        </div>
    </div>
</div>

