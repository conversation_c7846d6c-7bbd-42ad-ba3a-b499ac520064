package com.asiainfo.crm.assist.app.order;

import com.asiainfo.crm.assist.model.ServiceOffer;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderSMO;
import com.asiainfo.crm.util.ListUtil;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/5/18 14:36
 * @Description TODO
 **/
@Component("vita.orderHisDetail")
public class OrderHisDetail extends AbstractComponent{
    @Autowired
    private IOrderSMO orderSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map options = new HashMap();
        String csNbr = String.valueOf(params[0]);
        String iUId = String.valueOf(params[1]);
        String resultJson = orderSMO.qryTraceDetailHisDetail(csNbr,iUId);
        HashMap<String,Object> map = (HashMap<String, Object>) jsonConverter.toBean(resultJson,Map.class);
        HashMap<String,Object> resultMap = (HashMap<String, Object>) map.get("resultObject");
        if(resultMap != null){
            List<Map<String,Object>> offs = (List<Map<String, Object>>) resultMap.get("traceOrderItems");
            //获取查询服务提供配置列表，从现有数据获取查询配置。
            ArrayList<ServiceOffer> serviceOfferTypes = new ArrayList<ServiceOffer>();
            for(Map<String,Object>  off : ListUtil.nvlList(offs)){
                String serviceOfferId = String.valueOf(off.get("serviceOfferId"));
                String serviceOfferName = String.valueOf(off.get("serviceOfferName"));
                ServiceOffer serviceOffer = new ServiceOffer();
                serviceOffer.setServiceOfferId(serviceOfferId);
                serviceOffer.setServiceOfferName(serviceOfferName);
                serviceOfferTypes.add(serviceOffer);
            }
            options.put("totalNumber", offs.size());
            options.put("offerOrderItems", offs);
            options.put("iUId",iUId);
            //去重
            if(!ListUtil.isListEmpty(serviceOfferTypes)){
                LinkedHashSet hs = new LinkedHashSet(serviceOfferTypes);
                serviceOfferTypes.clear();
                serviceOfferTypes.addAll(hs);
                options.put("serviceOfferTypes",serviceOfferTypes);
           }
        }
        return options;
    }

    /**
     * 订单项列表信息查询
     * @param param
     * @param from
     * @param size
     * @return
     * @throws Exception
     */
    public Map getChainTraceCustomerOrder(String datas, String from, String size,String param) throws Exception {
        Map<String, Object> retMap = new HashMap<>();
        ArrayList orderList = jsonConverter.toBean(datas,ArrayList.class);
        //点击查询过滤orderList数据,获取查询条件入参
        Map reqMap = jsonConverter.toBean(param, Map.class);
        String chainCreate = String.valueOf(reqMap.get("chainCreate"));
        String serviceOfferId = String.valueOf(reqMap.get("serviceOfferId"));
        String applyObjSpecName = String.valueOf(reqMap.get("applyObjSpecName"));
        if(StringUtils.isNotBlank(chainCreate) && orderList != null && orderList.size() > 0){
            Iterator<HashMap<String,Object>> it = orderList.iterator();
            while (it.hasNext()){
                HashMap<String,Object> m = it.next();
                String _chainCreate = String.valueOf(m.get("chainCreate"));
                if(!chainCreate.equals(_chainCreate)){
                    it.remove();
                }
            }
        }
        if(StringUtils.isNotBlank(serviceOfferId) && orderList != null && orderList.size() > 0){
            Iterator<HashMap<String,Object>> it = orderList.iterator();
            while (it.hasNext()){
                HashMap<String,Object> m = it.next();
                String _serviceOfferId = String.valueOf(m.get("serviceOfferId"));
                if(!serviceOfferId.equals(_serviceOfferId)){
                    it.remove();
                }
            }
        }
        if(StringUtils.isNotBlank(applyObjSpecName) && orderList != null && orderList.size() > 0){
            Iterator<HashMap<String,Object>> it = orderList.iterator();
            while (it.hasNext()){
                HashMap<String,Object> m = it.next();
                String _applyObjSpecName = String.valueOf(m.get("applyObjSpecName"));
                //这里做模糊匹配
                if(!_applyObjSpecName.contains(applyObjSpecName)){
                    it.remove();
                }
            }
        }
        int totalCount = orderList.size();
        int startPos = Integer.valueOf(from);
        int step = Integer.valueOf(size);
        ArrayList orders = new ArrayList() ;
        if(step > totalCount){
            step = totalCount;
        }
        for (int i = startPos - 1; i < step; i++) {
            orders.add(orderList.get(i)) ;
        }
        retMap.put("totalNumber", totalCount);
        retMap.put("offerOrderItems", orders);
        return retMap;
    }
    public Map getFilterChainTraceCustomerOrder(String datas , String param) throws Exception {
        Map<String, Object> retMap = new HashMap<>();
        ArrayList<HashMap<String,Object>> orderList = jsonConverter.toBean(datas,ArrayList.class);
        //点击查询过滤orderList数据,获取查询条件入参
        Map reqMap = jsonConverter.toBean(param, Map.class);
        String chainCreate = String.valueOf(reqMap.get("chainCreate"));
        String serviceOfferId = String.valueOf(reqMap.get("serviceOfferId"));
        String applyObjSpecName = String.valueOf(reqMap.get("applyObjSpecName"));
        if(StringUtils.isNotBlank(chainCreate) && orderList != null && orderList.size() > 0){
            Iterator<HashMap<String,Object>> it = orderList.iterator();
            while (it.hasNext()){
                HashMap<String,Object> m = it.next();
                String _chainCreate = String.valueOf(m.get("chainCreate"));
                if(!chainCreate.equals(_chainCreate)){
                    it.remove();
                }
            }
        }
        if(StringUtils.isNotBlank(serviceOfferId) && orderList != null && orderList.size() > 0){
            Iterator<HashMap<String,Object>> it = orderList.iterator();
            while (it.hasNext()){
                HashMap<String,Object> m = it.next();
                String _serviceOfferId = String.valueOf(m.get("serviceOfferId"));
                if(!serviceOfferId.equals(_serviceOfferId)){
                    it.remove();
                }
            }
        }
        if(StringUtils.isNotBlank(applyObjSpecName) && orderList != null && orderList.size() > 0){
            Iterator<HashMap<String,Object>> it = orderList.iterator();
            while (it.hasNext()){
                HashMap<String,Object> m = it.next();
                String _applyObjSpecName = String.valueOf(m.get("applyObjSpecName"));
                //这里做模糊匹配
                if(!_applyObjSpecName.contains(applyObjSpecName)){
                    it.remove();
                }
            }
        }
        retMap.put("totalNumber", orderList!=null&&orderList.size()>0 ?orderList.size():0);
        retMap.put("offerOrderItems", orderList);
        return retMap;
    }
    public Map queryTraceHisList(String csNbr) throws Exception {
        String traceResult = orderSMO.qryTraceDetailHis(csNbr);
        HashMap<String,Object> resultMap = (HashMap<String, Object>) jsonConverter.toBean(traceResult,Map.class);
        List<Map<String,Object>> traceHisList = (List<Map<String, Object>>) resultMap.get("resultObject");
        Map<String, Object> options = Maps.newHashMap();
      /*  int pageIndex = 0;
        int pageCount = 0;
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);*/
        options.put("totalNumber", traceHisList.size());
        options.put("traceHisList", traceHisList);
        return options;
    }
}
