(function(vita) {
    var disputeUpload = vita.Backbone.BizView.extend({
    events: {
        "click #button": "_button",
        "change #file":"_showUploadFile",
        "change #chooseImage": "_chooseImage",
        "click #cancleCustBtn": "_closePage",
        "click #closeBtn": "_closePage",
        "click #disputeErrorTypes": "_clickDisputeError",
        "click #submitBtn": "_partyCheckArgue",
        "click #typeDropdownDiv" : "_toggleOpen",
    },
    global : {
       certNum:"",
       picPath:""

    },
    _initialize : function() {
        var widget = this,
            global = widget.global,
            element = $(widget.el),
            data = element.data("data");
        global.certNum=data.certNum;
        element.find("#certNum").val(data.certNum);
        element.find("#custName").val(data.custName);
        element.find("#picPath").val(data.picPath);
        element.find("#certTypeName").val(data.certTypeName);
        widget.model.set("picPath", data.picPath);
        widget.model.set("certType", data.certType);
        widget.model.set("certNum", data.certNum);
        widget.model.set("certTypeName", data.certTypeName);
        widget.model.set("custName", data.custName);
    },
    _getObjectURL :function (file) {
       // element.find("#certAddrTxt").val();
      var url = null;
      if (window.createObjcectURL != undefined) {
          url = window.createOjcectURL(file);
      } else if (window.URL != undefined) {
          url = window.URL.createObjectURL(file);
      } else if (window.webkitURL != undefined) {
          url = window.webkitURL.createObjectURL(file);
      }
      return url;
    },
    _clickDisputeError: function (e) {
        var widget = this,
            element = $(widget.el);
        data = element.data("data");
        var tarLi = $(e.target).closest("li");
        var data = tarLi.data("data");
        widget._setDisputeError(data);
    },

    _toggleOpen:function(e){
        $(e.currentTarget).toggleClass("open");
    },

    _setDisputeError: function (data) {
        var widget = this,
            global = widget.global,
            element = $(widget.el);
        element.find("#disputeErrorType").html(data.attrValueName + '<span class="caret"></span>');
        widget.model.set("disputeError", data.attrValue);
    },
    _partyCheckArgue: function () {
        var widget = this,
            global = widget.global,
            element = $(widget.el),
            professionName = null;
        gSession = widget.gSession;
        var soUtil = widget.require("soUtil");
        var data = widget.model.toJSON();
        var custName=$("#custName").val();
        var certNum=$("#certNum").val();
        var certType=$("#certTypeName").val();
        var picPath=$("#file").val();
        var disputeErrorType = $("#problemTypeSelect").val();
        var contactPhone=element.find("#disputePhone").val();
        var disputeRemark=$("#disputeRemark").val();
        var orgUscc=$("#orgUscc").val();
        gSession = widget.gSession;
        var resultMsg = "";
        var isNullOrEmptyCertNum = soUtil.isNullOrEmpty(certNum);
        var isNullOrEmptyCertType = soUtil.isNullOrEmpty(certType);

        if(isNullOrEmptyCertNum||isNullOrEmptyCertType){
            widget.popup("合规性验证证件类型和证件号不能为空");
            return false;
        }
        if (!widget._checkValue(disputeErrorType) || "请选择" == disputeErrorType) {
            widget.popup("错误类型不能为空");
            return false;
        }
        var param = {
            "problemDesc": disputeRemark,
            "proviceCode": "8110000",
            "problemType": disputeErrorType,
            "contactPhone":contactPhone,
            "appendix": picPath,
            "channelId" : gSession.curChannelId,
            "staffId" : gSession.staffId,
            "staffCode" : gSession.staffCode,
            "applyStaffName": widget.gSession.staffName,
            "applyStaffPhone": widget.gSession.contactTele
        }
        var partyCheck = {
            "lanId": "8110100",
            "custName": custName,
            "certType": certType,
            "certNum": certNum,
            "orgUscc" : orgUscc,
            "certPhoto": picPath,
            "applyStaffName": widget.gSession.staffName,
            "applyStaffPhone": widget.gSession.contactTele
        }
        param.partyCheck=partyCheck;
        var retVal="";
        var resultMsg="";

        widget.callService("partyCheckArgue", param, function (res) {
        	var ret = JSON.parse(res);
            if (ret.resultCode == 0) {
                retVal = ret.resultObject;
                if (retVal == null ) {
                    widget.popup("争议单上传失败");
                    return false;
                }
                if (retVal != null ) {
                    widget.popup(retVal.retMsg);
                }
            }else{
                resultMsg = ret.resultMsg;
            }
            var isNullOrEmpty = soUtil.isNullOrEmpty(resultMsg);
            if (!isNullOrEmpty) {
                widget.popup(resultMsg);
                return false;
            }
        }, {
            async: false,
            mask: true,
            headers : {
                "regionId" : widget.gSession.installArea
            }
        });
        widget._closePage();
    },
    _checkValue : function(condValue) {
        var widget = this;
        var soUtil = widget.require("soUtil");
        var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
        return isNullOrEmpty ? false : true;
    },
    _closePage: function () {
        var widget = this,
            element = $(widget.el);
        element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
    },
    getValue : function() {
        var widget = this;
        return this.global.picPath;
    },
    _showUploadFile:function(e){
        var widget = this,arr = [];
        for (var i = 0; i < $(e.currentTarget).get(0).files.length; i++) {
            arr.push($(e.currentTarget).get(0).files[i].name)
        }
        var strr = $(widget.el).find('input[type=file]').val();
        $(widget.el).find("#show-file").val(arr)
    },
  });
  vita.widget.register("disputeUpload", disputeUpload, true);
})(window.vita);
