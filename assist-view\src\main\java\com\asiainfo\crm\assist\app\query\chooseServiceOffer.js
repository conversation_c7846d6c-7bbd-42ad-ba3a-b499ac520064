(function(vita){
	var chooseServiceOffer = vita.Backbone.BizView.extend({
		events : {
			"click #searchBtn" : "_queryList",
			"click #searchDel": "_searchParamsDel",
			"click .close" : "_closePage",
			"click #submit " : "_sumbitData",
			"click .pagination li" : "_pageClick"
		},
		_initialize : function() {
			debugger;
			var widget = this,
			element = $(widget.el);
            /**
             * 设置之前已经被选择维度，进行过滤
             */
            var data = element.data("data");
            //初始化查询
            widget.global.pageSize = data.pageInfo ? data.pageInfo.pageSize : 10;

            widget._createPageInfo();
		},
		_closePage : function(){
			var widget = this,
			element = $(widget.el);
			var dialog = element.closest("[id=" + widget.widgetName + "Dialog]");
			if (dialog.length) {
				dialog.dialog("close");
			}
		},
		global : {
		},
		/**
		 * 获取查询条件参数对象
		 */
		_getConds : function(pageIndex) {
			var widget = this,
				element = $(widget.el);
			var param = {
					"pageInfo" : {
						"pageIndex" : pageIndex||1,
						"pageSize" : widget.global.pageSize
					}
				};
			
			var attrName = widget.model.get("attrName");
            attrName = attrName ? attrName.trim() : "";
			var soUtil = widget.require("soUtil");
			var isNullOrEmpty = soUtil.isNullOrEmpty(attrName);
			if (!isNullOrEmpty) {
				param.attrName = attrName;
			}
			return param;
		},
		/**
		 * 清空查询条件
		 */
		_searchParamsDel : function() {
			var widget = this,
				element = $(widget.el);
	//			gSession = widget.gSession;
			widget.model.set("attrName","");
		},
		_queryList : function() {
			var widget = this,
				element = $(widget.el);
			var param = widget._getConds();
			widget.refreshPart("qryItems", param, "#itemTable", function(res) {
                widget._createPageInfo($(res));
				}, {
					mask : true
				});
		},
		setDatas : function(isBatchSelect){
			var widget = this, element = $(widget.el);
			widget.model.set("isBatchSelect",isBatchSelect);
		},
		_sumbitData : function (e){
			var widget = this,
				element = $(widget.el);
			validate = false;
			//该字段是否为多选类型
			var isBatchSelect = widget.model.get("isBatchSelect");
			var checkeds = element.find('#items').find('input:checked');
			var idArray = [];
			var items = [];
			$.each(checkeds,function(i,item){
				idArray.push($(item).data('data').attrValue);
				items.push($(item).data('data'));
			});
			if(idArray.length == 0){
				widget.popup("请选择业务动作！");
				return false;
			}
			//如果不是多选，判断单选
			if(isBatchSelect != "YES" && idArray.length > 1){
				widget.popup("只能选择一个业务动作!");
				return false;
			}
			validate = true;
			if(isBatchSelect != "YES"){
				widget.model.set({
					validate : validate,
					attrValue : items[0].attrValue,
					attrName : items[0].attrName
				});
			}else{
				widget.model.set({
					validate : validate,
					items : items
				});
			}
			widget._closePage();
			return false;
		},
		getValue : function() {
			var widget = this;
			var valueJSON = widget.model.toJSON();
			return valueJSON;
		},
        _createPageInfo : function(el) {
            var widget = this, element = el||$(widget.el);
            var showPageInfo = element.find("#showPageInfo"),
                totalNumber = showPageInfo.data("totalNumber");
            var paging = widget.require("paging");
            var e = paging.new({
                pageIndex: 1,
                recordNumber: widget.global.pageSize,
                total: totalNumber,
                callback : function(pageIndex){
                    var param = widget._getConds(pageIndex);
                    widget.refreshPart("qryItems", param, ".table-hover", function(res) {}, {
                            mask : true
                        });
				}
			});
            showPageInfo.empty().append(e.getElement());
        }
	});
	 vita.widget.register("chooseServiceOffer", chooseServiceOffer, true); 
})(window.vita);
