package com.asiainfo.crm.assist.app.orderCheckBusi;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderCheckBusiSMO;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("vita.updateOrderCheckBusi")
public class UpdateOrderCheckBusi extends AbstractComponent {
    @Autowired
    private IOrderCheckBusiSMO orderCheckBusiSMO;
    @Override
    public Map achieveData(Object... params) throws Exception {
        String json = params[0].toString();
        Map options = jsonConverter.toBean(json, Map.class);
        String orderCheckBusiId = MapUtils.getString(options, "orderCheckBusiId");
        Map<String, Object> argMap = new HashMap<String, Object>();
        argMap.put("orderCheckBusiId", orderCheckBusiId);
        Map<String, Object> invokeResult = (Map<String, Object>) invokeMethod("orderCheckBusi", "queryOrderCheckBusis", new Object[]{jsonConverter.toJson(argMap)});
        List<Map> orderCheckBusis = (List<Map>) MapUtils.getObject(invokeResult, "orderCheckBusis");
        Map<String, Object> data = new HashMap<String, Object>();
        Map<String, Object> checkBusiInfo = null;
        if (orderCheckBusis != null) {
            checkBusiInfo = orderCheckBusis.get(0);
        }
        data.put("checkBusiInfo", checkBusiInfo);
        return data;
    }

    public String editOrderCheckBusi(String jsonStr) throws Exception {
        String resultJson = orderCheckBusiSMO.updateOrderCheckBusi(jsonStr);
        return resultJson;
    }
}
