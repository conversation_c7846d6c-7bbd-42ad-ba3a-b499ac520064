<div data-widget="reportListBusinessHall" style="height: 100%">
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->

        <!--客户定位 start-->
        <div class="box-rightmax">
            <div class="positionuser_title"><h5 class="textcolor6"><i
                    class="icon-user glyphicon titledot"></i><span>客户定位</span></h5></div>
            <div class="positionuser_search">
                <div class="searchbutt"><i class="icon-search glyphicon text16"></i></div>
                <input type="text" class="form-control" placeholder="输入客户证件活名称搜索">
            </div>

            <div class="scan_box">
                <span class="icon-scan glyphicon scanf"></span>
                <div>扫描身份证</div>
            </div>
            <div class="adduser_box"><a class="btn btn-primary btn-rounded" href="buttons.html#">新增客户</a></div>
        </div>
        <!---客户定位en ReportSummary-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page-nav">
                    <div class="row">
                        <div class="pagenav_box">
                            <div class="page_title">大分成报表清单（按营业厅）</div>
                        </div>
                    </div>
                </div>
                <div class="page_main ">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>

                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-6 ">
                                            <label class="col-md-4 control-label lablep">
                                                登录员工工号</label>
                                            <div class="col-md-8">
                                                <input id="staffId" readonly="readonly" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="col-md-4 control-label lablep">
                                                登录员工名称</label>
                                            <div class="col-md-8">
                                                <input id="staffName" readonly="readonly" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="col-md-4 control-label lablep">
                                                <input id="c_channelId" type="checkbox" name="payment">
                                                要统计的渠道</label>
                                            <div class="col-md-8">
                                                <div class="input-group">
                                                    <input type="text" id="channelId" readonly="readonly" class="form-control" placeholder="">
                                                    <div class="input-group-btn">
                                                        <button id="channelIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6" style="visibility: hidden">
                                            <label class="col-md-4 control-label lablep">
                                            </label>
                                            <div class="col-md-8">
                                                <input  readonly="readonly" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6 ">
                                            <label class="col-md-4 control-label lablep">
                                                起始时间</label>
                                            <div class="col-md-8">
                                                    <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="col-md-4 control-label lablep">
                                                截止时间</label>
                                            <div class="col-md-8">
                                                    <input name="endDate" id="endDate" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-12">
                                            <label class="col-md-2 control-label lablep"></label>
                                            <div class="col-md-10 text-right">
                                                <!--<button type="button" class="btn btn-white">清除</button>-->
                                                <button id="btn-query" type="button" class="btn btn-primary">查询</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row" id="row" style="display: none">
                                <div class="form_title">
                                    <div><i class="bot"></i>营业清单</div>
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12">
                                     <!--   <a id="print" class="btn btn-gray btn-outline">
                                            <i class="glyphicon fa-print text18"> </i> 打印
                                        </a>
                                        <a id="export" class="btn btn-gray btn-outline">
                                            <i class="glyphicon fa-derive text18"> </i> 导出
                                        </a>-->
                                        <button type="button" id="print" class="btn btn-primary" >打印</button>
                                        <button type="button" id="export" class="btn btn-primary" >导出</button>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <p style="color: red" id="countSize"></p>
                                    </div>
                                    <div class="col-lg-12 " id="divTable" >
                                        <div class="mart10" id="ReportListBusinessHallList">
                                            <table class="table table-bordered">
                                                <thead>
                                                <tr>
                                                    <td class="labellcont">制表人</td>
                                                    <td id="tableTd"></td>
                                                    <td class="labellcont">制表时间</td>
                                                    <td id="tableDate"></td>
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                </tr>
                                                <tr>
                                                    <th>渠道ID</th>
                                                    <th>渠道名称</th>
                                                    <th>地区ID</th>
                                                    <th>地区名称</th>
                                                    <th>营业员工号</th>
                                                    <th>营业员名称</th>
                                                    <th>用户号码</th>
                                                    <th>受理时间</th>
                                                    <th>预存支付计划名称</th>
                                                    <th>预存款金额</th>
                                                    <th>座扣金额</th>
                                                    <th>应缴营业款金额</th>
                                                </tr>
                                                </thead>
                                                <tbody >
                                                    #if($options != "null" && $options.ReportListBusinessHallList &&
                                                    $options.ReportListBusinessHallList != "null"  &&
                                                    $options.ReportListBusinessHallList.size() > 0)
                                                    #foreach($spList in $options.ReportListBusinessHallList)
                                                <tr>
                                                    <td>$!{spList.orgId}</td>
                                                    <td>$!{spList.channelName}</td>
                                                    <td>$!{spList.commonRegionId}</td>
                                                    <td>$!{spList.regionName}</td>
                                                    <td>$!{spList.staffAccount}</td>
                                                    <td>$!{spList.staffName}</td>
                                                    <td>$!{spList.accNum}</td>
                                                    <td>$!{spList.createDate}</td>
                                                    <td>$!{spList.prodName}</td>
                                                    <td>$!{spList.realAmount}</td>
                                                    <td>$!{spList.deductionAmount}</td>
                                                    <td>$!{spList.paidInAmount}</td>
                                                </tr>
                                                #end
                                                #end
                                                <tr>
                                                    <th>总计</th>
                                                    <th></th>
                                                    <th></th>
                                                    <th></th>
                                                    <th></th>
                                                    <th></th>
                                                    <th></th>
                                                    <th></th>
                                                    <th></th>
                                                    <th>$!{options.countRealAmount}</th>
                                                    <th>$!{options.countSun}</th>
                                                    <th>$!{options.countPaidInAmount}</th>
                                                </tr>
                                                </tbody>
                                            </table>
                                            <div id="showPageInfo">
                                            </div>
                                            #if($options.totalNumber && $options.totalNumber != "null")
                                            <p class="vita-data">{"totalNumber":$options.totalNumber,"exportNum":$options.exportNum}</p>
                                            #end
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
