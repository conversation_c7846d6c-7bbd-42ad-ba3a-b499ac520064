package com.asiainfo.crm.assist.app.orderDdn;

import com.alibaba.fastjson.JSONObject;
import com.asiainfo.appframe.ext.flyingserver.org.apache.commons.collections.MapUtils;
import com.asiainfo.crm.common.AbstractComponent;

import com.asiainfo.crm.service.intf.ICrmTools;
import com.asiainfo.crm.service.intf.IOrderQueryMultiSMO;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Component("vita.chooseTool")
public class ChooseTool extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(ChooseTool.class);
    @Autowired
    private IOrderQuerySMO iOrderQuerySMO;
    @Autowired
    private IOrderQueryMultiSMO iOrderQueryMultiSMO ;
    @Autowired
    private ICrmTools iCrmTools;

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        String paramStr = (String)params[0];
        Map paramMap = jsonConverter.toBean(paramStr, Map.class);
        String repairResult =  iOrderQuerySMO.qryRepairTypesList(paramMap);
        Map repairMap = jsonConverter.toBean(repairResult, Map.class);
        List repairList = (List) MapUtils.getObject(repairMap, "resultObject");
        Map repair = new HashMap();
        if (null!= repairList && repairList.size()>0){
             repair = jsonConverter.toBean(JSONObject.toJSONString(repairList.get(0)), Map.class);
            Map toolsMap=new HashMap<>();
            toolsMap.put("repairType",repair.get("repairType"));
            toolsMap.put("repairSubType",repair.get("repairSubType"));
            String result =  iOrderQuerySMO.qryOrderToolsList(toolsMap);
            Map map = jsonConverter.toBean(result, Map.class);
            List tools = (List) MapUtils.getObject(map, "resultObject");
            if ("coId".equals(paramMap.get("func"))){
                //0 根据coId 查询
                options.put("func","coId");
            }else {
                //1 根据csNbr 查询
                options.put("func", "csNbr");
            }
            options.put("objIdType",paramMap.get("objIdType"));
            options.put("tools", tools);
            options.put("errType", paramMap.get("errType"));
            options.put("orderItemId", paramMap.get("orderItemId"));
            if (null!=paramMap.get("premaryKey")){
                options.put("premaryKey",paramMap.get("premaryKey").toString() );
            }
            if (null!=paramMap.get("orderItemIds")){
                options.put("orderItemIds",paramMap.get("orderItemIds").toString() );
            }
        }
        options.put("str",  paramMap.get("str"));
        return options;
    }

    public Map sendMessage(String params) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        Map req = jsonConverter.toBean(params,Map.class);
        List<Long> orderItemIds = new ArrayList<>();
        //主键冲突错误
        if (null!=req.get("errType").toString()&&req.get("errType").toString().equals("2")){
            if (null!=req.get("premaryKey")){
                req.put("premaryKey",StrToList(req.get("premaryKey").toString()));
            }
            if (null!=req.get("orderItemIds")){
                req.put("orderItemIds",StrToList(req.get("orderItemIds").toString()));
            }
            try {
                String result = null;
                if (null!=req.get("toolsId")&&req.get("toolsId").equals("110006")){
                    result =iCrmTools.saveCalEffRepare(JSONObject.toJSONString(req));
                }else {
                    result =iCrmTools.orderErrRepair(JSONObject.toJSONString(req));
                }
                Map map = jsonConverter.toBean(result, Map.class);
                if (null!=map &&null!=map.get("result")){
                    Integer resultCode = Integer.parseInt(map.get("result").toString());
                    options.put("resultCode", resultCode);
                    if (null!=map.get("resultMsg")){
                        options.put("resultMsg", map.get("resultMsg"));
                    }else {
                        options.put("resultMsg", "resultMsg返回为空");
                    }
                }else {
                    options.put("resultCode", 1);
                    options.put("resultMsg", "返回为空,未匹配到对应发送消息的接口");
                }
            }catch (Exception e){
                options.put("resultCode", 1);
                options.put("resultMsg", e.getMessage());
            }
        }else {
            //errType1  重发消息
            if (null!=req.get("orderItemIds")){
                req.put("orderItemIds",StrToList(req.get("orderItemIds").toString()));
            }else {
                orderItemIds.add(Long.parseLong(req.get("orderItemId").toString()));
                req.put("orderItemIds",orderItemIds);
            }
            if (null!=req.get("objIdType")&&req.get("objIdType").equals("archGrpId")){
                req.put("archGrpId",Long.parseLong(req.get("orderItemId").toString()));
            }
            try {
                String result = null;
                result =iOrderQueryMultiSMO.sendMsgRepairTools(JSONObject.toJSONString(req));
                Map map = jsonConverter.toBean(result, Map.class);
                Map resultObject = (Map) MapUtils.getObject(map, "resultObject");
                if (null!=resultObject&&resultObject.size()>0){
                    Integer resultCode = (Integer) resultObject.get("resultCode");
                    options.put("resultCode", resultCode);
                    options.put("resultMsg", resultObject.get("resultMsg"));
                }else if (resultObject.size()==0){
                    options.put("resultCode", 1);
                    options.put("resultMsg", "后台未找到对应的megType="+req.get("msgType").toString());
                }
                else {
                    options.put("resultCode", 1);
                    options.put("resultMsg", map.get("resultMsg"));
                }
            }catch (Exception e){
                options.put("resultCode", 1);
                if (null==options.get("resultMsg")){
                    options.put("resultMsg", e.getMessage());
                }
            }
        }
        return  options;

    }


    public List StrToList(String str) throws Exception {
        String demosub = str.substring(1,str.length()-1);
        demosub = demosub.replaceAll("\\s+", "")
                .replace("\n","")
                .replace("\t","")
                .replace("\r","");;
        String demoArray[] = demosub.split(",");
        List demoList = Arrays.asList(demoArray);
        return demoList;
    }


}
