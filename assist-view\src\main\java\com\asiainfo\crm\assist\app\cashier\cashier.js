(function(vita) {
	var cashier = SoComp.extend({
		events : {
			"click #returnBtn" : "returnBtnClick",
			"click #nextBtn" : "nextBtnClick"
		},
		_initialize : function() {},
		returnBtnClick : function() {
			this.end("back");
		},
		nextBtnClick : function(extData) {
			var widget = this, element = $(widget.el), gSession = widget.gSession;
			var data = element.data("data"), objUtil = widget.require("objectUtil");
			if (!objUtil.isEmpty(data) && data.flowInstId && data.submitFillOrderRespVo) {
				var custOrder = data.submitFillOrderRespVo;
				var param = {
					custOrderId:custOrder.custOrderId,
					custOrderNbr:custOrder.custOrderNbr,
					custTokenId:gSession.custTokenId,
					glbSessionId:gSession.glbSessionId
				};
				if(extData && extData.isPay){
                    param.isPay = extData.isPay;
				}
				widget.callService("commitOrder", JSON.stringify(param),function(re) {
					if(re){
						widget.dispatchEvent("index.indent", "hideCustView");
						widget.end("findNextNode", {
							"param" : {
								"flowParam" : {
									"curNodeCode" : "cashier",
									"flowCode" : "commitOrder"
								},
								"flowInstParamVo" : {
									"flowCode" : "commitOrder",
									"flowInstId" : data.flowInstId,
	                                "flowInstIds" : data.flowInstIds,
									"custOrderId" : custOrder.custOrderId,
									"custOrderNbr" : custOrder.custOrderNbr
								},
								"redisInfo" : {
									"custTokenId" : gSession.custTokenId,
									"glbSessionId" : gSession.glbSessionId
								}
							}
						});
						var params = {
							"type" : "refresh",
							"custTokenId" : gSession.custTokenId
						};
						widget.refreshCart(params);
					}
				}, {
					async : false
				});
			}
		}
	});
	vita.widget.register("cashier", cashier, true);
})(window.vita);
/**
 * 提供给收银调用的上一步,订单结算方法
 * 放组件内,通过iFrame内获取父cashier组件时,
 * dom通过组件名获取不到组件,不能调用组件内方法
 **/
var returnBtnClick = function(){
	debugger;
    var comp = $("div[data-widget=cashier]");
    var dialog = comp.closest("[id=cashierDialog]");
    if (dialog.length) {
        dialog.dialog("close");
    }
};
var nextBtnClick = function(data){
	debugger;
	var comp = $("div[data-widget=cashier]");
	var dialog = comp.closest("[id=cashierDialog]");
	if (dialog.length) {
		dialog.dialog("close");
	}
};