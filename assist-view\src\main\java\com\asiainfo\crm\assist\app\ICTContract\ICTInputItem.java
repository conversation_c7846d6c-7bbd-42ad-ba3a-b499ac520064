package com.asiainfo.crm.assist.app.ICTContract;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IAssetCenterSMO;
import com.asiainfo.crm.util.ListUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类简述: ICT合同收入计划查询
 */
@Component("vita.ICTInputItem")
public class ICTInputItem extends AbstractComponent {

    private static final Logger LOGGER = LoggerFactory.getLogger(ICTInputItem.class);

    @Autowired
    private IAssetCenterSMO assetCenterSMO;
    @Override
    public Map achieveData(Object... params) throws IOException {
        String paramStr = (String)params[0];
        Map paramMap = jsonConverter.toBean(paramStr, Map.class);
        return paramMap;

    }
    public Map queryInputItem(String jsonStr) throws IOException {
        String re=assetCenterSMO.queryIctInputItemByCode(jsonStr);
        Map<String, List> resultObject = (Map) resolveResult(re);
        List<Map> items= resultObject.get("ictInputItemVoExtendList");
        for(Map item : ListUtil.nvlList(items)){
            Integer confirmFee=item.get("confirmFee")!=null ?(Integer)item.get("confirmFee"):0;
            BigDecimal b   =   new   BigDecimal(100);
            BigDecimal a   =   new   BigDecimal(confirmFee);
            a=a.divide(b,2,BigDecimal.ROUND_HALF_UP);
            item.put("confirmFee",a);
        }
        Map<String, Integer> pageInfo = (Map) resultObject.get("pageInfo");
        List contracts = new ArrayList();
        Map options=new HashMap();
        long rowCount = 0L;
        if(items != null && !items.isEmpty()){
            rowCount = Long.valueOf(pageInfo.get("rowCount"));
            options.put("totalNumber", rowCount);
            options.put("ICTSubProjectDeail", items);
            return options;
        }
        options.put("totalNumber", rowCount);
        options.put("ICTSubProjectDeail", contracts);
        return options;
    }


}
