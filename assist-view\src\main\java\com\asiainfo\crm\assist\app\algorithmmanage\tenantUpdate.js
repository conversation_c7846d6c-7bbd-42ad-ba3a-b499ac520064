(function (vita) {
    var tenantUpdate = vita.Backbone.BizView.extend({
        events: {
            "click #submitBtn": "_handleSubmit",
            "click #cancelBtn": "_handleClosePage",
            "click #algoIdBtn": "_algorithmChoose",
        },
        global: {
            preset: "datetime",
            algorithmChoose: "../query/algorithmChoose"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            widget._initDateDom();
            var data = element.data("data");
            var tenantInfo = data.tenantInfo;
            widget.model.set("tenantNbr", tenantInfo.tenantNbr);
            widget.model.set("statusCd", tenantInfo.statusCd);
            widget.model.set("tenantDesc", tenantInfo.tenantDesc);
            element.find("#algoId").val(tenantInfo.tar.algoNbr || tenantInfo.tar.algoId).attr("value", tenantInfo.tar.algoId);
            element.find("#effDate").val(tenantInfo.tar.effDate);
            element.find("#expDate").val(tenantInfo.tar.expDate);
            //关联的算法信息
            var tarAlgorithm = tenantInfo.tar;
            widget.model.set("orginalAlgoId",tarAlgorithm.algoId);
            widget.model.set("relId",tarAlgorithm.relId);
        },
        _initDateDom: function(){
            var widget = this, element = $(widget.el);
            var datetime = widget.require("datetime");
            var effDate = element.find("input[name='effDate']");
            var expDate = element.find("input[name='expDate']");
            datetime.register(effDate, {
                preset: widget.global.preset,
                dateFormat: "yy-mm-dd hh:mm:ss"
            });
            datetime.register(expDate, {
                preset: widget.global.preset,
                dateFormat: "yy-mm-dd hh:mm:ss"
            });
        },
        _handleSubmit: function () {
            var widget = this, element = $(widget.el);
            var algoId = element.find("#algoId").attr("value");
            var tar = {
                "algoId": algoId,
                "effDate": element.find("#effDate").val(),
                "expDate": element.find("#expDate").val()
            };
            var orginalAlgoId = widget.model.get("orginalAlgoId");
            if(orginalAlgoId && (orginalAlgoId == algoId)){
                tar.relId = widget.model.get("relId");
            }
            var params = {
                "tenantNbr": widget.model.get("tenantNbr"),
                "tenantDesc": widget.model.get("tenantDesc"),
                "statusCd": widget.model.get("statusCd"),
                "tar": tar
            };
            var data = element.data("data");
            var tenantInfo = data.tenantInfo;
            var tenantId = tenantInfo.tenantId;
            params.tenantId = tenantId;
            if (!widget._validate()) {
                return false;
            }
            widget.callService("updateTenant", params, function (res) {
                var ret = JSON.parse(res);
                var resultMsg = null, resultObject = null;
                if (ret.resultCode == 0) {
                    resultObject = ret.resultObject;
                    if (resultObject.code != 0) {
                        widget.popup(resultObject.msg || "修改失败");
                        return false;
                    }
                } else {
                    resultMsg = ret.resultMsg || "修改失败";
                }
                if (widget._checkValue(resultMsg)) {
                    widget.popup(resultMsg);
                    return false;
                }
                widget.popup("修改租户成功", function () {
                    widget._handleClosePage();
                });

            }, {
                async: true,
                mask: true
            });
        },
        _checkValue: function (condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty ? false : true;
        },
        _validate: function () {
            var widget = this, element = $(widget.el);
            var tenantNbr = widget.model.get("tenantNbr");
            if (!widget._checkValue(tenantNbr)) {
                widget.popup("租户编码不能为空，请输入租户编码");
                return false;
            }
            var algoId = element.find("#algoId").attr("value");
            if (!widget._checkValue(algoId)) {
                widget.popup("请选择算法");
                return false;
            }
            var effDate = element.find("#effDate").val();
            if (!widget._checkValue(effDate)) {
                widget.popup("生效时间不能为空");
                return false;
            }
            var expDate = element.find("#expDate").val();
            if (!widget._checkValue(expDate)) {
                widget.popup("失效时间不能为空");
                return false;
            }
            if(effDate >= expDate){
                widget.popup("失效时间应大于生效时间");
                return false;
            }
            return true;
        },
        _handleClosePage: function () {
            var widget = this, element = $(widget.el);
            element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
        },
        _algorithmChoose: function () {
            var widget = this, element = $(widget.el);
            var compCode = "algorithmChoose";
            widget.dialog({
                id: "algorithmChooseDialog",
                url: widget.global.algorithmChoose,
                params: {},
                onClose: function (res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#algoId").val(data.algoNbr).attr("value", data.algoId);
                }
            });
        }
    });
    vita.widget.register("tenantUpdate", tenantUpdate, true);
})(window.vita);