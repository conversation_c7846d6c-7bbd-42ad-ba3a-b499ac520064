package com.asiainfo.crm.assist.app.algorithmmanage;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ISecuritySmo;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Date 2022/1/20 16:04
 */
@Component("vita.algorithmList")
public class AlgorithmList extends AbstractComponent {
    @Autowired
    private ISecuritySmo securitySmo;

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map<String, Object> data = new HashMap<String, Object>();
        data.put("dataSecurityStatusCds", AssistMDA.DATA_SECURITY_STATUS_CDS);
        return data;
    }

    public Map<String, Object> queryAlgorithms(String params) throws Exception {
        String result = securitySmo.findAlgorithmsForPages(params);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(result);
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap, "pageInfo");
        List<Map> algorithms = (List) MapUtils.getObject(resultObjectMap, "algos");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        Map<String, Object> options = Maps.newHashMap();
        options.put("pageCount", pageCount);
        options.put("pageIndex", pageIndex);
        options.put("totalNumber", total);
        options.put("algorithms", algorithms);
        options.put("dataSecurityStatusCds", AssistMDA.DATA_SECURITY_STATUS_CDS);
        return options;
    }

    /**
     * 算法归档
     *
     * @param params
     * @return
     */
    public String archiveAlgorithm(String params) {
        String result = securitySmo.updateAlgorithm(params);
        return result;
    }
}
