<div data-widget="intfWhole" style="height:100%">
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                	地 区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custId" type="checkbox" name="payment">
                                                </label> 所属客户
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="custId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="custIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_accNum" type="checkbox" name="payment">
                                                </label> 号码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="accNum" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">用户状态</label>
                                            <div class="col-md-7">
                                                <select id="b_statusCd" class="form-control">
                                                    <option value="1">非实名单停</option>
                                                    <option value="2">非实名双停</option>
                                                    <option value="3" selected="selected">非实名停机(单停，双停)</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <div class="col-md-12 searchbutt_r" align="center">
                                                <button id="btn-query" type="button" class="btn btn-primary">查询</button>
                                            </div>
                                        </div>
                                        <input id="channelId" type="hidden" class="form-control" placeholder="" readonly="readonly">
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12">
                                        <a id="recoverConnect" class="btn btn-gray btn-outline">  复机
                                        </a>
                                        <a id="temporaryRecover" class="btn btn-gray btn-outline">  临时复机
                                        </a>
                                    </div>
                                    <div class="col-lg-12 mart10" id="userListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>选择</th>
                                                <th>号码</th>
                                                <th>用户姓名</th>
                                                <th>用户状态</th>
                                            </tr>
                                            </thead>
                                            <tbody id="userList">
                                            #if($options != "null" && $options.users && $options.users != "null" &&
                                            $options.users.size() > 0)
                                            #foreach($user in $options.users)
                                            <tr>
                                                <td><label class="wu-radio full absolute" data-scope=""><input
                                                        type="radio" name="userInfo">
                                                        <p class="vita-data">{"data":$user}</p> 
                                                    </label>
                                                </td>
                            							
                                                <td>$!{user.accNum}</td>
                                       
                                                <td>$!{user.userName}</td>
                                               
                                                <td>$!{user.statusName}</td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.users && $options.users != "null" &&
                                            $options.users.size() == 0)
                                                <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
