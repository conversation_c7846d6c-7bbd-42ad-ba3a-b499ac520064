<div data-widget="preAccNumQuery" style="height: 100%">

    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>预占号码查询</div>
                                </div>

                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-4 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_accNum" type="checkbox" name="payment">
                                                </label>
                                                接入号码：
                                            </label>
                                            <div class="col-md-7">
                                                <input type="text" id="accNum" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_partyCert" type="checkbox" name="payment">
                                                </label>
                                                证件号码：
                                            </label>
                                            <div class="col-md-7">
                                                <input id="partyCert" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                                <div class="col-md-2 text-left">
                                                    <button type="button" id="btn-query" class="btn btn-primary">查询</button>
                                                </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询结果</div>
                                </div>
                                <div class="wmin_content" id="preAccNumResult">
                                    <div class="col-lg-12 mart10" id="preAccNumList">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>编号</th>
                                                <th>号码</th>
                                                <th>随机密码</th>
                                                <th>当前状态</th>
                                                <th>号码等级</th>
                                                <th>预占时间</th>
                                                <th>备注</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                                #if($options != "null" && $options.preAccNumList && $options.preAccNumList != "null" &&
                                                $options.preAccNumList.size() > 0)
                                                #foreach($preAccNum in $options.preAccNumList)
                                                <tr>
                                                    <td>$!{preAccNum.precontractCd}</td>
                                                    <td>$!{preAccNum.precontractNum}</td>
                                                    <td>$!{preAccNum.identityNum}</td>
                                                    <td>$!{preAccNum.statusName}</td>
                                                    <td>$!{preAccNum.pnlName}</td>
                                                    <td>$!{preAccNum.startDate}</td>
                                                    <td>$!{preAccNum.remarks}</td>
                                                </tr>
                                                #end
                                                #elseif($options != "null" && $options.preAccNumList && $options.preAccNumList != "null" &&
                                                $options.preAccNumList.size() == 0)
                                                <tr><td align='center' colspan='7'>未查询到数据！<td></tr>
                                                #end
                                            </tbody>
                                        </table>
                                    </div>
                                    <!--翻页start -->
                                    <div id="showPageInfo">
                                    </div>
                                    #if($options.totalNumber && $options.totalNumber != "null")
                                    <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                    #end
                                    <!--翻页end -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
