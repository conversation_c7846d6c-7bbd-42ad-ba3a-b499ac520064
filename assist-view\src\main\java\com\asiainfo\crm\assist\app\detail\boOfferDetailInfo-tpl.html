<div data-widget="boOfferDetailInfo" style="height: 100%">
    <p class="vita-data">{"data" : {
            "custOrderId":"$!options.custOrderId",
            "orderItemId":"$!options.orderItemId",
            "prodId":"$!options.prodId"
    }}
    </p>
    #macro( nullNotShow $val)
        #if($val && $val != "null")
            $!val
        #end
    #end

    #macro( table $arry $name $value $e )
        <table class="table table-bordered conttd-w">
            <tbody>
            <tr>
                #foreach($a in $arry)
                <td class="labellcont">#nullNotShow($a[$name])&nbsp;</td>
                <td>#nullNotShow($a[$value])</td>
                #if ( ($velocityCount % 2 == 0) && ($velocityCount - $arry.size() != 0))
            </tr>
            <tr>
                #end
                #end
                #if($e)
                <td class="labellcont">&nbsp;</td>
                <td></td>
                #end
            </tr>
            </tbody>
        </table>
    #end
    <div class="calcw_rightbox noneleft">
        <div class="calctitle">
            <div class="titlefont">
                <ul id="myTab" class="nav nav-tabs">
                    <li class="active"><a id="#1">受理信息</a></li>
                </ul>
            </div>
            <div class="toolr">
                <!--<button id="closeBtn" type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>-->
            </div>
        </div>

        <div class="calcw_rightcont">
            #if($options.detailInfo && $options.detailInfo != "null")
            #set($offerItemVo = $options.detailInfo.offerOrderItemVo)
            #if($offerItemVo && $offerItemVo != "null")
            <div class="tab-content">
                <div class="tab-pane fade active in" id="1">
                    #set($baseInfo = $offerItemVo.orderItemDetailInfo)
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>基本信息</h5>
                        <table class="table table-bordered conttd-w">
                            <tbody>
                            <tr>
                                <td class="labellcont">订单项ID</td>
                                <td>#nullNotShow($baseInfo.orderItemId)</td>
                                <td class="labellcont">受理地区</td>
                                <td>#nullNotShow($baseInfo.acceptRegionName)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">受理渠道</td>
                                <td>#nullNotShow($baseInfo.createOrgName)</td>
                                <td class="labellcont">业务类型</td>
                                <td>#nullNotShow($baseInfo.serviceOfferName)-#nullNotShow($baseInfo.applyObjSpecName)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">受理时间</td>
                                <td>#nullNotShow($baseInfo.acceptDate)</td>
                                <td class="labellcont">受理员工</td>
                                <td>#nullNotShow($baseInfo.createStaffName)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">状态</td>
                                <td>#nullNotShow($baseInfo.statusCdName)</td>
                                <td class="labellcont">竣工时间</td>
                                <td>#nullNotShow($baseInfo.completeDate)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">压单时间</td>
                                <td>#nullNotShow($baseInfo.retentionDate)</td>
                                <td class="labellcont">未竣工原因</td>
                                <td>#nullNotShow($baseInfo.uncompleteReason)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">第一协销人</td>
                                <td>
                                    #foreach($name in $baseInfo.devStaffNames)
                                    #if($name.devStaffType == "1000")
                                    #nullNotShow($name.devStaffName)
                                    #end
                                    #end
                                </td>
                                <td class="labellcont">第二协销人</td>
                                <td>
                                    #foreach($name in $baseInfo.devStaffNames)
                                    #if($name.devStaffType == "2000")
                                    #nullNotShow($name.devStaffName)
                                    #end
                                    #end
                                </td>
                            </tr>
                            #if($baseInfo.orderContactInfos && $baseInfo.orderContactInfos!="null" && $baseInfo.orderContactInfos.size()>0)
                                #foreach($contact in $baseInfo.orderContactInfos)
                                <tr>
                                    <td class="labellcont">联系人</td>
                                    <td>#nullNotShow($contact.contactName)</td>
                                    <td class="labellcont">联系号码</td>
                                    <td>#nullNotShow($contact.contactPhone)</td>
                                </tr>
                                #end
                            #end
                            </tbody>
                        </table>
                    </div>

                    <!--销售品信息-->
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>销售品信息</h5>
                        <div class="row">
                            <div class="col-md-12">
                                <table class="table table-bordered conttd-w">
                                    <tbody>
                                    <!--客户-->
                                    <tr>
                                        <td class="labellcont">销售品名称</td>
                                        <td>#nullNotShow($offerItemVo.offerName)</td>
                                        <td class="labellcont">状态</td>
                                        <td>#nullNotShow($offerItemVo.statusCdName)</td>
                                    </tr>
                                    <!--生失效时间-->
                                    <tr>
                                        <td class="labellcont">生效时间</td>
                                        <td>
                                            #if($offerItemVo.effDates && $offerItemVo.effDates != "null" && $offerItemVo.effDates.size()>0)
                                            #foreach($effDate in $offerItemVo.effDates)
                                                #if($!effDate.operType == "1000")
                                                    <font color="red">【新】</font>
                                                #elseif($!effDate.operType == "1100")
                                                    <font color="red">【旧】</font>
                                                #else
                                                    <font color="red">【原】</font>
                                                #end
                                                #nullNotShow($effDate.value)
                                                <br/>
                                            #end
                                            #end
                                        </td>
                                        <td class="labellcont">失效时间</td>
                                        <td>
                                            #if($offerItemVo.expDates && $offerItemVo.expDates != "null" && $offerItemVo.expDates.size()>0)

                                            #foreach($expDate in $offerItemVo.expDates)
                                            #if($!expDate.operType == "1000")
                                            <font color="red">【新】</font>
                                            #elseif($!expDate.operType == "1100")
                                            <font color="red">【旧】</font>
                                            #else
                                            <font color="red">【原】</font>
                                            #end
                                            #nullNotShow($expDate.value)
                                            <br/>
                                            #end
                                            #end
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!--销售品参数-->
                    #if($offerItemVo.ordOfferInstAttrVos && $offerItemVo.ordOfferInstAttrVos != "null" && $offerItemVo.ordOfferInstAttrVos.size() > 0)
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>销售品参数</h5>
                        <div class="row">
                            <div class="col-md-12">
                                #set($even = $offerItemVo.ordOfferInstAttrVos.size() % 2 == 1)
                                #table($offerItemVo.ordOfferInstAttrVos,"attrName","attrValueName",$even)
                            </div>
                        </div>
                    </div>
                    #end

                    <!--销售品成员-->
                    #if($offerItemVo.offerMembers && $offerItemVo.offerMembers != "null" && $offerItemVo.offerMembers.size() > 0)
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>销售品成员</h5>
                        <div class="row">
                            <div class="col-md-12">
                                <table class="table table-bordered">
                                    <thead>
                                    <tr>
                                        <th>套餐名称</th>
                                        <th>成员角色</th>
                                        <th class="hidden-xs hidden-sm">成员类型</th>
                                        <th class="hidden-xs hidden-sm">规格名称</th>
                                        <th>成员号码</th>
                                        <th>动作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    #foreach($ooRole in $offerItemVo.offerMembers)
                                    <tr>
                                        <td>#nullNotShow($ooRole.offerName)</td>
                                        <td>#nullNotShow($ooRole.roleName)</td>
                                        <td class="hidden-xs hidden-sm">#nullNotShow($ooRole.memberTypeName)</td>
                                        <td class="hidden-xs hidden-sm">#nullNotShow($ooRole.memberSpecName)</td>
                                        <td>#nullNotShow($ooRole.memberNum)</td>
                                        #if($ooRole.operType && $ooRole.operType != "null" && $ooRole.operType == "1000")
                                        <td>加入</td>
                                        #else
                                        <td>退出</td>
                                        #end
                                    </tr>
                                    #end
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    #end

                    <!--订单属性-->
                    #if($baseInfo.orderItemAttrs && $baseInfo.orderItemAttrs != "null" && $baseInfo.orderItemAttrs.size()>0)
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>订单属性</h5>
                        <div class="row">
                            <div class="col-md-12">
                                #set($even = $baseInfo.orderItemAttrs.size() % 2 == 1)
                                #table($baseInfo.orderItemAttrs,"attrName","attrValueName",$even)
                            </div>
                        </div>
                    </div>
                    #end

                    <!--销售品 账户成员-->
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>账户成员</h5>
                        <div class="row">
                            <div class="col-md-12">
                                <table class="table table-bordered">
                                    <thead>
                                    <tr>
                                        <th>账户名称</th>
                                        <th>角色名称</th>
                                        <th>账户标识</th>
                                        <th class="hidden-xs hidden-sm">生效时间</th>
                                        <th class="hidden-xs hidden-sm">失效时间</th>
                                        <th>动作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    #if($offerItemVo.ordOfferObjInstRels && $offerItemVo.ordOfferObjInstRels != "null" && $offerItemVo.ordOfferObjInstRels.size() > 0)
                                    #foreach($acctRole in $offerItemVo.ordOfferObjInstRels)
                                    #if($acctRole.objType == "02")
                                    <tr>
                                        <td>#nullNotShow($acctRole.objName)</td>
                                        <td>#nullNotShow($acctRole.roleName)</td>
                                        <td>#nullNotShow($acctRole.objId)</td>
                                        <td class="hidden-xs hidden-sm">#nullNotShow($acctRole.effDate)</td>
                                        <td class="hidden-xs hidden-sm">#nullNotShow($acctRole.expDate)</td>
                                        #if($acctRole.operType && $acctRole.operType != "null" && $acctRole.operType == "1000")
                                        <td>加入</td>
                                        #else
                                        <td>退出</td>
                                        #end
                                    </tr>
                                    #end
                                    #end
                                    #end
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    #if($offerItemVo.discountOffers && $offerItemVo.discountOffers != "null" && $offerItemVo.discountOffers.size() > 0)
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder">
                            <i class="bot"></i>优惠信息
                        </h5>
                        <div class="row">
                            <div class="col-md-12">
                                <table class="table table-bordered conttd-tow">
                                    <tbody>
                                    <tr>
                                        <td class="labellcont">套餐级优惠</td>
                                        <td>
                                            #foreach($preferentialInfo in $offerItemVo.discountOffers)
                                            <a class="textcolorgreen marl15 col-lg-4">
                                                $!preferentialInfo.offerName
                                            </a>
                                            #end
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    #end

                </div>
            </div>
            #end
            #end
        </div>
    </div>
</div>