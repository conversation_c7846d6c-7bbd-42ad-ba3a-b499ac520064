(function (vita) {
    var blackListManage = vita.Backbone.BizView.extend({
    	events : {
    		"click #m_fileAdd":"_selectExcelAdd",
    		"change .m_fileAdd":"_fileOnChangeAdd",
    		"click #m_fileDel":"_selectExcelDel",
    		"change .m_fileDel":"_fileOnChangeDel",
    		"click #readCard":"_readCard",
    		"click #qryBlacklist":"_qryBlacklist",
    		"click #addTempletDownload":"_addTempletDownload",
    		"click #delTempletDownload":"_delTempletDownload",
    		"click #batchAdd":"_batchAdd",
    		"click #batchDel":"_batchDel",
    		"click #batchDelOne":"_batchDelOne"
    	},
    	
    	global: {
    		
        },
        
        _initialize:function(){
        	
        },
        
        /**
         * 读卡
         */
        _readCard:function(){
        	var widget = this,
    			element = $(widget.el);
        	widget.popup("读卡方法未写！");
        },
        
        /**
         * 根据身份证查询黑名单                 
         */
        _qryBlacklist:function(){
        	var widget = this, gSession = widget.gSession,
				element = $(widget.el);
        	var certNumInput = element.find("#certNum").val();
        	if (certNumInput == "") {
        		widget.popup("请输入证件号码！");
        		return;
        	}
        	var IdNum = element.find("input[name='certNum']").val();
        	var params = {
        		"idNum" : IdNum,
        		"glbSessionId":gSession.glbSessionId
        	};
        	widget.refreshPart("qryBlacklist",params,"#result","customers",function (res) {
        		$("#result").show();
        	});
        },
        
        _addTempletDownload:function(){
        	if(window.confirm("将导出批量新增黑名单模板，确认导出？")){
                var url = "../batch/downBlackListAddExcel";
                location.href = url;
            }
        },
        
        _delTempletDownload:function(){
        	if(window.confirm("将导出批量删除黑名单模板，确认导出？")){
                var url = "../batch/downBlackListDelExcel";
                location.href = url;
            }
        },
        
        
        _selectExcelAdd:function () {
            return $('.m_fileAdd').click();
        },
        
        _selectExcelDel:function () {
            return $('.m_fileDel').click();
        },
        
        _fileOnChangeAdd:function () {
            isChanged = true;
            var filename = $(".m_fileAdd").get(0).files[0].name;
            $('#m_fileAdd span').html(filename);
        },
        
        _fileOnChangeDel:function () {
            isChanged = true;
            var filename = $(".m_fileDel").get(0).files[0].name;
            $('#m_fileDel span').html(filename);
        },
        
        /**
         * 导入新增模板数据
         * @returns {Boolean}
         */
        _batchAdd : function () {
        	var widget = this,
        		element = $(widget.el);
            var m_fileAddVal = $('.m_fileAdd').val();
            if(m_fileAddVal=="") {
                widget.popup('请选择文件！');
                return ;
            }
            if('-1' == m_fileAddVal.indexOf('.xls')) {
                widget.popup('只能上传Excel文件！');
                return ;
            }
            var gsession = widget.gSession;
            var params = {
            	"createStaffId": gsession.staffId,
                "glbSessionId": gsession.glbSessionId,
                "docId":$("#docId").val(),
                "addOrUpdate":$("#addOrUpdate").val(),
                "checkChannelId":$("#checkChannelValue").val(),
            }
            $('.route_input').val(JSON.stringify(params));
            var loading = this.require("loading");
            
        	var url = "../batch/uploadAddBlackListExcel";
            $("#m_formAdd").ajaxSubmit({
                type:'post',
                url:url,
                beforeSend: function () {
                	loading.open();
                },
                success:function(jsonData){
                	loading.close();
                	var retVal = JSON.parse(jsonData)
                	widget.popup(retVal.resultMsg);
                },
                error:function(){
                    
                }
            });
        },
        
        /**
         * 导入删除模板数据
         * @returns {Boolean}
         */
        _batchDel : function () {
        	var widget = this,
        		element = $(widget.el);
            var m_fileDelVal = $('.m_fileDel').val();
            if(m_fileDelVal=="") {
                widget.popup('请选择文件！');
                return ;
            }
            if('-1' == m_fileDelVal.indexOf('.xls')) {
                widget.popup('只能上传Excel文件！');
                return ;
            }
            var gsession = widget.gSession;
            var params = {
                "glbSessionId": gsession.glbSessionId,
            }
            $('.route_input').val(JSON.stringify(params));
            var loading = this.require("loading");
            
            var url = "../batch/uploadDelBlackListExcel";
            $("#m_formDel").ajaxSubmit({
                type:'post',
                url:url,
                beforeSend: function () {
                	loading.open()
                },
                success:function(jsonData){
                	loading.close();
                	var retVal = JSON.parse(jsonData)
                	widget.popup(retVal.resultMsg);
                },
                error:function(){
                    
                }
            },{mask:true});
        },
    });
    vita.widget.register("blackListManage", blackListManage, true);
})(window.vita);