(function (vita) {
    var prepareCardInfoQry = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_qryPrepareCardInfo",
            "click #regionIdBtn": "_chooseArea",
            "click input[name=payment]": "_nbrClick"
        },

        _initialize: function () {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
        },
        global: {
            pageIndex: 1,
            pageSize: 5,
            chooseArea: "../comm/chooseArea"
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        /**
         * 查询卡信息
         * @private
         */
        _qryPrepareCardInfo:function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("qryPrepareCardInfoList", JSON.stringify(params), "#prepareCardInfoListResult", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if(_pageIndex > 1 && !params.pageInfo.rowCount) {
                                    params.pageInfo.rowCount = totalNumber;
                                }
                                if (widget._getConds()) {
                                    widget.refreshPart("qryPrepareCardInfoList", JSON.stringify(params), "#prepareCardInfoList");
                                }
                                ;
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }

                }, {
                    async: false
                });
            }
        },
        /**
         * 拼装查询参数
         * @private
         */
        _getConds : function () {
            var widget = this,element = $(widget.el);
            var params = {
                pageInfo : {
                    pageIndex : widget.global.pageIndex,
                    pageSize : widget.global.pageSize
                },
                routeParam : {
                    regionId : widget.gSession.staffRegionId
                },
                mktResInstNbr : "",
                accNum : ""
            };
            var paramCnt = 0;
            // if (element.find("#c_region").is(":checked")) {
                var regionId = element.find("#regionId").attr("value");
                if(widget._isNullStr(regionId)) {
                    widget.popup("请选择地区！");
                    return false;
                } else {
                    params.regionId = regionId;
                }
            // }
            if (element.find("#c_cardStatus").is(":checked")) {
                var cardStatus = element.find("#cardStatus").val();
                if(widget._isNullStr(cardStatus)) {
                    widget.popup("请选择卡状态！");
                    return false;
                } else {
                    params.cardStatus = cardStatus;
                }
            }
            //是否查询卡历史参数暂时隐藏
            /*if (element.find("#c_isCardHis").is(":checked")) {
                paramCnt++;
                var isCardHis = element.find("#isCardHis").val();
                if(widget._isNullStr(isCardHis)) {
                    widget.popup("请选择是否查询历史！");
                    return false;
                } else {
                    params.isCardHis = isCardHis;
                }
            }*/
            if (element.find("#c_accNum").is(":checked")) {
                paramCnt++;
                var accNum = $.trim(element.find("#accNum").val());
                if(widget._isNullStr(accNum)) {
                    widget.popup("请输入接入号码！");
                    return false;
                } else {
                    params.accNum = accNum;
                }
            }
            if (element.find("#c_accNumScope").is(":checked")) {
                paramCnt++;
                var beginAccNum = $.trim(element.find("#beginAccNum").val());
                var endAccNum = $.trim(element.find("#endAccNum").val());
                if(widget._isNullStr(beginAccNum)){
                    widget.popup("请输入起始接入号码！");
                    return false;
                }
                if(widget._isNullStr(endAccNum)) {
                    widget.popup("请输入终止接入号码！");
                    return false;
                } else {
                    params.accNumScope = {};
                    params.accNumScope.beginAccNum = beginAccNum;
                    params.accNumScope.endAccNum = endAccNum;
                }
            }
            if (element.find("#c_cardNum").is(":checked")) {
                paramCnt++;
                var cardNum = $.trim(element.find("#cardNum").val());
                if(widget._isNullStr(cardNum)) {
                    widget.popup("请输入卡号！");
                    return false;
                } else {
                    params.mktResInstNbr = cardNum;
                }
            }
            if (element.find("#c_cardNumScope").is(":checked")) {
                paramCnt++;
                var beginCardNum = $.trim(element.find("#beginCardNum").val());
                var endCardNum = $.trim(element.find("#endCardNum").val());
                if(widget._isNullStr(beginCardNum)){
                    widget.popup("请输入起始卡号！");
                    return false;
                }
                if(widget._isNullStr(endCardNum)) {
                    widget.popup("请输入终止卡号！");
                    return false;
                } else {
                    params.cardNumScope = {};
                    params.cardNumScope.beginCardNum = beginCardNum;
                    params.cardNumScope.endCardNum = endCardNum;
                }
            }
            if(paramCnt == 0){
                widget.popup("请至少输入一个除'卡状态'的其他查询条件！");
                return false;
            }
            return params;
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _nbrClick :function (e) {
            var widget = this,element = $(widget.el);
            //判断被点击的复选框是否是接入号码和是否查询卡历史，若是则点击其中任意一个时，都默认选中另一项
            var currentTarget = $(e.currentTarget);
            var isChecked = currentTarget.is(":checked");
            element.find("input[type=text]").attr('disabled',true);
            element.find("input[name=payment]").attr("checked",false);
            //获取复选框ID
            var currentId = currentTarget.attr("id");
            switch (currentId){
                case 'c_isCardHis' :
                    if(isChecked){
                        element.find("#accNum").attr("disabled",false);
                        element.find("#c_accNum").prop("checked","checked");
                        element.find("#c_isCardHis").prop("checked","checked");
                    }
                    break;
                case 'c_accNum':
                    if(isChecked){
                        element.find("#c_accNum").prop("checked","checked");
                        element.find("#c_isCardHis").prop("checked","checked");
                        element.find("#accNum").attr("disabled",false);
                    }
                    break;
                case 'c_accNumScope':
                    if(isChecked){
                        element.find("#c_accNumScope").prop("checked","checked");
                        element.find("#beginAccNum").attr("disabled",false);
                        element.find("#endAccNum").attr("disabled",false);
                    }
                    break;
                case 'c_cardNum':
                    if(isChecked){
                        element.find("#c_cardNum").prop("checked","checked");
                        element.find("#cardNum").attr("disabled",false);
                    }
                    break;
                case 'c_cardNumScope':
                    if(isChecked){
                        element.find("#c_cardNumScope").prop("checked","checked");
                        element.find("#beginCardNum").attr("disabled",false);
                        element.find("#endCardNum").attr("disabled",false);
                    }
                    break;
            };
        }
    });
    vita.widget.register("prepareCardInfoQry", prepareCardInfoQry, true);
})(window.vita);