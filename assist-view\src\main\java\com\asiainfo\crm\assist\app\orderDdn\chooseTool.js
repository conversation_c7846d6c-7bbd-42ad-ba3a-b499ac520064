(function (vita) {

    var chooseTool = vita.Backbone.BizView.extend({
        events: {
            "click #toolRepair": "_toolRepair",
        },
        _toolRepair: function (e) {
            var widget = this,
                aEl = $(e.currentTarget);
            var params = {
            };
            var divId = aEl.val();
            element = $(widget.el)
            params.toolsId = aEl.val()
            if ("coId" == document.getElementById(divId).getElementsByTagName('input')[5].value){
                params.coId=document.getElementById(divId).getElementsByTagName('input')[6].value
            }
            else {
                params.coNbr=document.getElementById(divId).getElementsByTagName('input')[6].value
            }
            orderItemIds = document.getElementById(divId).getElementsByTagName('input')[2].value;
            premaryKey= document.getElementById(divId).getElementsByTagName('input')[1].value;
            if (null!=orderItemIds&&''!=orderItemIds){
                params.orderItemIds = orderItemIds;
            }
            if (null!=premaryKey&&''!=premaryKey){
                params.premaryKey = premaryKey;
            }
            params.errType = document.getElementById(divId).getElementsByTagName('input')[3].value;
            params.msgType = aEl.val();
            params.objIdType = document.getElementById(divId).getElementsByTagName('input')[4].value;;
            params.orderItemId =e.currentTarget.nextElementSibling.value;
            params.repairType=document.getElementById(divId).getElementsByTagName('span')[0].innerHTML;
            params.repairSubType=document.getElementById(divId).getElementsByTagName('span')[1].innerHTML;
            if (params) {
                widget.callService("sendMessage", JSON.stringify(params), function(res) {
                    if (res.resultCode == 0) {
                        document.getElementById(divId).getElementsByTagName('button')[0].disabled=true;
                        widget.popup(res.resultMsg);
                    }else{
                        widget.popup(res.resultMsg);
                    }
                },{ async : false });
            }
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this,element = $(widget.el);
            var params = {};
            var accNum = $.trim(element.find("#accNum").val());
            var func = $.trim(element.find("#isQryHis").val());
                if(widget._isNullStr(accNum)) {
                    widget.popup("请输入数据！");
                    return false;
                } else {
                    params.str = accNum;
                    params.func = func;
                }
            return params;
        },
    });
    vita.widget.register("chooseTool", chooseTool, true);
})(window.vita);