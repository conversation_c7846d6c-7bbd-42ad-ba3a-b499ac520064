(function(vita){
    var mktResTypeRegion = vita.Backbone.BizView.extend({
        events : {
            "click #qryCommonLogSearchBtn" : "_qryMktResTypeRegionList",
            "click #searchDel": "_searchParamsDel",
            "click [name='moduleRadio'] " : "_clickRadio",
            "click [link=mktResTypeAttr]": "_queryMktResTypeAttr",
        },
        _initialize : function() {
            var widget = this,
                element = $(widget.el);
            data = element.data("data");
            widget.model.set(data);
            //初始化查询
            widget._qryMktResTypeRegionList();
        },
        global : {
            pageIndex: 1,
            pageSize: 5, //每页记录数
        },
        _clickRadio : function(e){
            var widget = this,
                element = $(widget.el);
            var radio = $(e.target).closest('input');
            var data = radio.data('data');
            widget.model.set("selectedMktResTypeRegion",data);
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },

        _queryMktResTypeAttr: function(e){
            var widget = this;
            var aEl = $(e.target).closest("a");
            var mktResTypeId = aEl.data("mktResTypeId");
            if(!mktResTypeId){
                widget.popup("营销资源不存在,请重新查询后再试.");
                return;
            }
            var data = {
                mktResTypeId:mktResTypeId
            };
            window.open("../../crm_assist/query/mktResTypeAttr?mktResTypeId=" + mktResTypeId);
        },
        /**
         * 获取查询条件参数对象
         */
        _getConds : function(pageIndex) {
            var widget = this,
                element = $(widget.el);
            gSession = widget.gSession;
            var param = {
                "pageInfo" : {
                    "pageIndex" : pageIndex,
                    "pageSize" : widget.global.pageSize
                }
            };
            var mktResTypeId = widget.model.get("mktResTypeId");
            if (!widget._isNullOrEmpty(mktResTypeId)) {
                param.mktResTypeId = mktResTypeId;
            }
            return param;
        },
        _isNullOrEmpty: function (condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty;
        },
        /**
         * 清空查询条件
         */
        _searchParamsDel : function() {
            var widget = this,
                element = $(widget.el);
            widget.model.set("mktResTypeId","");
            element.find("#mktResTypeId").val("");
        },

        _qryMktResTypeRegionList : function(e) {
            var widget = this, element = $(widget.el);
            var param = widget._getConds(widget.global.pageIndex);
            element.find('#showPageInfo').empty();
            widget.refreshPart("qryMktResTypeRegion", param, ".table-hover",
                "mktResTypeRegions", function(res) {
                    var paging = this.require("paging");
                    var totalNumber = $(res).find("input").eq(1).data("totalNumber");
                    if (parseInt(totalNumber) > 0) {
                        var e = paging.new({
                            recordNumber : widget.global.pageSize,
                            total : totalNumber,
                            pageIndex : widget.global.pageIndex,
                            callback : function(pageIndex){
                                //判断当前页
                                if(pageIndex == null || pageIndex == undefined || isNaN(pageIndex)){
                                    pageIndex = 1;
                                }
                                widget.model.set("pageIndex",pageIndex);
                                widget.refreshPart("qryMktResTypeRegion", widget._getConds(pageIndex), ".table-hover");
                            }
                        });
                        element.find('#showPageInfo').append(e.getElement());
                    }
                }, {
                    mask : true
                });
        },

    });
    vita.widget.register("mktResTypeRegion", mktResTypeRegion, true);
})(window.vita);
