<div data-widget="transitOrderQuery" style="height: 100%">
	
    <div class="sypage-nav">
	    <div class="nav_alink"><a>系统管理</a>&gt;<a>权限控制</a>&gt;<a class="active">授权管理</a></div>
	</div>
	<div class="sypage-min">
	    <div class="form_title">
	        <div><i class="bot"></i><strong class="text16">查询条件</strong></div>
	    </div>
	    <div class="sypagemin_content ">
	        <form>
	            <div class="form-group col-md-4 ">
	                <label class="col-md-4 control-label lablep">
	                    <label class="wu-checkbox full absolute" data-scope=""></label>
	                    	地区
	                </label>
	                <div class="col-md-7">
	                     <div class="input-group">
	                        <input type="text" id="areaId" class="form-control" placeholder="">
	                        <div class="input-group-btn">
	                            <button class="btn btn-green" type="button">选择</button>
	                        </div>
	                    </div>
	                </div>
	            </div>
	
	            <div class="form-group col-md-4 ">
	                <label class="col-md-4 control-label lablep">
	                    <label class="wu-checkbox full absolute" data-scope=""><input type="checkbox" name="payment"></label>
	                    	渠道</label>
	                <div class="col-md-7">
	                    <div class="input-group">
	                        <input type="text" id="channelId" class="form-control" placeholder="">
	                        <div class="input-group-btn">
	                            <button class="btn btn-green" type="button">选择</button>
	                        </div>
	                    </div>
	
	                </div>
	            </div>
	            <div class="form-group col-md-4 ">
	                <label class="col-md-4 control-label lablep">
	                    <label class="wu-checkbox full absolute" data-scope=""><input type="checkbox" name="payment"></label>
	                  	  营业员</label>
	                <div class="col-md-7">
	                    <div class="input-group">
	                        <input type="text" id="staffId" class="form-control" placeholder="">
	                        <div class="input-group-btn">
	                            <button class="btn btn-green" type="button">选择</button>
	                        </div>
	                    </div>
	                </div>
	            </div>
	            <div class="form-group col-md-4 ">
	                <label class="col-md-4 control-label lablep">
	                    <label class="wu-checkbox full absolute" data-scope=""><input type="checkbox" name="payment"></label>
	                   	号码
	                </label>
	                <div class="col-md-7">
	                        <input type="text" id="accNum" class="form-control" placeholder="">
	                </div>
	            </div>
	            
	            <div class="form-group col-md-4">
	                <label class="col-md-4 control-label lablep">起始日期</label>
	                <div class="col-md-7">
	                    <input type="text" id="beginDate" class="form-control" placeholder="" readonly>
	
	                </div>
	            </div>
	            <div class="form-group col-md-4">
	                <label class="col-md-4 control-label lablep">结束日期</label>
	                <div class="col-md-7">
	                    <input type="text" id="endDate" class="form-control" placeholder="" readonly>
	
	                </div>
	            </div>
	            <div class="form-group col-md-4 ">
	                <label class="col-md-4 control-label lablep">
	                    <label class="wu-checkbox full absolute" data-scope=""><input type="checkbox" name="payment"></label>
	                   	购物车流水
	                </label>
	                <div class="col-md-7">
	                        <input type="text" id="custOrderNbr" class="form-control" placeholder="">
	                </div>
	            </div>
	            <div class="form-group col-md-4 ">
	                <label class="col-md-4 control-label lablep">
	                    <label class="wu-checkbox full absolute" data-scope=""><input type="checkbox" name="payment"></label>
	                  	  所属客户</label>
	                <div class="col-md-7">
	                    <div class="input-group">
	                        <input type="text" id="custId" class="form-control" placeholder="">
	                        <div class="input-group-btn">
	                            <button class="btn btn-green" type="button">选择</button>
	                        </div>
	                    </div>
	                </div>
	            </div>
	            
	            <div class="form-group col-md-4">
	                <label class="col-md-4 control-label lablep">订单状态</label>
	                <div class="col-md-7">
	                    <select id="statusCds" class="form-control">
	                        <option value="201300">受理中</option>
                            <option value="301200">完成</option>
                            <option value="100001">待处理</option>
	                    </select>
	                </div>
	            </div>
	            <div class="form-group col-md-4 ">
	                <label class="col-md-4 control-label lablep">
	                    	查询类型</label>
	                  	<label class="wu-radio full absolute" data-scope=""><input type="radio" name="payment"></label>订单信息
	                	<label class="wu-radio full absolute" data-scope=""><input type="radio" name="payment"></label>客户动作
	                	<label class="wu-radio full absolute" data-scope=""><input type="radio" name="payment"></label>账户动作
	            </div>
				<div class="form-group col-md-6">
				</div>
	            <div class="form-group col-md-2">
	                <button id="btn-qryTransit" type="button" class="btn btn-primary col-md-11">查询</button>
	            </div>
	
	        </form>
	    </div>
	
	    <div class="form_title col-md-12">
	        <div><i class="bot"></i><strong class="text16">在途单列表</strong></div>
	    </div>
	    <div class="sypagemin_content ">
	        <div class="col-lg-12 mart10">
	            <table class="table table-hover" id="transitTable">
	                <thead>
	                <tr>
	                    <th>选择</th>
	                    <th>购物车流水</th>
	                    <th>业务类型</th>
	                    <th>受理时间</th>
	                    <th>订单状态</th>
	                    <th>受理渠道</th>
	                    <th>受理员工</th>
	                </tr>
	                </thead>
	                <tbody id="transitList">
		                #if($options.orderList && $options.orderList != "null" && $options.orderList.size() > 0)
	                    #foreach($orderList in $options.orderList)
		                <tr>
		                    <td><label class="wu-radio full absolute" data-scope=""><input type="checkbox" name="payment"></label></td>
		                    <td>$orderList.custOrderNbr</td>
		                    <td>$orderList.custOrderType</td>
		                    <td>$orderList.acceptDate</td>
		                    <td>$orderList.statusCdName</td>
		                    <td>$orderList.createOrgId</td>
		                    <td>$orderList.orderHandlerId</td>
		                </tr>
		                #end
                    	#end
	                </tbody>
	            </table>
	            <!--翻页start -->
	            <div class="page-box">
                    <div class="floatl pagel_font">展示第<span>1</span>条到第<span>1</span>条，共<span>$options.totalNumber</span>条</div>
                    <div class="floatr">
                        <ul class="pagination">

                        </ul>
                    </div>
                </div>
	            <!--翻页end -->
	        </div>
	    </div>
	</div>

</div>