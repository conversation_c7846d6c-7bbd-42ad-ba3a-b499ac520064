<div data-widget="acctItemType">
    <p class="vita-data">{"data":$options}</p>
    <div class="box-maincont">
        <div class="page_main notopnav">
            <div class="col-lg-12">
                <div class="box-item">
                    <div class="container-fluid row">
                        <div class="form_title">
                            <div><i class="bot"></i>查询条件</div>
                        </div>
                        <div class="wmin_content row">
                            <form class=" form-bordered">
                                <div class="form-group col-md-4">
                                <label class="col-md-4 control-label lablep">归类标识</label>
                                <div class="col-md-7">
                                    <input type="text" class="form-control" value=""
                                           id="acctItemClassId">
                                </div>
                                </div>
                                <div class="form-group col-md-4">
                                    <label class="col-md-4 control-label lablep">运营商标识</label>
                                    <div class="col-md-7">
                                        <input type="text" class="form-control" value=""
                                               id="partnerId">
                                    </div>
                                </div>
                                <div class="form-group col-md-4">
                                    <label class="col-md-4 control-label lablep">账目类型名称</label>
                                    <div class="col-md-7">
                                        <input type="text" class="form-control" value=""
                                               id="name">
                                    </div>
                                </div>
                                <div class="form-group col-md-4">
                                    <label class="col-md-4 control-label lablep">费用账目类型标识</label>
                                    <div class="col-md-7">
                                        <input type="text" class="form-control" value=""
                                               id="chargeMark">
                                    </div>
                                </div>
                                <div class="form-group col-md-4">
                                    <label class="col-md-4 control-label lablep">计总账目类型标识</label>
                                    <div class="col-md-7">
                                        <input type="text" class="form-control" value=""
                                               id="totalMark">
                                    </div>
                                </div>
                                <div class="form-group col-md-4">
                                    <label class="col-md-4 control-label lablep">账目类型编码</label>
                                    <div class="col-md-7">
                                        <input type="text" class="form-control" value=""
                                               id="acctItemTypeCode">
                                    </div>
                                </div>
                                <div class="form-group col-md-4">
                                    <label class="col-md-4 control-label lablep">是否可打印发票</label>
                                    <div class="col-md-4">
                                        <select id="isInvoiceItem" class="form-control">
                                            <option value="1">已打</option>
                                            <option value="0">未打</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group col-md-4">
                                    <label class="col-md-4 control-label lablep">是否限制费用调整</label>
                                    <div class="col-md-4">
                                        <select id="isLimitChange" class="form-control">
                                            <option value="1">是</option>
                                            <option value="0">否</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group col-md-4">
                                    <label class="col-md-4 control-label lablep">是否限制补退费</label>
                                    <div class="col-md-4">
                                        <select id="isLimitRedo" class="form-control">
                                            <option value="1">是</option>
                                            <option value="0">否</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group col-md-4">
                                    <label class="col-md-4 control-label lablep">状态</label>
                                    <div class="col-md-4">
                                        <select id="statusCd" class="form-control">
                                            <option value="1">有效</option>
                                            <option value="2">失效</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group col-md-4 ">
                                    <label class="col-md-2 control-label lablep"></label>
                                    <div class="col-md-9 text-right">
                                        <button type="button" class="btn btn-white" id="resetBtn">清除</button>
                                        <button type="button" class="btn btn-primary" id="queryBtn">查询</button>
                                        <button type="button" class="btn btn-primary" id="addBtn">添加</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="container-fluid row">
                        <div class="form_title">
                            <div><i class="bot"></i>查询结果</div>
                        </div>
                        <div class="wmin_content" id="loadData">
                            <div class="col-lg-12">
                                <a class="btn btn-gray btn-outline" id="editBtn">
                                    <i class="glyphicon icon-progress text18"> </i> 编辑
                                </a>
<!--                                <a style="pointer-events: none" class="btn btn-gray btn-outline"  id="deleteBtn">-->
<!--                                    <i class="glyphicon icon-batch text18" > </i> 删除-->
<!--                                </a>-->
                            </div>
                            <div class="col-lg-12 mart10" name="tableDiv">
                                <table class="table table-hover">
                                    <thead>
                                    <tr>
                                        <th>选择</th>
                                        <th>账目类型标识</th>
                                        <th>归类标识</th>
                                        <th>运营商标识</th>
                                        <th>账目类型名称</th>
                                        <th>费用账目类型标识</th>
                                        <th>计总账目类型标识</th>
                                        <th>账目类型编码</th>
                                        <th>优先级</th>
                                        <th>父账目类型</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>打印发票</th>
                                        <th>补退费</th>
                                        <th>费用调整</th>
                                        <th>调帐</th>
<!--                                        <th>操作</th>-->
                                    </tr>
                                    </thead>
                                    <tbody>
                                    #if($options.reqList!="null" && $options.reqList.size()>0)
                                    #foreach($fc in $options.reqList)
                                    <tr>
                                        <td>
                                            <label class="wu-radio full absolute" data-scope="">
                                                <input type="radio" name="payment">
                                                <p class="vita-data">{"Id":"$!fc.acctItemTypeId"}</p>
                                            </label>
                                        </td>
                                        <td>$!fc.acctItemTypeId</td>
                                        <td>$!fc.acctItemClassId</td>
                                        <td>$!fc.partnerId</td>
                                        <td>$!fc.name</td>
                                        <td value="$!fc.chargeMark">$!fc.chargeMark : $!fc.chargeMarkName</td>
                                        <td value="$!fc.totalMark">$!fc.totalMark : $!fc.totalMarkName</td>
                                        <td>$!fc.acctItemTypeCode</td>
                                        <td>$!fc.priority</td>
                                        <td>$!fc.parentItemTypeId</td>
                                        <td value="$!fc.statusCd" >$!fc.statusCdName</td>
                                        <td>$!fc.createDate</td>
                                        <td value="$!fc.isInvoiceItem">$!fc.isInvoiceItemName</td>
                                        <td value="$!fc.isLimitRedo">$!fc.isLimitRedoName</td>
                                        <td value="$!fc.isLimitChange">$!fc.isLimitChangeName</td>
                                        <td>$!fc.ifAdjust</td>
<!--                                        <td><button type="button" class="btn btn-primary" value= $!fc.id id="checkZkValue">查看</button></td>-->
                                    </tr>
                                    #end
                                    #end
                                    </tbody>
                                </table>
                                #if($options.totalNumber)
                                <p class="vita-data" id = "_totalNumber">{"totalNumber":$options.totalNumber}</p>
                                #end
                            </div>
                            <div id="pageInfo">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--弹窗 start-->
    <div class="popup" style="display:none;">
        <div class="popup-mask active"></div>
        <div class="agile-popup right active" style="width: 75%">
            <div class="popup-container">
                <!--内容-->
                <div class="calctitle">
                    <div class="titlefont">添加</div>
                    <div class="toolr">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                    </div>
                </div>

                <div class="wmin_content row" style="margin-top: 10px">
                    <form class=" form-bordered" name="_popupForm">
                        <div class="hideAndShow form-group col-md-6" >
                            <label class="col-md-4 control-label lablep">账目类型标识</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_acctItemTypeId" placeholder="可自动生成" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">归类标识</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_acctItemClassId"  placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">运营商标识</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_partnerId"   required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">账目类型名称</label>
                            <div class="col-md-8">
                                <input  name="_startDate"  type="text" class="form-control" value=""
                                       id="_name" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label class="col-md-4 control-label lablep">费用账目类型标识</label>
                            <div class="col-md-8">
                                <input name="_endDate"  type="text" class="form-control" value=""
                                       id="_chargeMark" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label class="col-md-4 control-label lablep">计总账目类型标识</label>
                            <div class="col-md-8">
                                <input name="_endDate"  type="text" class="form-control" value=""
                                       id="_totalMark" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 " >
                            <label class="col-md-4 control-label lablep">账目类型编码</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_acctItemTypeCode" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 " >
                            <label class="col-md-4 control-label lablep">优先级</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_priority"  placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 " >
                            <label class="col-md-4 control-label lablep">父账目类型</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_parentItemTypeId" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 " >
                            <label class="col-md-4 control-label lablep">状态</label>
                            <div class="col-md-8">
                                <select id="_statusCd" class="form-control">
                                    <option value="1">有效</option>
                                    <option value="2">失效</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group col-md-6 " >
                            <label class="col-md-4 control-label lablep">打印发票</label>
                            <div class="col-md-8">
                                <select id="_isInvoiceItem" class="form-control">
                                    <option value="1">已打</option>
                                    <option value="0">未打</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group col-md-6 " >
                            <label class="col-md-4 control-label lablep">补退费</label>
                            <div class="col-md-8">
                                <select id="_isLimitRedo" class="form-control">
                                    <option value="1">限制</option>
                                    <option value="0">不限制</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group col-md-6 " >
                            <label class="col-md-4 control-label lablep">费用调整</label>
                            <div class="col-md-8">
                                <select id="_isLimitChange" class="form-control">
                                    <option value="1">限制</option>
                                    <option value="0">不限制</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group col-md-6 " >
                            <label class="col-md-4 control-label lablep">调帐</label>
                            <div class="col-md-8">
                                <select id="_ifAdjust" class="form-control">
                                    <option value="1">1</option>
                                    <option value="0">0</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group col-md-12">
                            <div class="col-md-12 text-center">
                                <button type="button" class="btn btn-primary" id="addSubmitBtn">添加</button>
                                <button type="button" class="btn btn-primary" id="editSubmitBtn">修改</button>
                                <button type="button" class="btn btn-primary" id="closeBtn">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!--end-->


</div>
</div>
</div>
<style>
    .modal{z-index: 9910;}
    .agile-popup{z-index: 9000;}
</style>
