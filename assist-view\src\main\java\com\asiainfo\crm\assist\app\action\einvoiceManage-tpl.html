<div data-widget="einvoiceManage" style="height:100%">

    <div class="pace-done wrapper mini-rightmax no-minright"><!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="page-nav">
                <div class="row">
                    <div class="pagenav_box">
                        <div class="titlefont paddt8">
                            <ul id="myTab" class="nav nav-tabs">
                                <li class="active"><a href="#1" data-toggle="tab" aria-expanded="true">手工开票管理</a></li>
                                <li class=""><a href="#2" data-toggle="tab" aria-expanded="false">特殊用户管理</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="page_main">
                <div id="myTabContent" class="tab-content">
                    <div class="tab-pane fade active in" id="1">
                        <div class="col-lg-12">
                            <div class="box-item">
                                <div class="form_title nobrder">
                                    <div><i class="bot"></i>手工开票管理</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                </label> 订单流水号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custOrderNbr" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <div class="col-md-10 searchbutt_r" align="center">
                                                <button id="btn-sign" type="button" class="btn btn-primary">标记为已开票</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-unSign" type="button" class="btn btn-primary">撤销已开票</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="2">
                        <div class="col-lg-12">
                            <div class="box-item">
                                <div class="form_title nobrder">
                                    <div><i class="bot"></i>特殊用户管理</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_accNum" type="radio" name="payment" checked="checked">
                                                </label> 接入号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="accNum" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custId" type="radio" name="payment">
                                                </label> 客户ID
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custId" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <div class="col-md-10 searchbutt_r" align="center">
                                                <button id="btn-queryCust" type="button" class="btn btn-primary">查询客户</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-custSign" type="button" class="btn btn-primary">标记特殊客户</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-custUnSign" type="button" class="btn btn-primary">取消标记</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>

                                <div class="container-fluid row">
                                    <div class="form_title">
                                    </div>
                                    <div class="wmin_content" id="custListResult">
                                        <div class="col-lg-12 mart10" id="custList">
                                            <table class="table table-hover">
                                                <thead>
                                                <tr>
                                                    <th>选择</th>
                                                    <th>客户ID</th>
                                                    <th>客户名称</th>
                                                    <th>是否特殊客户</th>
                                                    <th>客户地区</th>
                                                    <th>更新时间</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                #if($options != "null" && $options.specCusts && $options.specCusts != "null" &&
                                                $options.specCusts.size() > 0)
                                                #foreach($cust in $options.specCusts)
                                                <tr name="trClick">

                                                    <td custId="$!cust.custId"><label class="wu-radio full absolute" data-scope=""><input
                                                            type="radio" name="payment"><p class="vita-data">{"custId":"$!cust.custId"}</p></label></td>
                                                    
                                                    <td>$!{cust.custId}</td>
                                                    <td>$!{cust.custName}</td>
                                                    <td>
                                                        #if($cust.markFlag == "1")
                                                            是
                                                        #else
                                                            否
                                                        #end

                                                    </td>
                                                    <td>$!{cust.regionName}</td>
                                                    <td>$!{cust.statusDate}</td>
                                                </tr>
                                                #end
                                                #elseif($options != "null" && $options.resultCode ==  "-1")
                                                <tr><td align='center' colspan='6'>$options.resultMsg<td></tr>
                                                #elseif($options != "null" && $options.specCusts && $options.specCusts != "null" &&
                                                $options.specCusts.size() == 0)
                                                <tr><td align='center' colspan='6'>未查询到数据！<td></tr>
                                                #end
                                                </tbody>
                                            </table>
                                        </div>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

