(function (vita) {

    var batchFluxPackage = vita.Backbone.BizView.extend({

        events: {
            "click #batchImportPackage":"_batchImportPackage",
            "click #tempDownloadBtn": "_tempDownloadBtn",
            "click #chooseFluxPackage": "_chooseFluxPackage",
            "click #choosePackage": "_choosePackage",
            "click button.close": "_closeBtnClick",
            "click #submitImportBtn": "_submitImportBtn",
            "click #selectRadio": "_selectRadio",
        },
        global: {
            totalNumber: 0,
            pageIndex: 1,
            pageSize: 5,
        },
        _selectRadio: function (radio) {
            debugger
            var widget = this,
                el = $(widget.el);
            var a1 = el.find("#selectRadio").value();
        },
        _batchImportPackage: function () {
            var widget = this,
                el = $(widget.el);
            el.find(".popup1").show();
        },
        _tempDownloadBtn: function(){
            var widget = this;
            window.open("../comm/downLoadTemplateExcel")
        },
        _chooseFluxPackage: function(){
            debugger
            var widget = this,
                el = $(widget.el);
            el.find(".popup").show();
            var params = widget._getConds(widget.global.pageIndex);
            widget.refreshPart("getFluxPackage", params, "#itemTable", function(res) {
                widget._createPageInfo($(res));
            }, {
                mask : true
            });

        },
        /**
         * exel批量导入
         */
        _submitImportBtn: function () {
            debugger
            var widget = this,
                el = $(widget.el);
            var upFile = $.trim(el.find("#upFile").val());
            var offerId = $.trim(el.find("#offerId").val());
            if (upFile === "") {
                widget.popup("请选择要导入的Excel文件！");
                return;
            }else if (offerId ===""){
                widget.popup("请选择流量包");
            }else {
                var fileType = upFile.substr(upFile.lastIndexOf(".") + 1);
                if (fileType == "xls" || fileType == "xlsx") {
                    var loading = vita.widget.require("loading");
                    el.find("#importForm").ajaxSubmit({
                        type : 'post',
                        dataType : 'json',
                        url : '../query/insertBatchFluxPackage',
                        timeout: 300000,  // 5分钟
                        beforeSubmit:function(){
                            loading.open();
                        },
                        success : function(data) {
                            loading.close();
                            if(data.resultCode == 0){
                                el.find(".popup").hide();
                                el.find('#offerId').val('')
                                el.find('#offerName').val('')
                                el.find("#upFile").val("")
                                widget.popup("导入成功，批次号："+data.batchTaskId+data.resultMsg+"！请到“批量受理查询”中查询受理结果");
                            } else {
                                widget.popup(data.resultMsg);
                            }
                        },
                        error : function(data, status, e) {
                            loading.close();
                            widget.popup("请求可能发生异常，请稍后再试！");
                        }
                    });
                } else {
                    widget.popup("导入文件类型错误！");
                    return;
                }
            }
        },
        _choosePackage: function(e){
            var widget = this,
                element = $(widget.el);
            element.find(".popup").hide();
            validate = false;
            //该字段是否为多选类型
            var checkeds = element.find('#items').find('input:checked');
            var idArray = [];
            var items = [];
            $.each(checkeds,function(i,item){
                idArray.push($(item).data('data').offerId);
                var offerId = $(item).data('data').offerId
                var offerName = $(item).data('data').offerName
                element.find('#offerId').val(offerId)
                element.find('#offerName').val(offerName)
                items.push($(item).data('data'));
            });
            if(idArray.length == 0){
                widget.popup("请选择销售品！");
                return false;
            }

        },
        _createPageInfo : function(el) {
            var widget = this, element = el||$(widget.el),
                showPageInfo = element.find("#showPageInfo");
            var paging = widget.require("paging");
            var e = paging.new({
                pageIndex: 1,
                recordNumber: widget.global.pageSize,
                total: showPageInfo.data("totalNumber"),
                callback : function(pageIndex){
                    var param = widget._getConds(pageIndex);
                    widget.refreshPart("getFluxPackage", param, ".table-hover",
                        "items", function(res) {}, {
                            mask : true
                        });
                }
            });
            showPageInfo.empty().append(e.getElement());
        },
        _getConds: function (pageIndex) {
            var widget = this, el = $(widget.el);
          /*  var widget = this, el = $(widget.el);
            var regionId = $("#queryLanId").attr("value");*/
            var param = {
               /* "seqName": el.find("#querySequenceName").val().trim(),
                "regionId":regionId,*/
                "pageInfo": {
                    "pageIndex": pageIndex,
                    "pageSize": widget.global.pageSize,
                },
                "offerName":"流量包"
            };
            return param;
        },
        /**
         * 弹出框关闭事件
         */
        _closeBtnClick: function () {
            var widget = this,
                el = $(widget.el);

            el.find(".popup1").hide();
        },

    });
    vita.widget.register("batchFluxPackage", batchFluxPackage, true);
})(window.vita);