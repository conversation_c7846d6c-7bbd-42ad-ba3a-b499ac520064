package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IEopHttpClientSmo;
import com.asiainfo.crm.service.intf.IEopServiceSmo;
import com.asiainfo.crm.service.intf.IOrderQueryMultiSMO;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import net.sf.json.JSONObject;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by fengjie on 2019/1/3
 */
@Component("vita.qryComboValue")
public class QryComboValue extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(QryComboValue.class);


    @Autowired
    private IProdInstSMO prodInstSMO;
    @Autowired
    private IEopHttpClientSmo eopHttpClientSmo;

    @Override
    public Map achieveData(Object... param) throws Exception {
        Map options = new HashMap();
        return options;
    }

    public Map qryComboValue(String jsonString) throws Exception {
        Map options = new HashMap();
        String resStirng = prodInstSMO.qryAccProdInstListLocal(jsonString);
        Map resultObject = (Map) resolveResult(resStirng);
        Object obj = MapUtils.getObject(resultObject,"accProdInsts");
        if(obj != null){
            List<Map> list = (List<Map>)obj;
            Map map = list.get(0);
            String prodInstId = MapUtils.getString(map,"prodInstId");
            JSONObject json = new JSONObject();
            json.put("libraryId","2");
            json.put("tenantId","13");
            json.put("depictCode","usertag");
            json.put("user","1");
            json.put("key",prodInstId);
            json.put("tags","DEC_TC_EFF_DATE_YCX,DEC_TC_EXP_DATE_YCX,DEC_TCMC_YCX,DEC_TCID_YCX,DEC_TCJZ_YCX");
            String res = eopHttpClientSmo.libraryQueryTagData(json.toString(),"libraryQueryTagData","GET");
            JSONObject resJson = JSONObject.fromObject(res);
            options = MapUtils.getMap(resJson,"datas");
        }
        return options;
    }


}
