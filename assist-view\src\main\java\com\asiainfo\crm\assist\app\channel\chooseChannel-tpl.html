<div data-widget="chooseChannel" class="calcw_rightbox noneleft" style="height:100%">
    <p class="vita-data">{"data":$options}</p>

    <div class="calcw_rightbox noneleft">
        <div class="calctitle">
            <div class="titlefont">选择渠道</div>
            <div class="toolr">
                #if($options.hasCurChannel && $options.hasCurChannel == "Y")
                <button id="closeBtn" type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
                #end
            </div>
        </div>
        <div class="calcw_rightcont ">
            <div class="container-fluid">
                <div class="list-group" id="channelDiv">
                    #if($options.channelList && $options.channelList != "null" && $options.channelList.size()>0)
                    #foreach($channel in $options.channelList)
                    #if( !($velocityCount >= 9) )
                    <a class="list-group-item border_dashed">$!channel.channelName</a>

                    <p class="vita-data">{"data":$channel}</p>
                    #end
                    #end
                    #end
                </div>
                <p class="vita-data">
                    {"totalNumber":$options.totalNumber}
                </p>
            </div>
            #if($options.totalNumber && $options.totalNumber != "null" && $options.totalNumber >8)
            <!--翻页start -->
            <div id="showPageInfo"></div>
            <!--翻页end -->
            #end
        </div>
    </div>
</div>