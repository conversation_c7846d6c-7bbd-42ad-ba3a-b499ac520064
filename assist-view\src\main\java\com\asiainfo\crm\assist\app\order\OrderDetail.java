package com.asiainfo.crm.assist.app.order;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderSMO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/5/11 10:24
 * @Description TODO
 **/
@Component("vita.orderDetail")
public class OrderDetail extends AbstractComponent{

    @Autowired
    private IOrderSMO orderSMO;

    @Override
    public Map achieveData(Object... objects) throws Exception {
        String csNbr = String.valueOf(objects[0]);
        String iUId = String.valueOf(objects[1]);
        String currentItem = String.valueOf(objects[2]);
        /*String resultJson = orderSMO.qryTraceDetailHisDetail(csNbr,iUId);
        HashMap<String,Object> map = (HashMap<String, Object>) jsonConverter.toBean(resultJson,Map.class);
        HashMap<String,Object> resultMap = (HashMap<String, Object>) map.get("resultObject");
        List<Map<String,Object>> traceOrderItems = (List<Map<String, Object>>) resultMap.get("traceOrderItems");*/
        HashMap<String,Object> traceOrderItem = (HashMap<String, Object>) jsonConverter.toBean(currentItem,Map.class);

        Map options = new HashMap();
        if(StringUtils.isNotBlank(iUId) && !iUId.equals("null")){
            options.put("isTree",true);
        }else{
            options.put("isTree",false);
        }
        options.put("traceOrderItem",traceOrderItem);
        return options;
    }
}
