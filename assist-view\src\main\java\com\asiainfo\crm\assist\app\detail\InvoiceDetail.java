package com.asiainfo.crm.assist.app.detail;

import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.service.intf.ISoReceiptSMO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component("vita.invoiceDetail")
public class InvoiceDetail extends AbstractSoComponent {

    @Autowired
    private ISoReceiptSMO soReceiptSMO;

    @Override
    public Map achieveData(Object... param) throws Exception {
        String p = (String) param[0];
        Map options = new HashMap();
        Map inParam = jsonConverter.toBean(p,Map.class);
        Map orders = qryInvoiceDetailInfo(inParam);
        options.putAll(inParam);
        options.putAll(orders);
        return options;
    }

    public Map qryInvoiceDetailInfo(Map inParam) throws Exception {
        Map options = new HashMap();
        String receiptId = String.valueOf(inParam.get("receiptId"));
        String retStr = soReceiptSMO.queryReceiptByReceiptId(receiptId);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(retStr);
        options.put("detailInfo", resultObjectMap);
        return options;
    }
}
