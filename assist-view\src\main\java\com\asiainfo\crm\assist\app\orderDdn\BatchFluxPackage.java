package com.asiainfo.crm.assist.app.orderDdn;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.IOrderSMO;
import com.asiainfo.crm.service.intf.ISoBatchCenterSMO;
import com.asiainfo.crm.service.intf.ISpecSMO;
import com.asiainfo.crm.sm.vo.GlbSessionVo;;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Component("vita.batchFluxPackage")
public class BatchFluxPackage extends AbstractComponent {

    @Autowired
    private ISpecSMO specSMO;


    @Override
    public Map achieveData(Object... params) throws Exception {

        return null;
    }

    /**
     * 获取销售品
     * @param params
     * @return
     * @throws IOException
     */
   public Map getFluxPackage(String params) throws IOException {
       Map options = new HashMap();
       String itemStr = specSMO.qryOfferList(params);
       Map retMap = (Map) resolveResult(itemStr);
       options.put("items", MapUtils.getObject(retMap, "offers"));
       Map pageInfo = MapUtils.getMap(retMap, "pageInfo");
       options.put("totalNumber", MapUtils.getString(pageInfo, "rowCount"));
       return options;
   }





}
