<div data-widget="certNumFiveHis" class="wrapper mini-rightmax" style="overflow: auto; height: 100%;">
    <div class="box-maincont">
        <div class="homenofood">
            <div class="page-nav">
                <div class="row">
                    <div class="pagenav_box"><div class="page_title">一证五号查询历史</div></div>
                </div>
            </div>
            <div class="page_main">
                <div class="col-lg-12">
                    <div class="box-item">
                        <div class="container-fluid row">
                            <div class="form_title">
                                <div><i class="bot"></i>查询条件</div>
                            </div>
                            <div class="wmin_content row">
                                <form class=" form-bordered">
                                    <div class="form-group col-md-3">
                                        <label class="col-md-5 control-label" style="text-align: right;">员工编码</label>
                                        <div class="col-md-7">
                                            <input id="staffCode" type="text" class="form-control">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label class="col-md-5 control-label" style="text-align: right;">渠道编码</label>
                                        <div class="col-md-7">
                                            <input id="channelNbr" type="text" class="form-control">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label class="col-md-5 control-label" style="text-align: right;">渠道名称</label>
                                        <div class="col-md-7">
                                            <input id="channelName" type="text" class="form-control">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label class="col-md-5 control-label" style="text-align: right;">证件号码</label>
                                        <div class="col-md-7">
                                            <input id="certNum" type="text" class="form-control">
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="wmin_content row">
                                <form class=" form-bordered">
                                    <div class="form-group col-md-3">
                                        <label class="col-md-5 control-label" style="text-align: right;">查询时间</label>
                                        <div class="col-md-7">
                                            <input id="startDate" type="text" class="form-control">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label class="col-md-5 control-label" style="text-align: right;">到</label>
                                        <div class="col-md-7">
                                            <input id="endDate" type="text" class="form-control">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <div class="col-md-10 text-right">
                                            <button id="queryHis" type="button" class="btn btn-primary">查询</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="container-fluid row">
                            <div class="form_title">
                                <div><i class="bot"></i>查询结果</div>
                            </div>
                            <div class="container-fluid row" id="certNumTbody">
                                <div class="wmin_content" id="certNumFiveListR">
                                    <div class="col-lg-12 mart10" id="certNumFiveList">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr id="certNumTable">
                                                    <th>地市</th>
                                                    <th>工号</th>
                                                    <th>渠道</th>
                                                    <th>证件号码</th>
                                                    <th>查询时间</th>
                                                    <th>号码总数</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            #if($options != "null" && $options.result && $options.result != "null" && $options.result.size() > 0)
                                                #foreach($certNumFive in $options.result)
                                                <tr>
                                                    <td>$!{certNumFive.staffLanName}</td>
                                                    <td>$!{certNumFive.staffCode}</td>
                                                    <td>$!{certNumFive.channelName}</td>
                                                    <td>$!{certNumFive.certNum}</td>
                                                    <td>$!{certNumFive.createDate}</td>
                                                    <td>共<a data-seq="$!{certNumFive.seq}" style="cursor: pointer">$!{certNumFive.resultTotal}</a>条</td>
                                                </tr>
                                                #end
                                            #elseif( $options.result == "null")
                                            <tr>
                                                <td align='center' colspan='7'>未查询到数据！<td>
                                            </tr>
                                            #end
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <!--翻页start-->
                                <div id="showPageInfo" class="page-box"></div>
                                #if($options.pageInfo && $options.pageInfo != "null")
                                <p class="vita-data">{"pageInfo":$options.pageInfo}</p>
                                #end
                                <!--翻页end -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>