<script type="text/javascript" src="../bundles/assist/common/jquery-form.js"></script>

<div data-widget="configProdlnst" style="height:100%">
    <p class="vita-data">
        $!options
    </p>
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->

        <!--客户定位 start-->
        <div class="box-rightmax">
            <div class="positionuser_title"><h5 class="textcolor6"><i class="icon-user glyphicon titledot"></i><span>客户定位</span>
            </h5></div>
            <div class="positionuser_search">
                <div class="searchbutt"><i class="icon-search glyphicon text16"></i></div>
                <input type="text" class="form-control" placeholder="输入客户证件活名称搜索">
            </div>

            <div class="scan_box">
                <span class="icon-scan glyphicon scanf"></span>
                <div>扫描身份证</div>
            </div>
            <div class="adduser_box"><a class="btn btn-primary btn-rounded" href="buttons.html#">新增客户</a></div>
        </div>
        <!---客户定位en-->
        <div class="box-maincont">
            <div class="homenofood nopagenav">
                <div class="page_main">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="wmin_content row mart15">
                                    <form class="form-bordered">
                                        <div class="form-group col-md-5">
                                            <label class="col-md-4 control-label lablep">地区</label>
                                            <div class="col-md-8">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder=""
                                                           readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">
                                                            选择
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-5">
                                            <label class="col-md-4 control-label lablep">用户号码</label>
                                            <div class="col-md-8">
                                                <input id="userNum" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-2">
                                            <div class="col-md-8 text-right">
                                                <button id="btn-queryCust" type="button" class="btn btn-primary">查询
                                                </button>
                                            </div>
                                        </div>

                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12">
                                        <a class="btn btn-gray btn-outline" id="failureCorrelation">
                                            <i class="glyphicon fa-stale text18"> </i> 失效关联数据
                                        </a>
                                    </div>
                                    <div class="col-lg-12 mart10" id="configProdlnstResult">
                                        <table class="table table-hover" id="configProdlnstList">
                                            <thead>
                                            <tr>
                                                <th>选择</th>
                                                <th>用户号码</th>
                                                <!--<th>用户名称</th>-->
                                                <th>产品规格</th>
                                                <th>地区</th>
                                                <th>限制业务动作</th>
                                                <th>备注</th>
                                                <th>操作员编号</th>
                                                <th>类型名称</th>

                                            </tr>
                                            </thead>
                                            <tbody>
                                            #if($options != "null" && $options.configProdlnstList &&
                                            $options.configProdlnstList !=
                                            "null"
                                            &&
                                            $options.configProdlnstList.size() > 0)
                                            #foreach($configProdlnst in $options.configProdlnstList)
                                            <tr name="trClick">
                                                <td><input id="c_accNum" type="checkbox" name="checkbox">
                                                    <a class="textcolorgreen" link="customerConfig"></a>
                                                    <p class="vita-data">{"prodServLimitCpgId" :
                                                        $!{configProdlnst.prodServLimitCpgId}}</p></td>
                                                <td>$!{configProdlnst.accNum}</td>
                                                <!--<td>$!{options.projectDetailsname}</td>-->
                                                <td>$!{configProdlnst.prodName}</td>
                                                <td>$!{configProdlnst.regionName}</td>
                                                <td>$!{configProdlnst.serviceOfferName}</td>
                                                <td>$!{configProdlnst.remark}</td>
                                                <td>$!{configProdlnst.statusStaff}</td>
                                                <td>$!{configProdlnst.typeName}</td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.configProdlnstList &&
                                            $options.configProdlnstList
                                            !=
                                            "null" &&
                                            $options.configProdlnstList.size() == 0)
                                            <tr>
                                                <td align='center' colspan='7'>未查询到数据！
                                                <td>
                                            </tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->


                                        <!--填单end-->
                                        <!--填单1start-->
                                        <div class="col-lg-12">
                                            <div class="box-item">
                                                <div class="container-fluid row">
                                                    <div class="wmin_content row mart15">
                                                        <form class="form-bordered">
                                                            <div class="form-group col-md-5">
                                                                <label class="col-md-4 control-label lablep">用户号码</label>
                                                                <div class="col-md-8">
                                                                    #if($options != "null" && $options.userNum && $options.userNum != "null")
                                                                    #set($userNum = $options.userNum)
                                                                    #end

                                                                    <input id="configNum" name="configNum" type="text" readonly="readonly"
                                                                           value="$!userNum"
                                                                           class="form-control"
                                                                    >
                                                                </div>
                                                            </div>
                                                            <div class="form-group col-md-5">
                                                                <label class="col-md-4 control-label lablep">动作名称</label>
                                                                <div class="col-md-8">
                                                                    <select id="addBoType" class="form-control">
                                                                        #if($options != "null" && $options.serviceOfferIdsQuery &&
                                                                        $options.serviceOfferIdsQuery !=
                                                                        "null"
                                                                        && $options.serviceOfferIdsQuery.size() > 0)
                                                                        #foreach($itme in $options.serviceOfferIdsQuery)

                                                                        <option serviceOfferId="${itme.parServiceOfferId}"
                                                                                prodInstId="$!{itme.serviceOfferId}">
                                                                            $!{itme.serviceOfferName}
                                                                        </option>
                                                                        #end
                                                                        #end
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="form-group col-md-5">
                                                                <label class="col-md-4 control-label lablep">备注</label>
                                                                <div class="col-md-8">
                                                                    <input id="remarks" type="text" class="form-control"
                                                                           placeholder="">
                                                                </div>
                                                            </div>
                                                            <div class="form-group col-md-5">
                                                                <label class="col-md-4 control-label lablep"> 接入号类型</label>
                                                                <div class="col-md-8">
                                                                    <select id="statusType" class="form-control">
                                                                        <option value="2">普通电话</option>
                                                                        <option value="9">宽带</option>
                                                                        <option value="69">固话类</option>
                                                                        <option value="650020001">iTV账号</option>
                                                                        <option value="379">手机</option>

                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="form-group col-md-5">
                                                                <label class="col-md-4 control-label lablep"></label>
                                                                <div class="col-md-8 ">
                                                                    <button id="btn-addCust"  type="button" class="btn btn-primary">新增动作关系
                                                                    </button>
                                                                </div>
                                                            </div>


                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="container-fluid row">
                                <div class="form_title">导入EXCEL文件批量功能</div>
                                <!--  <div class="wmin_content row mart15">
                                  <form class="form-bordered">
                                        <div class="form-group col-md-5">
                                            <label class="col-md-4 control-label lablep"> 产品规格</label>
                                            <div class="col-md-8">
                                                <select id="status" class="form-control">
                                                    <option>手机</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-5">
                                            <label class="col-md-4 control-label lablep">动作名称</label>
                                            <div class="col-md-8">
                                                <select id="batchAddBoType" class="form-control">
                                                    #if($options != "null" && $options.serviceOfferIds && $options.serviceOfferIds != "null"
                                                    && $options.serviceOfferIds.size() > 0)
                                                    #foreach($item in $options.serviceOfferIds)
                                                    <option serviceOfferId="${item.parServiceOfferId}">
                                                        $!{item.serviceOfferName}
                                                    </option>
                                                    #end
                                                    #end
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-5">
                                            <label class="col-md-4 control-label lablep">备注</label>
                                            <div class="col-md-8">
                                                <input id="batchAddRemark"  type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                </div>
                                    </form>
                            </div>-->

                                <div class="form_title"></div>
                                <div class="wmin_content ">
                                    <div class="col-lg-12">
                                        <form id="a_form" action="../action/uploadConfigExcels" method="post"
                                              enctype="multipart/form-data">
                                            <input type="file" class="m_file" name="data"  accept="xls">
                                            <br>
                                            <!--<button type="button" id="m_file" class="btn btn-default"><span>选择要上传的EXCEL文件</span>-->
                                            <!--</button>-->
                                            <input class="route_input" type="text" value="" name="jsonStr"
                                                   style="display: none"/>
                                            <button id="button" type="button" class="btn btn-primary">提交</button>
                                            <a href="javascript:;" id="btn-querys" class="btn btn-primary">导出操作结果信息</a>
                                            <a href="javascript:;" id="upLoadTemplet" class="btn btn-primary">批量上传文件模板下载</a>
                                        </form>
                                    </div>

                                <div class="col-lg-12 mart10">
                                    <p><strong>注意：</strong></p>
                                    <p>
                                        1、接入号类型说明：2:普通电话，9:宽带，69:固话类，379：手机，$!options.itvId:iTV账号<br>
                                        2、操作类型：ADD表示增加，DEL表示删除<br>
                                        3、一次不超过500条<br>
                                        4、接入号类型，接入号，禁止业务id，操作状态为必填<br>
                                        5、文件内容参考下面列表
                                    </p>
                                    <table width="55%" cellspacing="0" cellpadding="0" border="0"
                                           class="list_table_inner">
                                        <tbody>
                                        <tr>
                                            <th class="h_td">A</th>
                                            <th class="h_td">B</th>
                                            <th class="h_td">D</th>
                                            <th class="h_td">E</th>
                                            <th class="h_td">F</th>
                                        </tr>
                                        <tr>
                                            <td class="td_content_mid">接入号类型</td>
                                            <td class="td_content_mid">接入号</td>
                                            <td class="td_content_mid">禁止业务id</td>
                                            <td class="td_content_mid">操作类型</td>
                                            <td class="td_content_mid">备注</td>
                                        </tr>
                                        <tr>
                                            <td class="td_content_mid">379</td>
                                            <td class="td_content_mid">18981101171</td>
                                            <td class="td_content_mid">3020400001</td>
                                            <td class="td_content_mid">ADD</td>
                                            <td class="td_content_mid">测试新增2</td>
                                        </tr>
                                        <tr>
                                            <td class="td_content_mid">379</td>
                                            <td class="td_content_mid">18981101172</td>
                                            <td class="td_content_mid">3020501002</td>
                                            <td class="td_content_mid">ADD</td>
                                            <td class="td_content_mid">测试新增2</td>
                                        </tr>
                                        <tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>


                        </div>
                    </div>
                    <!--填单1end-->

                </div>

            </div>
        </div>
    </div>
    </div>
</div>
