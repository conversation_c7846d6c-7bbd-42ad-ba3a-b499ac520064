(function (vita) {
    var chargeAdjustLevelCfg = vita.Backbone.BizView.extend({
        events: {
            "click #queryBtn": "_qryChargeAdjustLevelCfg",
            "click #resetBtn": "_clearConditions",
            "click #queryRegionIdBtn": "_chooseQryAddArea",
            "click #addBtn": "_addPopup",
            "click #editBtn": "_editPopup",
            "click #deleteBtn": "_deleteSubmit",
            "click #addSubmitBtn": "_addSubmit",
            "click #editSubmitBtn": "_editSubmit",
            "click button.close": "_closeBtnClick",
            "click #closeBtn": "_closeBtnClick",
            "click #_regionIdBtn": "_chooseAddArea",
        },
        _initialize: function () {
            var widget = this,
                el = $(widget.el);
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            this._qryChargeAdjustLevelCfg();
        },
        global: {
            totalNumber: 0,
            pageIndex: 1,
            pageSize: 10,
            datetime: 'time',
            date: 'date',
            chooseArea: "../comm/chooseArea",
        },

        /**
         * 打开添加弹框
         */
        _addPopup: function () {
            var widget = this,
                el = $(widget.el);
            widget._cleanPopupData();
            widget._openPopup("add");
        },
        _openPopup: function (action) {
            var widget = this,
                el = $(widget.el);

            switch (action) {
                case "add":
                    el.find(".titlefont").html("添加");
                    el.find(".hideAndShow").show();
                    el.find("#addSubmitBtn").show();
                    el.find("#_regionIdBtn").show();
                    el.find("#_chargeAdjustLevelCfgId").attr("disabled","disabled");
                    el.find("#editSubmitBtn").hide();
                    el.find(".popup").show();
                    break;
                case "edit":
                    el.find(".titlefont").html("编辑");
                    el.find(".hideAndShow").show();

                    el.find("#_chargeAdjustLevelCfgId").attr("disabled","disabled");
                    el.find("#_lanId").attr("disabled","disabled");
                    el.find("#addSubmitBtn").hide();
                    el.find("#editSubmitBtn").show();
                    el.find(".popup").show();
                    break;
                default:
                    break;
            }
        },
 
        /**
         * 新增
         * @private
         */
        _addSubmit: function () {
            var widget = this;
            var param = widget._getPopupData();
            if(!this._checkParam(param)){
                return;
            }
            widget.callService("createChargeAdjustLevelCfg", JSON.stringify(param), function (res) {
                    if("0" == res.resultCode){
                        widget.popup("添加成功");
                        widget._closeBtnClick();
                        widget._qryChargeAdjustLevelCfg();
                    }else{
                        widget.popup(res.resultMsg);
                    }
            }, {async: false, mask: false});
        },
        /**
         * 编辑更新
         * @private
         */
        _editSubmit: function () {
            var widget = this,el = $(widget.el);
            var param = widget._getPopupData();
            param["chargeAdjustLevelCfgId"] = el.find("#_chargeAdjustLevelCfgId").val().trim()
            if(!this._checkParam(param)){
                return;
            }
            widget.callService("updateChargeAdjustLevelCfg", JSON.stringify(param), function (res) {
                if (res) {
                    if("0" == res.resultCode){
                        widget.popup("修改成功");
                        widget._closeBtnClick();
                        widget._qryChargeAdjustLevelCfg();
                    }else{
                        widget.popup(res.resultMsg);
                    }
                }
            }, {async: false, mask: false});
        },
        /**
         * 打开编辑弹框
         */
        _editPopup: function () {
            var widget = this,
                el = $(widget.el);
            var payment = el.find("table").find("input[name='payment']:checked");
            if (payment.length > 0) {
                widget._cleanPopupData();
                var tr = payment.parent().parent().parent();
                el.find("#_chargeAdjustLevelCfgId").val(tr.find("td:eq(1)").text());
                el.find("#_lanId").val(tr.find("td:eq(2)").text()).attr("value", tr.find("td:eq(2)").attr("value"));
                el.find("#_acctItemTypeId").val(tr.find("td:eq(3)").text());
                el.find("#_serviceOfferId").val(tr.find("td:eq(4)").attr("value"));
                el.find("#_prodId").val(tr.find("td:eq(5)").text());
                el.find("#_adjustLevelId").val(tr.find("td:eq(6)").text());
                widget._openPopup("edit");
            } else {
                widget.popup("请选择要编辑的一行!");
                return;
            }
        },
        /**
         * 弹出框关闭事件
         */
        _closeBtnClick: function () {
            var widget = this,
                el = $(widget.el);
            el.find(".popup").hide();
            el.find(".popup1").hide();
        },

        /**
         * 清除弹框数据
         */
        _cleanPopupData: function () {
            var widget = this,
                el = $(widget.el);
            el.find("form[name=_popupForm]").resetForm();
            el.find("#_lanId").val('').attr("value","");
        },


        /**
         * 重置查询条件
         */
        _clearConditions: function () {
            var widget = this,
                el = $(widget.el);
            el.find("#acctItemTypeId").val('');
            el.find("#prodId").val('');
            el.find("#queryLanId").val('').attr("value","");
        },
        /**
         * 获取弹出框数据
         * @private
         */
        _getPopupData: function () {
            var widget = this,
                el = $(widget.el);
            var params = {
                "chargeAdjustLevelCfgId": el.find("#_chargeAdjustLevelCfgId").val().trim(),
                "regionId":  $("#_lanId").attr("value"),
                "acctItemTypeId": el.find("#_acctItemTypeId").val().trim(),
                "serviceOfferId": el.find("#_serviceOfferId").val().trim(),
                "prodId": el.find("#_prodId").val().trim(),
                "adjustLevelId": el.find("#_adjustLevelId").val().trim(),

            };
            return params;
        },
        _checkParam: function (param){
            var widget = this,
                el = $(widget.el);
            var soUtil = widget.require("soUtil");
            if(soUtil.isNullOrEmpty(param.adjustLevelId)){
                widget.popup("参数必填，请检查后重新提交!");
                return false;
            }
            return true;
        },

        /**
         * 查询
         */
        _qryChargeAdjustLevelCfg: function () {
            var widget = this,
                el = $(widget.el);
            var param = widget._getConds();
            widget.refreshPart("qryChargeAdjustLevelCfgPageInfo", param, "#loadData",
                "reqList", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = $(res).find("table").data("totalNumber");
                    if (parseInt(totalNumber) > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                param.pageInfo.pageIndex = _pageIndex;
                                param.pageInfo.pageSize = _recordNumber;
                                widget.refreshPart("qryChargeAdjustLevelCfgPageInfo", param, "div[name=tableDiv]",null,null,{mask: true});

                                ;
                            }
                        });
                        r.find("#pageInfo").append(e.getElement());
                    }
                }, {mask: true});
        },
        _getConds: function () {
            var widget = this, el = $(widget.el);
            var param = {
                "pageInfo": {
                    "pageIndex": widget.global.pageIndex,
                    "pageSize": widget.global.pageSize,
                }
            };
            param.regionId = $("#queryLanId").attr("value");
            param.acctItemTypeId = $("#acctItemTypeId").val();
            param.prodId = $("#prodId").val();

            return param;
        },
        _chooseQryAddArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#queryLanId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _chooseAddArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#_lanId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
    });
    vita.widget.register("chargeAdjustLevelCfg", chargeAdjustLevelCfg, true);})(window.vita);
