(function (vita) {
    var protocolManageList = vita.Backbone.BizView.extend({
        events: {
            "click #addAgreement": "_addAgreement",
            "click #reviewBtn" : "_reviewBtn",//预览
            "click #activeBtn" : "_activeBtn",//上线
            "click #invalidBtn" : "_invalidBtn",//下线
            "click #channelIdBtn": "_selChannel",
            "click #offerIdBtn": "_openConfig",
            "click #editBtn" : "_editBtn",//编辑
            "click #saveBtn" : "_saveBtn",//保存
            "click #qryButton" : "_qryButton"//查询
        },
        _initialize: function () {
        	var widget = this,element = $(widget.el);
        	var data = element.data("data");
        	widget.model.set("regionId", data.regionId); 
        	widget.model.set("privCode", data.privCode); 
        },
        global: {
        	"review" : "../action/protocolDisplay",
        	"newOrUpdateProtocol" : "../action/newOrUpdateProtocol",
        	"chooseOffer" : "../comm/chooseOffer",
        	"selChannel" : "../comm/selChannel",
        	"openConfig" : "../action/openProtocolConfig"
        },
        //查询
        _qryButton : function(){
        	debugger;
        	var widget = this,element = $(widget.el);
        	var gsession = widget.gSession;
        	var data = {
        			custOrderNbr : "",
        			rengSe : ""
        	};
        	data.regionId = gsession.staffRegionId;
        	data.staffId = gsession.staffId;
        	data.privCode = widget.model.get("privCode"); 
        	data.status = $('#status').val();
            if($("#c_custOrderNbr").prop("checked")==true){
            	data.custOrderNbr = $('#custOrderNbr').val();
            }
            if (data.privCode=='true') {
            	data.rengSe = $('#rengSe').val();
			}
            widget.refreshPart("qryProtocolManage", JSON.stringify(data),"#qryprotool",function(res) {
            	
            	})
        }, 
        _selChannel: function (e) {
            var widget = this,element = $(widget.el);
            
            var button = $(e.target).closest("button");
        	var tr = button[0].parentElement.parentElement;
        	var protocolTempletId = $(tr).find("td[name=tds]").text();
            
            var id = "selChannel";
            var dialogId = id + 'Dialog';
            widget.dialog({
                id : dialogId,
                url : widget.global[id],
                params : {
                },
                onOpen : function(res){
					var channels = $(res).closest("[data-widget="+ id +"]");
					channels.selChannel("setDatas","YES");
				},
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + id + "]");
                    var data = comp[id]("getValue");
                    if (!data) {
                        return false;
                    }
                    debugger;
                    var orgs = data.orgs;
                    var rels = new Array();
					if(orgs && typeof(orgs)!="undefined" && orgs.length>0){
						$.each(orgs,function(i,info){
							debugger;
							var protocolTempletRel = new Object();
							protocolTempletRel.objId = info.orgId;
							protocolTempletRel.objType = "1100";
							protocolTempletRel.protocolTempletId = protocolTempletId;
							protocolTempletRel.regionId = widget.model.get("regionId");
							rels.push(protocolTempletRel);
						});
						
						widget.callService("saveProtocolTempletConfig", JSON.stringify(rels),function(res) {
	        				var ret = JSON.parse(res);
	        				if (ret.resultCode == 0) {
	        					widget.popup("操作成功！");
	        					widget._refresh();
	        				}else{
	        					widget.popup("操作失败！");
	        				}				
	        			});
					}
                }
            });
        },
        _openConfig:function (e) {
        	var widget = this,element = $(widget.el);
        	var button = $(e.target).closest("button");
        	var tr = button[0].parentElement.parentElement;
        	var protocolTempletId = $(tr).find("td[name=tds]").text();
        	
        	var id = "openConfig";
			var param = {
					"protocolTempletId":protocolTempletId
			};
			var dialogId = id + 'Dialog';
			var option = {
				url : widget.global[id],
				params : param,
				id : dialogId,
				onClose : function(res) {
					//widget._refresh();
				}
			};
			widget.dialog(option);
        },
        _chooseOffer: function (e) {
        	debugger;
            var widget = this,element = $(widget.el);
            
            var button = $(e.target).closest("button");
        	var tr = button[0].parentElement.parentElement;
        	var protocolTempletId = $(tr).find("td[name=tds]").text();
            
            var id = "chooseOffer";
            var dialogId = id + 'Dialog';
            widget.dialog({
                id : dialogId,
                url : widget.global[id],
                params : {
                },
                onOpen : function(res){
					var offers = $(res).closest("[data-widget="+ id +"]");
					offers.chooseOffer("setDatas","YES");
				},
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + id + "]");
                    var data = comp[id]("getValue");
                    if (!data) {
                        return false;
                    }
                    debugger;
                    var items = data.items;
                    var rels = new Array();
					if(items && typeof(items)!="undefined" && items.length>0){
						$.each(items,function(i,info){
							debugger;
							var protocolTempletRel = new Object();
							protocolTempletRel.objId = info.offerId;
							protocolTempletRel.objType = "1200";
							protocolTempletRel.protocolTempletId = protocolTempletId;
							protocolTempletRel.regionId = widget.model.get("regionId");
							rels.push(protocolTempletRel);
						});
						
						widget.callService("saveProtocolTempletConfig", JSON.stringify(rels),function(res) {
	        				var ret = JSON.parse(res);
	        				if (ret.resultCode == 0) {
	        					widget.popup("操作成功！");
	        					widget._refresh();
	        				}else{
	        					widget.popup("操作失败！");
	        				}				
	        			});
					}
                }
            });
        },
        _addAgreement:function(){
        	var widget = this,element = $(widget.el);
        	var id = "newOrUpdateProtocol";
			var param = {
					name : ""
			};
			var dialogId = id + 'Dialog';
			var option = {
				url : widget.global[id],
				params : param,
				id : dialogId,
				onClose : function(res) {
					widget._refresh();
				}
			};
			widget.dialog(option);
			
        	//window.open("../action/newOrUpdateProtocol");
        },
        //预览
        _reviewBtn : function (e){
        	debugger
        	var widget = this,element = $(widget.el);
        	var button = $(e.target).closest("button");
        	var tr = button[0].parentElement.parentElement;
        	var protocolTempletId = $(tr).find("td[name=tds]").text();
        	var params = {
        			"protocolTempletId" : protocolTempletId
        	}
        	widget.callService("qryProtocolAndOrderVo", JSON.stringify(params),function(res){
        		if (res.result == null) {
        			widget.popup("查询异常，无法预览！");
					return;
				}
        		var  result = res.result;
        		var id = "review";
    			var param = {
    					"str":result
    			};
    			var dialogId = id + 'Dialog';
    			var option = {
    				url : widget.global[id],
    				params : param,
    				id : dialogId,
    				onClose : function(res) {}
    			};
    			widget.dialog(option);
				//window.open("../action/protocolDisplay?str="+encodeURI(encodeURI(result)));
			})
        	
        },
        //编辑
        _editBtn : function (e){
        	debugger;
        	var widget = this,element = $(widget.el);
        	var options = element.data("data"); 
        	var button = $(e.target).closest("button");
        	var tr = button[0].parentElement.parentElement;
        	var protocolTempletId = $(tr).find("td[name=tds]").text();
        	var name = $(tr).find("td[name=tdName]").text();
        	
        	var flag = true;
        	$.each(options.protocolManageLists,function(i,protocal){
				if(protocal.protocolTempletId == protocolTempletId && protocal.statusCd == "1000"){
					widget.popup("请先将协议模板："+protocal.name+"下线后再进行编辑操作！");
					flag = false;
					return;
				}
			});
        	
			if(flag){
				var id = "newOrUpdateProtocol";
				var param = {
						"str":protocolTempletId,
						"name":name
				};
				var dialogId = id + 'Dialog';
				var option = {
					url : widget.global[id],
					params : param,
					id : dialogId,
					onClose : function(res) {
						widget._refresh();
					}
				};
				widget.dialog(option);
			}
        	
			
        	//window.open("../action/newOrUpdateProtocol?str="+protocolTempletId);
        },
        //上线
        _activeBtn : function(e){
        	var widget = this, gsession = widget.gSession;
        	var data = {};
        	var pams = {};
        	var n = window.prompt('请输入上线备注');
        	if ("" == n) {
        		widget.popup("备注不能为空！");
        		return;
    		}else if (null == n) {
    			return;
			}
        	pams.remark = n;
        	pams.createStaff = gsession.staffId;
        	pams.changeMode = "1000";
			//保存为下线状态
			data.statusCd = "1000";
			data.pams = pams;
			widget._saveProtocol(e, data);
        },
        //下线
        _invalidBtn : function (e){
        	var widget = this, gsession = widget.gSession;
        	var data = {};
        	var pams = {};
        	var n = window.prompt('请输入下线备注');
    	
    		if ("" == n) {
        		widget.popup("备注不能为空！");
        		return;
    		}else if (null == n) {
    			return;
			}
    		pams.remark = n;
    		pams.createStaff = gsession.staffId;
    		pams.changeMode = "1200";
			//提交为上线状态
			data.statusCd = "1200";
			data.pams = pams;
			widget._saveProtocol(e, data);
        },
        //保存
        _saveBtn : function(e){
        	debugger
        	var widget = this,element = $(widget.el);
        	var button = $(e.target).closest("button");
        	var tr = button[0].parentElement.parentElement;
        	var reg = $(tr).find("select[id=sele]").val();
        	var data = {};
        	var pams = {};
        	
        	data.pams = pams;
			data.regionId = reg;
			widget._saveProtocol(e, data);
        },
        _saveProtocol : function(e, data){
        	var widget = this,element = $(widget.el);
        	var button = $(e.target).closest("button");
        	var tr = button[0].parentElement.parentElement;
        	var protocolTempletId = $(tr).find("td[name=tds]").text();
			
			if(protocolTempletId == ""){
				widget.popup("协议标识不能为空！");
				return false;
			}
			data.protocolTempletId = protocolTempletId;
			data.crateStaff = data.pams.createStaff;
			data.pams.protocolTempletId = protocolTempletId;
	    	widget.callService("saveProtocol", JSON.stringify(data),function(res) {
				var ret = JSON.parse(res);
				if (ret.resultCode == 0) {
					widget.popup("操作成功！");
					widget._refresh();
				}else{
					widget.popup("操作失败！");
				}				
			});
			 
		},
		_refresh : function(e, data){
			location.reload();
		}
    
    });
    vita.widget.register("protocolManageList", protocolManageList, true);
})(window.vita);