(function (vita) {
    var showdownloadpage = vita.Backbone.BizView.extend({
        events: {
            "click #downloadfile": "_download",
        },

        _initialize: function () {
            var widget = this,
                el = $(widget.el);
            var params = {
              /*  accNums:accessNums,
                manageNum:manageNum,*/
                pageInfo: {
                    pageIndex: 1,
                    pageSize: 10
                }
            };
            widget.refreshPart("queryFiles", JSON.stringify(params), "#auditAdjustInfoListResult", function (res) {
                debugger;
                var paging = widget.require("paging"), r = $(res);
                var totalNumber = r.find("#showPageInfo").data("totalNumber");
                if (totalNumber > 0) {
                    var e = paging.new({
                        recordNumber: widget.global.pageSize,
                        total: totalNumber,
                        pageIndex: widget.global.pageIndex,
                        callback: function (_pageIndex, _recordNumber) {
                            params.pageInfo.pageIndex = _pageIndex;
                            params.pageInfo.pageSize = _recordNumber;
                            if (widget._getConds()) {
                                widget.refreshPart("queryFiles", JSON.stringify(params), "#fileInfoListTable");
                            }
                            ;
                        }
                    });
                    r.find("#showPageInfo").append(e.getElement());
                }

            }, {
                async: false
            });
        },
        global:{
            pageIndex: 1,
            pageSize: 10
        },
        _getConds: function () {
            var widget = this;
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            return params;
        },
        _download : function(e) {
            var widget = this,
            aEl = $(e.target).closest("a");
            var filePath = aEl.data("filePath");
            var fileName = aEl.data("fileName");
            var url = "../query/downLoadfile?filePath=" + filePath +"&fileName=" + fileName;
            location.href=url;
        },
    });
    vita.widget.register("showdownloadpage", showdownloadpage, true);
})(window.vita);