<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="Spring" name="Spring">
      <configuration />
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_7">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Maven: com.ai:vita-plus:1.1.11" level="project" />
    <orderEntry type="library" name="Maven: com.al:al-common:1.0.1-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis:3.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis-spring:1.0.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-lang3:3.1" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.jackson:jackson-jaxrs:1.9.4" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.jackson:jackson-xc:1.9.4" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:log4j-over-slf4j:1.6.4" level="project" />
    <orderEntry type="library" name="Maven: net.sf.json-lib:json-lib:jdk15:2.4" level="project" />
    <orderEntry type="library" name="Maven: net.sf.ezmorph:ezmorph:1.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.jsoup:jsoup:1.9.2" level="project" />
    <orderEntry type="library" name="Maven: eu.bitwalker:UserAgentUtils:1.20" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context-support:4.1.4.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:4.1.4.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:4.1.4.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:4.1.4.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: aopalliance:aopalliance:1.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:4.1.4.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:4.1.4.RELEASE" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Maven: javax.servlet:servlet-api:2.5" level="project" />
    <orderEntry type="library" name="Maven: commons-fileupload:commons-fileupload:1.2.2" level="project" />
    <orderEntry type="library" name="Maven: commons-beanutils:commons-beanutils:1.8.0" level="project" />
    <orderEntry type="library" name="Maven: commons-logging:commons-logging:1.1.1" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.5" level="project" />
    <orderEntry type="library" name="Maven: commons-httpclient:commons-httpclient:3.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.velocity:velocity:1.7" level="project" />
    <orderEntry type="library" name="Maven: commons-lang:commons-lang:2.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.velocity:velocity-tools:2.0" level="project" />
    <orderEntry type="library" name="Maven: commons-digester:commons-digester:1.8" level="project" />
    <orderEntry type="library" name="Maven: commons-chain:commons-chain:1.1" level="project" />
    <orderEntry type="library" name="Maven: commons-validator:commons-validator:1.3.1" level="project" />
    <orderEntry type="library" name="Maven: dom4j:dom4j:1.1" level="project" />
    <orderEntry type="library" name="Maven: oro:oro:2.0.8" level="project" />
    <orderEntry type="library" name="Maven: sslext:sslext:1.2-0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.struts:struts-core:1.3.8" level="project" />
    <orderEntry type="library" name="Maven: antlr:antlr:2.7.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.struts:struts-taglib:1.3.8" level="project" />
    <orderEntry type="library" name="Maven: org.apache.struts:struts-tiles:1.3.8" level="project" />
    <orderEntry type="library" name="Maven: junit:junit:4.9" level="project" />
    <orderEntry type="library" name="Maven: org.hamcrest:hamcrest-core:1.1" level="project" />
    <orderEntry type="library" name="Maven: com.asiainfo.crm:sm-auth-busi:0.0.2-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.al:al-dsession:0.0.3-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.esotericsoftware.kryo:kryo:2.21" level="project" />
    <orderEntry type="library" name="Maven: com.esotericsoftware.reflectasm:reflectasm:shaded:1.07" level="project" />
    <orderEntry type="library" name="Maven: com.esotericsoftware.minlog:minlog:1.2" level="project" />
    <orderEntry type="library" name="Maven: org.objenesis:objenesis:1.2" level="project" />
    <orderEntry type="library" name="Maven: org.elasticsearch:elasticsearch:2.4.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-core:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-backward-codecs:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-analyzers-common:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-queries:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-memory:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-highlighter:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-queryparser:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-sandbox:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-suggest:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-misc:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-join:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-grouping:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-spatial:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-spatial3d:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: com.spatial4j:spatial4j:0.5" level="project" />
    <orderEntry type="library" name="Maven: com.google.guava:guava:18.0" level="project" />
    <orderEntry type="library" name="Maven: org.elasticsearch:securesm:1.0" level="project" />
    <orderEntry type="library" name="Maven: com.carrotsearch:hppc:0.7.1" level="project" />
    <orderEntry type="library" name="Maven: joda-time:joda-time:2.9.5" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.8.1" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.dataformat:jackson-dataformat-smile:2.8.1" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.8.1" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.8.1" level="project" />
    <orderEntry type="library" name="Maven: org.yaml:snakeyaml:1.15" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty:3.10.6.Final" level="project" />
    <orderEntry type="library" name="Maven: com.ning:compress-lzf:1.0.2" level="project" />
    <orderEntry type="library" name="Maven: com.tdunning:t-digest:3.0" level="project" />
    <orderEntry type="library" name="Maven: org.hdrhistogram:HdrHistogram:2.1.6" level="project" />
    <orderEntry type="library" name="Maven: commons-cli:commons-cli:1.3.1" level="project" />
    <orderEntry type="library" name="Maven: com.twitter:jsr166e:1.1.0" level="project" />
    <orderEntry type="library" name="Maven: csf:csf-client:2.2.1" level="project" />
    <orderEntry type="library" name="Maven: csf:csf-common:2.2.1" level="project" />
    <orderEntry type="library" name="Maven: csf:csf-protocol-http:2.2.1" level="project" />
    <orderEntry type="library" name="Maven: csf:csf-protocol-http-server:2.2.1" level="project" />
    <orderEntry type="library" name="Maven: csf:csf-servicerouter:2.2.1" level="project" />
    <orderEntry type="library" name="Maven: csf:csf-zookeeperclient:2.2.1" level="project" />
    <orderEntry type="library" name="Maven: csf:csf-executor:2.2.1" level="project" />
    <orderEntry type="library" name="Maven: csf:csf-protocol-socket:2.2.1" level="project" />
    <orderEntry type="library" name="Maven: com.ai.aif:log4x:2.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.curator:curator-framework:2.10.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.curator:curator-client:2.10.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.zookeeper:zookeeper:3.4.6" level="project" />
    <orderEntry type="library" name="Maven: log4j:log4j:1.2.16" level="project" />
    <orderEntry type="library" name="Maven: jline:jline:0.9.94" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpcore:4.4.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpclient:4.5.2" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.hystrix:hystrix-core:1.5.5" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.archaius:archaius-core:0.4.1" level="project" />
    <orderEntry type="library" name="Maven: commons-configuration:commons-configuration:1.8" level="project" />
    <orderEntry type="library" name="Maven: commons-io:commons-io:2.4" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty:1.0" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-all:4.1.5.Final" level="project" />
    <orderEntry type="library" name="Maven: com.asiainfo.crm:al-bcomm:0.2.4-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.asiainfo.angel:angel-core:2.0.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webmvc:4.1.4.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-web:4.1.4.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.woodstox:stax2-api:4.0.0" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.5.1" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.5.1" level="project" />
    <orderEntry type="library" name="Maven: com.asiainfo:al-comm-seq:0.0.1-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: studio.raptor:raptor-gid:4.1.0-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: org.softee:pojo-mbean:1.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jdbc:4.1.4.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-tx:4.1.4.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.aictcrm:comm-mq:0.0.1-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.aictcrm:comm-mq-ext-rocketmq:0.0.1-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.5.1" level="project" />
    <orderEntry type="library" name="Maven: aif.msgframe:msgframe-client:2.0" level="project" />
    <orderEntry type="library" name="Maven: aif.msgframe:msgframe-common:2.0" level="project" />
    <orderEntry type="library" name="Maven: aif.msgframe:msgframe-server:2.0" level="project" />
    <orderEntry type="library" name="Maven: aif.msgframe:xmlmsgframe:2.7.4" level="project" />
    <orderEntry type="library" name="Maven: aif.msgframe:xbean:1.0" level="project" />
    <orderEntry type="library" name="Maven: aif.third.amber-client:amber-client:1.1.1" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.rocketmq:rocketmq-client:3.5.8" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.rocketmq:rocketmq-common:3.5.8" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.rocketmq:rocketmq-remoting:3.5.8" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.rocketmq:rocketmq-srvutil:3.2.6" level="project" />
    <orderEntry type="library" name="Maven: studio.raptor:databus-client:0.1.3-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: studio.raptor:cmdb-client:0.0.2" level="project" />
    <orderEntry type="library" name="Maven: studio.raptor:cmdb-core:0.0.2" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.gson:gson:2.8.0" level="project" />
    <orderEntry type="library" name="Maven: studio.raptor:cmdb-foundation-service:0.0.2" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.plexus:plexus-container-default:1.6" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.plexus:plexus-classworlds:2.5.1" level="project" />
    <orderEntry type="library" name="Maven: studio.raptor:cmdb-foundation:0.0.2" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.plexus:plexus-utils:3.0.24" level="project" />
    <orderEntry type="library" name="Maven: org.apache.xbean:xbean-reflect:4.5" level="project" />
    <orderEntry type="library" name="Maven: javax.validation:validation-api:1.1.0.Final" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate:hibernate-validator:5.1.3.Final" level="project" />
    <orderEntry type="library" name="Maven: org.jboss.logging:jboss-logging:3.1.3.GA" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml:classmate:1.0.0" level="project" />
    <orderEntry type="library" name="Maven: javax.el:javax.el-api:2.2.4" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.web:javax.el:2.2.4" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:fastjson:1.2.32" level="project" />
    <orderEntry type="library" name="Maven: com.google.protobuf:protobuf-java:3.3.1" level="project" />
    <orderEntry type="library" name="Maven: io.protostuff:protostuff-core:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: io.protostuff:protostuff-api:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: io.protostuff:protostuff-runtime:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: io.protostuff:protostuff-collectionschema:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: javax.management.j2ee:javax.management.j2ee-api:1.1.1" level="project" />
    <orderEntry type="library" name="Maven: org.fusesource.hawtbuf:hawtbuf:1.11" level="project" />
    <orderEntry type="library" name="Maven: org.apache.geronimo.specs:geronimo-jms_1.1_spec:1.1.1" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.12" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.jackson:jackson-mapper-asl:1.9.13" level="project" />
    <orderEntry type="library" name="Maven: com.al:spring-zext:1.0.6-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.al.crm.nosql:nosql-cache:1.2.0-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: redis.clients:jedis:2.9.0" level="project" />
    <orderEntry type="library" name="Maven: jaxen:jaxen:1.1.4" level="project" />
    <orderEntry type="library" name="Maven: com.oracle:ojdbc14:10.2.0.4.0" level="project" />
    <orderEntry type="library" name="Maven: cglib:cglib-nodep:2.2.2" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.8.7" level="project" />
    <orderEntry type="library" name="Maven: aif:aicache-client:1.0" level="project" />
    <orderEntry type="library" name="Maven: aif:aicache-proxy:1.0" level="project" />
    <orderEntry type="library" name="Maven: com.101tec:zkclient:0.10" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:dubbo:2.5.3" level="project" />
    <orderEntry type="library" name="Maven: org.jboss.netty:netty:3.2.5.Final" level="project" />
    <orderEntry type="library" name="Maven: com.al.crm.nosql:nosql-cache-multilevel:1.2.0-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.google.inject:guice:4.1.0" level="project" />
    <orderEntry type="library" name="Maven: javax.inject:javax.inject:1" level="project" />
    <orderEntry type="library" name="Maven: com.google.inject.extensions:guice-assistedinject:4.1.0" level="project" />
    <orderEntry type="library" name="Maven: cglib:cglib:3.2.4" level="project" />
    <orderEntry type="library" name="Maven: org.ow2.asm:asm:5.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.ant:ant:1.9.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.ant:ant-launcher:1.9.6" level="project" />
    <orderEntry type="library" name="Maven: com.al:al-uniconfig:1.0.2-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: com.al:al-notifier:1.0.1-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: org.apache.mina:mina-core:2.0.7" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-pool2:2.4.2" level="project" />
    <orderEntry type="library" name="Maven: com.asiainfo.angel:angel-invoke:2.0.2-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: org.aspectj:aspectjweaver:1.7.1" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-classic:1.1.3" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-core:1.1.3" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jcl-over-slf4j:1.7.12" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.jackson:jackson-core-asl:1.9.4" level="project" />
    <orderEntry type="library" name="Maven: com.al.bss:bsspublic:1.0" level="project" />
    <orderEntry type="library" name="Maven: csf:csf-db:2.2.1" level="project" />
    <orderEntry type="library" name="Maven: csf:csf-protocol-local:2.2.1" level="project" />
    <orderEntry type="library" name="Maven: csf:csf-protocol-remote:2.2.1" level="project" />
    <orderEntry type="library" name="Maven: csf:csf-server:2.2.1" level="project" />
    <orderEntry type="library" name="Maven: aif.appframe:appframe_ext_flyingserver:1.0" level="project" />
    <orderEntry type="library" name="Maven: aif.appframe:appframe:6.0" level="project" />
    <orderEntry type="library" name="Maven: aif.common:common:1.0" level="project" />
    <orderEntry type="library" name="Maven: aif.third.concurrent:concurrent:1.0" level="project" />
    <orderEntry type="library" name="Maven: aif.third.borlandxml:borlandxml:1.0" level="project" />
    <orderEntry type="library" name="Maven: org.jdom:jdom:1.1" level="project" />
    <orderEntry type="library" name="Maven: aif.third.xercesImpl:xercesImpl:1.0" level="project" />
    <orderEntry type="library" name="Maven: aif.third.rowset:rowset:1.0" level="project" />
    <orderEntry type="library" name="Maven: aif.third.j2ee:j2ee:1.0" level="project" />
    <orderEntry type="library" name="Maven: commons-dbcp:commons-dbcp:1.3" level="project" />
    <orderEntry type="library" name="Maven: commons-pool:commons-pool:1.5.4" level="project" />
    <orderEntry type="library" name="Maven: org.javassist:javassist:3.18.1-GA" level="project" />
    <orderEntry type="library" name="Maven: opensymphony:quartz-all:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.curator:curator-recipes:2.10.0" level="project" />
    <orderEntry type="library" name="Maven: io.reactivex:rxjava:1.1.8" level="project" />
    <orderEntry type="library" name="Maven: commons-collections:commons-collections:3.2.1" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Maven: javax.servlet:javax.servlet-api:3.1.0" level="project" />
  </component>
</module>