package com.asiainfo.crm.assist.app.query;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component("vita.cloudOrderInfo")
public class CloudOrderInfo extends AbstractComponent {

    @Autowired
    private IOrderQuerySMO orderQuerySMO;

    @Override
    public Map achieveData(Object... objects) throws Exception {
        Map<String, Object> data = new HashMap<String, Object>();
        return data;
    }

    public Map qryCloudOrderInfo(String jsonString) throws Exception {

        Map option = new HashMap();
        Map<String,Object> returnMap = new HashMap<String,Object>();
        String resultStr  = orderQuerySMO.qryCloudOrderDetail(jsonString);
        Map resultMap = jsonConverter.toBean(resultStr, Map.class);
        String resultCode = MapUtils.getString(resultMap, "resultCode");
        if (!StringUtil.isEmpty(resultCode) && resultCode.equals(MDA.RESULT_SUCCESS.toString())) {
            Map resultObject = (Map) resultMap.get("resultObject");
            option.put("cloudOrderInfos",resultObject);
        }
        return  option;

    }
}
