(function(vita) {
    var attachServicesQuery = vita.Backbone.BizView.extend({
        events : {
            "click #channelIdBtn": "_chooseChannel",
        },
        global: {
            chooseChannel: "../comm/chooseChannel"
        },
        _initialize : function() {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            var channelName = "";
            for(var channels=gSession.channels,i=0; channels.length && i<channels.length; i++) {
                if(gSession.curChannelId == channels[i].orgId) {
                    channelName = channels[i].channelName;
                    break;
                }
            }
            element.find("#channelId").val(channelName).attr("value", gSession.curChannelId);
            widget._goToVsop();
        },
        _chooseChannel: function () {
            var widget = this,element = $(widget.el);
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseChannel,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);
                    widget._goToVsop();
                }
            });
        },
        _goToVsop : function(){
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            // $vsop=$('<iframe id="vsop" class="iframecss"></iframe>').appendTo($('#vsopIframe'));
            // var uri=vsopUri+'&smChannelId='+gsession.channelId+'&smStaffId='+gsession.staffId;
            // $vsop.attr("src",uri);
            var $vsopIframe = $("#vsopIframe");
            var vsopUri = element.data("vsopUri");
            vsopUri += '&smChannelId='+$("#channelId").attr("value")+'&smStaffId='+gSession.staffId;
            // vsopUri += '&smChannelId='+100014407+'&smStaffId='+104001425159;
            $vsopIframe.attr("src",vsopUri);
        }
    });
    vita.widget.register("attachServicesQuery", attachServicesQuery, true);
})(window.vita);