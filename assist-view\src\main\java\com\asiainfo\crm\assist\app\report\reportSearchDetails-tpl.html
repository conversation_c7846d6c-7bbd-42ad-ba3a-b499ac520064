
<div data-widget="reportSearchDetails" style="overflow: auto;height: 100%">

	<div class="pace-done wrapper mini-rightmax no-minright">
		<!--控制测边栏mini-rightmax-->
		<!--控制底部按钮nobuttnfood-->

		<!--客户定位 start-->
		<div class="box-rightmax">
			<div class="positionuser_title">
				<h5 class="textcolor6">
					<i class="icon-user glyphicon titledot"></i><span>客户定位</span>
				</h5>
			</div>
			<div class="positionuser_search">
				<div class="searchbutt">
					<i class="icon-search glyphicon text16"></i>
				</div>
				<input type="text" class="form-control" placeholder="输入客户证件活名称搜索">
			</div>

			<div class="scan_box">
				<span class="icon-scan glyphicon scanf"></span>
				<div>扫描身份证</div>
			</div>
			<div class="adduser_box">
				<a class="btn btn-primary btn-rounded" href="buttons.html#">新增客户</a>
			</div>
		</div>
		<!---客户定位en-->
		<div class="box-maincont">
			<div class="homenofood">
					<!--填单start-->
							<!-- <div class="container-fluid row">
								<div class="form_title">
									<div>
										<i class="bot"></i>查询条件
									</div>
								</div>
							</div> -->
							<button id="reportSearchDetailsBtn" type="button" class="close" data-dismiss="modal" aria-label="Close">
												<span aria-hidden="true">×</span></button>
							<div class="container-fluid row">
								<div class="wmin_content">
									<div class="col-lg-12 ">
										<div class="mart10" >
											<div >
												<table class="table table-bordered">
													<thead>
														<tr>
															<th>购物车流水</th>
															<th>受理时间</th>
														</tr>
													</thead>
													<tbody>
														#if($options && $options != "null" && $options.custOrderNbr 
														&& $options.custOrderNbr != "null")
														#foreach($custOrderNbrs in $options.custOrderNbr)
															<tr>
																<td>
																<a name="custOrderIdA" class="textcolorgreen" href="javascript:void(0);">$!{custOrderNbrs.custOrderNbr}
					                                           	 <p class="vita-data">{"custOrderId":"$!custOrderNbrs.custOrderId"}</p>
					                                            </a>
																	
																</td>
																<td>$!custOrderNbrs.acceptDate</td>
															</tr>
														#end
														#else
														<tr>
															<td align='center' colspan='7'>未查询到数据！
															<td>
														</tr>
														#end
													</tbody>
												</table>
											</div>
										</div>
									</div>
								</div>
							</div>
					<!--填单end-->
			</div>
		</div>
	</div>

</div>

