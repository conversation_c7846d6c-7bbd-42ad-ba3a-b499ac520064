<div data-widget="custDetailInfo" style="height: 100%">

    #macro( nullNotShow $val)
        #if($val && $val != "null")
            $!val
        #end
    #end

    <div class="calcw_rightbox noneleft">
        <div class="calctitle">
            <div class="titlefont">客户详情</div>
            <div class="toolr">
                <button id="closeBtn" type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
            </div>
        </div>
        <div class="calcw_rightcont" >
            #if($options.detailInfo && $options.detailInfo != "null")
            #set($detailInfo = $options.detailInfo)
            #set($baseInfo = $detailInfo.customerDetail)
            <div class="container-fluid">
                <h5 class="meal_htitle nobrder"><i class="bot"></i>基本信息</h5>
                <div class="row">
                    <div class="col-md-12 ">
                        <table class="table table-bordered conttd-w">
                            <tbody>
                            <tr>
                                <td class="labellcont">客户名称</td>
                                <td>#nullNotShow($baseInfo.custName)&nbsp;</td>
                                <td class="labellcont">客户类型</td>
                                <td>#nullNotShow($baseInfo.partyTypeName)&nbsp;</td>
                            </tr>
                            <tr>
                                <td class="labellcont">客户地区</td>
                                <td>#nullNotShow($baseInfo.regionName)&nbsp;</td>
                                <td class="labellcont">通信地址</td>
                                <td>#nullNotShow($baseInfo.email)&nbsp;</td>
                            </tr>
                            <tr>
                                <td class="labellcont">客户地址</td>
                                <td>#nullNotShow($baseInfo.custAddr)&nbsp;</td>
                                <td class="labellcont">联系电话</td>
                                <td>#nullNotShow($baseInfo.mobilePhone)&nbsp;</td>
                            </tr>
                            <tr>
                                #if($baseInfo.industryTypeId == "20010")
                                    <td class="labellcont">职 业</td>
                                    <td>#nullNotShow($baseInfo.occupationName)&nbsp;</td>
                                #else
                                    <td class="labellcont">行 业</td>
                                    <td>#nullNotShow($baseInfo.industryTypeName)&nbsp;</td>
                                #end
                                <td class="labellcont">客户品牌</td>
                                <td>#nullNotShow($baseInfo.custBrankName)
                                    #if($baseInfo.custSubBrankName && $baseInfo.custSubBrankName != "null" && $baseInfo.custSubBrankName != "")
                                        > $baseInfo.custSubBrankName
                                    #end
                                    &nbsp;
                                </td>
                            </tr>
                            <tr>
                                <td class="labellcont">战略分群</td>
                                <td>#nullNotShow($baseInfo.custTypeName)&nbsp;</td>
                                <td class="labellcont">客户状态</td>
                                <td>#nullNotShow($baseInfo.statusCdName)&nbsp;</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!--#if($detailInfo.party && $detailInfo.party != "null" && $detailInfo.party.identities && $detailInfo.party.identities.size() > 0)
            <div class="container-fluid">
                <h5 class="meal_htitle nobrder"><i class="bot"></i>客户标识信息</h5>
                <div class="row">
                    <div class="col-md-12">
                        <div class="show-grid">
                            <ul>
                                #foreach($ide in $detailInfo.party.identities)
                                <li class="col-xs-6 col-sm-2 labellcont">$!ide.identidiesTypeName</li>
                                <li class="col-xs-6 col-sm-4">$!ide.identityNum</li>
                                #end
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            #end-->

            <div class="container-fluid">
                <h5 class="meal_htitle nobrder"><i class="bot"></i>客户扩展信息</h5>
                <div class="row">
                    <div class="col-md-12">
                        <table class="table table-bordered conttd-w">
                            <tbody>
                            #if($baseInfo.custContactDetails && $baseInfo.custContactDetails != "null" && $baseInfo.custContactDetails.size() > 0)
                            <tr>
                                #foreach($contact in $baseInfo.custContactDetails)
                                #if($contact.contactsInfo && $contact.contactsInfo != "null")
                                    #if($contact.headFlag == 1)
                                    <td class="labellcont">联系人1</td>
                                    <td>#nullNotShow($contact.contactsInfo.contactName)</td>
                                    #else
                                    <td class="labellcont">联系人2</td>
                                    <td>#nullNotShow($contact.contactsInfo.contactName)</td>
                                    #end
                                #end
                                #end
                            </tr>
                            #end
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>


            <div class="container-fluid">
                <h5 class="meal_htitle nobrder"><i class="bot"></i>客户账户信息</h5>
                <div class="row">
                    <div class="col-md-12">
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <th>合同号</th>
                                <th>付费人</th>
                                <th>支付号码</th>
                                <th>支付信息</th>
                            </tr>
                            </thead>
                            <tbody>
                            #if($baseInfo.accounts && $baseInfo.accounts != "null" && $baseInfo.accounts.size() > 0)
                                #foreach($acct in $baseInfo.accounts)
                                    <tr>
                                        <td>$!acct.acctCd</td>
                                        <td>$!acct.paymentMan</td>
                                        <td>$!acct.accessNumber</td>
                                        <td>$!acct.paymentTypeName</td>
                                    </tr>
                                #end
                            #end
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            #if($baseInfo.custGroups && $baseInfo.custGroups != "null" && $baseInfo.custGroups.size() > 0)
            <div class="container-fluid">
                <h5 class="meal_htitle nobrder"><i class="bot"></i>客户群信息</h5>
                <div class="row">
                    <div class="col-md-12">
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <th>客户群编码</th>
                                <th>客户群名称</th>
                                <th>客户群描述</th>
                            </tr>
                            </thead>
                            <tbody>
                            #foreach($group in $baseInfo.custGroups)
                            <tr>
                                <td>#nullNotShow($group.custGroupCode)</td>
                                <td>#nullNotShow($group.custGroupName)</td>
                                <td>#nullNotShow($group.custGroupDesc)</td>
                            </tr>
                            #end
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            #end
            #end
        </div>
    </div>
</div>