(function (vita) {
    var algorithmUpdate = vita.Backbone.BizView.extend({
        events: {
            "click #submitBtn": "_handleSubmit",
            "click #cancelBtn": "_handleClosePage"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            var data = element.data("data");
            var algorithmInfo = data.algorithmInfo;
            widget.model.set("algoNbr", algorithmInfo.algoNbr);
            widget.model.set("statusCd", algorithmInfo.statusCd);
            widget.model.set("algoDesc", algorithmInfo.algoDesc);
        },
        _handleSubmit: function () {
            var widget = this, element = $(widget.el);
            var data = element.data("data");
            var algorithmInfo = data.algorithmInfo;
            var resultMsg = null, resultObject = null;
            var params = widget.model.toJSON();
            var algoId = algorithmInfo.algoId;
            params.algoId = algoId;
            if (!widget._validate()) {
                return false;
            }
            widget.callService("updateAlgorithm", params, function (res) {
                var ret = JSON.parse(res);
                if (ret.resultCode == 0) {
                    resultObject = ret.resultObject;
                    if (resultObject.code != 0) {
                        widget.popup(resultObject.msg || "修改失败");
                        return false;
                    }
                } else {
                    resultMsg = ret.resultMsg || "修改失败";
                }
                if (widget._checkValue(resultMsg)) {
                    widget.popup(resultMsg);
                    return false;
                }
                widget.popup("修改算法成功", function () {
                    widget._handleClosePage();
                });

            }, {
                async: true,
                mask: true
            });
        },
        _checkValue: function (condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty ? false : true;
        },
        _validate: function () {
            var widget = this, element = $(widget.el);
            var algoNbr = widget.model.get("algoNbr");
            if (!widget._checkValue(algoNbr)) {
                widget.popup("算法编码不能为空，请输入算法编码");
                return false;
            }
            return true;
        },
        _handleClosePage: function () {
            var widget = this, element = $(widget.el);
            element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
        }
    });
    vita.widget.register("algorithmUpdate", algorithmUpdate, true);
})(window.vita);