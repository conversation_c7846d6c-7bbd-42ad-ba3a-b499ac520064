package com.asiainfo.crm.assist.app.query;

import com.al.common.utils.DateUtil;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IEpaySmo;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Created on 2017/7/7.
 */
@Component("vita.epayAccountListQuery")
public class EpayAccountListQuery extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(EpayAccountListQuery.class);

    @Autowired
    private IEpaySmo epaySmo;

    @Override
    public Map achieveData(Object... params) throws Exception {

        return null;
    }

    /**
     * 查询账户内容
     */
    public Map queryEpayAccount(String jsonStr) throws Exception {
        Map<String, Object> param = jsonConverter.toBean(jsonStr, Map.class);
        String accNbr = MapUtils.getString(param, "accNbr", "");
        Map<String, Object> options = Maps.newHashMap();
        if (StringUtils.isEmpty(accNbr)) {
            return options;
        }

        Map<String, Object> reqCtrlMap = new HashMap<>(4);
        reqCtrlMap.put("@APPFROM", MDA.CTRL_INFO_MAP.get("@APPFROM"));
        reqCtrlMap.put("@KEEP", MDA.CTRL_INFO_ACCOUNT_KEEP);
        reqCtrlMap.put("@WEBSVRCODE", MDA.CTRL_INFO_ACCOUNT_WEBSVRCODE);
        reqCtrlMap.put("@WEBSVRNAME", "客户信息同步");

        String str = this.eopParam(reqCtrlMap, param, MDA.CTRL_INFO_ACCOUNT_METHOD);


        String retStr = epaySmo.qryEpayAccount(str);
        Map<String, Object> resMap = this.eopReponseParam(retStr);
        Map<String, Object> responseMap = (Map<String, Object>) resMap.get("RESPONSE");

        if (MDA.EPAY_SUCCESS.equals(responseMap.get("@RESULT"))) {
            options.put("account", resMap.get("DATAS"));
        }

        return options;
    }

    /**
     * 拼凑 EOP请求报文
     *
     * @param reqCtrlMap "SvcCont": 中的 CTRL-INFO
     * @param reqMap     具体的请求参数
     * @param method     调用EOP 的方法
     * @return
     * @throws Exception
     */
    public String eopParam(Map<String, Object> reqCtrlMap, Map<String, Object> reqMap, String method) throws Exception {
        Map<String, Object> payPlatRequestParameter = new HashMap<>(2);
        Map<String, Object> contractRoot = new HashMap<>(2);
        Map<String, Object> tcpCont = new HashMap<>(4);

        tcpCont.put("Method", method);
        tcpCont.put("ReqTime", DateUtil.getNowDefault());
        String transactionID = "**********" + DateUtil.getNow("yyyyMMddHHmmssSSSS");
        tcpCont.put("TransactionID", transactionID);
        tcpCont.put("Version", System.currentTimeMillis());

        payPlatRequestParameter.put("CTRL-INFO", reqCtrlMap);
        payPlatRequestParameter.put("PARAMETERS", reqMap);

        contractRoot.put("SvcCont", payPlatRequestParameter);
        contractRoot.put("TcpCont", tcpCont);

        String contractRootStr = jsonConverter.toJson(contractRoot);
        return contractRootStr;
    }


    /**
     * EOP返回报文解析
     *
     * @return
     * @throws Exception
     */
    public Map<String, Object> eopReponseParam(String str) throws Exception {
        Map<String, Object> returnMap = new HashMap<>(2);

        if (!StringUtils.isEmpty(str)) {
            //去掉特殊字符
            if (StringUtils.indexOf(str, "@") > 0) {
                str = str.replace("@", "");
            }
            Map<String, Object> resMap = jsonConverter.toBean(str, Map.class);
            //固定回参
            Map<String, Object> responseMap = (Map<String, Object>) resMap.get("PayPlatResponseParameter");
            //RESPONSE
            Map<String, Object> responseParameter = (Map<String, Object>) responseMap.get("RESPONSE-INFO");
            //DATAS
            Map<String, Object> result = (Map<String, Object>) responseMap.get("RESULTDATESET");
            Map<String, Object> resultDATAS = (Map<String, Object>) result.get("DATAS");

            returnMap.put("RESPONSE", responseParameter);
            returnMap.put("DATAS", resultDATAS);
        }

        return returnMap;
    }
}
