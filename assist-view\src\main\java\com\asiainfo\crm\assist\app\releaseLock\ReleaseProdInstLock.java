package com.asiainfo.crm.assist.app.releaseLock;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IOrderSMO;
import com.asiainfo.crm.service.intf.IStaffDataPrvQuerySMO;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Component("vita.releaseProdInstLock")
public class ReleaseProdInstLock extends AbstractComponent{
    @Autowired
    private IStaffDataPrvQuerySMO staffDataPrvQuerySMO;
    @Autowired
    private IOrderSMO iOrderSMO;
    @Override
    public Map achieveData(Object... objects) throws Exception {
        return null;
    }

    public String releaseLock(String param) throws IOException {
       String resultStr= iOrderSMO.releaseProdInstLock(param);
       Map result=(Map)resolveResult(resultStr);
       return result.get("resultMsg").toString();

    }


}
