<div data-widget="qryCardResourceLogInfo" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_accNum" type="checkbox" name="payment">
                                                </label> 服务号码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="accNum" type="text" class="form-control" placeholder="请输入用户查询的号码">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_checker" type="checkbox" name="payment">
                                                </label> 批准人
                                            </label>
                                            <div class="col-md-7">
                                                <input id="checker" type="text" class="form-control" placeholder="请输入批准人">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_orderNum" type="checkbox" name="payment">
                                                </label> 派单号1
                                            </label>
                                            <div class="col-md-7">
                                                <input id="orderNum" type="text" class="form-control" placeholder="请输入派单号1">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-12" id="qryTimeQuantum">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_date" type="checkbox" name="payment">
                                                </label> 起止时间
                                            </label>
                                            <div class="col-md-10 form-inline">

                                                <div class="form-group">
                                                    <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="开始时间">
                                                </div>
                                                <div class="form-group">
                                                    <input name="endDate" id="endDate" type="text" class="form-control" placeholder="结束时间">
                                                </div>
                                                <button type="button" id="timeQuantum_1" name="timeQuantum_1" class="btn btn-gray btn-outline btn-sm">当天</button>
                                                <button type="button" id="timeQuantum_7" name="timeQuantum_7" class="btn btn-gray btn-outline btn-sm">七天</button>
                                                <button type="button" id="timeQuantum_30" name="timeQuantum_30" class="btn btn-gray btn-outline btn-sm">一个月</button>
                                                <button type="button" id="timeQuantum_90" name="timeQuantum_90" class="btn btn-gray btn-outline btn-sm">三个月</button>

                                            </div>
                                        </div>
                                        <div class="form-group col-md-11">
                                            <div class="col-md-12 searchbutt_r" align="right">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-clear" type="button" class="btn btn-white">清除</button>
                                            </div>
                                        </div>

                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询结果</div>
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12 mart10" id="cardResourceLogInfo">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>服务号码</th>
                                                <th>操作员工</th>
                                                <th>受理地区</th>
                                                <th>派单号1</th>
                                                <th>派单号2</th>
                                                <th>批准人</th>
                                                <th>查询时间</th>
                                                <th>备注</th>
                                            </tr>
                                            </thead>
                                            <tbody id="cardResourceLogList">
                                            #if($options != "null" && $options.cardResourceQryLogList && $options.cardResourceQryLogList != "null" &&
                                            $options.cardResourceQryLogList.size() > 0)
                                            #foreach($cardResourceQryLog in $options.cardResourceQryLogList)
                                            <tr>
                                                <td>$!{cardResourceQryLog.accNum}</td>
                                                <td>$!{cardResourceQryLog.staffName}</td>
                                                <td>$!{cardResourceQryLog.regionName}</td>
                                                <td>$!{cardResourceQryLog.sendNumber}</td>
                                                <td>$!{cardResourceQryLog.sendNumber2}</td>
                                                <td>$!{cardResourceQryLog.approveStaff}</td>
                                                <td>$!{cardResourceQryLog.createDate}</td>
                                                <td>$!{cardResourceQryLog.remark}</td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.cardResourceQryLogList && $options.cardResourceQryLogList != "null" &&
                                            $options.cardResourceQryLogList.size() == 0)
                                                <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
