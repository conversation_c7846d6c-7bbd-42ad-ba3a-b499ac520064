package com.asiainfo.crm.assist.app.report;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IReportSMO;
import org.apache.commons.collections.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017-9-7.
 */
@Component("vita.reportSummarySalesClerk")
public class ReportSummarySalesClerk extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(ReportSummarySalesClerk.class);

    @Autowired
    private IReportSMO reportFormSMO;

    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map reportSummarySalesClerkQuery(String json) throws IOException {
        Map options = new HashedMap();
        String str = reportFormSMO.reportSummarySalesClerkQuery(json);
        Map<String,Object> map = jsonConverter.toBean(str,Map.class);
        Map<String,Object> mapObj = (Map<String, Object>) map.get("resultObject");
        List list = (List) mapObj.get("reportSummarySalesClerkList");
        double countRealAmount=0D,countPaidInAmount=0D,countSun=0D;
        int countNum =  list.size();//总条数
        for (int i=0; i<list.size(); i++){
            Map msg = (Map) list.get(i);
            double  realAmount = (double ) msg.get("realAmount");
            double  paidInAmount = (double) msg.get("paidInAmount");
            double  deductionAmount = (double) msg.get("deductionAmount");
            countSun+=deductionAmount;
            countRealAmount+=realAmount;
            countPaidInAmount+=paidInAmount;
        }

        options.put("countRealAmount",countRealAmount);
        options.put("countPaidInAmount",countPaidInAmount);
        options.put("countSun",countSun);
        options.put("reportSummarySalesClerkList",list);
        options.put("exportNum", MDA.EXPORT_NUM);
        options.put("totalNumber", countNum);
        return options;
    }
    public Map channelQuery(String json) throws IOException {
        Map options = new HashedMap();
        String str = reportFormSMO.channelQuery(json);
        Map<String,Object> map = jsonConverter.toBean(str,Map.class);
        Map mapResulObj = (Map) map.get("resultObject");
        List list = (List) mapResulObj.get("channelList");
        options.put("channelList",list);
        return options;
    }
}
