package com.asiainfo.crm.assist.app.action;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.bcomm.utils.LogHelper;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.ReadCommonExcel;
import com.asiainfo.crm.service.intf.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by jdj on 2019/4/22
 */
@Component("vita.releaseRsBatch")
public class releaseRsBatch extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(releaseRsBatch.class);

    @Autowired
    private IProdInstSMO prodInstSMO;

    @Autowired
    protected IOrderQuerySMO orderQuerySMO;

    @Autowired
    private ISoQuerySMO iSoQuerySMO;

    @Autowired
    private IResourceSMO resourceSMO;

    @Autowired
    private ISoReleaseResourceSMO soReleaseResourceSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    /**
     * 卡资源批量写入数据
     * @param jsonStr
     * @param item
     * @return
     * @throws Exception
     */
    public Map releaseRsBatch(String jsonStr,DiskFileItem item) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        Map<String, Object> paramMap = jsonConverter.toBean(jsonStr, Map.class);
        ArrayList<Map<String, Object>> allList = new ArrayList();
        InputStream inputStream = item.getInputStream();
        String name = item.getName();
        ReadCommonExcel rce = new ReadCommonExcel(inputStream,name,1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyMMddHHmmssSSS");
        String batchNo = (sdf.format(new Date().getTime())+ String.valueOf(new Random().nextInt(10)));
        // 解析后字段名
        List<String> reqList = new ArrayList();
        reqList.add(AssistMDA.ACCNUM);
        reqList.add("uim");
        List excelList = rce.readExcelData();
        int exSize = excelList.size();
        int reqSize = reqList.size();
        if(exSize>0) {
            for (int i = 0; i < exSize; i++) {
                Object[] obj = (Object[]) excelList.get(i);
                Map<String, Object> map = new HashMap<String, Object>();
                for (int j = 0; j < reqSize; j++) {
                    if (obj.length> j && obj[j] !=null) map.put(reqList.get(j), obj[j]);
                }
                allList.add(map);
            }
        }
       int size = allList.size();
        if(size>100) {
            retMap.put("retCode", "-1");
            retMap.put("retDesc", "卡资源批量释放号码个数超过最大限制100！");
            return retMap;
        }
        String regionId = String.valueOf(paramMap.get("regionId"));//地市编码
        String channelId = String.valueOf(paramMap.get("channelId"));//渠道编码
        String staffId = String.valueOf(paramMap.get("staffId"));//操作工号
        String lanId = "";
        //根据输入的地区编码查询LAN_ID
        Map<String, Object> regionIdMap = new HashMap();
        regionIdMap.put("regionId", regionId);
        String regionIdStr = iSoQuerySMO.qryCommonRegionByIds(regionIdMap);
        String regionIdStrs =  (String) resolveResult(regionIdStr);
        Map<String, Object> regionIdMaps = jsonConverter.toBean(regionIdStrs, Map.class);
        if (null !=  regionIdMaps.get("lanId")) {
            lanId = String.valueOf(regionIdMaps.get("lanId"));
        }else{
            retMap.put("retCode", "-1");
            retMap.put("retDesc", "根据地址编码:"+regionId+"查询本地网编码异常！");
            return retMap;
        }
        ArrayList numSuccList = new ArrayList();//接入号码成功数组
        ArrayList numFailList = new ArrayList();//接入号码失败数组
        ArrayList uimSuccList = new ArrayList();//uim号码成功数组
        ArrayList uimFailList = new ArrayList();//uim号码失败数组
        //接入号码，目前支持固话和手机号码
        for(int i=0;i<size;i++) {
            String accNum = (String) allList.get(i).get("accNum");
            String uim = (String) allList.get(i).get("uim");
            Map<String, Object> jsonMap = new HashMap();
            jsonMap.put("accNbr", accNum);
            jsonMap.put("rsType", 1);
            jsonMap.put("statusCd", 0);
            jsonMap.put("createStaff", staffId);
            jsonMap.put("batchId", batchNo);
            jsonMap.put("regionId", regionId);
            jsonMap.put("channelId", channelId);
            String resourceNbr = soReleaseResourceSMO.addResourceNbr(jsonConverter.toJson(jsonMap));
            Map<String, Object>  resultMap =  (Map<String, Object>)  resolveResult(resourceNbr);
            Map<String, Object> jsonMap1 = new HashMap();
            jsonMap1.put("mktResNbr", uim);
            jsonMap1.put("rsType", 2);
            jsonMap1.put("statusCd", 0);
            jsonMap1.put("createStaff", staffId);
            jsonMap1.put("batchId", batchNo);
            jsonMap1.put("regionId", regionId);
            jsonMap1.put("channelId", channelId);
            String resourceNbr1 = soReleaseResourceSMO.addResourceNbr(jsonConverter.toJson(jsonMap1));
            Map<String, Object>  resultMap1 =  (Map<String, Object>)  resolveResult(resourceNbr1);
            if (resultMap == null || resultMap.isEmpty()){
                numSuccList.add(accNum);
            }  else{
                numFailList.add(accNum);
            }
            if (resultMap1 == null || resultMap1.isEmpty()){
                uimSuccList.add(uim);
            }  else{
                uimFailList.add(uim);
            }
        }
        if (numFailList.size() == 0 && uimFailList.size() == 0) {
            retMap.put("retCode", "0");
            retMap.put("retDesc", "操作成功！");
            retMap.put("retNo","本次导入的批号为"+batchNo);
            return retMap;
        } else {
            StringBuffer numStr = new StringBuffer();
            StringBuffer uimStr = new StringBuffer();
            for (Object str : numFailList) {
                numStr.append(str+",");
            }
            for (Object str : uimFailList) {
                uimStr.append(str+",");
            }
            if(numStr!=null) numStr.substring(0,numStr.toString().length()-1);
            if(uimStr!=null) uimStr.substring(0,uimStr.toString().length()-1);
            retMap.put("retCode", "-1");
            retMap.put("retDesc", "批量释放资源失败，接入号码失败"+numFailList.size()+"个,终端卡号失败"+uimFailList.size()+"个");
            retMap.put("retInfo","接入号码失败的:"+numStr.toString()+"，终端卡号失败的:"+uimStr.toString());
            retMap.put("retNo","本次导入的批号为"+batchNo);
            return retMap;
        }
    }

    /**
     * 释放资源查询
     * @param jsonStr
     * @return
     * @throws Exception
     */
    public Map releaseRs(String jsonStr) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        retMap.put("retCode", "-1");
        retMap.put("retDesc", "未查询到相关资源号码信息！");
        Map<String, Object> paramMap = jsonConverter.toBean(jsonStr, Map.class);
        String accNum = String.valueOf(paramMap.get("accNum"));//需要释放的资源号码
        String mktNum = String.valueOf(paramMap.get("mktNum"));//需要释放的资源号码
        String batchNum = String.valueOf(paramMap.get("batchNum"));//需要释放的资源号码
        String regionId = String.valueOf(paramMap.get("regionId"));//地市编码
        String channelId = String.valueOf(paramMap.get("channelId"));//渠道编码
        Map<String, Object> jsonMap1 = new HashMap();
        jsonMap1.put("regionId", regionId);
        jsonMap1.put("channelId", channelId);
        if (accNum!=null && !"".equals(accNum))jsonMap1.put("accNbr", accNum);
        if (mktNum!=null && !"".equals(mktNum)) jsonMap1.put("mktResNbr", mktNum);
        if (batchNum!=null && !"".equals(batchNum))jsonMap1.put("batchId", batchNum);
        jsonMap1.put("stateCd", 0);
        String resourceNbr1 = soReleaseResourceSMO.getResourceNbrList(jsonConverter.toJson(jsonMap1));
        List<Map<String, Object>> retList = (List<Map<String, Object>>) resolveResult(resourceNbr1);
        if (!retList.isEmpty() && retList.size()>0) {
            retMap.put("retCode", "0");
            retMap.put("retDesc", "操作成功！");
            retMap.put("retObj", retList);
        }
        return retMap;
    }
}
