(function (vita) {
    var orderAuditHis = vita.Backbone.BizView.extend({
        events: {
            "click #closeBtn": "_closeBtnClick"
        },
        _closeBtnClick: function () {
            var widget = this, element = $(widget.el);
            element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
        }
    });
    vita.widget.register("orderAuditHis", orderAuditHis, true);
})(window.vita);