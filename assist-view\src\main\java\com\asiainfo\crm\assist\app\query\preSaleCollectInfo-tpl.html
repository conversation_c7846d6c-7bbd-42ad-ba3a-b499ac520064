<div data-widget="preSaleCollectInfo">
    <vita-data>{"data":$options}</vita-data>
    <p class="vita-data">$options</p>
    <div class="sypage-nav">
        <div class="nav_alink"><strong class="text16">前置内容采集信息</strong></div>
    </div>
    <div class="sypage-min">
        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">查询条件</strong></div>
        </div>
        <div class="sypagemin_content ">
            <form>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">证件名称</label>
                    <div class="col-md-6">
                        <input type="text" class="form-control" placeholder="" id="certName">
                        <p class="vita-bind" model="certName"></p>
                    </div>
                </div>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">证件号码</label>
                    <div class="col-md-6">
                        <input type="text" class="form-control" placeholder="" id="certNum">
                        <p class="vita-bind" model="certNum"></p>
                    </div>
                </div>

                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">业务号码</label>
                    <div class="col-md-6">
                        <input type="text" class="form-control" placeholder="" id="accNum">
                        <p class="vita-bind" model="accNum"></p>
                    </div>
                </div>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">对象类型</label>
                    <div class="col-md-6">
                        <input type="text" class="form-control" placeholder="" id="objType">
                        <p class="vita-bind" model="objType"></p>
                    </div>
                </div>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">前置预约流水号</label>
                    <div class="col-md-6">
                        <input type="text" class="form-control" placeholder="" id="preSaleNbr">
                        <p class="vita-bind" model="preSaleNbr"></p>
                    </div>
                </div>
                <div class="form-group col-md-6">
                    <label class="col-md-4 control-label lablep">状态</label>
                    <div class="col-md-6">
                        <select id="statusCd" class="form-control">
                            <option value="">请选择状态</option>
                            <option value="1000">有效</option>
                            <option value="1100">失效</option>
                        </select>
                    </div>
                </div>

                <div class="form-group col-md-12">
                    <div class="col-md-12 searchbutt_r" align="right">
                        <button type="button" class="btn btn-default" id="searchDel">清除</button>
                        <button type="button" class="btn btn-primary" id="qryCommonLogSearchBtn">搜索</button>
                    </div>
                </div>

            </form>
        </div>

        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">前置内容采集信息列表</strong></div>
        </div>
        <div class="sypagemin_content ">
            <div class="col-lg-12">
                <a class="btn btn-gray btn-outline" id="deleteBtn">
                    <i class="glyphicon fa-revise text18" > </i> 作废
                </a>
            </div>
            <div class="col-lg-12 mart10">
                <table class="table table-hover" id="systemRoleList">
                    <thead>
                    <tr>
                        <th>选择</th>
                        <th>证件名称</th>
                        <th>证件号码</th>
                        <th>业务号码</th>
                        <th>对象类型</th>
                        <th>对象内容</th>
                        <th>前置预约流水号</th>
                        <th>状态</th>
                        <th>创建时间</th>
                    </tr>
                    </thead>
                    <tbody>
                    #if($options != "null" && $options.preSaleInfos && $options.preSaleInfos != "null" && $options.preSaleInfos.size() > 0)
                    #foreach($preSaleCollectInfo in $options.preSaleInfos)
                    <tr >
                        <td>
                            <label class="wu-radio full absolute" data-scope="">
                                <input type="radio" name="moduleRadio" />
                                <p class="vita-data">{"data":$preSaleCollectInfo,"totalNumber":$options.totalNumber}</p>
                            </label></td>
                        <td>$!preSaleCollectInfo.certName</td>
                        <td>$!preSaleCollectInfo.certNum</td>
                        <td>$!preSaleCollectInfo.accNum</td>
                        <td>$!preSaleCollectInfo.objType</td>
                        <td>$!preSaleCollectInfo.objValue</td>
                        <td>$!preSaleCollectInfo.preSaleNbr</td>
                        <td>#if($!preSaleCollectInfo.statusCd == 1000)有效
                            #elseif($!preSaleCollectInfo.statusCd == 1100)无效
                            #end
                        </td>
                        <td>$!preSaleCollectInfo.createDate</td>
                    </tr>
                    #end
                    #end
                    </tbody>
                </table>
                <!--翻页start -->
                <div class="page-box" id="showPageInfo">
                </div>
                <!--翻页end -->
            </div>
        </div>
    </div>
</div>