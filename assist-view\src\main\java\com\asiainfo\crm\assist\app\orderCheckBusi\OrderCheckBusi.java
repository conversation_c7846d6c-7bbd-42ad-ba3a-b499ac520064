package com.asiainfo.crm.assist.app.orderCheckBusi;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IOrderCheckBusiSMO;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("vita.orderCheckBusi")
public class OrderCheckBusi extends AbstractComponent {
    @Autowired
    private IOrderCheckBusiSMO orderCheckBusiSMO;


    @Override
    public Map achieveData(Object... objects) throws Exception {
        Map<String, Object> data = new HashMap<String, Object>();
        return data;
    }

    public Map queryOrderCheckBusi(String jsonString) throws Exception {
        Map option = new HashMap();
        Map<String,Object> returnMap = new HashMap<String,Object>();
        String resultStr  = orderCheckBusiSMO.qryOrderCheckBusi(jsonString);
        Map resultMap = jsonConverter.toBean(resultStr, Map.class);
        String resultCode = MapUtils.getString(resultMap, "resultCode");
        if (!StringUtil.isEmpty(resultCode) && resultCode.equals(MDA.RESULT_SUCCESS.toString())) {
            Map map = (Map) resultMap.get("resultObject");
            Map maps = (Map) map.get("pageInfo");
            option.put("totalNumber",  maps.get("rowCount"));
            option.put("orderCheckBusis", map.get("orderCheckBusis"));
        }
        return option;
    }

    public Map queryOrderCheckBusis(String jsonString) throws Exception {
        Map option = new HashMap();
        Map<String,Object> returnMap = new HashMap<String,Object>();
        String resultStr = orderCheckBusiSMO.queryOrderCheckBusis(jsonString);
        Map resultMap = jsonConverter.toBean(resultStr, Map.class);
        String resultCode = MapUtils.getString(resultMap, "resultCode");
        if (!StringUtil.isEmpty(resultCode) && resultCode.equals(MDA.RESULT_SUCCESS.toString())) {
            List<Map<String, Object>> list = (List<Map<String, Object>>) resultMap.get("resultObject");
            option.put("totalNumber", list.size());
            option.put("orderCheckBusis", list);
        }
        return option;
    }

    public String delOrderCheckBusi(String jsonString)throws Exception{
        return orderCheckBusiSMO.deleteOrderCheckBusi(jsonString);
    }

}
