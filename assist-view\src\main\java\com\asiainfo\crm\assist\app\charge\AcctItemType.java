package com.asiainfo.crm.assist.app.charge;


import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ISpecChargeSmo;
import com.asiainfo.crm.util.ListUtil;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("vita.acctItemType")
public class AcctItemType extends AbstractComponent {

    @Autowired
    private ISpecChargeSmo chargeSmo;

    @Override
    public Map achieveData(Object... objects) throws Exception {
        return null;
    }

    public Map qryAcctItemTypePageInfo(String params) throws Exception {
        Map<String, Object> options = new HashMap();
        String queryInfo = chargeSmo.queryAcctItemTypePage(params);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(queryInfo);
        Map<String,Object> pageInfo = (Map<String, Object>) MapUtils.getObject(resultObjectMap, "pageInfo");
        List<Map> listData = (List) MapUtils.getObject(resultObjectMap, "listData");
        for (Map map : ListUtil.nvlList(listData)) {
            map.put("isInvoiceItemName", "1".equals(MapUtils.getString(map,"isInvoiceItem"))?"以打":"未打");
            map.put("isLimitRedoName", "1".equals(MapUtils.getString(map,"isLimitRedo"))?"限制":"不限制");
            map.put("isLimitChangeName", "1".equals(MapUtils.getString(map,"isLimitChange"))?"限制":"不限制");
        }
        resultObjectMap.clear();
        resultObjectMap.put("reqList", listData);
        resultObjectMap.put("totalNumber", pageInfo.get("rowCount"));
        return resultObjectMap;
    }

    public Map createAcctItemType(String params) throws Exception {
        String insertResult = chargeSmo.createAcctItemType(params);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(insertResult);
        resultObjectMap.put("resultCode",MapUtils.getString(resultObjectMap, "resultCode"));
        resultObjectMap.put("resultMsg",MapUtils.getString(resultObjectMap, "resultMsg"));
        return resultObjectMap;
    }
    public Map updateAcctItemType(String params) throws Exception {
        String updateResult = chargeSmo.updateAcctItemType(params);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(updateResult);
        resultObjectMap.put("resultCode",MapUtils.getString(resultObjectMap, "resultCode"));
        resultObjectMap.put("resultMsg",MapUtils.getString(resultObjectMap, "resultMsg"));
        return resultObjectMap;
    }



}
