package com.asiainfo.crm.assist.app.action;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.common.AbstractComponent;

/**
 * Created by pushuo on 2018/1/31.
 */
@Component("vita.uploadExcle")
public class UploadExcle extends AbstractComponent {

	private static final Logger logger = LoggerFactory.getLogger(UploadExcle.class);

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

}
