package com.asiainfo.crm.assist.app.query;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.MDA;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.common.Constant;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import com.asiainfo.crm.service.intf.IStaffInfoQuerySMO;
import com.asiainfo.crm.service.intf.IStaffOrgAreaSMO;
import com.asiainfo.crm.util.ListUtil;

/**
 * 公共日志查询.
 */
@Component("vita.commOperInfo")
public class CommOperInfo extends AbstractSoComponent {

	@Autowired
	private IOrderQuerySMO orderQuerySMO;

	@Autowired
	private IStaffOrgAreaSMO staffOrgAreaSMO;

	@Autowired
	private ISoQuerySMO soQuerySMO;

	@Autowired
	private IStaffInfoQuerySMO staffInfoQuerySMO;

	@Autowired
	private ICustMultiSMO custMultiSMO;

	@Override
	public Map achieveData(Object... params) throws Exception {
		return null;
	}

	public Map queryCommOperInfoLogPage(String params) throws Exception {
		Map options = new HashMap();
		long rowCount = 0L;
		String re = orderQuerySMO.queryCommOperInfoLogPage(params);
		Map resultObject = (Map) resolveResult(re);
		List<Map> commOperInfos = (List) resultObject.get("commOperInfoLogs");
		if (commOperInfos != null && !commOperInfos.isEmpty()) {
			for (Map commOperInfo : commOperInfos) {
				Map comm = new HashMap();
				String channelName = MapUtils.getString(commOperInfo,"channelName");
				if(StringUtils.isEmpty(channelName)){
					String channelId = MapUtils.getString(commOperInfo, "channelId");
					if (!StringUtil.isEmpty(channelId)) {
						commOperInfo.put("channelName", qryChannelName(channelId));
					}
				}
				String regionName = MapUtils.getString(commOperInfo,"regionName");
				if(StringUtils.isEmpty(regionName)){
					String regionId = MapUtils.getString(commOperInfo, "regionId");
					if (!StringUtil.isEmpty(regionId)) {
						commOperInfo.put("regionName", qryRegionName(regionId));
					}
				}
				String staffName = MapUtils.getString(commOperInfo,"staffName");
				String staffId = MapUtils.getString(commOperInfo, "staffId");
				if(StringUtils.isEmpty(staffName)){
					if (!StringUtil.isEmpty(staffId)) {
						staffName = qryStaffName(staffId);
						commOperInfo.put("staffName", staffName);
					}
				}
				String agentStaffId = MapUtils.getString(commOperInfo, "agentStaffId");
				if (!StringUtil.isEmpty(agentStaffId)) {
					commOperInfo.put("agentStaffName", agentStaffId.equals(staffId) ? staffName : qryStaffName(agentStaffId));
				}
				String custName = MapUtils.getString(commOperInfo,"custName");
				String custId = MapUtils.getString(commOperInfo, "custId");
				if(StringUtils.isEmpty(custName)){
					if (!StringUtil.isEmpty(custId)) {
						custName = qryCustName(custId);
						commOperInfo.put("custName", custName);
					}
				}
				String isDesens = MapUtils.getString(commOperInfo, "isDesens");
				commOperInfo.put("isDesensName", Constant.LOGIC_STR_0.equals(isDesens) ? "脱敏" : "未脱敏");
				if(Constant.LOGIC_STR_0.equals(isDesens)){
					custName = doCustNameDesensitize(custName);
					commOperInfo.put("custName", custName);
				}
				String resultType = MapUtils.getString(commOperInfo, "resultType");
				commOperInfo.put("resultTypeName", Constant.LOGIC_STR_0.equals(resultType) ? "成功" : "失败");
				String certType = MapUtils.getString(commOperInfo, "certType");
				if(StringUtils.isNotEmpty(certType)){
					commOperInfo.put("certTypeName", MapUtils.getString(MDA.OPEN_CARD_NUMBER_QRY_IDENTITYS,certType,""));
				}
				if("Y".equals(AssistMDA.COMM_OPER_INFO_CUST_INFO_DESENSITIZE_SWITCH)){
					doDesensitizeCustInfo(commOperInfo);
				}
			}
			Map pageInfo = (Map) resultObject.get("pageInfo");
			String count = MapUtils.getString(pageInfo, "rowCount");
			options.put("totalNumber", !StringUtil.isEmpty(count) ? Long.valueOf(count) : rowCount);
			options.put("commOperInfos", commOperInfos);
			return options;
		}
		options.put("totalNumber", rowCount);
		options.put("commOperInfos", commOperInfos);
		return options;
	}

	/**
	 * 查询渠道名称
	 *
	 * @param createOrgId
	 * @return
	 * @throws Exception
	 */
	private String qryChannelName(String createOrgId) throws Exception {
		String channelName = "";
		List<String> orgIds = new ArrayList<>(1);
		orgIds.add(createOrgId);
		Map<String, Object> channelParam = new HashMap<>(1);
		channelParam.put("orgIds", orgIds);
		String result = staffOrgAreaSMO.queryChannelListByIds(jsonConverter.toJson(channelParam));
		List resultList = (List) resolveResult(result);
		if (!ListUtil.isListEmpty(resultList)) {
			Map<String, Object> orgRetMap = (Map) resultList.get(0);
			channelName = MapUtils.getString(orgRetMap, "channelName", "");
		}
		return channelName;
	}

	/**
	 * 查询地区名称
	 *
	 * @param regionId
	 * @return
	 * @throws Exception
	 */
	private String qryRegionName(String regionId) throws Exception {
		Map<String, Object> regionIdMap = new HashMap(1);
		regionIdMap.put("regionId", regionId);
		String ret = soQuerySMO.qryCommonRegionByIds(regionIdMap);
		String regionStr = (String) resolveResult(ret);
		Map<String, Object> regionMap = jsonConverter.toBean(regionStr, Map.class);
		String regionName = MapUtils.getString(regionMap, "regionName", "");
		return regionName;
	}

	/**
	 * 查询员工名称
	 *
	 * @param createStaff
	 * @return
	 * @throws Exception
	 */
	private String qryStaffName(String createStaff) throws Exception {
		Map param = new HashMap(1);
		param.put("staffId", createStaff);
		String staffInfoRet = staffInfoQuerySMO.qryStaffInfo(jsonConverter.toJson(param));
		Map staffInfoMap = (Map) resolveResult(staffInfoRet);
		Map staffMap = MapUtils.getMap(staffInfoMap, "staffInfo");
		String staffName = MapUtils.getString(staffMap, "staffName", "");
		return staffName;
	}

	/**
	 * 查询客户名称
	 *
	 * @param custId
	 * @return
	 * @throws Exception
	 */
	private String qryCustName(String custId) throws Exception {
		Map<String, Object> custParams = new HashMap<>(1);
		custParams.put("custId", custId);
		String custStr = custMultiSMO.qryCustomerDetailForMulti(jsonConverter.toJson(custParams));
		Map cust = (Map) resolveResult(custStr);
		Map customer = (Map) cust.get("customerDetail");
		String custName = MapUtils.getString(customer, "custName", "");
//		if (!StringUtil.isEmptyStr(custName) && custName.length() > 1) {
//			custName = custName.substring(0, 1) + "***";
//		}
		return custName;
	}

	/**
	 * 用户名脱敏
	 * @param custName
	 * @return
	 */
	private String doCustNameDesensitize(String custName) {
		if (StringUtils.isNotEmpty(custName) && custName.length() > 1) {
			custName = custName.substring(0, 1) + "***";
		}
		return custName;
	}

	/**
	 * 公共日志信息中的客户信息进行脱敏处理
	 *
	 * @param commOperInfo
	 */
	private void doDesensitizeCustInfo(Map<String, Object> commOperInfo) {
		String custName = MapUtils.getString(commOperInfo, "custName");
		custName = doCustNameDesensitize(custName);
		commOperInfo.put("custName", custName);
		String certNum = MapUtils.getString(commOperInfo, "certNum");
		if (StringUtils.isNotEmpty(certNum)) {
			certNum = encrypt(certNum, "cert");
		}
		commOperInfo.put("certNum", certNum);
		String dealParam = MapUtils.getString(commOperInfo, "dealParam");
		if (StringUtils.isNotEmpty(dealParam) && dealParam.contains("IdNum")) {
			try {
				Map<String, Object> dealParamToMap = jsonConverter.toBean(dealParam, Map.class);
				String IdNum = MapUtils.getString(dealParamToMap, "IdNum");
				IdNum = StringUtils.isNotEmpty(IdNum) ? encrypt(IdNum, "cert") : "";
				dealParamToMap.put("IdNum", IdNum);
				dealParam = jsonConverter.toJson(dealParamToMap);
			} catch (IOException e) {
				//not need handle
			}
		}
		commOperInfo.put("dealParam", dealParam);
	}

}