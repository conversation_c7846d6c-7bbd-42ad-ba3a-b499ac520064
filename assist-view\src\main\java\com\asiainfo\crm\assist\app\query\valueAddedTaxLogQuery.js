(function (vita) {
    var valueAddedTaxLogQuery = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_valueAddedTaxLogQuery",
            "click #btn-clear": "_clearCond",
            "click input[type=checkbox]": "_nbrClick",
            "click [link=customerOrder]": "_queryCustomerOrderDetailLink",
            "click [link=valueAddedTaxLogDetail]": "_queryValueAddedTaxLogDetailLink",
            "click [link=valueAddedTaxDetail]": "_queryValueAddedTaxDetailLink",
            "click #qryTimeQuantum button": "_qryTimeQuantum"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var nowDate = new Date();
            var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
            var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
            var startYear = nowDate.getFullYear() - 5, endYear = nowDate.getFullYear();
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear: endYear
                });
            }
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear: endYear
                });
            }
            //日期控件设置默认时间
            widget.global.defaultBeginTime = widget._getFormatDate(widget.global.sevenDayCount); //默认是七天
            widget.global.defaultEndTime = widget._currentDate();
            widget._setDefaultDateTime();
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
            var channelName = "";
            for (var channels = gSession.channels, i = 0; channels.length && i < channels.length; i++) {
                if (gSession.curChannelId == channels[i].orgId) {
                    channelName = channels[i].channelName;
                    break;
                }
            }
            element.find("#staffId").val(gSession.staffName).attr("value", gSession.staffId);
            //判断是否为首页近三个月退单页面请求
            var options = element.data('data');
            //nbrLabelsStr初始化
            var nbrLabels = widget.global.nbrLabels;
            var nbrLabelsStr = "#" + nbrLabels[0];
            for (var k = 1; k < nbrLabels.length; k++) {
                nbrLabelsStr = nbrLabelsStr + ",#" + nbrLabels[k];
            }
            widget.global.nbrLabelsStr = nbrLabelsStr;
        },
        _getFormatDate: function (days) {
            var date = new Date();
            var yesterday_milliseconds = date.getTime() + days * 1000 * 60 * 60 * 24;
            var yesterday = new Date();
            yesterday.setTime(yesterday_milliseconds);
            var strYear = yesterday.getFullYear();
            var strDay = yesterday.getDate();
            var strMonth = yesterday.getMonth() + 1;
            if (strMonth < 10) {
                strMonth = "0" + strMonth;
            }
            if (strDay < 10) {
                strDay = "0" + strDay;
            }
            var datastr = strYear + "-" + strMonth + "-" + strDay;
            return datastr;
        },
        global: {
            pageIndex: 1,
            pageSize: 5,
            preset: "date",
            chooseStaff: "../comm/chooseStaff",
            defaultBeginTime: "",
            defaultEndTime: "",
            currentDayCount: 0,
            sevenDayCount: -6,
            lastMonthDayCount: -29,
            ninthDayCount: -89,
            nbrLabels: ["c_custOrderNbr", "c_custOrderId"],
            compUrl: {
                valueAddedTaxLogDetail: "../query/valueAddedTaxLogDetail",// 专票交互日志详情
                valueAddedTaxDetail: "../query/valueAddedTaxDetail"// 专票信息详情
            }
        },
        _clearCond: function () {
            var widget = this, element = $(widget.el);
            var $checked = element.find(".form-group").find("input[type=checkbox]:checked");
            $.each($checked, function (i, chkInput) {
                chkInput.click();
                var $input = $(chkInput).closest(".form-group").find("input.form-control");
                $input.val("");
                $input.attr("value", null);
            })
            //时间设置为7天
            widget._changeTimeQuanTum(widget.global.sevenDayCount);
        },
        _nbrClick: function(e) {
            var widget = this, element = $(widget.el),
                nbrLabels = widget.global.nbrLabels,
                nbrLabelsStr = widget.global.nbrLabelsStr;
            var currentTarget = $(e.currentTarget);
            //界面样式渲染后才会調click事件，所以当状态是checked的时候才去清空其他选项
            var nbrChk = element.find(nbrLabelsStr).filter(":checked");
            var beginDate = element.find("#beginDate"),endDate =  element.find("#endDate");
            var dateTimeFlag = "on";
            if(!beginDate.prop("disabled")){
                widget.global.defaultBeginTime = beginDate.val();
                widget.global.defaultEndTime = endDate.val();
            }
            if(nbrChk.length != 0){
                var $checked = $(".form-group").find("input[type=checkbox]:checked").not(nbrLabelsStr);
                $.each($checked, function (i,chkInput) {
                    $(chkInput).attr('checked', false);
                });
                dateTimeFlag = "off";
            } else {
                dateTimeFlag = "on";
            }
            //时间控件开关
            widget._dateTimeSwitch(dateTimeFlag);
            //如果本次点击的是nbrLabels中的一个，
            // 需要判断是否有其他nbrLabels中的成员是checked的
            //如果有，则本次点击无效
            var currentTargetId = currentTarget.attr("id");
            if($.inArray(currentTargetId,nbrLabels)>-1) {
                var nbrFlag = false;
                $.each(nbrLabels,function(i,n){
                    if (n == currentTargetId) {
                        return true;
                    }
                    if (element.find("#" + n).is(":checked")) {
                        nbrFlag = true;
                        return false;
                    }
                });
                if(nbrFlag){
                    currentTarget.attr("checked", false);
                }
            }
        },
        _valueAddedTaxLogQuery: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("queryValueAddedTaxLogList", JSON.stringify(params), "#orderListResult", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if (_pageIndex > 1 && !params.pageInfo.rowCount) {
                                    params.pageInfo.rowCount = totalNumber;
                                }
                                if (widget._getConds()) {
                                    widget.refreshPart("queryValueAddedTaxLogList", JSON.stringify(params), "#invoicePrintLogList");
                                }
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }
                }, {
                    async: false
                });
            }
            ;
        },
        _queryCustomerOrderDetailLink: function (e) {
            var widget = this;
            var aEl = $(e.target).closest("a");
            var custOrderId = aEl.data("custOrderId");
            if (!custOrderId) {
                widget.popup("订单不存在,请重新查询后再试.");
                return;
            }
            var data = {
                custOrderId: custOrderId
            };
            window.open("../../crm/so/olDetailInfo?custOrderId=" + custOrderId);
        },
        _isNullStr: function (str) {
            if (str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this, element = $(widget.el);
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            var paramCnt = 0;
            if (element.find("#c_custOrderNbr").is(":checked")) {
                paramCnt++;
                var custOrderNbr = $.trim(element.find("#custOrderNbr").val());
                if (widget._isNullStr(custOrderNbr)) {
                    widget.popup("请输入购物车流水号！");
                    return false;
                } else {
                    params.custOrderNbr = custOrderNbr;
                }
            }
            if (element.find("#c_custOrderId").is(":checked")) {
                paramCnt++;
                var custOrderNbr = $.trim(element.find("#custOrderId").val());
                if (widget._isNullStr(custOrderNbr)) {
                    widget.popup("请输入购物车流水号！");
                    return false;
                } else {
                    params.custOrderNbr = custOrderNbr;
                }
            }

            var startTime = element.find("#beginDate").val();
            var endTime = element.find("#endDate").val();
            startTime = startTime ? startTime + " 00:00:00" : startTime;
            endTime = endTime ? endTime + " 23:59:59" : endTime;
            if (!widget._isNullStr(startTime) && !widget._isNullStr(endTime)) {
                if (startTime >= endTime) {
                    widget.popup("结束时间应大于开始时间!");
                    return false;
                }
                var start = new Date(startTime.replace(/-/g, "/")).getTime();
                var end = new Date(endTime.replace(/-/g, "/")).getTime();

                var flag = end - start > 90 * 24 * 60 * 60 * 1000
                if (flag) {
                    widget.popup("开始、结束时间跨度不能超出90天!");
                    return false;
                }
            }
            if (!widget._isNullStr(startTime) || !widget._isNullStr(endTime)) {
                if (!widget._isNullStr(startTime)) {
                    params.beginDate = startTime;
                }
                if (!widget._isNullStr(endTime)) {
                    params.endDate = endTime;
                }
            }
            if (element.find("#c_state").is(":checked")) {
                paramCnt++;
                var state = element.find("#state").val();
                if (widget._isNullStr(state)) {
                    widget.popup("请选择处理状态！");
                    return false;
                }
                params.state = state;
            }
            if (paramCnt < 1) {
                widget.popup("请至少选择一个查询条件！");
                return false;
            }
            return params;
        },
        _qryTimeQuantum: function (e) {
            var widget = this, element = $(widget.el),
                timeQuantum = $(e.currentTarget).attr("name"),
                dayCount;
            timeQuantum = timeQuantum.substring(timeQuantum.indexOf("_") + 1);
            switch (timeQuantum) {
                case "1":
                    dayCount = widget.global.currentDayCount;
                    break;
                case "7":
                    dayCount = widget.global.sevenDayCount;
                    break;
                case "30":
                    dayCount = widget.global.lastMonthDayCount;
                    break
                case "90":
                    dayCount = widget.global.ninthDayCount;
                    break
                default:
                    dayCount = widget.global.currentDayCount;
            }
            widget._changeTimeQuanTum(dayCount);
        },
        _changeTimeQuanTum: function (dayCount) {
            var widget = this, element = $(widget.el), beginDate = element.find('#beginDate'),
                endDate = element.find('#endDate');
            if (dayCount == widget.global.currentDayCount) {
                beginDate.val(widget._currentDate());
            } else if (dayCount == widget.global.lastMonthDayCount) {
                beginDate.val(widget._lastMonthDate());
            } else {
                beginDate.val(widget._getFormatDate(dayCount));
            }
            endDate.val(widget._getFormatDate(widget.global.currentDayCount));
        },
        //当天从0点开始
        _currentDate: function () {
            var widget = this, el = $(widget.el);
            var nowDate = widget._getFormatDate(widget.global.currentDayCount);
            return nowDate;
        },
        //设置默认时间
        _setDefaultDateTime: function () {
            var widget = this, element = $(widget.el),
                beginDate = element.find("#beginDate"),
                endDate = element.find("#endDate");
            beginDate.val(widget.global.defaultBeginTime);
            endDate.val(widget.global.defaultEndTime);
        },
        //时间控件开发方法
        _dateTimeSwitch: function (flag) {
            var widget = this, element = $(widget.el),
                beginDate = element.find("#beginDate"),
                endDate = element.find("#endDate");
            if (flag === "on") {
                widget._setDefaultDateTime();
                beginDate.prop("disabled", false);
                endDate.prop("disabled", false);
                element.find("#qryTimeQuantum").find('button').removeAttr('disabled');
            } else {
                beginDate.val("");
                endDate.val("");
                element.find("#qryTimeQuantum").find('button').attr('disabled', 'disabled');
                beginDate.prop("disabled", true);
                endDate.prop("disabled", true);
            }
        },
        //获取上个月时间
        _lastMonthDate: function () {
            var Nowdate = new Date();
            var vYear = Nowdate.getFullYear();
            var vMon = Nowdate.getMonth() + 1;
            var vDay = Nowdate.getDate();
            //每个月的最后一天日期（为了使用月份便于查找，数组第一位设为0）
            var daysInMonth = new Array(0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31);
            if (vMon == 1) {
                vYear = Nowdate.getFullYear() - 1;
                vMon = 12;
            } else {
                vMon = vMon - 1;
            }
            //若是闰年，二月最后一天是29号
            if (vYear % 4 == 0 && vYear % 100 != 0 || vYear % 400 == 0) {
                daysInMonth[2] = 29;
            }
            if (daysInMonth[vMon] < vDay) {
                vDay = daysInMonth[vMon];
            }
            if (vDay < 10) {
                vDay = "0" + vDay;
            }
            if (vMon < 10) {
                vMon = "0" + vMon;
            }
            var date = vYear + "-" + vMon + "-" + vDay;
            return date;
        },
        _queryValueAddedTaxLogDetailLink: function (e) {
            var widget = this;
            var aEl = $(e.target).closest("a");
            var id = aEl.data("id");
            if (!id) {
                widget.popup("专票交互日志不存在,请重新查询后再试.");
                return;
            }
            var data = {
                "id": id
            };
            widget._openDialog("valueAddedTaxLogDetail", data);
        },
        _queryValueAddedTaxDetailLink: function (e) {
            var widget = this;
            var aEl = $(e.target).closest("a");
            var valueAddedTaxId = aEl.data("valueAddedTaxId");
            if (!valueAddedTaxId) {
                widget.popup("标识不存在,请重新查询后再试.");
                return;
            }
            var data = {
                "valueAddedTaxId": valueAddedTaxId
            };
            widget._openDialog("valueAddedTaxDetail", valueAddedTaxId);
        },
        _openDialog: function (compName, data) {
            var widget = this;
            var option = {
                url: widget.global.compUrl[compName],
                params: data,
                headers: {
                    "regionId": widget.gSession.installArea
                }
            };
            widget.dialog(option);
        }
    });
    vita.widget.register("valueAddedTaxLogQuery", valueAddedTaxLogQuery, true);
})(window.vita);