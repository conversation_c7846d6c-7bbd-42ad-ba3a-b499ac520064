<div data-widget="orgCustDetail">
    <p class="vita-data">{"importBatchId" : $options.importBatchId}</p>
    <div class="page_main nopagenav">
        <div class="wmin_content">
            <div class="col-lg-12">
            </div>
            <div class="col-lg-12 mart10" id="auditAdjustInfoListResult">
                <div class="wmin_content" id="batctOrgListR">
                    <div class="col-lg-12 mart10" id="batchOrgList">
                        <table class="table table-hover">
                            <thead>
                            <tr id="batchOrgTable" style="display: none;">
                                <th>用户ID</th>
                                <th>接入号</th>
                                <th>原合同主体名称</th>
                                <th>导入状态</th>
                                <th>导入日期</th>
                                <th>导入描述</th>
                            </tr>
                            <tr>
                                <th>用户ID</th>
                                <th>接入号</th>
                                <th>原合同主体名称</th>
                                <th>导入状态</th>
                                <th>导入日期</th>
                                <th>导入描述</th>
                            </tr>

                            </thead>

                            <tbody>
                            #if($options != "null" && $options.orgDtVoList != "null" && $options.orgDtVoList.size() > 0)
                            #foreach($voList in $options.orgDtVoList)
                            <tr>
                                <td>$!{voList.prodInstId}</td>
                                <td>$!{voList.accNum}</td>
                                <td>$!{voList.oldDevStaffName}</td>
                                <td>
                                    #if($voList.importStatus == "0")
                                    成功
                                    #elseif($voList.importStatus == "1")
                                    失败
                                    #end
                                </td>
                                <td>$!{voList.createDate}</td>
                                <td>$!{voList.importDesc}</td>
                            </tr>
                            #end
                            #elseif($options.orgDtVoList != "null" || $options.orgDtVoList.size() == 0)
                            <tr>
                                <td align='center' colspan='7'>未查询到数据！
                                <td>
                            </tr>
                            #end
                            </tbody>
                        </table>
                    </div>
                </div>
                <!--翻页start -->
                <div id="showPageInfo">
                </div>
                #if($options.totalNumber && $options.totalNumber != "null")
                <p class="vita-data">{"totalNumber":$!options.totalNumber}</p>
                #end
                <!--翻页end -->
            </div>
        </div>
    </div>
    </div>
</div>
