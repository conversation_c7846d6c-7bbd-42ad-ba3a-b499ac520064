<div data-widget="qryComboValue" style="height:100%">
    #set($options = {"isFirst":"Y"})
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                地 区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                 宽带上网账号：
                                            </label>
                                            <div class="col-md-7">
                                                <input id="account" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <div class="col-md-4 searchbutt_r" align="right">
                                                <button id="btn-query" type="button" class="btn btn-primary">查询</button>
                                            </div>
                                        </div>


                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <!--<div><i class="bot"></i>查询结果</div>-->
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12 mart10" id="orderListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>套餐名称</th>
                                                <th>套餐id</th>
                                                <th>套餐值</th>
                                                <th>生效时间</th>
                                                <th>失效时间</th>
                                            </tr>
                                            </thead>
                                            <tbody id="orderList">
                                                #if($options.DEC_TCMC_YCX)
                                                    <tr>
                                                        <td>$!{options.DEC_TCMC_YCX}</td>
                                                        <td>$!{options.DEC_TCID_YCX}</td>
                                                        <td>$!{options.DEC_TCJZ_YCX}</td>
                                                        <td>$!{options.DEC_TC_EFF_DATE_YCX}</td>
                                                        <td>$!{options.DEC_TC_EXP_DATE_YCX}</td>
                                                    </tr>
                                                #elseif($options.isFirst != "Y")
                                                    <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                                                #end
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
