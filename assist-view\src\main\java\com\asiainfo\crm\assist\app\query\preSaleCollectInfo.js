(function(vita){
    var preSaleCollectInfo = vita.Backbone.BizView.extend({
        events : {
            "click #qryCommonLogSearchBtn" : "_qryPreSaleCollectInfo",
            "click #searchDel": "_searchParamsDel",
            "click [name='moduleRadio'] " : "_clickRadio",
            "click #deleteBtn" : "_deleteSubmit"
        },
        _initialize : function() {
            var widget = this,
                element = $(widget.el);
            data = element.data("data");
            widget.model.set(data);
            //初始化查询
            widget._qryPreSaleCollectInfo();
        },
        global : {
            pageIndex: 1,
            pageSize: 5, //每页记录数
        },
        _clickRadio : function(e){
            var widget = this,
                element = $(widget.el);
            var radio = $(e.target).closest('input');
            var data = radio.data('data');
            widget.model.set("selectedPreSale",data);
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        /**
         * 获取查询条件参数对象
         */
        _getConds : function(pageIndex) {
            var widget = this,
                element = $(widget.el);
            gSession = widget.gSession;
            var param = {
                "pageInfo" : {
                    "pageIndex" : pageIndex,
                    "pageSize" : widget.global.pageSize
                }
            };
            var certName = widget.model.get("certName");
            if (!widget._isNullOrEmpty(certName)) {
                param.certName = certName;
            }
            var certNum = widget.model.get("certNum");
            if (!widget._isNullOrEmpty(certNum)) {
                param.certNum = certNum;
            }
            var accNum = widget.model.get("accNum");
            if (!widget._isNullOrEmpty(accNum)) {
                param.accNum = accNum;
            }
            var objType = widget.model.get("objType");
            if (!widget._isNullOrEmpty(objType)) {
                param.objType = objType;
            }
            var statusCd = element.find("#statusCd").val();
            if (!widget._isNullOrEmpty(statusCd)) {
                param.statusCd = statusCd;
            }
            var preSaleNbr = widget.model.get("preSaleNbr");
            if (!widget._isNullOrEmpty(preSaleNbr)) {
                param.preSaleNbr = preSaleNbr;
            }
            return param;
        },
        _isNullOrEmpty: function (condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty;
        },
        /**
         * 清空查询条件
         */
        _searchParamsDel : function() {
            var widget = this,
                element = $(widget.el);
            widget.model.set("certName","");
            widget.model.set("certNum","");
            widget.model.set("accNum","");
            widget.model.set("objType","");
            widget.model.set("preSaleNbr","");
            element.find("#statusCd").val('');
        },

        /**
         * 删除
         * @private
         */
        _deleteSubmit: function () {
            var widget = this,el = $(widget.el),gSession = widget.gSession;
            var preSaleCollectInfo = widget.model.get("selectedPreSale");

            if (!preSaleCollectInfo) {
                widget.popup("请先选择一条记录！");
                return;
            }
            widget.popup("确定要作废该前置单吗？", function () {
                var param = {
                    "collectId": preSaleCollectInfo.collectId,
                };
                widget.callService("deletePreSaleCollectInfo", JSON.stringify(param), function(res) {
                    var ret = JSON.parse(res);
                    if (ret.resultCode == 0) {
                        widget.popup("作废前置单成功！", function () {
                            widget._qryPreSaleCollectInfo(); // 刷新列表
                        });
                    } else {
                        widget.popup("作废前置单失败！失败的原因为：" + ret.resultMsg);
                    }
                }, {
                    async: false
                });
            }, null);
        },

        _qryPreSaleCollectInfo : function(e) {
            var widget = this, element = $(widget.el);
            var param = widget._getConds(widget.global.pageIndex);
            element.find('#showPageInfo').empty();
            widget.refreshPart("queryPreSaleInfo", param, ".table-hover",
                "preSaleInfos", function(res) {
                    var paging = this.require("paging");
                    var totalNumber = $(res).find("input").eq(1).data("totalNumber");
                    if (parseInt(totalNumber) > 0) {
                        var e = paging.new({
                            recordNumber : widget.global.pageSize,
                            total : totalNumber,
                            pageIndex : widget.global.pageIndex,
                            callback : function(pageIndex){
                                //判断当前页
                                if(pageIndex == null || pageIndex == undefined || isNaN(pageIndex)){
                                    pageIndex = 1;
                                }
                                widget.model.set("pageIndex",pageIndex);
                                widget.refreshPart("queryPreSaleInfo", widget._getConds(pageIndex), ".table-hover");
                            }
                        });
                        element.find('#showPageInfo').append(e.getElement());
                    }
                }, {
                    mask : true
                });
        },
    });
    vita.widget.register("preSaleCollectInfo", preSaleCollectInfo, true);
})(window.vita);
