(function (vita) {
    var creditFJ = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query":  function () {
                this._creditFJ();
            } ,
            "click #recoverConnect": function () {
                this._recoverConnect();
            }
        },
        _recoverConnect: function () {
            var widget = this, element = $(widget.el);
            var users = element.find('table input[name="userInfo"]:checked');
            if (users.length == 0) {
                widget.popup("请选择复机用户！");
                return false;
            }
            var gSession = widget.gSession;
            var userInfo = $(users[0]).data("data");
            if(userInfo && userInfo.restore && userInfo.restore != "credit" && userInfo.restore != "right"){
                widget.popup(userInfo.restore);
                return false;
            }

            var param = {
                accNum: userInfo.accNum,
                custId: userInfo.custId,
                areaCode: gSession.areaCode,
                prodId: userInfo.prodId,
                stopType: userInfo.stopType,
                glbSessionId: gSession.glbSessionId
            };

            widget.callService("recoverConnect", param, function (response) {
                widget.popup(response.resultMsg);
            },{async:true,mask:true});
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            var option = {
                id: dialogId,
                url: widget.global.chooseChannel,
                maskClose: false,
                params: {},
                onClose: function (res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);
                }
            };
            widget.dialog(option);
        },
        global: {
            chooseArea: "../comm/chooseArea",
            chooseChannel: "../comm/chooseChannel"
        },
        _chooseArea: function () {
            var widget = this, element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id: dialogId,
                url: widget.global.chooseArea,
                params: {},
                onClose: function (res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _creditFJ: function () {
            //执行授信复机操作
            var widget = this ,gSession = widget.gSession, el = $(widget.el);
            debugger;
            var params = {
                accNum : "",
                areaCode:gSession.areaCode,
                staffCode:gSession.staffCode,
            };
            var accNum = el.find("#accNum").val();
            if (!accNum) {
                widget.popup("请输入号码！");
                return false;
            } else {
                params.accNum = accNum;
            }
            widget.refreshPart("queryProduct", params, "#userListResult", function (response) {}, {async: true,mask: true});
        }
    });
    vita.widget.register("creditFJ", creditFJ, true);
})(window.vita);