<div data-widget="homeCloudService" style="height: 100%">

    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>家庭云业务查询</div>
                                </div>

                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-5 ">
                                            <label class="col-md-4 control-label lablep">
                                                请输入号码:</label>
                                            <div class="col-md-8">
                                                <input type="text" id="accNum" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-5">
                                            <div class="col-md-2 text-left">
                                                <button type="button" id="btn-query" class="btn btn-primary">查询</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询结果</div>
                                </div>
                                <div class="wmin_content" id="homeCloudCodeResult">
                                    <div class="col-lg-12 mart10" >
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>接入号</th>
                                                <th>终端序列号</th>
                                                <th>终端厂商</th>
                                                <th>终端型号</th>
                                                <th>终端类别</th>
                                                <th>端口数量</th>
                                                <th>是否有wifi</th>
                                                <th>wifi能力(b/g/n)</th>
                                                <th>是否有GE端口</th>
                                                <th>是否支持家庭云</th>
                                                <th>最大带宽能力</th>
                                                <th>是否支持升级APK功能</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            #if($options != "null" && $options.homeCloudInfo && $options.homeCloudInfo != "null"
                                            && $options.homeCloudInfo.resultCode == "0" )
                                            <tr>
                                                #if($options.homeCloudInfo.orderList && $options.homeCloudInfo.orderList.anid
                                                && $options.homeCloudInfo.orderList.dev_sno )
                                                <th>$!{options.homeCloudInfo.orderList.anid}</th>
                                                <th>$!{options.homeCloudInfo.orderList.dev_sno}</th>
                                                <th>$!{options.homeCloudInfo.orderList.dev_vendor_name}</th>
                                                <th>$!{options.homeCloudInfo.orderList.dev_type_name}</th>
                                                #set($dev_class = $options.homeCloudInfo.orderList.dev_class)
                                                #if($dev_class == "1") #set($dev_class = "家庭网")
                                                #elseif($dev_class == "2") #set($dev_class = "政企网关")
                                                #elseif($dev_class == "3") #set($dev_class = "天翼网关")
                                                #elseif($dev_class == "4") #set($dev_class = "10G家庭网")
                                                #elseif($dev_class == "5") #set($dev_class = "智能二合一终端")
                                                #elseif($dev_class == "6") #set($dev_class = "智能三合一终端")
                                                #elseif($dev_class == "7") #set($dev_class = "校园定制终端")
                                                #else#set($dev_class = "")
                                                #end
                                                <th>$dev_class</th>
                                                <th>$!{options.homeCloudInfo.orderList.portnum}</th>
                                                #set($is_wifi = $options.homeCloudInfo.orderList.is_wifi)
                                                #if($is_wifi == "0") #set($is_wifi = "无")
                                                #elseif($is_wifi == "1") #set($is_wifi = "有")
                                                #else#set($is_wifi = "")
                                                #end
                                                <th>$is_wifi</th>
                                                #set($wifi_pro = $options.homeCloudInfo.orderList.wifi_pro)
                                                #if($wifi_pro == "1") #set($wifi_pro = "802.11b")
                                                #elseif($wifi_pro == "2") #set($wifi_pro = "802.11b/g")
                                                #elseif($wifi_pro == "3") #set($wifi_pro = "802.11b/g/n")
                                                #elseif($wifi_pro == "4") #set($wifi_pro = "802.11b/g/n/ac")
                                                #else#set($wifi_pro = "")
                                                #end
                                                <th>$wifi_pro</th>
                                                #set($is_geport = $options.homeCloudInfo.orderList.is_geport)
                                                #if($is_geport == "0") #set($is_geport = "无千兆口")
                                                #elseif($is_geport == "1") #set($is_geport = "LAN1口")
                                                #elseif($is_geport == "2") #set($is_geport = "LAN2口")
                                                #elseif($is_geport == "3") #set($is_geport = "LAN4口")
                                                #elseif($is_geport == "4") #set($is_geport = "全千兆口")
                                                #else#set($is_geport = "")
                                                #end
                                                <th>$is_geport</th>
                                                #set($ver_remark = $options.homeCloudInfo.orderList.ver_remark)
                                                #if($ver_remark == "0") #set($ver_remark = "不支持")
                                                #elseif($ver_remark == "1") #set($ver_remark = "支持")
                                                #elseif($ver_remark == "2") #set($ver_remark = "升级可支持")
                                                #else#set($ver_remark = "")
                                                #end
                                                <th>$ver_remark</th>
                                                #set($max_band = $options.homeCloudInfo.orderList.max_band)
                                                #if($max_band == "0") #set($max_band = "100M以下")
                                                #elseif($max_band == "1") #set($max_band = "100M")
                                                #elseif($max_band == "2") #set($max_band = "200M")
                                                #elseif($max_band == "3") #set($max_band = "300M")
                                                #elseif($max_band == "4") #set($max_band = "500M")
                                                #elseif($max_band == "5") #set($max_band = "1000M")
                                                #elseif($max_band == "6") #set($max_band = "1000M以上")
                                                #else#set($max_band = "")
                                                #end
                                                <th>$max_band</th>
                                                #set($ver_remark4 = $options.homeCloudInfo.orderList.ver_remark4)
                                                #if($ver_remark4 == "0") #set($ver_remark4 = "不支持")
                                                #elseif($ver_remark4 == "1") #set($ver_remark4 = "支持")
                                                #elseif($ver_remark4 == "2") #set($ver_remark4 = "升级可支持")
                                                #else#set($ver_remark4 = "")
                                                #end
                                                <th>$ver_remark4</th>
                                                #else
                                                <th>对端接口返回数据，格式错误</th>
                                                #end
                                            </tr>
                                            #end
                                            #if($options != "null" && $options.homeCloudInfo && $options.homeCloudInfo != "null"
                                            && $options.homeCloudInfo.resultCode == "1" )
                                            <tr>
                                                <td align='center' colspan='11'>$!{options.homeCloudInfo.resultMsg}</td>
                                            </tr>
                                            #end
                                            </tbody>
                                        </table>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

