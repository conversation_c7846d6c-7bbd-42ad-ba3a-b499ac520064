(function(vita) {
    var websocketclient = null;
    var isOnMessage="0";
    var pageInit="0";
	var custAuth = vita.Backbone.BizView.extend({
		events : {
			"click #myTab li" : "_clickTab",
			"click #jumpBtn" : "_jump",
			"click #custAuthSubmitBtn" : "_submit",
			"click #closeCustAuthBtn" : "_closePage",
			"click #sendMsgNum" : "_qrySmsCode", //短信验证发送短信验证码
			"click #validateMsgBtn" : "_verifySmsAuth",//短信码校验
			"click #certRead" : "_showReadCert", //身份证识别
			"click #prodPwdBtn" : "_verifyProdPwd", //维护发送短信验证码
			"click #prodPwdReleaseBtn" : "_releaseProdPwd", //号码解锁按钮
			"click #scanCert" : "_scanCert",  //读卡
            "click #dkq" : "_dkqScan",  //读卡器
			"click #nfc" : "_nfcScan",//CNF读卡
			"click #tab3" : "_tab3",
		},
		_initialize : function() {
			//1:证件识别，2：短信认证，3：密码识别，4：跳过验证
			var widget = this,
				element = $(widget.el);
			var gSession = widget.gSession;
			var param = {
					sysUserId : gSession.systemUserId
			}
			widget._Jurisdiction(param);
//			禁止右键
//			document.oncontextmenu = function () { return false; };
			var data = element.data("data");
			$("#accNum3").val(gSession.contactTele)
			if (!_.isEmpty(data)) {
				var data = element.data("data");
				widget.model.set(data);
			}
            var certOpt = element.data("data");
            var useDeskTop=certOpt.useDeskTop;
            if(useDeskTop=="0") {
                widget._isReadyCont();
                widget._init();
            }else{
                var sp =  element.find("#controlsIdentify");
                sp.html("请确认已启动智慧桌面");
                widget._deskTopInit('0');
			}
            widget.model.set("verifyProdPwd", 1);
            widget.model.set("releaseProdPwd", 1);
            widget.model.set({
                validate: false
            });
            element.find("#panel").slideToggle("slow");
		},
		
		_init : function () {  // 初始化
			var widget = this,
			element = $(widget.el);
			isRead=0;
			// 绑定按钮事件
			
		        var isReady=widget._isReadyCont(); 
	        
				if (isReady) {
					return;
		        }
			else{
				var hdSrc = "http://133.37.135.116/四川电信(鸿达).exe";
			    var krSrc = "http://133.37.135.116/四川电信(卡尔).exe";
			    var ynSrc = "http://133.37.135.116/四川电信(因纳伟盛).exe";
			    var hdSrcA =  element.find("#hdSrc")[0];
			    var krSrcA =  element.find("#krSrc")[0];
			    var ynSrcA =  element.find("#ynSrc")[0];
			    var loadInfo =  element.find("#loadInfo")[0];
			    hdSrcA.style.display = 'inline-block';
			    krSrcA.style.display = 'inline-block';
			    ynSrcA.style.display = 'inline-block';
			    loadInfo.style.display = 'inline-block';
			    hdSrcA.href = hdSrc;
			    krSrcA.href = krSrc;
			    ynSrcA.href = ynSrc;
			}
			
		},
			_connect : function() {
			var widget = this,
			element = $(widget.el);				
			var CertCtl =  element.find("#CertCtl")[0] ;
			var ret = CertCtl.connect();				
			ret = widget._JStrToObj(ret);	
			widget._DisplayInfo(ret);		
			return;
		},
		
		_nfcScan:function(){
			var widget = this,
			element = $(widget.el);   
			var dkqLi = element.find("#dkq");			
			dkqLi.removeClass("active");
			var cnfLi = element.find("#nfc");			
			cnfLi.addClass("active");

			var cnf = element.find("#nav-pills-tab-2");
			cnf.show();
			cnf.addClass(" in active");
			var dkq = element.find("#nav-pills-tab-1");
			dkq.hide();			
			dkq.removeClass("active");
		},
		
		_dkqScan:function(){
			var widget = this,			
			element = $(widget.el);	
			
			var dkqLi = element.find("#dkq");			
			dkqLi.addClass("active");
			var cnfLi = element.find("#nfc");			
			cnfLi.removeClass("active");
			
			var dkq = element.find("#nav-pills-tab-1");
			dkq.show();
			dkq.addClass("active");
			var cnf = element.find("#nav-pills-tab-2");
			cnf.hide();
			cnf.removeClass(" in active");			
			
		},
		//根据权限做展示
		_Jurisdiction : function (param){
			var widget = this,			
			element = $(widget.el);	
			widget.callService("maintenanceAuthority",param,function(res){
				var ret = JSON.parse(res);
				if("" != ret){
					$("#tab3").hide();
					$("#content3").hide();
				}else{
					$("#tab3").show();
					$("#content3").show();
				}
			})
		},
		
		_JStrToObj : function(str){
			return eval("(" + str + ")");
		},
		
		_DisplayInfo : function (ret){
			 var widget = this,
			 element = $(widget.el);			
			if (ret.resultFlag==0){
				//成功
			}else{
				alert(ret.errorMsg);
			}
		},
        _scanCert : function () {
            var widget = this,
                element = $(widget.el);
            var certOpt=element.data("data");
            var useDeskTop=certOpt.useDeskTop;
            if(useDeskTop=="0") {
                widget.model.set("scaned", 0);
                var CertCtl = element.find("#CertCtl")[0];
                var soConst = vita.widget.require("soConst");
                var soUtil = widget.require("soUtil");
                var connect = widget._connect();
                if (connect.isCloud != undefined && connect.isCloud == "0") {
                    if (soConst.get("CONST_CLOUD_DIRECT_INFO") == "0") {
                        widget._cloudRead();
                    } else {
                        var ret = soUtil.cloudRead(widget, CertCtl);
                        ret.resultContent.isCloud = "0";
                        widget._fillForm(widget,ret);
                        CertCtl.disconnect();
                    }

                } else {
                    var ret = CertCtl.readCert();
                    ret = widget._JStrToObj(ret);
                    if (ret.resultFlag == -1) {
                        widget._DisplayInfo(ret);
                    } else {
                        widget._fillForm(widget,ret);
                        CertCtl.disconnect();
                    }
                }
            }else{
                widget._deskTopScanCert();
            }
        },
        _connect : function() {
            var widget = this,
                element = $(widget.el);
            var CertCtl =  element.find("#CertCtl")[0] ;
            var ret = CertCtl.connect();
            ret = widget._JStrToObj(ret);
            widget._DisplayInfo(ret);
            return ret;
        },
        _cloudRead : function () {
            var widget = this,
                element = $(widget.el),
                soConst = widget.require("soConst");

            var appSecret = "30b5c231a8ea42c09c87f75d22ebc9ea"; // appId对应的加密密钥
            var appId = "1035";
            var nonce = "jfoiiuylkjljpohi";
            var secretKey = "D34AE719CE3246E40729411452759F86D34AE719CE3246E4";
            if("Y" == soConst.getMda("GZ_CLOUDREAD_SWITCH")){
                appSecret = soConst.getMda("IDENTITY_NFC_APP_SECRET");
                appId = soConst.getMda("IDENTITY_NFC_APP_ID");
                nonce = soConst.getMda("IDENTITY_NFC_NONCE");
                secretKey = soConst.getMda("IDENTITY_NFC_SECRET_KEY");
            }
            var date = new Date();
            var timestamp = ""+date.getTime();
            var business = "{'areaCode':'020','busiSerial':'12345','channelCode':'2001','clientIP':'','deviceModel':'llllllllsdfsdffsdfjka  sdfjfsdfsdfsddddddddddd','deviceSerial':'','osType':'','srcSystem':'CRM','staffCode':'110011','teminalType':'PC'}";
            var signatureData = appId + appSecret + business + nonce + timestamp;
            var shaObj = new jsSHA("SHA-1", "TEXT");
            shaObj.update(signatureData);
            var signatureData = shaObj.getHash("HEX");
            json = CertCtl.cloudReadCert(appId,timestamp,nonce,business,signatureData);
            json = eval('(' + json + ')');
            var cert;
            if(json.resultFlag == 0){
                var IDCard = json.resultContent.certificate;
                var url = window.location.href;
                var data = CertCtl.DecryptInfo(IDCard,secretKey);
                certIdent= CertCtl.photoMagick(data);
                try //Internet Explorer
                {
                    xmlDoc=new ActiveXObject("Microsoft.XMLDOM");
                    xmlDoc.async="false";
                    xmlDoc.loadXML(data);
                    cert=widget._filCloudlForm();
                }
                catch(e){
                    try //Firefox, Mozilla, Opera, etc.
                    {
                        parser=new DOMParser();
                        xmlDoc=parser.parseFromString(data,"text/xml");
                        cert=widget._filCloudlForm();
                    }
                    catch(e){
                        widget.popup(e.errorMsg);
                    }
                }
            }else{
                widget.popup(json.errorMsg);
            }
            json = CertCtl.getStatus();
            json = eval('(' + json + ')');
            if(json.resultFlag == 0){
            }else{
                document.getElementById("ActivityLTo").value=fmtDate(new Date());
                widget.popup(json.errorMsg);
                return;
            }
            json = CertCtl.disconnect();
            json = eval('(' + json + ')');
            if(json.resultFlag != 0){
                widget.popup(json.errorMsg);
            }
        },
        _filCloudlForm : function () {
            var widget = this,
                element = $(widget.el);

            var pName = xmlDoc.getElementsByTagName("partyName")[0].childNodes[0].nodeValue;
            var pSex = xmlDoc.getElementsByTagName("gender")[0].childNodes[0].nodeValue;
            var pNation = xmlDoc.getElementsByTagName("nation")[0].childNodes[0].nodeValue;
            var pBorn = xmlDoc.getElementsByTagName("bornDay")[0].childNodes[0].nodeValue;
            var pCardNo = xmlDoc.getElementsByTagName("certNumber")[0].childNodes[0].nodeValue;
            var pAddress = xmlDoc.getElementsByTagName("certAddress")[0].childNodes[0].nodeValue;
            var pPolice = xmlDoc.getElementsByTagName("certOrg")[0].childNodes[0].nodeValue;
            var pActivityLFrom = xmlDoc.getElementsByTagName("effDate")[0].childNodes[0].nodeValue;
            var pActivityLTo=xmlDoc.getElementsByTagName("expDate")[0].childNodes[0].nodeValue;
            var pPhotoPic=xmlDoc.getElementsByTagName("identityPic")[0].childNodes[0].nodeValue;

            var pPhotoBuffer;
            if(certIdent==""||certIdent==null){
                pPhotoBuffer=xmlDoc.getElementsByTagName("identityPic")[0].childNodes[0].nodeValue;
            }else{
                pPhotoBuffer=certIdent;
            }


            pBorn=pBorn.substring(0,4)+"-"+pBorn.substring(4,6)+"-"+pBorn.substring(6,8)+" 00:00:00";
            pActivityLFrom=pActivityLFrom.substring(0,4)+"-"+pActivityLFrom.substring(4,6)+"-"+pActivityLFrom.substring(6,8)+" 00:00:00";
            if(pActivityLTo.indexOf('长期')>= 0){
                pActivityLTo = '3000-01-01 00:00:00';
            }else{
                pActivityLTo=pActivityLTo.substring(0,4)+"-"+pActivityLTo.substring(4,6)+"-"+pActivityLTo.substring(6,8)+" 00:00:00";
            }
            var cert={};
            cert.resultFlag=0;
            cert.resultContent={};
            cert.resultContent.partyName=pName;
            cert.resultContent.gender=pSex;
            cert.resultContent.nation=pNation;
            cert.resultContent.bornDay=pBorn;
            cert.resultContent.certAddress=pAddress;
            cert.resultContent.certNumber=pCardNo;
            cert.resultContent.certOrg=pPolice;
            cert.resultContent.effDate=pActivityLFrom;
            cert.resultContent.expDate=pActivityLTo;
            cert.resultContent.cardImg=pPhotoBuffer;
            cert.resultContent.identityPic=pPhotoPic;
            widget._fillForm(widget,cert);
        },
		_fillForm : function (widget,ret) {
             var element = $(widget.el);
			  widget._DisplayInfo(ret);	 
			  var pName=ret.resultContent.partyName; 
			  var pSex=ret.resultContent.gender;
			  var pNation=ret.resultContent.nation;
			  var pBorn=ret.resultContent.bornDay;
			  var pAddress=ret.resultContent.certAddress;
			  var pCardNo=ret.resultContent.certNumber;
			  var pPolice=ret.resultContent.certOrg;
			  var pActivityLFrom=ret.resultContent.effDate; 
			  var pActivityLTo=ret.resultContent.expDate; 
			  
			  var name =  element.find("#Name")[0] ;
			  name.value = pName; 
			  var sex =  element.find("#Sex")[0];			 
			  if(pSex == 1){
				  sex.value = "男";
			  }else{
				  sex.value = "女";
			  }
			  var Nation =  element.find("#Nation")[0] ;
			  Nation.value = pNation; 
			  var Born =  element.find("#Born")[0] ;
			  Born.value = pBorn; 
			  var Address =  element.find("#Address")[0] ;
			  Address.value = pAddress; 
			  var CardNo =  element.find("#CardNo")[0] ;
			  CardNo.value = pCardNo; 
			  var Police =  element.find("#Police")[0] ;
			  Police.value = pPolice;  
			  var ActivityLFrom =  element.find("#ActivityLFrom")[0] ;
			  ActivityLFrom.value = pActivityLFrom; 
			  var ActivityLTo =  element.find("#ActivityLTo")[0] ;
			  ActivityLTo.value = pActivityLTo;			 
			  var IdPhoto =  element.find("#IdPhoto")[0] ;
			  IdPhoto.src = "data:image/jpg;base64," + ret.resultContent.identityPic;
			  
			 
		},		
		_isReadyCont :function(){		
			var widget = this,
			element = $(widget.el);
			  var sp =  element.find("#controlsIdentify");
		      if(navigator.userAgent.indexOf("Firefox")>0 || navigator.userAgent.indexOf("Chrome")>0){
		      var swf=navigator.plugins; 		
		       for(var i=0;i<swf.length;i++){		    	  
		    	   var	fileName=swf[i].filename.toString();
				   if(fileName=="npCertReader.dll"){	
					   sp[0].innerHTML  = "控件识别结果：控件正常。 ";
						return true;
				   }
				}		       
		       return false;
		       }
		      else if(navigator.userAgent.indexOf("MSIE")>0 ){
		    	if (window.ActiveXObject){		
		    		sp[0].innerHTML = "控件识别结果：控件正常";
				    return true; 
		        }
		    	else {		    		
					return false;  
		        }
		    }
		},
		global : {
                timeSec : 500,
                timeNum : 0,
                timer : null,
                authIdentity : 1,
                authMsg : 2,
                authPwd : 3,
                authPass : 4
		},
		_clickTab : function(e) {
			var widget = this,
				element = $(widget.el),
				global = widget.global,
				$tar = $(e.target),
				li = $tar.closest("li");
			li.addClass("active").siblings("li").removeClass("active");
			var tabIndex = li.attr("id").replace(/\D/g, '');
			var content = element.find("#content" + tabIndex);
			widget.model.set({
				validate : false
			});
			if (content.length) {
				content.fadeIn(300).addClass("in active")
					.siblings("div").hide().removeClass("in active");
			}
		},
		_jump : function() {
			var widget = this,
				element = $(widget.el),
				gSession = widget.gSession,
				global = widget.global;
			var param = {
				glbSessionId : gSession.glbSessionId,
				custTokenId : gSession.custTokenId
			};

			widget.model.set({
				authTypeCd : global.authPass
			});
			widget._closePage();
			return false;
		},
		//确定
		_submit : function() {
			var widget = this,
				element = $(widget.el),
				result = "",
				global = widget.global;
			var li = element.find("#myTab li.active");
			var tabIndex = li.attr("id").replace(/\D/g, '');
			switch (parseInt(tabIndex)) {
			case global.authIdentity: //证件识别
				result = widget._verifyIdentity();
				break;
			case global.authMsg: //短信认证
				result = widget._verifySmsAuth();
				break;
			case global.authPwd: //维护短信认证
				//result = widget._verifyProdPwd();//_releaseProdPwd
				result = widget._releaseProdPwd();
				break;
			default:
				break;
			}
			if (!result) {
				return false;
			}

			widget.model.set({
				authTypeCd : global.authPass,
				validate : true
			});
			widget._closePage();
			return false;
		},
		//取消
		_closePage : function() {
			var widget = this,
				element = $(widget.el);
			element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
		},
		_showReadCert : function() {
			return false;
		},
		_verifyIdentity : function() {
			var widget = this,
				element = $(widget.el);
			var CardNo = element.find("#CardNo");
			var certNumVal = $.trim(CardNo.val());
			var soUtil = widget.require("soUtil");
			var isNullOrEmpty = soUtil.isNullOrEmpty(certNumVal);
			if (isNullOrEmpty) {
				widget.popup("请读取身份证");
                //CardNo.focus();
				return false;
			}
			// var resultMsg = "",
			// 	isPass = false,
			// 	global = widget.global;
			// var param = {
			// 	frontCard : certNumVal,
			// 	custTokenId : widget.model.get("custTokenId")
			// };
            var data;
			data = element.data("data");

			var certNum = data.certNum;
				if (certNum != certNumVal){
					widget.popup("读取的证件号与输入的不一致")
					return false;
				}
			// widget.callService("verifyIdentity", param, function(res) {
			// 	var ret = JSON.parse(res);
			// 	/*if (ret.resultCode == 0) {
			// 		var resultObject = ret.resultObject;
			// 		if (resultObject && resultObject.isPass) {
			// 			isPass = resultObject.isPass;
			// 		}
			// 	} else {
			// 		resultMsg = ret.resultMsg;
			// 	}*/
			// },
			// 	{
			// 	async : false
			// });
			/*var isNullOrEmpty = soUtil.isNullOrEmpty(resultMsg);
			if (!isNullOrEmpty) {
				widget.popup(resultMsg);
				certNum.focus();
				return false;
			}
			if (!isPass) {
				widget.popup("身份证不存在,请核实");
				certNum.focus();
				return false;
			}*/

			widget.model.set({
				// authTypeCd : global.authIdentity,
				// authNumber : widget.model.get("certNum"),
				validate : true
			});

            widget._closePage();
			return true;
		},
		//短信验证发送验证码
		_qrySmsCode : function(e) {
			var widget = this,
				element = $(widget.el),
				gSession = widget.gSession,
				resultMsg = "",
				isPass = false;
			var accNum;
			var verifyProdPwd = widget.model.get("verifyProdPwd");
			if(verifyProdPwd != 3){
				accNum = element.find("#accNum2");//手机号
			}else if(verifyProdPwd == 3){
				accNum = element.find("#accNum3");//手机号
			}
			var accNumVal = $.trim(accNum.val());
			var soUtil = widget.require("soUtil");
			var isNullOrEmpty = soUtil.isNullOrEmpty(accNumVal);
			if (isNullOrEmpty) {
				widget.popup("请输入产品接入号");
				accNum.focus();
				return false;
			}


            var data = element.data("data");
            var certAccNum = data.certAccNum, widgetName;
            if(verifyProdPwd != 3){
            	if (widget.getMDA("AUTH_POINTS")[data.widgetName] == "Y") {
            		widgetName = data.widgetName;
            	} else {
    	            var boo = false;
    	            $.each(certAccNum, function(i, refund) {
    	            	if (accNumVal == refund.valueAccNum.accNum) {
    	            		boo = true;
    					}
    	            })
    	            if (!boo) {
    	            	widget.popup("请输入该证件所属的产品接入号");
    					return false;
    				}
            	}
            }
            /*JsonObject jsonParam = new JsonObject();
            jsonParam.addProperty("beginDate", beginDate);
            jsonParam.addProperty("beginHour", "0");
            jsonParam.addProperty("endDate", endDate);
            jsonParam.addProperty("endHour", "24");
            
            jsonParam.addProperty("businessId", "123123");
            jsonParam.addProperty("toTel", userPhone);
            jsonParam.addProperty("sysCode", "Crm");
            jsonParam.addProperty("createDespart", "Crm");
            jsonParam.addProperty("createStaff", "Crm");
            jsonParam.addProperty("flowCode", "124585955");
            jsonParam.addProperty("latnId", zone);
            jsonParam.addProperty("planId", zone);*/

			var param = {
				beginHour:0,//当前时间
                endHour:24,//结束时间
                toTel : accNumVal,//手机号
                sysCode:"Crm",
                createDespart:"Crm",
                createStaff:"Crm",
                flowCode : "12342132",
			};
			if (widgetName) {
				param.widgetName = widgetName;
				param.certType = data.certType;
				param.certNum = data.certNum;
			}
			widget.callService("qrySmsCode", JSON.stringify(param), function(res) {
				if (widgetName && res && res.indexOf("resultMsg") > -1) {
					var result = JSON.parse(res);
					widget.popup(result.resultMsg);
					return;
				}
				if (res == null || res == ""){
					widget.popup("未获取到接入号地区，发送失败！")
					return;
				}
				var ret = res;
				widget.model.set("randomCodes",ret);
				if (3 == verifyProdPwd) {
					 widget._startSends();
				}else{
					widget._startSend();
				}  
				widget.model.set("verifyProdPwd","")
				/*if (ret.resultCode == 0) {
					var resultObject = ret.resultObject;
					if (resultObject && resultObject.isPass) {
						isPass = resultObject.isPass;
					}
				} else {
					resultMsg = ret.resultMsg;
				}*/
			}, {
				async : false, headers:{}
			});


			/*var isNullOrEmpty = soUtil.isNullOrEmpty(resultMsg);
			if (!isNullOrEmpty) {
				widget.popup(resultMsg);
				return false;
			}*/
		},

		//短信校验短信码
		_verifySmsAuth : function() {
			var widget = this,
				element = $(widget.el),
				gSession = widget.gSession,
				global = widget.global,
				resultMsg = "",
				isPass = false;
			var boo = true;
			var msgNum;
			var verifyProdPwd = widget.model.get("releaseProdPwd");
			if(verifyProdPwd != 3){
				msgNum = element.find("#checkMsgNum");
			}else if(verifyProdPwd == 3){
				msgNum = element.find("#checkMsgNum3");
			}
			var msgNumVal = $.trim(msgNum.val());
			var soUtil = widget.require("soUtil");
			var isNullOrEmpty = soUtil.isNullOrEmpty(msgNumVal);
			if (isNullOrEmpty) {
				widget.popup("请输入手机验证码");
				msgNum.focus();
				return false;
			}
			widget.model.set("releaseProdPwd","");
            var data = element.data("data");
            var randomCodes = widget.model.get("randomCodes");
			var param = {
				inputCode : msgNumVal+"",
				randomCodes : widget.model.get("randomCodes")+""
			};
			widget.callService("verifySmsAuth", param, function(res) {
				if (!res == "1") {
					boo = false;
				}
			}, {
					async : false
			});
			 widget.model.set("releaseProdPwd","");
				if (!boo) {
					widget.popup("手机验证码错误,请重新输入");
					return false;
				}else{
					widget.popup("手机验证码验证成功");
					widget.model.set("randomCodes","");
					widget.model.set({
	                    validate : true
	                });
					widget._closePage();
				}
				var accNumVal = $.trim(element.find("#accNum2").val());
				widget.model.set({
					authTypeCd : global.authMsg,
					authNumber : accNumVal,
					validate : true
				});
				return true;
			},
		//维护发送短信验证码
		_verifyProdPwd : function() {
            var widget = this,
                element = $(widget.el),
                gSession = widget.gSession,
                resultMsg = "",
                isPass = false;
            widget.model.set("verifyProdPwd",3);
            widget._qrySmsCode();
           /* var  data = element.data("data");
            var contactTele = data.contactTele;
            $("#accNum3").val(contactTele);
            var accNum = element.find("#accNum3");//手机号
            var accNumVal = $.trim(accNum.val());
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(accNumVal);
            if (isNullOrEmpty) {
                widget.popup("请输入产品接入号");
                accNum.focus();
                return false;
            }
            debugger;
            var  data = element.data("data");
            var param = {
                accNum:contactTele,
                beginHour:0,//当前时间
                endHour:24,//结束时间
                toTel : accNumVal,//手机号
                sysCode:"Crm",
                createDespart:"Crm",
                createStaff:"Crm",
                flowCode : "12342132",
            };
            debugger;
            widget.callService("qrySmsCode", JSON.stringify(param), function(res) {
                if ( res == null || res == ""){
                    widget.popup("发送失败")
                    return;
                }else {
                    widget.popup("发送成功")
                    // $("#checkMsgNum").val("6666");
                    widget._startSends();
                    var data = element.data("data");
                    $("#checkMsgNum3").val(data.number);
                    return;
                }
                //return false;
                if (ret.resultCode == 0) {
                    var resultObject = ret.resultObject;
                    if (resultObject && resultObject.isPass) {
                        isPass = resultObject.isPass;
                    }
                } else {
                    resultMsg = ret.resultMsg;
                }
            }, {
                async : false
            });
			var isNullOrEmpty = soUtil.isNullOrEmpty(resultMsg);
			if (!isNullOrEmpty) {
				accNum.focus();
				return false;
			}
			return true;*/
		},
		//维护短信校验码
		_releaseProdPwd : function() {
			var widget = this,
				element = $(widget.el),
				global = widget.global,
				resultMsg = "",
				isPass = false;
			 widget.model.set("releaseProdPwd",3);
			widget._verifySmsAuth();
			/*var accNum = element.find("#checkMsgNum3");
			var accNumVal = $.trim(accNum.val());
			var soUtil = widget.require("soUtil");
			var isNullOrEmpty = soUtil.isNullOrEmpty(accNumVal);
			if (isNullOrEmpty) {
				widget.popup("请输入手机验证码");
				return false;
			}
            var data = element.data("data");
            var param = {
                inputCode : accNumVal,
				number:data.number
            };
            widget.callService("verifyProdPwd", param, function(res) {
                if(res == null || res == ""){
                    widget.popup("手机验证码错误,请重新输入");
                    return false;
                }
                widget.model.set({
                    validate : true
                });
				widget._closePage();
            }, {
                async : false
            });
			var isNullOrEmpty = soUtil.isNullOrEmpty(resultMsg);
			if (!isNullOrEmpty) {
				return false;
			}*/
		},
		
        _startSends : function() {
            var widget = this,
                global = widget.global,
                element = $(widget.el);
            var button = element.find('#prodPwdBtn');
            button.addClass("btn-greyd").removeClass("btn-green");
            clearTimeout(global.timer);
            global.timeNum = global.timeSec;
            button.attr("disabled", true).html('重新获取<span class="marl5 textcolorgreen">' + global.timeSec + 's</span>');
            var timeUpdate = function() {
                if (global.timeNum >= 0) {
                    button.html('重新获取<span class="marl5 textcolorgreen">' + global.timeNum + 's</span>');
                    global.timeNum--;
                    global.timer = setTimeout(timeUpdate, 1000);
                } else {
                    button.attr("disabled", false).html("获取验证码");
                    button.addClass("btn-green").removeClass("btn-greyd");
                }
            }
            timeUpdate();
        },
		_startSend : function() {
			var widget = this,
				global = widget.global,
				element = $(widget.el);
			var button = element.find('#sendMsgNum');
			button.addClass("btn-greyd").removeClass("btn-green");
			clearTimeout(global.timer);
			global.timeNum = global.timeSec;
			button.attr("disabled", true).html('重新获取<span class="marl5 textcolorgreen">' + global.timeSec + 's</span>');
			var timeUpdate = function() {
				if (global.timeNum >= 0) {
					button.html('重新获取<span class="marl5 textcolorgreen">' + global.timeNum + 's</span>');
					global.timeNum--;
					global.timer = setTimeout(timeUpdate, 1000);
				} else {
					button.attr("disabled", false).html("获取验证码");
					button.addClass("btn-green").removeClass("btn-greyd");
				}
			}
			timeUpdate();
		},
		//通过校验的权限验证接口
		_hasPassAuthOperationSpec : function(param) {
			var widget = this,
				element = $(widget.el);
			var jump = false;
			widget.callService("hasPassAuthOperationSpec", param, function(res) {
				var ret = JSON.parse(res);
				if (ret.resultCode == 0) {
					var resultObject = ret.resultObject;
					if (resultObject) {
						jump = true;
					}
				} else {
					widget.popup(ret.resultMsg);
				}
			}, {
				async : false
			});
			return jump;
		},
		getValue : function() {
			var widget = this;
			return widget.model.toJSON();
		},

        //安全桌面初始化websocket
        _deskTopInit:function(flag){
            var widget = this;
            pageInit=flag;
            if (window["WebSocket"]) {
                if(websocketclient==null){
                    widget._createWebsocket();
                }
            }
        },
        //创建websocket
        _createWebsocket:function () {
            var widget = this, element = $(widget.el);
            var startWS = false;
            var wsUrl = "";
            if (startWS) {
                wsUrl = "ws://127.0.0.1:8888/echo";
            } else {
                wsUrl = "wss://127.0.0.1:8889/echo";
            }
            websocketclient = new WebSocket(wsUrl);
            websocketclient.onclose = function (evt) {
                websocketclient = null;
            },
			websocketclient.onerror = function (evt) {
				widget.popup("连接智慧桌面失败。");
				websocketclient = null;
			},
			websocketclient.onopen = function (evt) {
				isOnMessage="1";
				if(pageInit=='1'){
					widget._deskTopScanCert();
				}
			},
			websocketclient.onmessage = function (evt) {
				if (evt.data=="ping"){
					websocketclient.send("pong");
					return;
				}else if (evt.data=="pong"){
					websocketclient.send("ping");
					return;
				}else if (evt.data=="PING"){
					websocketclient.send("PONG");
					return;
				}
				var jsonObj = eval('(' + evt.data + ')');
        var pContent = widget.aesDecryptReadCardParam(jsonObj.p);
				var pObj = eval('(' + pContent + ')');
				if(pObj.result.partyName!=undefined){
					var partyName = widget._decode(widget,pObj.result.partyName);
					var nation = widget._decode(widget,pObj.result.nation);
					var certAddress= widget._decode(widget,pObj.result.certAddress);
					var certOrg = widget._decode(widget,pObj.result.certOrg);
					var bornDay= pObj.result.bornDay;
					var certNumber= pObj.result.certNumber;
					var identityPic= pObj.result.identityPic;
                    var activityL=widget._decode(widget,pObj.result.activityL);
					var pActivityLFrom=activityL.split("-")[0];
					pActivityLFrom=pActivityLFrom.substring(0,4)+"-"+pActivityLFrom.substring(4,6)+"-"+pActivityLFrom.substring(6,8)+" 00:00:00";
					var pActivityLTo=activityL.split("-")[1];
					if(pActivityLTo.indexOf('长期')>= 0){
						pActivityLTo = '3000-01-01 00:00:00';
					}else{
						pActivityLTo=pActivityLTo.substring(0,4)+"-"+pActivityLTo.substring(4,6)+"-"+pActivityLTo.substring(6,8)+" 00:00:00";
					}
					bornDay=bornDay.substring(0,4)+"-"+bornDay.substring(4,6)+"-"+bornDay.substring(6,8)+" 00:00:00";
					var sex=widget._decode(widget,pObj.result.sex);
					if(sex=='男'){
						sex="1";
					}else{
						sex="0";
					}
					var cust={
						partyName:partyName,
						nation:nation,
						certAddress:certAddress,
						certOrg:certOrg,
						bornDay:bornDay,
						certNumber:certNumber,
						identityPic:identityPic,
						effDate:pActivityLFrom,
						expDate:pActivityLTo,
						gender:sex,
						isCloud:"0"
					}
					var ret={
						resultContent:cust
					}
					ret.resultFlag=0;
					widget._fillForm(widget,ret);
				}
			}
        },
        //读卡请求
        _deskTopScanCert:function(){
            var widget = this,
                element = $(widget.el),
                gSession = widget.gSession;
            var partyId="";
            if(gSession.custInfo!=undefined){
                partyId=gSession.custInfo.custId;
            }
            if(websocketclient!=null&&isOnMessage=="1"){
                var staffCode=gSession.staffCode;
		var channelCode="";
		if(gSession.curChannelNbr!=undefined&&gSession.curChannelNbr!=null){
			channelCode=gSession.curChannelNbr;
		}
                var sendData = '{"actiontype":"readIdCardInfo","staffNumber":"'+staffCode+'","channelCode":"'+channelCode+'","partyId":"'+partyId+'"}';
                //安全桌面读卡参数加密方法封装
                var base64Str = widget.aesReadCardParam(sendData);
                var jsonStr = '{"p":"' + base64Str + '"}';
                websocketclient.send(jsonStr);
            }else{
                widget.popup("智慧桌面连接失败，请连接后重试。");
                widget._deskTopInit('1');
            }
        },
        aesReadCardParam : function (data) {
            return window.btoa(data);
        },
        aesDecryptReadCardParam : function (data) {
            var widget = this;
            return widget._decode(widget,data);
        },
        _decode:function (widget,input) {
            var output = base64decode(input);
            output = widget._utf8_decode(output);
            return output;
        },
        _utf8_decode : function (utftext) {
            var string = "";
            var i = 0;
            var c = c1 = c2 = 0;
            while ( i < utftext.length ) {
                c = utftext.charCodeAt(i);
                if (c < 128) {
                    string += String.fromCharCode(c);
                    i++;
                } else if((c > 191) && (c < 224)) {
                    c2 = utftext.charCodeAt(i+1);
                    string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
                    i += 2;
                } else {
                    c2 = utftext.charCodeAt(i+1);
                    c3 = utftext.charCodeAt(i+2);
                    string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
                    i += 3;
                }
            }
            return string;
        }
	});
	vita.widget.register("custAuth", custAuth, true);
})(window.vita);