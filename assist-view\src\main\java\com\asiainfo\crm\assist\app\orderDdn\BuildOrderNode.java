package com.asiainfo.crm.assist.app.orderDdn;

import com.asiainfo.appframe.ext.flyingserver.org.apache.commons.collections.MapUtils;
import com.asiainfo.crm.common.AbstractComponent;

import com.asiainfo.crm.service.intf.IOrderSMO;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


@Component("vita.buildOrderNode")
public class BuildOrderNode extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(BuildOrderNode.class);

    @Autowired
    private IOrderSMO iOrderSMO ;

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        String paramStr = (String)params[0];
        Map paramMap = jsonConverter.toBean(paramStr, Map.class);
        String result=iOrderSMO.buildOrderNode(paramStr);
        Map map = jsonConverter.toBean(result, Map.class);
        List messageList = (List) MapUtils.getObject(map, "resultObject");
        options.put("messageList", messageList);
        return options;
    }

}
