(function(vita) {
	var saopGcAction = vita.Backbone.BizView.extend({
		events : {
			"click #btn-query" : "_queryGcSendResult",
			"click #btn-gCSend" : "_gcSend",
			"click #btn-clear" : "_clearCond"
		},
		_clearCond : function() {
			var $checked = $(".form-group").find("input[type=radio]:checked");
			$.each($checked, function(i, chkInput) {
				chkInput.click();
				var $input = $(chkInput).closest(".form-group").find("input.form-control");
				$input.val("");
			})
		},
		_isNullStr : function(str) {
			if (str == null || str == "") {
				return true;
			}
			return false;
		},
		_getAccNum : function() {
			var widget = this;
			if ($("#c_accNum").is(":checked")) {
				var accNum = $("#accNum").val();
				if (widget._isNullStr(accNum)) {
					widget.popup("请输入手机号码！");
					return false;
				} else {
					return accNum;
				}
			}
		},
		_getOrderTypeCd : function() {
			if ($("#gcSendRadio100").is(":checked")) {
				return $("#gcSendRadio100").attr("value");
			}
//			if ($("#gcSendRadio209").is(":checked")) {
//				return $("#gcSendRadio209").attr("value");
//			}
//			if ($("#gcSendRadio300").is(":checked")) {
//				return $("#gcSendRadio300").attr("value");
//			}
		},
		_gcSend : function() {
			var widget = this;
			var gSession = widget.gSession;
			var params = {
				staffName : gSession.staffName,
				staffCode : gSession.staffCode,
				srvcInstId : "SAOP_GC_ACTION",
				busyTypeCd : "GC_SEND",
				accNum : widget._getAccNum(),
				orderTypeCd : widget._getOrderTypeCd()
			};
			if (params.accNum && params.orderTypeCd) {
				widget.callService("doSaopAction", JSON.stringify(params), function(ret) {
					widget.popup(ret.rspDesc);
				});
				widget._queryGcSendResult();
			}
		},
		_queryGcSendResult : function() {
			var widget = this;
			var gSession = widget.gSession;
			var params = {
				staffName : gSession.staffName,
				staffCode : gSession.staffCode,
				srvcInstId : "SAOP_GC_ACTION",
				busyTypeCd : "QUERY_GC_SEND_RESULT",
				accNum : widget._getAccNum()
			};
			if (params.accNum) {
				widget.callService("doSaopAction", JSON.stringify(params), function(ret) {
					$("#ResultCode").text(ret.ResultCode);
					$("#ResultDescr").text(ret.ResultDescr);
					$("#MDN").text(ret.MDN);
					$("#GIMSI").text(ret.GIMSI);
					$("#IMSI").text(ret.IMSI);
					$("#ESN").text(ret.ESN);
					$("#OuterRoamAuth").text(ret.OuterRoamAuth);
					$("#MSActStatus").text(ret.MSActStatus);
					$("#GPRSActStatus").text(ret.GPRSActStatus);
					$("#SMSDPF").text(ret.SMSDPF);
					$("#NAM").text(ret.NAM);
					$("#InterSmsOriginating").text(ret.InterSmsOriginating);
					$("#InterSmsTerminating").text(ret.InterSmsTerminating);
					$("#InterVoiceOriginating").text(ret.InterVoiceOriginating);
					$("#InterVoiceTerminating").text(ret.InterVoiceTerminating);
					$("#InterDataService").text(ret.InterDataService);
					$("#ISDN").text(ret.ISDN);
					$("#Charge").text(ret.Charge);
					$("#ActiveIMSI").text(ret.ActiveIMSI);
					$("#MIMSI1").text(ret.MIMSI1);
				});
			}
		}
	});
	vita.widget.register("saopGcAction", saopGcAction, true);
})(window.vita);
