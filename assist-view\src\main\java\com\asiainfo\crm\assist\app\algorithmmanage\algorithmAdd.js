(function (vita) {
    var algorithmAdd = vita.Backbone.BizView.extend({
        events: {
            "click #submitBtn": "_handleSubmit",
            "click #cancelBtn": "_handleClosePage"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
        },
        _handleSubmit: function () {
            var widget = this, element = $(widget.el);
            var params = widget.model.toJSON();
            if (!widget._validate()) {
                return false;
            }
            widget.callService("createAlgorithm", params, function (res) {
                var ret = JSON.parse(res);
                var resultMsg = null, resultObject = null;
                if (ret.resultCode == 0) {
                    resultObject = ret.resultObject;
                    if (resultObject.code != 0) {
                        widget.popup(resultObject.msg || "新增失败");
                        return false;
                    }
                } else {
                    resultMsg = ret.resultMsg || "新增失败";
                }
                if (widget._checkValue(resultMsg)) {
                    widget.popup(resultMsg);
                    return false;
                }
                widget.popup("新增算法成功", function () {
                    widget._handleClosePage();
                });

            }, {
                async: true,
                mask: true
            });
        },
        _checkValue: function (condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty ? false : true;
        },
        _validate: function(){
            var widget = this, element = $(widget.el);
            var algoNbr = widget.model.get("algoNbr");
            if (!widget._checkValue(algoNbr)) {
                widget.popup("算法编码不能为空，请输入算法编码");
                return false;
            }
            return true;
        },
        _handleClosePage: function () {
            var widget = this, element = $(widget.el);
            element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
        }
    });
    vita.widget.register("algorithmAdd", algorithmAdd, true);
})(window.vita);