package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.ISoReceiptSMO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("vita.valueAddedTaxLogQuery")
public class ValueAddedTaxLogQuery extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(ValueAddedTaxLogQuery.class);

    @Autowired
    private IOrderQuerySMO orderQuerySMO;

    @Autowired
    private ISoReceiptSMO soReceiptSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map retMap = new HashMap();
        retMap.put("stateMap", AssistMDA.VALUEADDED_TAX_LOG_STATES);
        return retMap;
    }

    /**
     * 查询增值税专用发票日志列表
     */
    public Map queryValueAddedTaxLogList(String json) throws Exception {
        String valueAddedTaxLogs = soReceiptSMO.qryAddedValueTaxLogListByCond(json);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(valueAddedTaxLogs);
        Map<String, Object> options = Maps.newHashMap();
        List valueAddedTaxLogList = (List) MapUtils.getObject(resultObjectMap, "addedValueTaxLogVoList");
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", total);
        options.put("valueAddedTaxLogs", valueAddedTaxLogList);
        options.put("stateMap",AssistMDA.VALUEADDED_TAX_LOG_STATES);
        return options;
    }
}
