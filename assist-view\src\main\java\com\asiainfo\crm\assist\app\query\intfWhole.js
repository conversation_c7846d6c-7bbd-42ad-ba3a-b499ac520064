(function (vita) {
    var intfWhole = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_intfWhole",
            "click #regionIdBtn": "_chooseArea",
            "click #custIdBtn": "_chooseCust",
            "click #recoverConnect": "_recoverConnect",
            "click #temporaryRecover": "_temporaryRecover",
        },

        _recoverConnect: function () {
        	var widget = this,element = $(widget.el);
        	var users = element.find('table input[name="userInfo"]:checked');
            if(users.length == 0) {
                widget.popup("请选择复机用户！");
                return false;
            }
            var gSession = widget.gSession;
            var userInfo = $(users[0]).data("data");
            //渠道ID
            var createOrgId = $("#channelId").attr("value");
            if(widget._isNullStr(channelId)) {
            	createOrgId = gSession.curChannelId;
            }
            var createStaff = gSession.staffId;
            var param = {
            		userInfo : userInfo,
            		createStaff : createStaff,
            		createOrgId : createOrgId
        		};

            widget.callService("recoverConnect",JSON.stringify(param),function (ret) {
                if (0 == ret.retCode){
                    widget.popup(ret.retDesc)
                    widget._intfWhole();
                    return;
                }else {
                    widget.popup(ret.retDesc)
                    return;
                }
            })
        },
        _temporaryRecover: function () {
        	var widget = this,element = $(widget.el);
        	var gSession = widget.gSession;
        	var users = element.find('table input[name="userInfo"]:checked');
            if(users.length == 0) {
                widget.popup("请选择复机用户！");
                return false;
            }
            var userInfo = $(users[0]).data("data");
            //渠道ID
            var createOrgId = $("#channelId").attr("value");
            if(widget._isNullStr(channelId)) {
            	createOrgId = gSession.curChannelId;
            }
            var createStaff = gSession.staffId;
            var param = {
            		userInfo : userInfo,
            		sysUserId : gSession.systemUserId,
            		createStaff : createStaff,
            		createOrgId : createOrgId
        		};

            widget.callService("temporaryRecover",JSON.stringify(param),function (ret) {
                if (0 == ret.retCode){
                    widget.popup(ret.retDesc)
                    widget._intfWhole();
                    return;
                }else {
                    widget.popup(ret.retDesc)
                    return;
                }
            })
        },

        _initialize: function () {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            var option = {
                id : dialogId,
                url : widget.global.chooseChannel,
                maskClose : false,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);
                   
                }
            };
            widget.dialog(option);

        },
        global: {
            pageIndex: 1,
            pageSize: 5,
            preset: "date",
            chooseArea: "../comm/chooseArea",
            chooseCust: "../comm/chooseCust",
            chooseChannel: "../comm/chooseChannel"
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },

        _chooseCust: function () {
            var widget = this,element = $(widget.el);
            var compCode = "chooseCust";
            var dialogId = "chooseCustDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseCust,
                params : {
                },
                onClose : function(res) {
//                    debugger;
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data || !data.validate) {
                        return false;
                    }
                    element.find("#custId").val(data.custName).attr("value", data.custId);
                }
            });
        },
        _intfWhole: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("queryUserList", JSON.stringify(params), "#userListResult", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if (widget._getConds()) {
                                    widget.refreshPart("queryUserList", JSON.stringify(params), "#userList");
                                }
                                ;
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }else{
                    	widget.popup("未查到相关数据。")
                    	return;
                    }

                }, {
                    async: false
                });
            }
            ;
        },

    
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this;
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            var paramCnt = 0;
            var regionId = $("#regionId").attr("value");
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.regionId = regionId;
            }

            if ($("#c_custId").is(":checked")) {
                paramCnt++;
                var custId = $("#custId").attr("value");
                if(widget._isNullStr(custId)) {
                    widget.popup("请选择客户！");
                    return false;
                } else {
                    params.ownerCustId = custId;
                }
            }

            if ($("#c_accNum").is(":checked")) {
                paramCnt++;
                var accNum = $("#accNum").val();
                if(widget._isNullStr(accNum)) {
                    widget.popup("请输入号码！");
                    return false;
                } else {
                    params.accNum = accNum;
                }
            }

            var statusCd = $("#b_statusCd").val();
//            debugger;
            if (widget._isNullStr(statusCd)) {
                widget.popup("请选择用户状态！");
                return false;
            } else {
            	var list=[]; 
            	if(statusCd == "1"){
            		list.push("150001");
            	}else if(statusCd == "2"){
            		list.push("150002");
            	}else if(statusCd == "3"){
            		list.push("150001");
            		list.push("150002");
            	}else{
            		widget.popup("用户状态错误！");
                    return false;
            	}
            	
                params.stopTypes = list;
            }
            if(paramCnt < 1) {
                widget.popup("客户、号码必选一项!");
                return false;
            }
            return params;
        }
    });
    vita.widget.register("intfWhole", intfWhole, true);
})(window.vita);