(function(vita) {
    var paramDetail = vita.Backbone.BizView.extend({
        events : {
            "click .okbutt" : "_closeBtn",
            "click #traceHis" : "_traceHisQuery",
            "click #orderDetail" : "_orderDetail"

        },
        global: {
            pageSize: 4
        },
        _initialize : function() {
            var widget = this,
                el = $(widget.el);
            var datas = el.data("data");

        }


    });
    vita.widget.register("paramDetail", paramDetail, true);
})(window.vita);

