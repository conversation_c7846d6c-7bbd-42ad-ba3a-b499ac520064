package com.asiainfo.crm.assist.app.query;

import com.al.common.utils.DateUtil;
import com.al.common.utils.StringUtil;
import com.asiainfo.angel.json.JsonConverter;
import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.Constant;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.ICertNumFiveQuerySMO;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.service.intf.IMenuCollectionSMO;
import com.asiainfo.crm.service.intf.IOrderSMO;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.asiainfo.crm.service.intf.IStaffInfoQuerySMO;
import com.asiainfo.crm.util.ListUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

/**
 *
 */
@Component("vita.replaceUIMQuery")
public class ReplaceUIMQuery extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(ReplaceUIMQuery.class);
    @Autowired
    private IOrderSMO orderSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map<String,Object> options = Maps.newHashMap();
        return options;
    }

    public Map replaceUIMQry(String jsonString) throws Exception {
        Map<String,Object> options = Maps.newHashMap();
        JsonConverter jsonConverter = JsonConverter.buildNormalConverter();
        Map reqMap = jsonConverter.toBean(jsonString, Map.class);
        Map<String,Object> resultMap = orderSMO.queryCrmCommonBusi(jsonString);
        options.put("replaceUIMList",resultMap.get("data"));
        options.put("totalNumber",resultMap.get("total"));
        return options;
    }

}
