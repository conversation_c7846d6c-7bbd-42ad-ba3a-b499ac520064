<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="al-external" />
      <option name="name" value="al-external" />
      <option name="url" value="http://10.19.195.99:8081/nexus/content/repositories/AL-External" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="al-snapshot" />
      <option name="name" value="al-snapshot" />
      <option name="url" value="http://10.19.195.99:8081/nexus/content/repositories/AL-Snapshot" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nexus" />
      <option name="name" value="nexus" />
      <option name="url" value="http://10.19.37.81:9090/nexus/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="al-public" />
      <option name="name" value="al-public" />
      <option name="url" value="http://10.19.195.99:8081/nexus/content/groups/AL-Public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://10.19.37.81:9090/nexus/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="al-release" />
      <option name="name" value="al-release" />
      <option name="url" value="http://10.19.195.99:8081/nexus/content/repositories/AL-Release" />
    </remote-repository>
  </component>
</project>