package com.asiainfo.crm.assist.app.algorithmmanage;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ISecuritySmo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 *
 * @Date 2022/1/20 16:04
 */
@Component("vita.algorithmAdd")
public class AlgorithmAdd extends AbstractComponent {

    @Autowired
    private ISecuritySmo securitySmo;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public String createAlgorithm(String params){
        String result = securitySmo.createAlgorithm(params);
        return result;
    }
}
