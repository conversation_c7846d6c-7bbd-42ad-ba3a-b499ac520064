package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.asiainfo.crm.service.intf.IResourceSMO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by wenhy on 2017/7/19.
 */
@Component("vita.recyclingInfoQuery")
public class RecyclingInfoQuery extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(RecyclingInfoQuery.class);

    @Autowired
    private IProdInstSMO prodInstSMO;

    @Autowired
    private IResourceSMO resourceSMO;

    @Autowired
    private IOrderQuerySMO orderQuerySMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map queryRecyclingInfo(String jsonStr) throws Exception {
        Map<String, Object> paramMap = jsonConverter.toBean(jsonStr, Map.class);
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String paramStr = null;
        Map rsRetObject = null;
        //如果接入号不为空则先查接入号对应的prodInstId
        if(null != paramMap.get("accNum")) {
            if(null != paramMap.get("routeParam")) {
                paramMap.remove("routeParam");
            }
            String re = prodInstSMO.qryAccProdInstListLocal(jsonConverter.toJson(paramMap));
            Map resultObject = (Map) resolveResult(re);
            List<Map> prodInsts = (List<Map>) resultObject.get("accProdInsts");
            if(null != prodInsts && prodInsts.size() > 0) {
                Map map = prodInsts.get(0);
                Map rsMap = new HashMap();
                rsMap.put("prodInstId", map.get("prodInstId"));
                rsMap.putAll(jsonConverter.toBean(jsonStr, Map.class));
                String rsRetStr = resourceSMO.queryRecyclingInfo(jsonConverter.toJson(rsMap));
                rsRetObject = (Map) resolveResult(rsRetStr);
                List<Map> recyclingInfo = (List<Map>)rsRetObject.get("recyclingInfo");
                if(null != recyclingInfo && recyclingInfo.size() > 0) {
                    for(Map m : recyclingInfo) {
                        m.put("bindAccNum", paramMap.get("accNum"));
                    }
                    this.convertOrderItemIdToCoNbr(recyclingInfo);
                }
                return rsRetObject;
            } else {
                resultMap.put("resultCode", "-1");
                resultMap.put("resultMsg", "根据接入号未查询到产品实例！");
                return resultMap;
            }
        } else {
            String rsRetStr = resourceSMO.queryRecyclingInfo(jsonStr);
            rsRetObject = (Map) resolveResult(rsRetStr);
            List<Map> recyclingInfo = (List<Map>)rsRetObject.get("recyclingInfo");
            if(null != recyclingInfo && recyclingInfo.size() > 0) {
                Map param = new HashMap();
                for (Map map : recyclingInfo) {
                    if (null != map.get("prodInstId")) {
                        param.put("prodInstId", map.get("prodInstId"));
                        String re = prodInstSMO.qryAccProdInstListLocal(jsonConverter.toJson(param));
                        Map resultObject = (Map) resolveResult(re);
                        List<Map> prodInsts = (List<Map>) resultObject.get("accProdInsts");
                        if(null != prodInsts && prodInsts.size() > 0) {
                            Map prodInst = prodInsts.get(0);
                            map.put("bindAccNum", prodInst.get("accNum"));
                        }
                    }
                }
                this.convertOrderItemIdToCoNbr(recyclingInfo);
            }
        }
        return rsRetObject;
    }

    private void convertOrderItemIdToCoNbr(List<Map> recyclingInfo) throws IOException {
        if(null != recyclingInfo && recyclingInfo.size() > 0) {
            for(Map map : recyclingInfo) {
                if(null != map.get("orderItemId")) {
                    Long orderItemId = Long.valueOf(map.get("orderItemId").toString());
                    List<Long> orderItemIds = new ArrayList<>();
                    orderItemIds.add(orderItemId);
                    String re = orderQuerySMO.queryOrderItemsByOrderItemsIds(jsonConverter.toJson(orderItemIds));
                    List<Map> resultObject = (List<Map>)resolveResult(re);
                    if(null != resultObject && resultObject.size() > 0) {
                        Map inParam = new HashMap();
                        inParam.put("custOrderId", resultObject.get(0).get("custOrderId"));
                        String coStr = orderQuerySMO.qryCustOrderByCoIdOrCoNbr(jsonConverter.toJson(inParam));
                        Map custOrder = (Map)resolveResult(coStr);
                        map.put("coNbr", custOrder.get("custOrderNbr"));
                    }
                }
            }
        }
    }

}
