package com.asiainfo.crm.assist.app.orgCust;

import com.asiainfo.crm.bcomm.exception.BError;
import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.service.intf.IImportBatchOrgMultiSmo;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 导入文件下载
 * <AUTHOR>
 */
@Component("vita.orgCustDetail")
public class OrgCustDetail extends AbstractSoComponent {

    @Autowired
    private IImportBatchOrgMultiSmo smo;

    @Override
    public Map achieveData(Object... objects) throws Exception {
        String json = objects[0].toString();

        Map options = jsonConverter.toBean(json, Map.class);
        return options;
    }

    public Map<String, Object> queryOrgDetailInfos(String jsonStr) {
        Map<String, Object> retMap = new HashMap<String, Object>();
        String resultJson = "";
        try {
            resultJson = smo.queryOrgDetailInfos(jsonStr);
            if (resultJson.isEmpty()) {
                retMap.put("orgDtVoList", null);
                return retMap;
            }
            Map<String, Object> resultObject = (Map<String, Object>) resolveResult(resultJson);

            Map<String, Object> pageInfoMap = (Map<String, Object>) org.apache.commons.collections.MapUtils.getObject(resultObject,"pageInfo");
            int totalCount = org.apache.commons.collections.MapUtils.getIntValue(pageInfoMap, "totalCount");
            int pageIndex = org.apache.commons.collections.MapUtils.getIntValue(pageInfoMap, "pageIndex");
            int pageSize = org.apache.commons.collections.MapUtils.getIntValue(pageInfoMap, "pageSize");
            retMap.put("pageCount",pageIndex);
            retMap.put("pageIndex",pageSize);
            retMap.put("totalNumber", totalCount);
            retMap.put("orgDtVoList", MapUtils.getObject(resultObject,"orgDtVoList"));
            return retMap;
        } catch (Exception e) {
            retMap.put("retCode", "-1");
            if (e instanceof BError) {
                BError bError = (BError) e;
                retMap.put("retDesc", bError.getMsg());
            } else {
                retMap.put("retDesc", e.getMessage());
            }
            return retMap;
        }
    }


}
