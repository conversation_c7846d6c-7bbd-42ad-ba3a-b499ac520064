package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.service.intf.IAgencyCapitalSMO;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by pushuo on 2017/8/17.
 */
@Component("vita.auditChannel")
public class AuditChannel extends AbstractSoComponent {

    @Autowired
    private IAgencyCapitalSMO agencyCapitalSMO;

    @Override
    public Map achieveData(Object... param) throws Exception {
        String jsonString = param[0].toString();
        Map options = new HashMap();
        List channelList = this.queryChannelOfAuditGroup(jsonString);
        options.put("channelList",channelList);
        return options;
    }

    public List queryChannelOfAuditGroup(String json) throws Exception{
        String channelInfo = agencyCapitalSMO.queryChannelOfAuditGroup(json);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(channelInfo);
        List channelList = (List) MapUtils.getObject(resultObjectMap, "channelList");
        return channelList;
    }
}
