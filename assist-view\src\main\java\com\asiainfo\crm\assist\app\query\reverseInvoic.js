(function (vita) {
    var reverseInvoic = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_qryInvoiceListByCoNbr",
            "click #btn-cancel":"_deleteReceipt"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
        },
        global: {
            pageIndex: 1,
            pageSize: 10
        },
        _qryInvoiceListByCoNbr: function () {
			var widget = this;
            var gsession = widget.gSession;
            var coNbr = $.trim($("#coNbr").val());
            if(coNbr== null || coNbr==""){
                widget.popup("请输入购物车流水号！");
                return;
            }
            var params = {
                custOrderNbr: coNbr,
                //receiptType=2代表的是发票，根据流水号查询，只展示发票
                receiptType:"2"
            };
            widget.refreshPart("qryInvoiceListByCoNbr", JSON.stringify(params), "#invoiceResult", function (re) {

            }, { async: false });
        },
        _deleteReceipt: function () {
            var widget = this,
                element = $(widget.el);
            gSession = widget.gSession;
            var soUtil = widget.require("soUtil");
            var counts = element.find('table input[name="payment"]:checked');
            if(counts.length == 0) {
                widget.popup("请选择需要作废的发票!");
                return false;
            }
            element.find("#invoiceList :checkbox").each(function (i, oi) {
                var o = $(oi);
                if(o.is(":checked")){
                    o.data("data");
                    var resultMsg = "";
                    var data = widget.model.toJSON();
                    var param = {
                        "receiptType" : o.data("data").receiptType,
                        "statusCd" :o.data("data").statusCd,
                        "receiptId" : o.data("data").receiptId,
                        "commonRegionId":o.data("data").commonRegionId,
                        "custOrderNbr":o.data("data").custOrderNbr,
                        "staffId":o.data("data").staffId,
                    }
                    widget.callService("deleteReceipt", o.data("data"), function(res) {
                        var ret = JSON.parse(res);
                        if (ret.resultCode == 0) {
                            widget._qryInvoiceListByCoNbr();
                            widget.popup("作废发票成功")
                        } else {
                            resultMsg = ret.resultMsg;
                            widget.popup(resultMsg);
                        }
                    }, {
                        async : true,
                        mask : true
                    });
                }
            });
        },

    });
    vita.widget.register("reverseInvoic", reverseInvoic, true);
})(window.vita);