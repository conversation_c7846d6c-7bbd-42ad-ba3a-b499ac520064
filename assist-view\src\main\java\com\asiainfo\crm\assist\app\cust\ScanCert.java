package com.asiainfo.crm.assist.app.cust;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.common.Constant;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.util.JsCommUtil;
import com.asiainfo.crm.util.ListUtil;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 扫描证件
*/
@Component("vita.scanCert")
public class ScanCert extends AbstractSoComponent {
	
	@Autowired
	private ICustMultiSMO custMultiSMO;


	@Override
	public Map achieveData(Object... param) throws Exception {
		String jsonString = param[0].toString();
		Map options = jsonConverter.toBean(jsonString, Map.class);
		options.put("useDeskTop",MDA.CONST_USESECURE_DESKTOP);
		options.put("newPermantOn", MDA.NEW_PERMANENT_CERT_ON);
		return options;
	}
	
	
	public Map qryCustomerList(String jsonString) throws Exception {
		String resString = custMultiSMO.qryCustomerListForMulti(jsonString);
		Map resultMap = jsonConverter.toBean(resString, Map.class);
		Map retMap = new HashMap();
		List customers = new ArrayList();
		long totalNumber = 0L;
		String resultCode = MapUtils.getString(resultMap, "resultCode");
		if (!StringUtil.isEmpty(resultCode) && resultCode.equals(MDA.RESULT_SUCCESS.toString())) {
			Map resultObject = (Map) resultMap.get("resultObject");
			customers = (List) resultObject.get("customers");
			Map pageInfo = (Map) resultObject.get("pageInfo");
			String rowCount = MapUtils.getString(pageInfo, "rowCount");
			if (!StringUtil.isEmpty(rowCount)) {
				totalNumber = Long.valueOf(rowCount);
			}
		}
		if (Constant.LOGIC_STR_Y.equals(MDA.NEW_PERMANENT_CERT_ON) && ListUtil.isListEmpty(customers)) {
			//根据新卡证件号码查询客户，若无客户资料，则根据旧卡号码进行客户查询
			Map map = jsonConverter.toBean(jsonString, Map.class);
			String certNumber = MapUtils.getString(map, "value");
			String oldCertNumber = MapUtils.getString(map, "oldCertNumber");
			if (JsCommUtil.isNewPermanentCert(oldCertNumber, certNumber)) {
				map.put("value", oldCertNumber);
				String oldStr = custMultiSMO.qryCustomerListForMulti(jsonConverter.toJson(map));
				Map result = (Map) resolveResult(oldStr);
				customers = (List) result.get("customers");
				Map pageInfo = (Map) result.get("pageInfo");
				String rowCount = MapUtils.getString(pageInfo, "rowCount");
				if (!StringUtil.isEmpty(rowCount)) {
					totalNumber = Long.valueOf(rowCount);
				}
			}
		}
		retMap.put("totalNumber", totalNumber);
		retMap.put("customers", customers);
		return retMap;
	}
}