package com.asiainfo.crm.assist.app.action;

import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.common.AbstractComponent;

/**
 * Created by Administrator on 2017-7-26.
 */
@Component("vita.protocolDisplay")
public class ProtocolDisplay extends AbstractComponent {

	private static final Logger logger = LoggerFactory.getLogger(ProtocolDisplay.class);

	private static String ymd = "false";
	@Override
	public Map achieveData(Object... objects) throws Exception {
		Map options = new HashMap();
		String jsonString = objects[0].toString();
		Map map = jsonConverter.toBean(jsonString, Map.class);
		String str = (String) map.get("str").toString();

		// 转成dom对象
		Document document = DocumentHelper.parseText(str);
		// 获取根节点
		Element root = document.getRootElement();
		StringBuffer contentView = new StringBuffer();
		this.getNodes(root, contentView);
		if (contentView.length() > 0) {
			if ("true".equals(ymd)) {
				 Calendar    rightNow    =    Calendar.getInstance();     
			        /*用Calendar的get(int field)方法返回给定日历字段的值。 
			        HOUR 用于 12 小时制时钟 (0 - 11)，HOUR_OF_DAY 用于 24 小时制时钟。*/  
			        Integer year = rightNow.get(Calendar.YEAR);   
			        Integer month = rightNow.get(Calendar.MONTH)+1; //第一个月从0开始，所以得到月份＋1  
			        Integer day = rightNow.get(rightNow.DAY_OF_MONTH);  
				contentView.append("</br><div style='float:right' ><input type='text' size='7' value="+year+">年<input type='text' size='7' value="+month+">月<input size='7' type='text' value="+day+">日</div></div>");
				ymd="false";
			}else{
				contentView.append("</div>");
			}
		}
		options.put("str", contentView.toString());
		return options;
	}

	/**
	 * 从指定节点开始,递归遍历所有子节点
	 */
	public void getNodes(Element node, StringBuffer contentView) {
		// 当前节点的名称、文本内容和属性
		String nodeName = node.getName();
		String nodeText = node.getTextTrim();
		if ("title".equals(nodeName)) {
			contentView.append("<div style='text-indent:25px;text-align:center;font-size:large;font-weight:bold'>")
					.append(nodeText);
		} else if ("ymd".equals(nodeName)) {
			if ("true".equals(nodeText)) {
				ymd="true";
			}
		} else if ("section".equals(nodeName)) {
			contentView.append("</div><div style='text-indent:25px'>");
		} else if ("text".equals(nodeName)) {
//			contentView.append(nodeText); 
			// 当前节点的所有属性的list
			List<Attribute> listAttr = node.attributes();
			String boo = "false";
			String boos = "true";
			// 遍历当前节点的所有属性
			for (Attribute attr : listAttr) {
				// 属性名称
				String attrName = attr.getName();
				// 属性的值
				String attrValue = attr.getValue();
				
				//字体是否加粗
				if ("bold".equals(attrName) && "true".equals(attrValue)) {
					contentView.append("<font style='font-weight:bold'>"+nodeText+"</font> ");
					boo = "true";
				}else if ("colour".equals(attrName) && "red".equals(attrValue)) {
					contentView.append("<font color='red'>"+nodeText+"</font>");
					boo = "true";
				} //字体是否加红 加粗 
				else if ("colour".equals(attrName) && "redBold".equals(attrValue)) {
					contentView.append("<font color='red' style='font-weight:bold'>"+nodeText+"</font>");
					boo = "true";
				}else if(boo.equals("false")){
					contentView.append(nodeText);
					boo = "true";
				}
				
				 if ("editable".equals(attrName) && "true".equals(attrValue)) {
					 contentView.append("<input  type='text' ");
				} else if ("width".equals(attrName)) {
					contentView.append("style='width :").append(attrValue+"px;").append("' />");
				}
				 boos = "false";
			}
			if ("true".equals(boos)) {
				contentView.append(nodeText);
			}
		}

		// 递归遍历当前节点所有的子节点
		List<Element> listElement = node.elements();
		// 遍历所有一级子节点
		for (Element e : listElement) {
			// 递归
			this.getNodes(e, contentView);
		}
	}

}
