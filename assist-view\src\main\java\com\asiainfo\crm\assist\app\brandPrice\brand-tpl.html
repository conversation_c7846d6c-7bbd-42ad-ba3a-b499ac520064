<div data-widget="brand">
    <vita-data>{"data":$options}</vita-data>
    <p class="vita-data">$options</p>
    <div class="sypage-nav">
        <div class="nav_alink"><strong class="text16">品牌</strong></div>
    </div>
    <div class="sypage-min">
        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">查询条件</strong></div>
        </div>
        <div class="sypagemin_content ">
            <form>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">品牌编码</label>
                    <div class="col-md-8">
                        <input type="text" class="form-control" placeholder="" id="brandCode">
                        <p class="vita-bind" model="brandCode"></p>
                    </div>
                </div>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">品牌类型</label>
                    <div class="col-md-8">
                        <input type="text" class="form-control" placeholder="" id="brandName">
                        <p class="vita-bind" model="brandName"></p>
                    </div>
                </div>

                <div class="form-group col-md-12">
                    <div class="col-md-12 searchbutt_r" align="right">
                        <button type="button" class="btn btn-default" id="searchDel">清除</button>
                        <button type="button" class="btn btn-primary" id="qryCommonLogSearchBtn">搜索</button>
                    </div>
                </div>

            </form>
        </div>

        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">品牌列表</strong></div>
        </div>
        <div class="sypagemin_content ">
            <div class="col-lg-12 mart10">
                <table class="table table-hover" id="systemRoleList">
                    <thead>
                    <tr>
                        <th>选择</th>
                        <th>品牌编码</th>
                        <th>品牌名称</th>
                        <th>品牌描述</th>
                     <!--   <th>状态</th>-->
                        <th>创建时间</th>
                    </tr>
                    </thead>
                    <tbody>
                    #if($options != "null" && $options.brands && $options.brands != "null" && $options.brands.size() > 0)
                    #foreach($brand in $options.brands)
                    <tr >
                        <td>
                            <label class="wu-radio full absolute" data-scope="">
                                <input type="radio" name="moduleRadio" />
                                <p class="vita-data">{"data":$brand,"totalNumber":$options.totalNumber}</p>
                            </label></td>
                        <td>$!brand.brandCode</td>
                        <td>
                            <a class="textcolorgreen" link="brandRel" data-toggle="tooltip" data-placement="top">$!brand.brandName</a>
                            <p class="vita-data">{"brandId" : $!brand.brandId}</p>
                        </td>
                       <!-- <td>$!brand.brandName</td>-->
                        <td>$!brand.brandDesc</td>
                     <!--   <td>#if($!offerInformProg.statusCd == 1000)有效
                            #elseif($!offerInformProg.statusCd == 1100)无效
                            #end
                        </td>-->
                        <td>$!brand.createDate</td>
                    </tr>
                    #end
                    #end
                    </tbody>
                </table>
                <!--翻页start -->
                <div class="page-box" id="showPageInfo">
                </div>
                <!--翻页end -->
            </div>
        </div>
    </div>
</div>