(function (vita) {
    var qryArchivesDetailInfo = vita.Backbone.BizView.extend({
        events: {
            "click #operDiv button": "_downLoadOper",//经办人图片下载
            "click #receiptDownload": "_downLoadReceipt",//回执下载
            "click #sendEmail": "_sendEmail",//发送邮件
            "mouseover #operDiv div[name=operShowDiv]": "_showText",//显示图片类型
            "mouseout #operDiv div[name=operShowDiv]": "_hideText"//隐藏图片类型
        },
        _initialize: function () {
            var widget = this, el = $(widget.el), data = el.data("data"), element = $(widget.el);
            var data = el.data("data");
            var custOrderId = data.custOrderId;
            var custOrderNbr = data.custOrderNbr;
            var params = {"custOrderId": custOrderId, "custOrderNbr": custOrderNbr};
            if (widget.global.currentType == data.operClick) {
                // 经办人照片
                params.qryType = "oper";
                params.element = "#operDiv";
                widget._qryArchivesDetailInfo(params);
            } else if (widget.global.currentType == data.receiptClick) {
                // 回执
                params.qryType = "receipt";
                params.element = "#receiptDiv";
                widget._qryArchivesDetailInfo(params);
                var aObj = element.find("#go");
                if (aObj && aObj.length > 0) {
                    var src = widget.global.downLoadUrl + "?custOrderNbr=" + custOrderNbr + "&type=R";
                    var targetUrl = encodeURI(src);
                    aObj.attr("href", targetUrl);
                    aObj.find("span").trigger("click");
                }
            }
        },
        global: {
            currentType: "Y",
            downLoadUrl: "../query/downLoadArchivesInfo"
        },
        _qryArchivesDetailInfo: function (params) {
            var widget = this;
            if (params) {
                widget.refreshPart("qryArchivesDetailInfo", JSON.stringify(params), params.element, function (res) {
                }, {
                    async: false
                });
            }
        },
        _downLoadOper: function (e) {
            var widget = this,
                element = $(widget.el);
            var btn = $(e.currentTarget);
            var custOrderNbr = btn.data("custOrderNbr");
            var accNum = btn.data("accNum");
            var busiType = btn.data("busiType");
            var params = {
                "custOrderNbr": custOrderNbr,
                "accNum": accNum,
                "type": busiType
            };
            if (params) {
                widget._downLoad(params);
            }
        },
        _downLoadReceipt: function (e) {
            var widget = this,
                element = $(widget.el);
            var btn = $(e.currentTarget);
            var custOrderNbr = btn.data("custOrderNbr");
            var busiType = btn.data("busiType");
            var params = {
                "custOrderNbr": custOrderNbr,
                "type": busiType
            };
            if (params) {
                widget._downLoad(params);
            }
        },
        _downLoad: function (params) {
            var widget = this;
            var src = widget.global.downLoadUrl + "?custOrderNbr=" + params.custOrderNbr + "&accNum=" + params.accNum + "&type=" + params.type;
            var targetUrl = encodeURI(src);
            window.open(targetUrl, "_blank");
        },
        _isNullStr: function (str) {
            if (str == null || str == "") {
                return true;
            }
            return false;
        },
        _sendEmail: function (e) {
            var widget = this,
                element = $(widget.el);
            var btn = $(e.currentTarget);
            var custOrderNbr = btn.data("custOrderNbr");
            if (widget._isNullStr(custOrderNbr)) {
                widget.popup("订单流水为空!");
                return false;
            }
            var emailAddrInput = element.find("#emailAddr");
            var emailAddr = emailAddrInput.val();
            if (widget._isNullStr(emailAddr)) {
                widget.popup("邮箱地址为空!");
                return false;
            } else if (!widget._isAddress(emailAddr, emailAddrInput)) {
                return false;
            }
            var params = {
                "olId": custOrderNbr,
                "email": emailAddr
            };
            if (params) {
                widget.callService("sendEmail", JSON.stringify(params), function (res) {
                    if (res.resultCode != 0) {
                        widget.popup("邮件发送失败，原因：" + res.resultMsg);
                    } else {
                        widget.popup("邮件发送成功！");
                    }
                }, {
                    async: false
                });
            }
        },
        _isAddress: function (str, input) {
            var widget = this,
                element = $(widget.el);
            var reg = new RegExp("[\\w!#$%&'*+/=?^_`{|}~-]+(?:\\.[\\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\\w](?:[\\w-]*[\\w])?\\.)+[\\w](?:[\\w-]*[\\w])?");
            if (!reg.test(str)) {
                widget.popup("邮箱格式不正确，请重新输入！");
                input.val("");
                return false;
            } else {
                return true;
            }
        },
        _showText: function (e) {
            var widget = this,
                element = $(widget.el);
            var divObj = $(e.currentTarget);
            var spanObj = divObj.find("span:first");
            spanObj.attr("style", "color: darkseagreen;display:inline;position:absolute;");
        },
        _hideText: function (e) {
            var widget = this,
                element = $(widget.el);
            var divObj = $(e.currentTarget);
            var spanObj = divObj.find("span:first");
            spanObj.attr("style", "display:none");
        }
    });
    vita.widget.register("qryArchivesDetailInfo", qryArchivesDetailInfo, true);
})(window.vita);