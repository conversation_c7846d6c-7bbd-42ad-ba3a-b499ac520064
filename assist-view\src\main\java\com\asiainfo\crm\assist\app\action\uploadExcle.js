(function (vita) {
    var uploadExcle = vita.Backbone.BizView.extend({
        events: {
            "click #m_file":"_selectExcel",
            "click #upload_excel":"_uploadExcel",
            "click #out_excel":"_pcExcelPrint",
            "change .m_file":"_fileOnChange"
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        global: {
            pageIndex: 1,
            pageSize: 10,
            isChanged : false
        },
        _fileOnChange:function () {
            isChanged = true;
            var filename = $(".m_file").get(0).files[0].name;
            $('#m_file span').html(filename);
        },
        _selectExcel:function () {
            return $('.m_file').click();
        },
        _uploadExcel:function () {
            var widget = this;
            var m_fileVal = $('.m_file').val();
            if(m_fileVal=="") {
                widget.popup('请选择文件！');
                return ;
            }
            if('-1' == m_fileVal.indexOf('.xls')) {
                widget.popup('只能上传Excel文件！');
                return ;
            }
            var gsession = widget.gSession;
            var params = {
                areaId: gsession.staffRegionId,
                staffId: gsession.staffId
            }
            $('.route_input').val(JSON.stringify(params));
            var url = "../action/uploadExcles";
            $('#m_form').ajaxSubmit({
                type:'post',
                url:url,
                success:function(jsonData){
                    var data = JSON.parse(jsonData);
                    debugger
                    if(data.resultObject.resultCode == 0){
                        var dataInfo = data.resultObject.dataInfo[0];
                        localStorage.setItem('marking',dataInfo.marking);
                        alert('操作批次号：'+dataInfo.marking+' 成功'+
                            dataInfo.successSum+'个; 失败'+dataInfo.errorSum+'个');
                    }else {
                        alert('上传失败，请检查内容是否合法');
                    }
                },
                error:function(e){
                    alert('上传失败');
                }
            })
        },
        _pcExcelPrint:function () {
            var url = "../action/exportUploadExcles";
            var marking = localStorage.getItem('marking');
            var params = {
                marking: marking
            }
            location.href=url+"?jsonStr="+marking;
        },
        
    });
    vita.widget.register("uploadExcle", uploadExcle, true);
 })(window.vita);