(function(vita){
    var billMaintain = vita.Backbone.BizView.extend({
        events : {
            "click #billQuery":"_billQuery",
            "click #billUpdate":"_billUpdate",
            "click #archGrpId":"_changeData",
            "click tr[name=showBill]":"_showBill",
            "click tr[name=billObjVos]":"_showbillObjVos",
            "click #changeStatus":"_changeStatus",
            "click tr[name=ordProdInsts]":"_qryTimeQuantum"
        },
        _initialize:function(){
            var widget = this,
                element = $(widget.el);
            var datetime = widget.require("datetime");
            var beginDate = element.find("input[name='finishDate']");
            var now = widget._getNowFormatDate();
            beginDate.val(now);
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    timeFormat: "HH:ii:ss ",
                    timeWheels:"HHiiss"
                });
            }
            ;
            
        },
        global:{
        	preset: "datetime",
            pageIndex: 1,
            pageSize: 5,
            preset: "date"
        },
        _billQuery :function(){
        	var widget = this,
			element = $(widget.el);
        	var condChoose = element.find("#condChoose").val();
        	var condDesc = element.find("#condDesc").val();
			if (typeof(condDesc) == "undefined" || condDesc ==null || condDesc =="" )
			{
				widget.popup("查询条件不能为空");
        	}else{
        		 if(!isNaN(condDesc)){
        			 var rst_param = {
        					 condChoose : condChoose,
        					 condDesc : condDesc
        			 }
        			
        			 widget.refreshPart("billQuery", JSON.stringify(rst_param), "#billMaintainList", function (res) {
        				 var data = $(res).find("#billMaintainListR").data("data");
        				 var dataAll = $(res).find("#billMaintainListR").data("data");
        				 widget.model.set("bill",data);
        				 if(data.billData.length>0){
        					
        					$(element.find("#billObj")).show();
        					 widget.refreshPart("showordBillObjVos", JSON.stringify(rst_param), "#billMaintainListM", function (res) {
        	        				
                			 }, {
                	                async: false
                	         });
        				 }else{
        					$(element.find("#billObj")).hide();
        				 }
        			 }, {
        	                async: false
        	            });
        		 }else{
        			   widget.popup("请输入正确的查询条件");
        		 }
        	}
        },
        _billUpdate :function(){
        	debugger;
        	var widget = this,
			element = $(widget.el);
        	var gSession = widget.gSession;
        	var a = widget.model.get("bill");
        	var showBillval = element.find("tr[name=showBill]");
        	var billInfo ="";
        	var procCntinput = "";
        	var length = showBillval.length;
        	for(var i=0;i<length;i++){
        		var x = $(showBillval[i]).find("td[name=billInfo]").text();
        		var m = $(showBillval[i]).find("#procCntinput").val();
        		var h = $(showBillval[i]).find("#finishDate").val();
        		for(var f=0;f<a.billData.length;f++){
        			a.billData[f].updateStaff = gSession.staffId;
        			var n = a.billData[f].ordBill.archGrpId;
        			if(x==n){
        				a.billData[f].ordBill.procCnt = m;
        				a.billData[f].ordBill.finishDate = h;
        				break;
        			}
        		}
        	}
        	
        	var billObj = element.find("#billObj");
        	var billObjval = $(billObj).find("tbody[name=billtbody]");
        	for(var i=0;i<billObjval.length;i++){
        		var billObjValMore = $(billObjval[i]);
        		var orderItemId = billObjValMore.find("td[name=billtbodyyz]").text();
        		var billInfoStatus = billObjValMore.find("td[name=billInfoStatus]").text();
        		for(var f=0;f<a.billData.length;f++){
        			var n = a.billData[f].ordBillObjVos;
        			for(var m=0;m<n.length;m++){
        				if(orderItemId == n[m].orderItemId){
        					var ordProdInsts = billObjValMore.find("tr[name=ordProdInsts]");
        					var ordProdInstAttrs = billObjValMore.find("tr[name=ordProdInstAttrs]");
        					var ordProdInstAcctRels = billObjValMore.find("tr[name=ordProdInstAcctRels]");
        					var ordOfferInsts = billObjValMore.find("tr[name=ordOfferInsts]");
        					var ordOfferInstAttrs = billObjValMore.find("tr[name=ordOfferInstAttrs]");
        					var billordProdInsts = n[m].ordProdInsts;
        					var billordProdInstAttrs = n[m].ordProdInstAttrs;
        					var billordProdInstAcctRels = n[m].ordProdInstAcctRels;
        					var billordOfferInsts = n[m].ordOfferInsts;
        					var billordOfferInstAttrs = n[m].ordOfferInstAttrs;
        					n[m].statusCd = billInfoStatus;
        					if(billordProdInsts!==null){
        						for(var d=0;d<ordProdInsts.length;d++){
            						var rowid = $(ordProdInsts[d]).find("td[name=ordProdInstsrowid]").text();
            						var ownerCustId = $(ordProdInsts[d]).find("#ownerCustId").val();
            						var useCustId = $(ordProdInsts[d]).find("#useCustId").val();
            						for(var t=0;t<billordProdInsts.length;t++){
            							if(rowid == billordProdInsts[t].rowId){
            								billordProdInsts[t].ownerCustId = ownerCustId;
            								billordProdInsts[t].useCustId = useCustId;
            							}
            						}
            						
            					}
        					}
        					
        					if(billordProdInstAttrs!==null){
        						for(var d=0;d<ordProdInstAttrs.length;d++){
            						var rowid = $(ordProdInstAttrs[d]).find("td[name=ordProdInstAttrsrowid]").text();
            						var ordProdInstAttrsAttrValue = $(ordProdInstAttrs[d]).find("#ordProdInstAttrsAttrValue").val();
            						for(var t=0;t<billordProdInstAttrs.length;t++){
            							if(rowid == billordProdInstAttrs[t].rowId){
            								billordProdInstAttrs[t].attrValue = ordProdInstAttrsAttrValue;
            							}
            						}
            						
            					}
        					}
        					
        					
        					if(billordProdInstAcctRels!==null){
        						for(var d=0;d<ordProdInstAcctRels.length;d++){
            						var rowid = $(ordProdInstAcctRels[d]).find("td[name=ordProdInstAcctRelsrowid]").text();
            						var acctId = $(ordProdInstAcctRels[d]).find("#acctId").val();
            						for(var t=0;t<billordProdInstAcctRels.length;t++){
            							if(rowid == billordProdInstAcctRels[t].rowId){
            								billordProdInstAcctRels[t].acctId = acctId;
            							}
            						}
            						
            					}
        					}
        					
        					if(billordOfferInsts!==null){
        						for(var d=0;d<ordOfferInsts.length;d++){
            						var rowid = $(ordOfferInsts[d]).find("td[name=ordOfferInstsrowid]").text();
            						var effDate = $(ordOfferInsts[d]).find("#effDate").val();
            						var expDate = $(ordOfferInsts[d]).find("#expDate").val();
            						for(var t=0;t<billordOfferInsts.length;t++){
            							if(rowid == billordOfferInsts[t].rowId){
            								billordOfferInsts[t].effDate = effDate;
            								billordOfferInsts[t].expDate = expDate;
            							}
            						}
            						
            					}
        					}
        					
        					if(billordOfferInstAttrs!==null){
        						for(var d=0;d<ordOfferInstAttrs.length;d++){
            						var rowid = $(ordOfferInstAttrs[d]).find("td[name=ordOfferInstAttrsrowid]").text();
            						var ordOfferInstAttrsAttrValue = $(ordOfferInstAttrs[d]).find("#ordOfferInstAttrsAttrValue").val();
            						
            						for(var t=0;t<billordOfferInstAttrs.length;t++){
            							if(rowid == billordOfferInstAttrs[t].rowId){
            								billordOfferInstAttrs[t].attrValue = ordOfferInstAttrsAttrValue;
            							}
            						}
            						
            					}
        					}
        					
        				}
        			}
        			
        		}
        	}
            widget.callService("billUpdate",JSON.stringify(a),function (res) {
            	var ret = JSON.parse(res);
                if (ret.resultCode == 0) {
                	 widget.popup("修改成功!");
                	 widget._billQuery();
                } 
                else {
                	widget.popup(ret.resultMsg);
                    return false;
                }
            },{async: false});
           
        },
        _showBill :function(e){
        	var widget = this,element = $(widget.el);
        	var td = $(e.target).closest("td");
        	var tr = td[0].parentElement;
        	var id = $(tr).find("td[name=billInfo]").text();
            var data = widget.model.get("bill");
        	var billData = data.billData;
        	for(var i=0;i<billData.length;i++){
        		if(id == billData[i].ordBill.archGrpId ){
        			 var rst_param = {
        					 ordBillObjVos : billData[i].ordBillObjVos
        			 } 
        			 widget.refreshPart("showordBillObjVos", JSON.stringify(rst_param), "#billMaintainListM", function (res) {
        				
        			 }, {
        	                async: false
        	         });
        		}
        	}
        },
        _changeStatus :function(e){
           debugger;
           var widget = this,element = $(widget.el);
           var td = $(e.target).closest("td");
           var tr = td[0].parentElement;
           var orderItemId = $(tr).find("td[name=billInfoObj]").text();
           
           var isChange = widget.model.get("isChange");
             	var billInfoStatus = $(tr).find("td[name=billInfoStatus]").text();
               	var tbody = $(e.target).closest("tbody");
                var billtbody = element.find("tbody[name=billtbody]");
                for(var i=0;i<billtbody.length;i++){
                 	var billtbodyyz = $(billtbody[i]).find("td[name=billtbodyyz]").text();
         			var a = widget.model.get($(billtbody[i]).find("td[name=billtbodyyz]").text());
         			var b = $(billtbody[i]).find("td[name=billInfoStatus]").text();
                 	if(orderItemId == billtbodyyz && a!='1' && b!='D'){
                 		$(billtbody[i]).find("td[name=billInfoStatus]").text('D');
                 		$(tr).find("td[name=billShowStatus]").text('D');
                 		widget.model.set($(billtbody[i]).find("td[name=billtbodyyz]").text(),"1");
              			$(billtbody[i]).toggle();
                 		widget.popup("屏蔽成功");
                    }else if( orderItemId == billtbodyyz &&  b=='D'){
                    	widget.model.set($(billtbody[i]).find("td[name=billtbodyyz]").text(),"0");
                    	$(billtbody[i]).find("td[name=billInfoStatus]").text('N');
                    	$(tr).find("td[name=billShowStatus]").text('N');
              			$(billtbody[i]).toggle();
              			widget.popup("屏蔽取消");
                    }
                 }
        },
        _showbillObjVos :function(e){
        	var widget = this,element = $(widget.el);
        	var td = $(e.target).closest("td");
        	var tr = td[0].parentElement;
        	var orderItemId = $(tr).find("td[name=billInfoObj]").text();
            var tbody = $(e.target).closest("tbody");
            var billtbody = element.find("tbody[name=billtbody]");
            for(var i=0;i<billtbody.length;i++){
            	var billtbodyyz = $(billtbody[i]).find("td[name=billtbodyyz]").text();
            	var billInfoStatus  = $(billtbody[i]).find("td[name=billInfoStatus]").text();
            	if(orderItemId == billtbodyyz){
            	 $(billtbody[i]).toggle();
                }
            }
        },
        _getNowFormatDate: function() {
            var date = new Date();
            var seperator1 = "-";
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var strDate = date.getDate();
            var hour = date.getHours();
            var mins=date.getMinutes();
            var seconds = date.getSeconds();
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            if (strDate >= 0 && strDate <= 9) {
                strDate = "0" + strDate;
            }
            if (mins >= 0 && mins <= 9) {
                mins = "0" + mins;
            }
            if (seconds >= 0 && seconds <= 9) {
                seconds = "0" + seconds;
            }
            if (hour >= 0 && hour <= 9) {
                hour = "0" + hour;
            }
            var currentdate = year + seperator1 + month + seperator1 + strDate
                +" 00" + ":00" +":00";
            return currentdate;
        }
    });
    vita.widget.register("billMaintain", billMaintain, true);
}
)(window.vita);