<div data-widget="systemInfo">
    <vita-data>{"data":$options}</vita-data>
    <p class="vita-data">$options</p>
    <div class="sypage-nav">
        <div class="nav_alink"><strong class="text16">系统信息</strong></div>
    </div>
    <div class="sypage-min">
        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">查询条件</strong></div>
        </div>
        <div class="sypagemin_content ">
            <form>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">系统名称</label>
                    <div class="col-md-8">
                        <input type="text" class="form-control" placeholder="" id="systemName">
                        <p class="vita-bind" model="systemName"></p>
                    </div>
                </div>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">系统编码</label>
                    <div class="col-md-8">
                        <input type="text" class="form-control" placeholder="" id="systemNbr">
                        <p class="vita-bind" model="systemNbr"></p>
                    </div>
                </div>

                <div class="form-group col-md-12">
                    <div class="col-md-12 searchbutt_r" align="right">
                        <button type="button" class="btn btn-default" id="searchDel">清除</button>
                        <button type="button" class="btn btn-primary" id="qryCommonLogSearchBtn">搜索</button>
                    </div>
                </div>

            </form>
        </div>

        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">系统信息列表</strong></div>
        </div>
        <div class="sypagemin_content ">
            <div class="col-lg-12 mart10">
                <table class="table table-hover" id="systemRoleList">
                    <thead>
                    <tr>
                        <th>选择</th>
                        <th>系统名称</th>
                        <th>系统编码</th>
                        <th>系统类型</th>
                        <th>创建时间</th>
                    </tr>
                    </thead>
                    <tbody>
                    #if($options != "null" && $options.systemInfos && $options.systemInfos != "null" && $options.systemInfos.size() > 0)
                    #foreach($systemInfo in $options.systemInfos)
                    <tr >
                        <td>
                            <label class="wu-radio full absolute" data-scope="">
                                <input type="radio" name="moduleRadio" />
                                <p class="vita-data">{"data":$systemInfo,"totalNumber":$options.totalNumber}</p>
                            </label></td>
                        <td>$!systemInfo.systemName</td>
                        <td>$!systemInfo.systemNbr</td>
                        <td>$!systemInfo.systemType</td>
                        <td>$!systemInfo.createDate</td>
                       <!-- <td>#if($!sevDaysNoReasionLog.logStep == 1000)规则校验
                            #elseif($!sevDaysNoReasionLog.logStep == 2000)订单竣工
                            #end
                        </td>
                        <td>$!sevDaysNoReasionLog.createDate</td>-->
                    </tr>
                    #end
                    #end
                    </tbody>
                </table>
                <!--翻页start -->
                <div class="page-box" id="showPageInfo">
                </div>
                <!--翻页end -->
            </div>
        </div>
    </div>
</div>