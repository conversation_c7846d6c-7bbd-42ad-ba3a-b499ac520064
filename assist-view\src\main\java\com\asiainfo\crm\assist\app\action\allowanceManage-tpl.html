<div data-widget="allowanceManage" style="height:100%">

    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>基本查询</div>
                                </div>

                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-6 ">
                                            <label class="col-md-5 control-label lablep">
                                                	地 区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-6">
                                            <label class="col-md-5 control-label lablep">查询类型</label>
                                            <div class="col-md-7">
                                                <select id="qryType" class="form-control">
                                                    <option value="1">单位产品</option>
                                                    <option value="2">个人产品</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="col-md-5 control-label lablep"></label>
                                            <div class="col-md-7">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="busiAccNum" type="radio" name="payment" checked="checked">业务号码</input>
                                                </label>
                                                <label>&nbsp&nbsp&nbsp&nbsp</label>
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="busiAcct" type="radio" name="payment" >合同号</input>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="col-md-5 control-label lablep" id="queryLabel">
                                            	   接入号码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="queryValue" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6" id="accNumTypeDiv">
                                            <label class="col-md-5 control-label lablep">号码类型</label>
                                            <div class="col-md-7">
                                                <select id="accNumType" class="form-control">
                                                    <option value="1">接入号码</option>
                                                    <option value="2">宽带账号</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-5">
                                            <label class="col-md-4 control-label lablep"></label>
                                            <div class="col-md-8 text-right">
                                                <button id="qryGroupUserBtn" type="button" class="btn btn-primary">查&nbsp;&nbsp;询</button>
                                            </div>
                                        </div>
                                        <input id="channelId" type="hidden" class="form-control" placeholder="" readonly="readonly">
                                    </form>
                                </div>
                            </div>
                            <div class="container-fluid" id="baseInofDiv">
                            	<h5 class="meal_htitle nobrder paddtb0" style="font-weight:700"><i class="dot"></i>基本信息</h5>
                                <div class="wmin_content overflowh" >
				                    <table class="table table-bordered">
			                            <tbody>
			                            <tr>
			                                <td colspan="5" class="ivu-down_cont">
			                                
			                                    <div class="col-sm-4 textcont"><label>客户名称:</label><span>$!{options.custName}</span></div>
			                                    <div class="col-sm-4 textcont"><label>证件类型:</label><span>$!{options.identityTypeName}</span></div>
			                                    <div class="col-sm-4 textcont"><label>证件号码:</label><span>$!{options.identityNum}</span></div>
			                                    
			                                    #if($options != "null" && $options.accNumType == 3)
		                                            <div class="col-sm-4 textcont"><label>合同号:</label><span>$!{options.busiNum}</span></div>
				                                    <div class="col-sm-4 textcont"><label>账户状态:</label><span>$!{options.statusCdName}</span></div>
	                                            #else
		                                            <div class="col-sm-4 textcont"><label>产品名称:</label><span>$!{options.prodName}</span></div>
				                                    <div class="col-sm-4 textcont"><label>业务号码:</label><span>$!{options.busiNum}</span></div>
				                                    <div class="col-sm-4 textcont"><label>产品状态:</label><span>$!{options.statusCdName}</span></div>
	                                        	#end
			                                   		<div class="col-sm-12 textcont"><label>地址:</label><span>$!{options.custAddress}</span></div>
			                                    #if($options != "null" && $options.subsidyMode)
				                                    <div class="col-sm-4 textcont"><label>补贴模式:</label><span>$!{options.subsidyModeName}</span></div>
				                                    <div class="col-sm-4 textcont"><label>补贴额度:</label><span>$!{options.amont}</span></div>
				                                    <div class="col-sm-4 textcont">
														<button id="qryAddMemBtn" type="button" class="btn btn-primary">新增成员</button>
													</div>
			                                    #end
			
			                                </td>
			                            </tr>
			                            </tbody>
			                        </table>
			                        <div id="resultInfo">
                                        <p class="vita-data">{"data":$options}</p> 
                                    </div>
                    			</div>
                            </div>
                            
                            <div class="container-fluid row" id="addMemDiv">
                                <div class="form_title">
                                    <div><i class="bot"></i>新增成员</div>
                                </div>

                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                       <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep" >
                                            	   业务号码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="busiNumber" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4" id="memAccNumTypeDiv">
                                            <label class="col-md-5 control-label lablep">号码类型</label>
                                            <div class="col-md-7">
                                                <select id="memAccNumType" class="form-control">
                                                    <option value="1">接入号码</option>
                                                    <option value="2">宽带账号</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">补贴模式</label>
                                            <div class="col-md-7">
                                                <select id="memMode" class="form-control">
                                                #if($options != "null" && $options.memModeInfoList && $options.memModeInfoList != "null" &&
                                            	$options.memModeInfoList.size() > 0)
	                                            	#foreach($memModeInfo in $options.memModeInfoList)
	                                            		#if($memModeInfo.subsidyMode == 21)
	                                            		<option value=$!{memModeInfo.subsidyMode} selected="selected">$!{memModeInfo.subsidyModeName}</option>
	                                            		#else
	                                            		<option value=$!{memModeInfo.subsidyMode} >$!{memModeInfo.subsidyModeName}</option>
	                                            		#end
	                                            	#end
                                            	#end
                                                    
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group col-md-4" id="memOfferDiv">
                                            <label class="col-md-5 control-label lablep" >
                                            	#if($options != "null" && ($options.groupModeId == 1 || $options.groupModeId == 3))
                                            		订购销售品
                                            	#else
                                            		保底消费
                                            	#end
                                            </label>
                                            <div class="col-md-7">
                                            	<input  type="text"  value = "$!{options.memOfferId}"  type="hidden" id="orderOfferId">
                                                <input  type="text"  value = "$!{options.memOfferName}" class="form-control" placeholder="" readonly="readonly">
                                            </div>
                                        </div>
                                        
                                        <div class="form-group col-md-4" id="amountDiv">
                                            <label class="col-md-5 control-label lablep">
                                            #if($options != "null" && $options.groupModeId == 1)
                                            	补贴金额
                                            #elseif($options != "null" && $options.groupModeId == 3)
                                            	补贴金额(单位：分)
                                            #else
                                            	保底金额
                                            #end
                                            </label>
                                            <div class="col-md-7">
                                            #if($options != "null" && $options.groupModeId == 3)
                                            	<input id="amountInput" type="text" class="form-control" placeholder="">
                                            #else
                                            	<select id="amount" class="form-control">
                                            	#if($options != "null" && $options.amountList && $options.amountList != "null" &&
                                            	$options.amountList.size() > 0)
	                                            	#foreach($amountInfo in $options.amountList)
	                                            		<option value=$!{amountInfo.amount} >$!{amountInfo.amountText}</option>
	                                            	#end
                                            	#end
                                                </select>
                                            #end
                                                
                                                
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-4 control-label lablep"></label>
                                            <div class="col-md-8 text-right">
                                                <button id="addMemCommitBtn" type="button" class="btn btn-primary">确&nbsp;&nbsp;定</button>
                                            </div>
                                        </div>

                                    </form>
                                    <div id="qryAddMemInfo">
                                        <p class="vita-data">{"data":$options}</p> 
                                    </div>
                                </div>
                            </div>

                            <div class="container-fluid row" id="memInfoDiv">
                                <div class="form_title">
                                    <div><i class="bot"></i>成员明细</div>
                                </div>
                                <div class="wmin_content" id="preAccNumResult">
                               		<div class="col-lg-12" id="quitSubsidyBtnDiv">
                                        <a id="quitSubsidyBtn" class="btn btn-gray btn-outline">  退出补贴
                                        </a>
                                    </div>
                                    <div class="col-lg-12 mart10" id="memberList">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                            	<th>选择</th>
                                                <th>单位号码</th>
                                                <th>员工号码</th>
                                                <th>补贴模式</th>
                                                <th>补贴金额（元）</th>
                                                <th>开始时间</th>
                                                <th>结束时间</th>
                                                <th>状态</th>
                                            </tr>
                                            </thead>
                                            <tbody id="memUserList">
                                            #if($options != "null" && $options.memList && $options.memList != "null" &&
                                            $options.memList.size() > 0)
                                            #foreach($memUserInfo in $options.memList)
                                            <tr>
                                            	<td><label class="wu-radio full absolute" data-scope="">
	                                            		<input type="radio" name="memInfo">
	                                            		<p class="vita-data">{"data":$memUserInfo}</p> 
                                            		</label>
                                            	</td>
                                                <td>$!{memUserInfo.groupNum}</td>
                                                <td>$!{memUserInfo.memNum}</td>
                                                <td>$!{memUserInfo.subsidyModeName}</td>
                                                <td>$!{memUserInfo.amont}</td>
                                                <td>$!{memUserInfo.startTime}</td>
                                                <td>$!{memUserInfo.endTime}</td>
                                                <td>$!{memUserInfo.stateName}</td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.memList && $options.memList != "null" &&
                                            $options.memList.size() == 0)
                                            <tr><td align='center' colspan='7'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                    </div>
                                    <!--翻页start -->
                                    <div id="showPageInfo">
                                    </div>
                                    #if($options.totalNumber && $options.totalNumber != "null")
                                    <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                    #end
                                    <!--翻页end -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
