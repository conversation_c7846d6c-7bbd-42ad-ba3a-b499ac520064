
(function (vita) {
    var bizSearch = vita.Backbone.BizView.extend({
        events: {
            "click #btn_queryList": "_queryDealList",
            "click #channelId": "_chooseChannelForOut",
            "click #out_excel":"_export",
            "click #print":"_print",
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            $("#staffId").val(gSession.staffCode);
            $("#staff_Name").val(gSession.staffName);
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            var option = {
                id : dialogId,
                url : widget.global.chooseChannel,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);
                    widget._channelQuery(data.orgId);
                    // widget._channelQuery(-1);
                }
            };
            widget.dialog(option);
            var datetime = widget.require("datetime");
            var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
            var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
            var now = widget._getNowFormatDate();
            var endMis=widget._getEndFormatDate();
            beginDate.val(now);
            endDate.val(endMis);
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    timeFormat: "HH:ii:ss ",
                    timeWheels:"HHiiss"
                });
            }
            ;
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    timeFormat: "HH:ii:ss ",
                    timeWheels:"HHiiss"
                });
            }
            // var gSession = widget.gSession;
            // element.find("#regionId").val(gSession.commonRegionName).attr("value", gSession.commonRegionId);
        },
        global: {
            preset: "datetime",
            chooseChannel: "../comm/chooseChannel",
            chooseChannelForOut: "../comm/chooseChannelForOut",
        },
        _channelQuery: function (ret) {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            var  isTrue=true;
            var param = {
                sysUserId : gSession.systemUserId
            };
            widget.callService("checkSysUserPriv", param, function (res) {
                if(res.isHave==true){

                }else{
                    isTrue=false;
                }
            }, {
                async: false
            });
            var params = {
                channelId: ret
            };
            if(!isTrue){
                params.staffId=gSession.staffId;
            }

            widget.refreshPart("queryStaffByChannel", JSON.stringify(params), "#channelList")
        },
        //点击查询渠道
        _chooseChannelForOut: function(){
            var widget = this,element = $(widget.el);
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseChannel,
                params : {
                    // staffId:g_session.staffId
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);
                    // widget._channelQuery(data.orgId);
                    widget._channelQuery(data.orgId);
                }
            });
        },
        //查询清单列表
        _queryDealList: function () {
            var report = {};
            report.data = {};
            report.data.daily = {};
            report.queryParam = {};
            var widget = this;
            var params = widget._getConds();
            var gSession = widget.gSession;
            var  isTrue=true;
            //判断如果不选统计的营业员就需要判断是否是营业班长，只有营业班长才能统计当前渠道下的所有数据2018-03-22
            // if(params.staffId==null ||params.staffId==""){
                var param = {
                    sysUserId : gSession.systemUserId
                };
                widget.callService("checkSysUserPriv", param, function (res) {
                        if(res.isHave==true){

                        }else{
                            isTrue=false;
                        }
                }, {
                    async: false
                });
            // }
            if(!isTrue && !params.staffId){
                widget.popup("不是营业班长，只能统计当前登陆营业员");
                return;
            }
            if(params.staffId !=gSession.staffId && !isTrue){
                widget.popup("不是营业班长，只能统计当前登陆营业员");
                return;
            }
            //给要统计的营业员，赋值
            $("#madeMan").html($("#staff_Name").val());
            var now = new Date();
            var year = now.getFullYear();
            var month = now.getMonth() + 1;
            var day = now.getDate();
            var str = year + "-" + month + "-" + day ;
            $("#nowDate").html(str);

            $("#bizHallName").html($("#channelId").val());
            var staffIdSelect;
            staffIdSelect = $("#staffIdSelect").find("option:selected").text();
            $("#staffName").html(staffIdSelect);
            $("#staffNu").html($("#staffIdSelect").find("option:selected").attr("staffAccount"));
            $("#dateDistance").html($("#beginDate").val() + "--" + $("#endDate").val());
            if (params) {
                // widget.refreshPart("queryReportList", JSON.stringify(params), "#getDailyCust")
                widget.refreshPart("queryDealList", JSON.stringify(params), "#bizDealList", function (res) {
                    $("#row").attr("style","display: show");
                    var paging = this.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    var exportNum = r.find("#showPageInfo").data("exportNum");
                    var tax="导出功能限制一次性最多导出"+exportNum+"条，本次查询结果";
                    var exportStr="能导出";
                    var exportStrNo="不能导出";
                    if (totalNumber > 0) {
                        if(totalNumber>exportNum){
                            $("#countSize").text(tax+totalNumber+"条,"+exportStrNo);
                            $("#out_excel").attr("disabled",true);
                        }else {
                            $("#countSize").text(tax+totalNumber+"条,"+exportStr);
                            $("#out_excel").attr("disabled",false);
                        }
                    }else{
                        $("#countSize").text(tax+0+"条,"+exportStr);
                        $("#out_excel").attr("disabled",false);
                    }
                }, {
                    async: false
                });
            };
        },
        //请求参数设置
        _getConds: function () {
            var widget = this;
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            //渠道ID
            var channelId = $("#channelId").attr("value");
            if(widget._isNullStr(channelId)) {
                widget.popup("请选择渠道！");
                return false;
            } else {
                params.channelId = channelId;
            }
            //要统计的员工ID
            params.staffId=$("#staffIdSelect").find("option:selected").attr("createStaff");
            //时间
            var beginDate = $("#beginDate").val();
            var endDate = $("#endDate").val();
            if (widget._isNullStr(beginDate) || widget._isNullStr(endDate)) {
                widget.popup("请选择日期范围！");
                return false;
            }
            var d1 = new Date(beginDate.replace(/\-/g, "\/"));
            var d2 = new Date(endDate.replace(/\-/g, "\/"));
            if(beginDate!=""&&endDate!=""&&d1 >d2){
                widget.popup("起始时间不能大于截止时间！");
                return false;
            }
                params.startDt = beginDate;
                params.endDt = endDate;
            params.qryScope = $("#qryScope").val();
            return params;
        },
        _export:function () {
            var widget = this,element = $(widget.el);
            var params=widget._getConds();
            params.madeMan=$("#madeMan").text();
            params.nowDate=$("#nowDate").text();
            params.bizHallName=$("#bizHallName").text();
            params.staffName=$("#staffName").text();
            params.staffNu=$("#staffNu").text();
            params.dateDistance=$("#dateDistance").text();

            var gsession = widget.gSession;
            var url = "../query/exportBizReport";
            if (params.staffId!="" && params.staffId!=null){
                location.href=url+"?channelId="+params.channelId+"&staffId="+params.staffId+"&beginDate="+params.startDt+"&endDate="+params.endDt+
                    "&madeMan="+encodeURI(encodeURI(params.madeMan))+"&nowDate="+params.nowDate+"&bizHallName="+encodeURI(encodeURI(params.bizHallName))+"&staffName="+encodeURI(encodeURI(params.staffName))+"&staffNu="+
                    params.staffNu+"&dateDistance="+params.dateDistance +"&qryScope="+params.qryScope;
            }  else {
                location.href=url+"?channelId="+params.channelId+"&staffId="+"-777"+"&beginDate="+params.startDt+"&endDate="+params.endDt+
                    "&madeMan="+encodeURI(encodeURI(params.madeMan))+"&nowDate="+params.nowDate+"&bizHallName="+encodeURI(encodeURI(params.bizHallName))+"&staffName="+encodeURI(encodeURI(params.staffName))+"&staffNu="+
                    params.staffNu+"&dateDistance="+params.dateDistance+"&qryScope="+params.qryScope;
            }
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _print:function () {
            var widget = this, element = $(widget.el);
            debugger;
            var orderhtml = "";
            orderhtml = $("#divTable").html();  //获取div view的内容
			/* 创建iframe */
            var doc = document;
            var headobj = doc.getElementsByTagName("head").item(0); //提取head
            var pp = doc.getElementById("lldxi_printRegionFrame_2012_0112"); //iframe的ID
            if (pp) { doc.body.removeChild(pp); } //如果存在则remove
            pp = doc.createElement("iframe");
            pp.setAttribute("src", "about:blank");
            pp.setAttribute("id", "lldxi_printRegionFrame_2012_0112");
            pp.setAttribute("marginheight", "0");
            pp.setAttribute("marginwidth", "0");
            pp.style.display = "none";
            doc.body.appendChild(pp);
            if(widget.isChromeBrowser()){  //Chrome
                //Chrome体验最佳
                //var htmlstr = "<html>" + headobj.outerHTML + "<body>" + orderhtml + "<\/body>" + "<\/html>";
                //pp.contentWindow.document.write(htmlstr);
                pp.contentWindow.document.write(orderhtml);
                pp.contentWindow.print();

                //还要打印所有的协议, 每个调用一次print()保证页数完整性
                var ptFrames = $("iframe[name=protocolFrames]");
                $.each(ptFrames, function(i, pt){
                    var toPrintHtml = $(pt).contents().find("div[name=protocolPrintDiv]").html();
                    var doc = document;
                    var pp = doc.getElementById("lldxi_printRegionFrame_2012_0112"); //iframe的ID
                    if (pp) { doc.body.removeChild(pp); } //如果存在则remove
                    pp = doc.createElement("iframe");
                    pp.setAttribute("src", "about:blank");
                    pp.setAttribute("id", "lldxi_printRegionFrame_2012_0112");
                    pp.setAttribute("marginheight", "0");
                    pp.setAttribute("marginwidth", "0");
                    pp.style.display = "none";
                    doc.body.appendChild(pp);
                    toPrintHtml = '<html><head></head><body>' + toPrintHtml + '</body></html>';
                    pp.contentWindow.document.write(toPrintHtml);
                    pp.contentWindow.print();
                });
            }else if(widget.isFfBrowser()){ //FireFox
                //同理mozilla firefox也改为同IE一样
                var htmlstr = "<html>" + headobj.outerHTML + "<body>" + orderhtml + "<\/body>" + "<\/html>";
                var newWin= window.open();
                newWin.document.write(htmlstr);
                newWin.document.close();
                newWin.focus();
                newWin.print();
                newWin.close();
            }else{ //others like IE
                //IE浏览器实测iframe调用打印无反应，改为new一个window， 打印完毕自动关闭窗口
                var htmlstr = "<html>" + headobj.outerHTML + "<body>" + orderhtml + "<\/body>" + "<\/html>";
                var newWin= window.open();
                newWin.document.write(htmlstr);
                //防止write完之前就close,设置延时1s
                setTimeout(function(){
                    newWin.document.close();
                    newWin.focus();
                    newWin.print();
                    newWin.close();
                },1000);
            }

        },
        /**
         * 判断是否Chrome
         * @return {TypeName}
         */
        isChromeBrowser:function(){
            var match = this.bssUtilParam.rChrome.exec(this.bssUtilParam.userAgent);
            if (match != null) {
                return true;
            }else{
                return false;
            }
        },
        bssUtilParam : {
            userAgent : navigator.userAgent.toLowerCase(), // userAgent
            rMsie : /.*(msie) ([\w.]+).*/, // ie
            rFirefox : /.*(firefox)\/([\w.]+).*/, // firefox
            rOpera : /(opera).+version\/([\w.]+)/, // opera
            rChrome : /.*(chrome)\/([\w.]+).*/, // chrome
            rSafari : /.*version\/([\w.]+).*(safari).*/  // safari
        },
        _getNowFormatDate: function() {
            var date = new Date();
            var seperator1 = "-";
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var strDate = date.getDate();
            var hour = date.getHours();
            var mins=date.getMinutes();
            var seconds = date.getSeconds();
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            if (strDate >= 0 && strDate <= 9) {
                strDate = "0" + strDate;
            }
            if (mins >= 0 && mins <= 9) {
                mins = "0" + mins;
            }
            if (seconds >= 0 && seconds <= 9) {
                seconds = "0" + seconds;
            }
            if (hour >= 0 && hour <= 9) {
                hour = "0" + hour;
            }
            var currentdate = year + seperator1 + month + seperator1 + strDate
                +" 00"+ ":00" +":00";
            return currentdate;
        },
        _getEndFormatDate: function() {
            var date = new Date();
            var seperator1 = "-";
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var strDate = date.getDate();
            var hour = date.getHours();
            var mins=date.getMinutes();
            var seconds = date.getSeconds();
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            if (strDate >= 0 && strDate <= 9) {
                strDate = "0" + strDate;
            }
            if (mins >= 0 && mins <= 9) {
                mins = "0" + mins;
            }
            if (seconds >= 0 && seconds <= 9) {
                seconds = "0" + seconds;
            }
            if (hour >= 0 && hour <= 9) {
                hour = "0" + hour;
            }
            var currentdate = year + seperator1 + month + seperator1 + strDate
                +" 23"+ ":59" +":59";
            return currentdate;
        }
    });
    vita.widget.register("bizSearch", bizSearch, true);
})(window.vita);