<div data-widget="commChannelQuery">
    <p class="vita-data"> {"data":$options}</p>
    <div id="myTabContent" class="tab-content">
            <div class="sypage-min">
                <div class="form_title">
                    <div>
                        <i class="bot"></i><strong class="text16">渠道信息查询</strong>
                    </div>
                </div>
                <div class="sypagemin_content ">
                    <form class="form-bordered">
                        <div class="form-group col-md-6">
                            <label class="col-md-5 control-label lablep">地区</label>
                            <div class="col-md-7">
                                <div class="input-group" align="right">
                                <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                    <div class="input-group-btn"><button id="regionIdBtn" class="btn btn-green" type="button">选择</button></div>
                                </div>
                            </div>
                        </div><div class="form-group col-md-6"><label class="col-md-5 control-label lablep">渠道名称</label>
                        <div class="col-md-7"><div class="input-group" align="right"><input id="channelIdInput" type="text" class="form-control" placeholder=""></div></div>
                        <button id="btn-query" type="button" class="btn btn-primary">查&nbsp;&nbsp;询</button></div>
                    </form>
                </div>
                <div class="page_main">
                    <div class="col-lg-12 blockbox">
                        <div class="ibox bpaddlrtb10">
                            <div id="orderedProdListAll">
                                <div id="orderedProdList" class="col-md-12 mart10">
                                    <div class="tablepanel-group panel-group" role="tablist" aria-multiselectable="true">
                                        <ul class="table_th">
                                            <li class="col-xs-2">渠道名</li>
                                            <li class="col-xs-1">渠道状态</li>
                                            <li class="col-xs-1">渠道性质</li>
                                            <li class="col-xs-1">生效时间</li>
                                            <li class="col-xs-1">失效时间</li>
                                            <li class="col-xs-2">归属代理商</li>

                                            <li class="col-xs-3">组织机构层级</li>
                                        </ul>
                                        #if($options.channelDetails && $options.channelDetails != "null" && $options.channelDetails.size() > 0)
                                            #set($i = 1)
                                            #foreach($channel in $options.channelDetails)
                                            <div class="panel panel-default">
                                                <div class="panel-heading" role="tab" id="heading$i">
                                                    <h4 class="panel-title">
                                                        <ul class="table_td">
                                                             #if($channel.channelName && $channel.channelName != "null")
                                                            <li class="col-xs-2" ><span>$channel.channelName</span></li>
                                                            #else
                                                            <li class="col-xs-2"><span></span></li>
                                                            #end
                                                            #if($channel.statusCd && $channel.statusCd != "null")
                                                            <li class="col-xs-1" style="margin-left: 10px;"><span>
                                                                #if($channel.statusCd=="1000")
                                                                有效
                                                                #elseif($channel.statusCd=="1001")
                                                                主动暂停
                                                                #elseif($channel.statusCd=="1002")
                                                                异常暂停
                                                                #elseif($channel.statusCd=="1100")
                                                                无效
                                                                #elseif($channel.statusCd=="1101")
                                                                终止
                                                                #elseif($channel.statusCd=="1102")
                                                                退出
                                                                #elseif($channel.statusCd=="1200")
                                                                未生效
                                                                #elseif($channel.statusCd=="1300")
                                                                已归档
                                                                #end
                                                            </span></li>
                                                            #else
                                                            <li class="col-xs-1"><span></span></li>
                                                            #end
                                                            #if($channel.channelProperty && $channel.channelProperty != "null")
                                                            <li class="col-xs-1"><span>$channel.channelProperty</span></li>
                                                            #else
                                                            <li class="col-xs-1"><span></span></li>
                                                            #end
                                                            #if($channel.effDate && $channel.effDate != "null")
                                                            <li class="col-xs-1"><span>$channel.effDate</span></li>
                                                            #else
                                                            <li class="col-xs-1"><span></span></li>
                                                            #end
                                                            #if($channel.expDate && $channel.expDate != "null")
                                                            <li class="col-xs-1"><span>$channel.expDate</span></li>
                                                            #else
                                                            <li class="col-xs-1"><span></span></li>
                                                            #end
                                                            #if($channel.operatorsName && $channel.operatorsName != "null")
                                                            <li class="col-xs-2"><span>$channel.operatorsName</span></li>
                                                            #else
                                                            <li class="col-xs-2"><span></span></li>
                                                            #end
                                                            <!--<li class="col-xs-2"><span></span></li>-->
                                                            #if($channel.parentOrgName && $channel.parentOrgName != "null")
                                                            <li class="col-xs-3"><span>$channel.parentOrgName</span></li>
                                                            #else
                                                            <li class="col-xs-3"><span></span></li>
                                                            #end

                                                        </ul>
                                                    </h4>
                                                </div>

                                            </div>
                                            <p class="vita-data">{"totalNumber":$options.pageInfo.rowCount}</p>
                                            #set($i = $i + 1)
                                            #end
                                        #elseif($options.channelDetails && $options.channelDetails == "null")
                                            <ul class="table_td"><li class="col-xs-20">未查询到数据！</li></ul>
                                        #end
                                    </div>
                                </div>
                                <!--翻页start -->
                                <div class="page-box" id="showPageInfo"></div>
                                #if($options.channelDetails && $options.channelDetails != "null" && $options.channelDetails.size() > 0)
                                <p class="vita-data">{"totalNumber":$!{options.pageInfo.rowCount}}</p>
                                #end
                                <!--翻页end -->
                            </div>


                        </div>
                    </div>
                </div>

            </div>
    </div>
</div>
