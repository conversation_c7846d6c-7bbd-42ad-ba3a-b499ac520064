(function (vita) {
    var reverseInvoice = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_qryInvoiceListByCoNbr"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
        },
        global: {
            pageIndex: 1,
            pageSize: 10
        },
        _qryInvoiceListByCoNbr: function () {
            var widget = this;
            var gsession = widget.gSession;
            var coNbr = $.trim($("#coNbr").val());
            if(coNbr== null || coNbr==""){
                widget.popup("请输入订单流水号！");
                return;
            }
            var params = {
                coNbr: coNbr,
                staffId: gsession.staffId,
                regionId: gsession.staffRegionId,
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            debugger;
            widget.refreshPart("qryInvoiceListByCoNbr", JSON.stringify(params), "#invoiceResult", function (re) {
                // var r = $(re), paging = this.require("paging");
                // var e = paging.new({
                //     recordNumber : widget.global.pageSize,
                //     total : r.find("#showPageInfo").data("totalNumber"),
                //     pageIndex : widget.global.pageIndex,
                //     callback : function (pageIndex, pageSize) {
                //         debugger;
                //         var params = {
                //             accNum: $("#accNum").val(),
                //             staffId: gsession.staffId,
                //             regionId: gsession.staffRegionId,
                //             // channelId: gsession.curChannelId,
                //             pageInfo: {
                //                 pageIndex: pageIndex,
                //                 pageSize: widget.global.pageSize
                //             }
                //         };
                //         widget.refreshPart("preAccNumQuery", JSON.stringify(params), "#preAccNumList");
                //     }
                // });
                // r.find("#showPageInfo").append(e.getElement());
            }, { async: false });
        }
    });
    vita.widget.register("reverseInvoice", reverseInvoice, true);
})(window.vita);