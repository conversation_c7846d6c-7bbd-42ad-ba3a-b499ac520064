package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICertNumFiveQuerySMO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Component("vita.certNumFiveHis")
public class CertNumFiveHis extends AbstractComponent {

    @Autowired
    private ICertNumFiveQuerySMO certNumFiveQrySmo;

    @Override
    public Map achieveData(Object... objects) throws Exception {
        return null;
    }

    public Map queryHis(String params, String pageInfo) throws IOException {
        String result = certNumFiveQrySmo.queryLogsByConditionsForPage(params, pageInfo);
        return (Map) resolveResult(result);
    }

    public List<Map> queryHisData(String seq) throws IOException {
        String result = certNumFiveQrySmo.queryLogDataByLogSeq(seq);
        return (List) resolveResult(result);
    }

}
