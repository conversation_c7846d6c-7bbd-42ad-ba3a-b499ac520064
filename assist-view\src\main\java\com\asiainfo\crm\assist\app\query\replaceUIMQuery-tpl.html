<div data-widget="replaceUIMQuery" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright">
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_region" type="checkbox" name="payment">
                                                </label>  地 区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="queryRegionIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custName" type="checkbox" name="payment">
                                                </label> 客户姓名
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custName" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custOrderId" type="checkbox" name="payment">
                                                </label> 订单编号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custOrderId" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_accNum" type="checkbox" name="payment">
                                                </label> 接入号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="accNum" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_statusCD" type="checkbox" name="payment">
                                                </label> 状态
                                            </label>
                                            <div class="col-md-7">
                                                <select id="statusCD" class="form-control">
                                                    <option value="1000">有效</option>
                                                    <option value="1300">失效</option>
                                                    <option value="1100">完成</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_certNum" type="checkbox" name="payment">
                                                </label> 客户证件号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="certNum" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_callNum" type="checkbox" name="payment">
                                                </label> 联系电话
                                            </label>
                                            <div class="col-md-7">
                                                <input id="callNum" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_orderNo" type="checkbox" name="payment">
                                                </label> 数字人订单号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="orderNo" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-12" id="qryTimeQuantum">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_Date" type="checkbox" name="payment">
                                                </label> 登记时间
                                            </label>
                                            <div class="col-md-10 form-inline">

                                                <div class="form-group">
                                                    <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="开始时间">
                                                </div>
                                                <div class="form-group">
                                                    <input name="endDate" id="endDate" type="text" class="form-control" placeholder="结束时间">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-11">
                                            <div class="col-md-12 searchbutt_r" align="right">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-clear" type="button" class="btn btn-white">清除</button>
                                            </div>
                                        </div>


                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <!--<div><i class="bot"></i>查询结果</div>-->
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12">
                                        <a id="BKXX" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 补卡信息
                                        </a>
                                        <a id="printDelivery" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 补物流信息
                                        </a>
<!--                                        <a id="printReceipt" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 导入-->
<!--                                        </a>-->
                                        <a id="returnInfo" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 导出
                                        </a>
                                    </div>
                                    <div class="col-lg-12 mart10" id="replaceUIMListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>选择</th>
                                                <th>数字人订单号</th>
                                                <th>业务类型</th>
                                                <th>客户姓名</th>
                                                <th>客户证件号</th>
                                                <th>收货人姓名</th>
                                                <th>联系电话</th>
                                                <th>接入号码</th>
                                                <th>联系地址</th>
                                                <th>登记时间</th>
                                                <th>iccid卡号</th>
                                                <th>物流单号</th>
                                                <th>状态</th>
                                            </tr>
                                            </thead>
                                            <tbody id="infoList">
                                            #if($options != "null" && $options.replaceUIMList && $options.replaceUIMList != "null" &&
                                            $options.replaceUIMList.size() > 0)
                                            #foreach($replaceUIM in $options.replaceUIMList)
                                            <tr>
                                                <td><label class="wu-radio full absolute" data-scope=""><input
                                                        type="radio" name="payment"></label></td>
                                                <td>
                                                <a class="textcolorgreen" link="replaceUIM" data-toggle="tooltip" data-placement="top" > $!{replaceUIM.orderNo}</a>
                                                <p class="vita-data">
                                                    {"digitalOrderInfoId" : "$!replaceUIM.digitalOrderInfoId",
                                                    "iccid" : "$!replaceUIM.iccid",
                                                    "deliveryId" : "$!replaceUIM.deliveryId"}</p>
                                                <td>$!{replaceUIM.businessType}</td>
                                                <td>$!{replaceUIM.custName}</td>
                                                <td>$!{replaceUIM.certNum}</td>
                                                <td>$!{replaceUIM.contacter}</td>
                                                <td>
                                                    $!{replaceUIM.callNum}
                                                </td>
                                                <td>$!{replaceUIM.accNum}</td>
                                                <td>$!{replaceUIM.address}</td>
                                                <td>$!{replaceUIM.createTime}</td>
                                                <td>$!{replaceUIM.iccid}</td>
                                                <td>$!{replaceUIM.deliveryId}</td>
                                                #if($replaceUIM.statusCd==1000)
                                                <td>启用</td>
                                                #end
                                                #if($replaceUIM.statusCd==1100)
                                                <td>完成</td>
                                                #end
                                                #if($replaceUIM.statusCd==1300)
                                                <td>失效</td>
                                                #end
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.replaceUIMList && $options.replaceUIMList != "null" &&
                                            $options.replaceUIMList.size() == 0)
                                            <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
