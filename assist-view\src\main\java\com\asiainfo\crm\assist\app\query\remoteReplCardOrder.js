(function (vita) {
    var remoteReplCardOrder = vita.Backbone.BizView.extend({
        events: {
            "click #btn-readCert": "_readCert",
            "change input[name='cosponsor']": "_checkCosponsor",
            "click [name='clearCosponsor']": "_clearCosponsor",
            "click #btn-writeCard": "_toWriteCard",
            "click #firstStaffIdBtn": "_chooseStaff",
            "click #secondStaffIdBtn": "_chooseStaff",
            "click #thirdStaffIdBtn": "_chooseStaff",
            "click #btnSubmit": "_submit"
        },
        _cardInfoJson : {
                "serialNumber":"",
                "factoryCode":"",
                "cardTypeId":"",
                "cardName":"",
                "materialId":"",
                "canWrite":""
            },
        _initialize: function () {
            var widget = this,
                element = $(widget.el),gSession = widget.gSession,data =element.data("data");
            if("Y"==data.remoteReplCardJumpSwitch){
            	 widget._setRemoteReplCardOrderCookie("ALID",gSession.glbSessionId);
                 widget._setRemoteReplCardOrderCookie("loginRegionId",gSession.staffRegionId);	
            }
            	

        },
        /**
         * 根据工号查询协销人
         * */
        _checkCosponsor:function(eve){
                var widget = this, element = $(widget.el),tar = $(eve.currentTarget),inputVal = $.trim(tar.val()),staffInfos = {};
            if(!inputVal){
                return;
            }
            var params = {
                staffCode : inputVal,
                glbSessionId : widget.gSession.glbSessionId
            };
            widget.callService("queryStaffNumber", params,
                function(ret) {
                    debugger;
                    staffInfos = ret;
                    if(staffInfos.resultCode == 0 && staffInfos.staffInfo.staffCode){
                        tar.val(staffInfos.staffInfo.staffName);
                        tar.attr("staffCode",staffInfos.staffInfo.staffCode);
                        tar.prop("readonly",true);
                        tar.next().css("display","block");
                    }else{
                        widget.popup("工号不存在...");
                    }
                }, {
                    async : false,
                    headers : {
                        "regionId" : widget.gSession.installArea
                    }
                });
        },
        _clearCosponsor:function(eve){
              var $tar = $(eve.currentTarget);
            $tar.prev().val("").prop("readonly", false);
            $tar.hide();
        },
        /*_readCert: function () {

            var widget = this;
            var option = {
                url : widget.global.scanCertUrl+"?certOperType=" + "add",
                onClose : function(res) {
                    debugger;
                    var scanCert = $(res).closest("[data-widget=scanCert]");
                    if (scanCert.length) {

                        var cust = scanCert.scanCert("getValue");
                        widget.model.set("certInfo",cust);
                        if (!cust) {
                            return false;
                        }
                        widget._setCustomerInfo(cust);
                    }
                }
            };
            widget.dialog(option);
            return false;

        },
        _setCustomerInfo: function (data) {
            var widget = this,
                global = widget.global,
                element = $(widget.el);
            element.find("#certName").val(data.partyName);
            element.find("#certName").attr("disabled", true);
            element.find("#certNum").val(data.certNumber);
            element.find("#certNum").attr("disabled", true);
            element.find("#certAddress").val(data.certAddress);
            element.find("#certAddress").attr("disabled", true);
        },

        global: {
            scanCertUrl: "../comm/scanCert",
        },*/
        _toWriteCard:function () {
            var widget = this,el =$(widget.el),data =el.data("data");
            var compCode = "whiteCard";
            var dialogId = "whiteCardDialog";
            data["phoneNum"] = data.replPhoneNum;
            data["busiType"] = "4G";
            data["needQry"] = "N";
            data["compCode"]  = "whiteCard";
            var option = {
                url : "../common/whiteCard",
                params : data,
                id : dialogId,
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data || data == "") {
                        return false;
                    }
                    /*TODO:获取写卡信息*/
                    _cardInfoJson = data;
                }
            };
            widget.dialog(option);
        },
        _chooseStaff: function (e) {
            var widget = this,element = $(widget.el);
            var input = $(e.target).closest("div").prev();
            var compCode = "chooseStaff";
            var dialogId = "chooseStaffDialog";
            widget.dialog({
                id : dialogId,
                url : "../comm/chooseStaff",
                params : {},
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data || data == "") {
                        return false;
                    }
                    input.val(data.staffName).attr("value", data.staffId);
                }
            });
        },
        /**
         * 异地补办卡提交
         * */
        _submit:function(eve){
            var widget = this, 
            element = $(widget.el),
            gSession = widget.gSession,
            data =element.data("data"),
            tar = $(eve.currentTarget);
            var resp;
            //填写参数后续需要转化为attrInfos
            var params ={
            	"custOrderInfo":{	
                	"channelNbr":gSession.curChannelId,
                	"commonRegionId":data.regionId,	
                	"staffCode":gSession.staffCode
            	},
            	"prodInfo":{
            		"accNum":data.replPhoneNum,
            		"lanId":data.replPhoneNumLanId,	
                	"mktResouceInfo":{
                		"mktResourceCode":_cardInfoJson.materialId,
                	}
            	},
            	"orderPymnt":{
            		//"custOrderId": "",
                    //"custId": "123456",
                    "channelId": gSession.curChannelId,
                    "staffId": gSession.staffId,
                    //"acceptTime": "",
                    //"extSystem": "",
                    //"extCustOrderId": "",
                    "lanId": gSession.staffLanId,
                    "glbSessionId": gSession.glbSessionId,
                    "accNum":data.replPhoneNum
            	},
            	"inputInfo":{
            		"certType":$("#certType").val(),
                	"certName":$("#certName").val(),	
                	"certNum":$("#certNum").val(),	
                	"certAddress":$("#certAddress").val(),	
                	"linkPhone":$("#linkPhone").val(),	
                	"firstCosponsor":$("#firstCosponsor").val(),	
                	"secondCosponsor":$("#secondCosponsor").val(),	
                	"thirdCosponsor":$("#thirdCosponsor").val()
            	}
            		
            };
            
            widget.callService("remoteReplCardSubmit", params, function(ret) {
                   if(ret.retCode == 0 ){
                       resp = ret;
                   }else{
                       widget.popup("异地补办卡提交失败");
                       return;
                   }
                }, {
                    async : false,
                    headers : {
                        "regionId" : widget.gSession.installArea
                    }
                });

        },
        _setRemoteReplCardOrderCookie:function(name, value, date) {
        	 var widget = this,
             element = $(widget.el),
             data =element.data("data");
			var exp = new Date();
			if(date === undefined || date === null){
				date = 30;
			}
            if (typeof date === 'number') {
                exp.setTime(exp.getTime() + date * 24 * 60 * 60 * 1000);
            }
            document.cookie = name + "=" +value + ";expires=" + exp.toGMTString() + ";path=/;domain="+data.doMainUrl;
        }
    });
    vita.widget.register("remoteReplCardOrder", remoteReplCardOrder, true);
})(window.vita);