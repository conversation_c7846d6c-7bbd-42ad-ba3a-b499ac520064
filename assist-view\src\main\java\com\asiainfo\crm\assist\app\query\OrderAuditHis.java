package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderAuditSMO;
import com.asiainfo.crm.service.intf.IStaffInfoQuerySMO;
import com.asiainfo.crm.service.intf.IStaffOrgAreaSMO;
import com.asiainfo.crm.util.ListUtil;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created on 2019/10/28
 */
@Component("vita.orderAuditHis")
public class OrderAuditHis extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(OrderAuditHis.class);

    @Autowired
    private IOrderAuditSMO orderAuditSMO;

    @Autowired
    private IStaffInfoQuerySMO staffInfoQuerySMO;

    @Autowired
    private IStaffOrgAreaSMO staffOrgAreaSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map options = new HashMap(1);
        String orderAuditHisStr = orderAuditSMO.qryOrderAuditHisInfosByCoId(params[0].toString());
        List<Map> orderAuditHisInfos = (List<Map>) resolveResult(orderAuditHisStr);
        fillInfoToOrderAuditHisInfos(orderAuditHisInfos);
        options.put("orderAuditHisInfos", orderAuditHisInfos);
        return options;
    }

    /**
     * 填充工号信息与渠道信息
     *
     * @param orderAuditHisInfos
     */
    private void fillInfoToOrderAuditHisInfos(List<Map> orderAuditHisInfos) throws Exception {
        if (ListUtil.isListEmpty(orderAuditHisInfos) || orderAuditHisInfos.size() == 0) {
            return;
        }
        for (Map orderAuditHisInfo : ListUtil.nvlList(orderAuditHisInfos)) {
            //填充工号信息
            Long staffId = MapUtils.getLong(orderAuditHisInfo, "auditorId");
            Map staffMap = new HashMap(1);
            Map staffOrgRelMap = new HashMap(1);
            Map channelMap = new HashMap(2);
            if (staffId != null && staffId > 0L) {
                Map staffReq = new HashMap(1);
                staffReq.put("staffId", staffId);
                staffMap = (Map) resolveResult(staffInfoQuerySMO.getStaffBaseInfo(jsonConverter.toJson(staffReq)));
                //查询工号归属渠道
                staffReq.put("relType", "1400");
                List<Map> staffOrgRelList = (List) resolveResult(staffInfoQuerySMO.queryStaffOrgRel(jsonConverter.toJson(staffReq)));
                if (!ListUtil.isListEmpty(staffOrgRelList) && staffOrgRelList.size() > 0) {
                    staffOrgRelMap = staffOrgRelList.get(0);
                }
            }
            if (MapUtils.isNotEmpty(staffMap) && staffMap.containsKey("staffCode")) {
                orderAuditHisInfo.put("staffCode", MapUtils.getString(staffMap, "staffCode"));
            }
            if (MapUtils.isNotEmpty(staffOrgRelMap) && staffOrgRelMap.containsKey("orgId")) {
                //查询渠道信息
                Long orgId = MapUtils.getLong(staffOrgRelMap, "orgId", 0L);
                List orgIds = new ArrayList(1);
                orgIds.add(orgId);
                Map channelReq = new HashMap(1);
                channelReq.put("orgIds", orgIds);
                List<Map> channelList = (List<Map>) resolveResult(staffOrgAreaSMO.queryChannelListByIds(jsonConverter.toJson(channelReq)));
                if (!ListUtil.isListEmpty(channelList) && channelList.size() > 0) {
                    channelMap = channelList.get(0);
                }
            }
            if (MapUtils.isNotEmpty(channelMap) && channelMap.containsKey("orgId") && channelMap.containsKey("channelName")) {
                orderAuditHisInfo.put("orgId", MapUtils.getString(channelMap, "orgId"));
                orderAuditHisInfo.put("channelName", MapUtils.getString(channelMap, "channelName"));
            }
        }
    }
}