<div data-widget="qryCardResourceInfo" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright">
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_accNum" type="checkbox" name="payment">
                                                </label> 用户号码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="accNum" type="text" class="form-control"
                                                       placeholder="请输入用户号码" maxlength="32">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_umiNum" type="checkbox" name="payment">
                                                </label> UIM号码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="umiNum" type="text" class="form-control"
                                                       placeholder="请输入UIM号码" maxlength="32">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_imsiNum" type="checkbox" name="payment">
                                                </label> IMSI号码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="imsiNum" type="text" class="form-control"
                                                       placeholder="请输入IMSI号码" maxlength="32">
                                            </div>
                                        </div>

                                    </form>
                                </div>
                                #if($options != "null" && $options.logSwitch && $options.logSwitch == "Y" )
                                <div class="form_title">
                                    <div><i class="bot"></i>记录查询日志</div>
                                </div>
                                <div class="wmin_content row" id="recordLog">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4">
                                            <label class="col-md-2 control-label lablep"><span
                                                    class="textcolorred">*</span>批准人：</label>
                                            <div class="col-md-7">
                                                <input id="checker" type="text" class="form-control"
                                                       placeholder="请输入批准人" maxlength="32">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-2 control-label lablep"><span
                                                    class="textcolorred">*</span>派单号1：</label>
                                            <div class="col-md-7">
                                                <input id="orderNum1" type="text" class="form-control"
                                                       placeholder="请输入派单号1" maxlength="50">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-2 control-label lablep"><span
                                                    class="textcolorred"></span>派单号2：</label>
                                            <div class="col-md-7">
                                                <input id="orderNum2" type="text" class="form-control"
                                                       placeholder="请输入派单号2" maxlength="50">
                                            </div>
                                        </div>

                                    </form>
                                </div>
                                #end
                                <div class="form-group col-md-11">
                                    <div class="col-md-12 searchbutt_r" align="right">
                                        <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                        <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                        <p class="vita-data">{"logSwitch" : "$!options.logSwitch"}</p>
                                        <label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                        <button id="btn-clear" type="button" class="btn btn-white">清除</button>
                                    </div>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询结果</div>
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12 mart10" id="cardResourceInfo">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>所查号码</th>
                                                <th>IMSI码</th>
                                                <th>4G-IMSI码</th>
                                                <th>LTE-IMSI码</th>
                                                <th>ESN码</th>
                                                <th>PUK码</th>
                                                <th>PIN码</th>
                                                #if($options != "null" && $options.cardInfos && $options.cardInfos != "null")
                                                #set($cardInfo = $options.cardInfos)
                                                #if($cardInfo.accNum && $cardInfo.accNum != "null")
                                                <th>接入号</th>
                                                #end
                                                #if($cardInfo.uim && $cardInfo.uim != "null")
                                                <th>UIM号码</th>
                                                #end
                                                #end
                                            </tr>
                                            </thead>
                                            <tbody id="cardResourceList">
                                            #if($options != "null" && $options.cardInfos && $options.cardInfos != "null")
                                            #set($cardInfo = $options.cardInfos)
                                            <tr>
                                                <td>$!{cardInfo.qryNum}</td>
                                                <td>$!{cardInfo.imsi}</td>
                                                <td>$!{cardInfo.gImsi}</td>
                                                <td>$!{cardInfo.lteImsi}</td>
                                                <td>$!{cardInfo.esn}</td>
                                                <td>$!{cardInfo.puk}</td>
                                                <td>$!{cardInfo.pin}</td>
                                                #if($cardInfo.accNum && $cardInfo.accNum != "null")
                                                <td>$!{cardInfo.accNum}</td>
                                                #end
                                                #if($cardInfo.uim && $cardInfo.uim != "null")
                                                <td>$!{cardInfo.uim}</td>
                                                #end
                                            </tr>
                                            #elseif($options != "null" && $options.cardInfos &&
                                            $options.cardInfos != "null" )
                                            <tr>
                                                <td align='center' colspan='7'>未查询到数据！
                                                <td>
                                            </tr>
                                            #end
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
