(function(vita){
	var saveProtocolTempletConfig = vita.Backbone.BizView.extend({
		events : { 
			"click #submit" : "_submit",
			"click #chooseOffer,#chooseServiceOffer" : "_showPage",
			"click .close" : "_closePage",
			"blur #priorityLevel" : "_checkNum"
		},
		_initialize : function() {
			var widget = this,
			element = $(widget.el);
			var data = element.data("data");
			debugger;
			widget.model.set("protocolTempletConfigId",data.protocolTempletConfigId);
			widget.model.set("protocolTempletId",data.protocolTempletId);
			widget.model.set("regionId",data.regionId);
			
			var protocolTempletConfig = data.protocolTempletConfig;
			if(protocolTempletConfig != null){
				widget._showDate(data.protocolTempletConfig);
			}
			//时间控件类型初始化
			var datetime = widget.require("datetime");
			var timeTxts = element.find("input[name='timeTxt']");
			if (timeTxts.length) {
				timeTxts.each(function(i, timeTxt) {
					datetime.register($(timeTxt), {
						preset : widget.global.preset
					});
				});
			}
			
		},
		global : {
			preset : 'date',
			"chooseOffer" : "../comm/chooseOffer",
			"chooseServiceOffer" : "../comm/chooseServiceOffer"
		},
		_showDate : function(protocolTempletConfig) {
			var widget = this,
				element = $(widget.el);
			debugger;
			widget.model.set("protocolTempletConfigId", protocolTempletConfig.protocolTempletConfigId);
			widget.model.set("protocolTempletId", protocolTempletConfig.protocolTempletId);
			widget.model.set("objId", protocolTempletConfig.objId);
			element.find("#objId").val(protocolTempletConfig.objId);
			widget.model.set("serviceOfferId", protocolTempletConfig.serviceOfferId);
			element.find("#serviceOfferId").val(protocolTempletConfig.serviceOfferId);
			widget.model.set("priorityLevel", protocolTempletConfig.priorityLevel);
			element.find("#controlType").val(protocolTempletConfig.controlType);
			element.find("#effDate").val(protocolTempletConfig.effDate);
			element.find("#expDate").val(protocolTempletConfig.expDate);
			element.find("#statusCd").val(protocolTempletConfig.statusCd);
		},
		_showPage : function(e) {
			var widget = this,
				element = $(widget.el);
			var button = $(e.target).closest("button");
			debugger;
			var id = button.attr("id") || "";
			if (id && widget.global[id]) {
				var dialogId = id + 'Dialog';
				var option = {
					url : widget.global[id],
					id : dialogId,
	                onOpen : function(res){
	                	if(id == "chooseOffer"){
	                		var chooseOffer = $(res).closest("[data-widget="+ id +"]");
							chooseOffer.chooseOffer("setDatas","NO");
	                	}else if(id == "chooseServiceOffer"){
	                		var chooseServiceOffer = $(res).closest("[data-widget="+ id +"]");
	                		chooseServiceOffer.chooseServiceOffer("setDatas","NO");
	                	}
						
					},
					onClose : function(res) {
						if(id == "chooseOffer"){
							var chooseOffer = $(res).closest("[data-widget="+ id +"]");
							if (chooseOffer.length) {
								var data = chooseOffer.chooseOffer("getValue");
								if (!data || !data.validate) {
									return false;
								}
								
								element.find("#objId").val(data.offerName);
								widget.model.set({
									"objId" : data.offerId
								});
							}
						}else if(id == "chooseServiceOffer"){
							var chooseServiceOffer = $(res).closest("[data-widget="+ id +"]");
							if (chooseServiceOffer.length) {
								var data = chooseServiceOffer.chooseServiceOffer("getValue");
								if (!data || !data.validate) {
									return false;
								}
								
								element.find("#serviceOfferId").val(data.attrName);
								widget.model.set({
									"serviceOfferId" : data.attrValue
								});
							}
						}
					}
				};
				widget.dialog(option);
			}
		},
		_submit : function() {
			debugger;
			var widget = this, gsession = widget.gSession,
				element = $(widget.el);
			var protocolTempletConfigId = widget.model.get("protocolTempletConfigId");
			var protocolTempletId = widget.model.get("protocolTempletId");
			var objId = widget.model.get("objId");
			var objName = element.find("#objId").val();
			var serviceOfferId = widget.model.get("serviceOfferId");
			var serviceOfferName = element.find("#serviceOfferId").val();
			var priorityLevel = widget.model.get("priorityLevel");
			var controlType = element.find("#controlType").val();
			var effDate = element.find("#effDate").val();
			var expDate = element.find("#expDate").val();
			var statusCd = element.find("#statusCd").val();
			
			var param = {
					"objType" : "1200",
					"regionId" : widget.model.get("regionId")
		    	};
			param.createStaff = gsession.staffId;
			var soUtil = widget.require("soUtil");
			var isNullOrEmpty = soUtil.isNullOrEmpty(protocolTempletConfigId);
			if (!isNullOrEmpty) {
				param.protocolTempletConfigId = protocolTempletConfigId;
			}
			
			isNullOrEmpty = soUtil.isNullOrEmpty(protocolTempletId);
			if (!isNullOrEmpty) {
				param.protocolTempletId = protocolTempletId;
			}
			
			isNullOrEmpty = soUtil.isNullOrEmpty(objId);
			if (!isNullOrEmpty) {
				param.objId = objId;
			}else{
				var soConst = this.require("soConst");
				if("Y" == soConst.getMda("SD_PROTOCOL_TEMPLET_SWITCH")){
					/*MDA 配置默认销售品*/
					param.objId = "-999"
				} else {
					widget.popup("指定销售品不能为空！");
					return;
				}
			}
			
			isNullOrEmpty = soUtil.isNullOrEmpty(objName);
			if (!isNullOrEmpty) {
				param.objName = objName;
			}
			
			isNullOrEmpty = soUtil.isNullOrEmpty(serviceOfferId);
			if (!isNullOrEmpty) {
				param.serviceOfferId = serviceOfferId;
			}else{
				widget.popup("业务动作不能为空！");
				return;
			}
			
			isNullOrEmpty = soUtil.isNullOrEmpty(serviceOfferName);
			if (!isNullOrEmpty) {
				param.serviceOfferName = serviceOfferName;
			}
			
			isNullOrEmpty = soUtil.isNullOrEmpty(priorityLevel);
			if (!isNullOrEmpty) {
				param.priorityLevel = priorityLevel;
			}
			
			isNullOrEmpty = soUtil.isNullOrEmpty(controlType);
			if (!isNullOrEmpty) {
				param.controlType = controlType;
			}
			
			isNullOrEmpty = soUtil.isNullOrEmpty(effDate);
			if (!isNullOrEmpty) {
				param.effDate = effDate;
			}
			
			isNullOrEmpty = soUtil.isNullOrEmpty(expDate);
			if (!isNullOrEmpty) {
				param.expDate = expDate;
			}
			
			isNullOrEmpty = soUtil.isNullOrEmpty(statusCd);
			if (!isNullOrEmpty) {
				param.statusCd = statusCd;
			}
						
			
	    	widget.callService("save", JSON.stringify(param),function(res) {
				var ret = JSON.parse(res);
				if (ret.resultCode == 0) {
					widget.popup("操作成功！",function(){
						widget._closePage();
					});
				}else{
					widget.popup("操作失败！失败的原因:"+ret.resultMsg);
				}				
			}, {
				async : false
			});
		},
		getValue : function() {
			var widget = this;
			var data = widget.model.toJSON();
		},
		_closePage : function() {
			var widget = this,
				element = $(widget.el);
			element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
		},
		_checkNum : function(e) {
			var widget = this,
				element = $(widget.el);
			debugger;
			var val = $(e.target).val();
			if(isNaN(val)){
				alert("请输入一个有效的数字！");
				$(e.target).val("");
		        return false;
		    }
		}
		 
	});
	 vita.widget.register("saveProtocolTempletConfig", saveProtocolTempletConfig, true); 
})(window.vita);