(function (vita) {
    var iTVOrderQuery = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_iTVOrderQuery",
        },

        _initialize: function () {
            var widget = this, element = $(widget.el);
        },
        global: {
            pageIndex: 1,
            pageSize: 5
        },
        
        _iTVOrderQuery: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("queryOrderList", JSON.stringify(params), "#iTVOrderListResult", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if (widget._getConds()) {
                                    widget.refreshPart("queryOrderList", JSON.stringify(params), "#orderList");
                                }
                                ;
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }

                }, {
                    async: false
                });
            }
            ;
        },
      
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this;
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };

            var iTVAccessNum = $("#iTVAccessNum").val();
            if(widget._isNullStr(iTVAccessNum)) {
                widget.popup("请输入号码！");
                return false;
            } else {
                params.iTVAccessNum = iTVAccessNum;
            }


            return params;
        }
    });
    vita.widget.register("iTVOrderQuery", iTVOrderQuery, true);
})(window.vita);