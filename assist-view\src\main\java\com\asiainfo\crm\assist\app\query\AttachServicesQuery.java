package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by wenhy on 2017/7/25.
 */
@Component("vita.attachServicesQuery")
public class AttachServicesQuery extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(AttachServicesQuery.class);

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map options = new HashMap();
        options.put("vsopUri", AssistMDA.VSOP_URI);
        return options;
    }
}
