<div data-widget="reverseInvoice" style="height: 100%">

    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>电子发票返销</div>
                                </div>

                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-5 ">
                                            <label class="col-md-4 control-label lablep">
                                                请输入购物车流水号:</label>
                                            <div class="col-md-8">
                                                <input type="text" id="coNbr" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-5">
                                            <div class="col-md-5" align="left">
                                                <button id="btn-query" type="button" class="btn btn-primary">查询</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-cancel" type="button" class="btn btn-primary">作废发票</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询结果</div>
                                </div>
                                <div class="wmin_content" id="invoiceResult">
                                    <div class="col-lg-12 mart10" id="invoiceList">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>勾选</th>
                                                <th>发票号</th>
                                                <th>发票状态</th>
                                                <th>打印日期</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                                #if($options != "null" && $options.invoiceList && $options.invoiceList != "null" &&
                                                $options.invoiceList.size() > 0)
                                                #foreach($invoice in $options.invoiceList)
                                                <tr>
                                                    <td><label class="wu-radio full absolute" data-scope=""><input
                                                            type="checkbox" name="payment"></label></td>
                                                    <td>$!{invoice.invoiceId}</td>
                                                    <td>$!{invoice.status}</td>
                                                    <td>$!{invoice.printDate}</td>
                                                </tr>
                                                #end
                                                #elseif($options != "null" && $options.invoiceList && $options.invoiceList != "null" &&
                                                $options.invoiceList.size() == 0)
                                                <tr><td align='center' colspan='7'>未查询到数据！<td></tr>
                                                #end
                                            </tbody>
                                        </table>
                                    </div>
                                    <!--翻页start -->
                                    <div id="showPageInfo">
                                    </div>
                                    #if($options.totalNumber && $options.totalNumber != "null")
                                    <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                    #end
                                    <!--翻页end -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
