(function(vita){
    var orderCheckBusi = vita.Backbone.BizView.extend({
        events : {
            "click #SearchBtn" : "_queryOrderCheckBusiList",
            "click #searchDel": "_searchParamsDel",
            "click #chooseArea" : "_chooseArea",
            "click [name='moduleRadio'] " : "_clickRadio",
            "click #addOrderCheckBusi" : "_addOrderCheckBusi",
            "click #updateOrderCheckBusi" : "_updateOrderCheckBusi",
            "click #deleteOrderCheckBusi" : "_deleteOrderCheckBusi"
        },
        _initialize : function() {
            var widget = this,
                element = $(widget.el);
            data = element.data("data");
            widget.model.set(data);
            //初始化查询
            widget._queryOrderCheckBusiList();
        },
        global : {
            pageIndex: 1,
            pageSize: 5, //每页记录数
            chooseArea : "../so/chooseArea",
            addOrderCheckBusi : "../query/addOrderCheckBusi",
            updateOrderCheckBusi : "../query/updateOrderCheckBusi"
        },
        _clickRadio : function(e){
            var widget = this,
                element = $(widget.el);
            var radio = $(e.target).closest('input');
            var data = radio.data('data');
            widget.model.set("selectedOrderCheckBusi",data);
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        /**
         * 获取查询条件参数对象
         */
        _getConds : function(pageIndex) {
            var widget = this,
                element = $(widget.el);
            gSession = widget.gSession;
            var param = {
                "pageInfo" : {
                    "pageIndex" : pageIndex,
                    "pageSize" : widget.global.pageSize
                }
            };
            var serviceName = widget.model.get("serviceName");
            if (!widget._isNullOrEmpty(serviceName)) {
                param.serviceName = serviceName;
            }
            var statusCd = element.find("#statusCd").val();
            if (!widget._isNullOrEmpty(statusCd)) {
                param.statusCd = statusCd;
            }
            return param;
        },
        _isNullOrEmpty: function (condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty;
        },
        /**
         * 清空查询条件
         */
        _searchParamsDel : function() {
            var widget = this,
                element = $(widget.el);
            widget.model.set("serviceName","");
            widget.model.set("statusCd","");
            $("#selectStatusCd").val("");
        },
        _addOrderCheckBusi : function(e) {
            var widget = this,
                element = $(widget.el);
            var button = $(e.target).closest("a");
            var id = button.attr("id") || "";
            if (id && widget.global[id]) {
                var dialogId = id + 'Dialog';
                var option = {
                    url : widget.global[id],
                    params : widget.model.get('addOrderCheckBusi'),
                    id : dialogId,
                    onClose : function(res) {
                            //新增之后刷新列表
                            widget._queryOrderCheckBusiList();
                    }
                };
                widget.dialog(option);
            }
        },
        _updateOrderCheckBusi : function(e){
            var widget = this,
                element = $(widget.el);
            var orderCheckBusi = widget.model.get("selectedOrderCheckBusi");
            if(orderCheckBusi == null || orderCheckBusi == undefined){
                widget.popup("请选择订单稽核业务配置！");
                return false;
            }
            var orderCheckBusiId = orderCheckBusi.orderCheckBusiId;
            var button = $(e.target).closest("a");
            var id = button.attr("id") || "";
            var param = {
                "orderCheckBusiId" : orderCheckBusiId
            }
            if (id && widget.global[id]) {
                var dialogId = id + 'Dialog';
                var option = {
                    url : widget.global.updateOrderCheckBusi,
                    params : param,
                    id : dialogId,
                    onClose : function(res) {
                            widget._queryOrderCheckBusiList();
                    }
                };
                widget.dialog(option);
            }
        },
        _queryOrderCheckBusiList : function(e) {
            var widget = this, element = $(widget.el);
            var param = widget._getConds(widget.global.pageIndex);
            element.find('#showPageInfo').empty();
            widget.refreshPart("queryOrderCheckBusi", param, ".table-hover",
                "orderCheckBusi", function(res) {
                    var paging = this.require("paging");
                    var totalNumber = $(res).find("input").eq(1).data("totalNumber");
                    if (parseInt(totalNumber) > 0) {
                        var e = paging.new({
                            recordNumber : widget.global.pageSize,
                            total : totalNumber,
                            pageIndex : widget.global.pageIndex,
                            callback : function(pageIndex){
                                //判断当前页
                                if(pageIndex == null || pageIndex == undefined || isNaN(pageIndex)){
                                    pageIndex = 1;
                                }
                                widget.model.set("pageIndex",pageIndex);
                                widget.refreshPart("queryOrderCheckBusi", widget._getConds(pageIndex), ".table-hover");
                            }
                        });
                        element.find('#showPageInfo').append(e.getElement());
                    }
                }, {
                    mask : true
                });
        },
        _deleteOrderCheckBusi : function() {
            var widget = this,el = $(widget.el),gSession = widget.gSession;
            var checkBusiInfo = widget.model.get("selectedOrderCheckBusi");
            var param = {
                "orderCheckBusiId": checkBusiInfo.orderCheckBusiId,
            };
            widget.callService("delOrderCheckBusi", JSON.stringify(param),function(res) {
                var ret = JSON.parse(res);
                if (ret.resultCode == 0) {
                    widget.popup("删除员工成功！",function(){
                        widget._queryOrderCheckBusiList();
                    });
                }else{
                    widget.popup("删除员工成功！失败的原因为："+ret.resultMsg);
                }
            }, {
                async : false
            });
        }

    });
    vita.widget.register("orderCheckBusi", orderCheckBusi, true);
})(window.vita);
