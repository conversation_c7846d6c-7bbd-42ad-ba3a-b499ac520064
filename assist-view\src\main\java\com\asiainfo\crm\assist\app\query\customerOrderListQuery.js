(function (vita) {
    var customerOrderListQuery = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_customerOrderListQuery",
            "click #btn-clear": "_clearCond",
            "click #regionIdBtn": "_chooseArea",
            "click #channelIdBtn": "_chooseChannelClick",
            "click #staffIdBtn": "_chooseStaff",
            "click #custIdBtn": "_chooseCust",
            "click [link=customerOrder]": "_queryCustomerOrderDetailLink",
            "click [link=orderItem]": "_queryOrderItemDetailLink",
            "click #qryArchives": "_qryArchives",
            "click #viewInvoice": "_viewInvoice",
            "click #printInvoice": "_printInvoice",
            "click #printReceipt": "_printReceipt",
            "click #printReceipts": "_printReceipts",
            "click #returnFee": "_returnFee",
            "click #orderDetail": "_queryCustomerOrderDetail",
//          "click #c_custOrderNbr": "_custOrderNbrClick",
            "click input[type=checkbox]": "_nbrClick",
            "click #afterPayCash": "_afterPayCash",
            "click #repairCharge": "_repairCharge",
            "click #commitFee": "_commitFee",
            "click #repealCharge": "_repealCharge",
            "click #qryTimeQuantum button" : "_qryTimeQuantum",
            "click #c_accNum" : "_accNumClick",
            // "click #c_orderItemNbr" : "_orderItemNbrClick",
            // "click #c_custOrderNbr" : "_custOrderNbrClick"
            "click #downloadPdf": "_downloadPdf",
            "click input[type=radio]": "_radioClick",
            "click #revokeOrder" : "_revokeOrder",
            "click #printOutStockOrder": "_printOutStockOrder",
            "click #viewOrder":"_viewOrder",
            "click #custoomerOrderLabels a" :"_doOperateRecorads"
        },
        //埋点方法
        _doOperateRecorads:function (e){
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var params = widget._getConds();
            if(!params){
                return
            }
            var orders = element.find('table input[name="payment"]:checked');
            var custOrderId = "";
            if(orders.length != 0) {
                var $order = $(orders[0]);
                var $tr = $($order.closest("tr"));
                var bindData = $($tr.find("[link=customerOrder]"));
                custOrderId = bindData.data("custOrderId");
                params.custOrderId = custOrderId;
                params.qry1Value = custOrderId;
            }

            var name = e.target.innerText;
            if(name){
                params.operateName = name.replace(/\s/g, "");
            }
            params.elId = e.target.id;
            params.glbSessionId = gSession.glbSessionId;
            widget.callService("logRecorads", JSON.stringify(params), function (res) {
                ret = res;
            },{async: false,mask: true,
                headers : {"regionId" : gSession.installArea}
            });
        },
        _doLinkLogRecorads:function (e){
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            params = e;
            params.glbSessionId = gSession.glbSessionId;
            widget.callService("logRecorads", JSON.stringify(params), function (res) {
                ret = res;
            },{async: false,mask: true,
                headers : {"regionId" : gSession.installArea}
            });
        },
        _viewInvoice: function () {
            var widget = this,element = $(widget.el);
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            var bindData = $($tr.find("[link=customerOrder]"));
            var custOrderId = bindData.data("custOrderId"),
                custOrderNbr = bindData.data("custOrderNbr");
            var data = element.data("data");
            var url = data.viewInvoice;
            url += "?custOrderId="+custOrderId+"&&custOrderNbr="+custOrderNbr;
            window.open(url);
        },
        _radioClick: function (e) {
            var widget = this, element = $(widget.el), data = element.data("data");
            var $tr = $(e.target).closest("tr");
            var statusCd = $($tr.find("[link=customerOrder]")).data("statusCd");
            if(statusCd == data.orderStatusCO) { // 客户需求确认 订单显示 作废按钮
                element.find("#revokeOrder").show();
            } else {
                element.find("#revokeOrder").hide();
            }
        },
        _revokeOrder: function () {
            var widget = this,element = $(widget.el), data = element.data("data");
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            var bindData = $($tr.find("[link=customerOrder]"));
            var custOrderId = bindData.data("custOrderId"), statusCd = bindData.data("statusCd");
            var params = {
                "custOrderId": custOrderId,
                "operType": "0" //operType 操作类型：0-作废，1-开通
            };

            //针对“客户确认中”状态的订单，可以进行作废操作
            if(statusCd == data.orderStatusCO) {
                widget.popup("确定要作废此订单？", function () {
                    widget.callService("revokeOrder", JSON.stringify(params),function (ret) {
                        if(ret.resultCode==0) {
                            widget._customerOrderListQuery();
                        }else{
                            widget.popup("订单作废失败：" + ret.resultMsg);
                        }
                    },{
                        async: false
                    });
                }, function () {
                });
            } else {
                widget.popup("只有“客户需求确认”状态的订单才可以作废！");
                return false;
            }
        },
        _printInvoice: function () {
            var widget = this,element = $(widget.el);
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            var custOrderId = $($tr.find("[link=customerOrder]")).data("custOrderId");
            // var custOrderNbr = $($tr.find("[link=customerOrder]")).data("custOrderNbr");
            var custOrderNbr = $tr.find("[link=customerOrder]").text();
            var data = element.data("data");
            var url = data.invoice;
            url += "?custOrderId="+custOrderId+"&custOrderNbr="+custOrderNbr;
            window.open(url);
        },
        _printReceipts:function (){
        	 var widget = this,element = $(widget.el);
             var orders = element.find('table input[name="payment"]:checked');
             if(orders.length == 0) {
                 widget.popup("请选择一条订单!");
                 return false;
             }
             var $order = $(orders[0]);
             var $tr = $($($order).closest("tr"));
             // var custOrderNbr = $($tr.find("[link=customerOrder]")).data("custOrderNbr");
             var orderItems = $tr.find("[link='orderItem']");
             var custOrderNbr = $tr.find("[link=customerOrder]").text(),
                 custOrderId = $tr.find("[link=customerOrder]").data("custOrderId"),
                 regionId = orderItems.length ? $(orderItems[0]).parent().data("regionId") : "";
             var url = element.data("data").receipts;
             url = url + "?custOrderNbr=" + custOrderNbr + "&custOrderId=" + custOrderId + "&regionId=" + regionId + "&source=assist";
             window.open(url);
        },
        
        _printReceipt: function () {
            var widget = this,element = $(widget.el);
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($($order).closest("tr"));
            // var custOrderNbr = $($tr.find("[link=customerOrder]")).data("custOrderNbr");
            var orderItems = $tr.find("[link='orderItem']");
            var custOrderNbr = $tr.find("[link=customerOrder]").text(),
                custOrderId = $tr.find("[link=customerOrder]").data("custOrderId"),
                regionId = orderItems.length ? $(orderItems[0]).parent().data("regionId") : "";
            var data = element.data("data"), url = data.receipt, statusCd = $tr.find("[link=customerOrder]").data("statusCd");
            url = url + "?custOrderNbr=" + custOrderNbr + "&custOrderId=" + custOrderId + "&regionId=" + regionId + "&source=assist";
            if (data.orderStatusNAEM == statusCd) {
           	 url += "&sysSource=" + data.cmSysSource;
            }
            window.open(url);
        },
        _createCashierDialog: function(params,closeInfo) {
            var widget = this;
            var gSession = widget.gSession;
            params.channelId = gSession.curChannelId;
            var dialogId = "cashierDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.cashier,
                params : params,
                onClose : function(res){
                    if(closeInfo&&typeof id === "string"){
                        widget.popup(closeInfo);
                    }
                }
            });
        },
        _returnFee: function () {
            //未竣工补/退费
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            if(!gSession.curChannelId) {
                widget.popup("请先选择渠道！");
                return false;
            }
            var data = element.data("data");
            var ret = widget._chenckFeePriv(data.unCompletePriv);
            if(!(ret && ret.isHave)) {
                widget.popup("管理员未开放当前工号【未竣工补/退费】权限！");
                return false;
            }
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            var custOrderId = $($tr.find("[link=customerOrder]")).data("custOrderId");
            var custOrderNbr = $tr.find("[link=customerOrder]").text();
            var statusCd = $($tr.find("[link=customerOrder]")).data("statusCd");
            statusCd = statusCd.toString();

            //根据订单查询费用项的来源判断，如果是押金费用不能做补退费
            var oneParam ={
                custOrderId:custOrderId
            };
            var isTrue=false;
            widget.callService("queryOneItemResultsSource",JSON.stringify(oneParam),function (ret) {
                if (ret != null && ret != "" && ret.isTrue){
                    isTrue=true;
                }
            },{
                async: false
            });
            if(isTrue){
                widget.popup("当前订单为押金订单不能进行补退费!");
                return false;
            }

            var unCompleteOrderStatus = data.unCompleteOrderStatus;
            if($.inArray(statusCd, unCompleteOrderStatus) == -1) {
                widget.popup("当前订单【"+custOrderNbr+"】状态不是未竣工状态！");
                return false;
            }
            var params = {
                custOrderNbr : custOrderNbr,
                isChange : 1
            }
            widget._createCashierDialog(params);
        },
		    _checkAfterPayCash:function(custOrderId){
            return false;
        },
        _afterPayCash: function () {
            //先装后付收费
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            if(!gSession.curChannelId) {
                widget.popup("请先选择渠道！");
                return false;
            }
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var data = element.data("data");
            var ret = widget._chenckFeePriv(data.afterPayPriv);
            if(!(ret && ret.isHave)) {
                widget.popup("管理员未开放当前工号【先装后付收费】权限！");
                return false;
            }

            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            // var custOrderNbr = $($tr.find("[link=customerOrder]")).data("custOrderNbr");
            var custOrderNbr = $tr.find("[link=customerOrder]").text();
            var params = {
                coNbr:custOrderNbr
            }
            var isAfterPayOrder = false;
            widget.callService("isOrderCommAttrItem",JSON.stringify(params),function (ret) {
                if (ret != null && ret != "" && ret.isAfterPayOrder == "Y"){
                    isAfterPayOrder = true;
                }
            },{async: false});
            if(!isAfterPayOrder){
                widget.popup("当前订单不是先装后付订单！");
                return true;
            }
            var custOrderId = $($tr.find("[link=customerOrder]")).data("custOrderId");
			      if(widget._checkAfterPayCash(custOrderId)){
                widget.popup("当前先装后付订单已经确认收费，无须再次收费！");
                return true;
            }
            var params = {
                custOrderNbr : custOrderNbr,
                custOrderId:custOrderId,
                isFirst : 1
            }
            widget._createCashierDialog(params,'提交收费成功！');
        },
        _repairCharge: function () {
            //未收费订单补收
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            if(!gSession.curChannelId) {
                widget.popup("请先选择渠道！");
                return false;
            }
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            // var custOrderNbr = $($tr.find("[link=customerOrder]")).data("custOrderNbr");
            var custOrderNbr = $tr.find("[link=customerOrder]").text();
            var statusCd = $($tr.find("[link=customerOrder]")).data("statusCd");
            var data = element.data("data");
            if(statusCd == data.orderStatusNC || statusCd == data.orderStatusFEE){
                var params = {
                    custOrderNbr : custOrderNbr,
                    isNoPay : 1
                }
                if(statusCd == data.orderStatusFEE){
                	params.sysSource = data.cmSysSource;
                }
                widget._createCashierDialog(params);
            }else{
                widget.popup("当前订单不是未收费订单!");
            }
        },
        _commitFee: function () {
            //先送计费，后收费收费，只有归档的才收 301200
            //修改为费用表只有特殊工号和渠道的才能收费
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            if(!gSession.curChannelId) {
                widget.popup("请先选择渠道！");
                return false;
            }
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            // var custOrderNbr = $($tr.find("[link=customerOrder]")).data("custOrderNbr");
            var custOrderNbr = $tr.find("[link=customerOrder]").text();
            var custOrderId = $($tr.find("[link=customerOrder]")).data("custOrderId");
            var statusCd = $($tr.find("[link=customerOrder]")).data("statusCd");
            var data = element.data("data");
            //根据订单ID查询费用信息
            var specialChannel =data.specialChannel;//特殊渠道
            var specialStaffB =data.specialStaffB;//特殊工号
            var oneParam ={
                custOrderId:custOrderId
            };

            //根据订单查询费用项的工号与渠道并判断是否是特殊工号与特殊渠道
            widget.callService("queryOneItemResults",JSON.stringify(oneParam),function (ret) {
                if (ret != null && ret != "" && ret.isTrue){
                    var params = {
                        custOrderNbr : custOrderNbr,
                        isNoPay : 1
                    };
                    widget._createCashierDialog(params);
                }else{
                    widget.popup("当前订单不是先送计费订单!");
                }
            },{
                async: false
            });
        },
        _repealCharge: function () {
            //已竣工补/退费
            var widget = this,element = $(widget.el);
            var data = element.data("data");
            var gSession = widget.gSession;
            if(!gSession.curChannelId) {
                widget.popup("请先选择渠道！");
                return false;
            }
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            // var custOrderNbr = $($tr.find("[link=customerOrder]")).data("custOrderNbr");
            var custOrderNbr = $tr.find("[link=customerOrder]").text();
            //根据订单查询费用项的来源判断，如果是押金费用不能做补退费
            var custOrderIdOne = $($tr.find("[link=customerOrder]")).data("custOrderId");
            var oneParam ={
                custOrderId:custOrderIdOne
            };
            var isTrue=false;
            widget.callService("queryOneItemResultsSource",JSON.stringify(oneParam),function (ret) {
                if (ret != null && ret != "" && ret.isTrue){
                    isTrue=true;
                }
            },{
                async: false
            });
            if(isTrue){
                widget.popup("当前订单为押金订单不能进行补退费!");
                return false;
            }

            var param = {
                "glbSessionId": gSession.glbSessionId,
                "sysUserId": gSession.systemUserId
            }
            var ret = {};
            widget.callService("qryReturnFeePriv", param, function (res) {
                ret = res;
            },{async: false,mask: true,headers : {"regionId" : gSession.installArea}
            });

            //这里逻辑参照收银台，跟他们保持一致
            //收银台已竣工补退费--------------------------------------------------------------------
            var createOrgId = $($tr.find("[link=customerOrder]")).data("createOrgId");
            var acceptDate = $($tr.find("[link=customerOrder]")).data("acceptDate");
            //判断是否只能对工号归属渠道的购物车做退补费操作
            if(null==ret.completedReturn || ret.completedReturn=="" || ret.completedReturn!="1" ){
                // if(createOrgId!=gSession.curChannelId){
                    widget.popup("您没有操作已竣工订单补退费的权限！");
                    return false;
                // }
            }

            //判断是否只能对工号归属渠道的购物车做退补费操作
            if(null!=ret.isLimitChannel && ret.isLimitChannel=="1"){
                if(createOrgId!=gSession.curChannelId){
                    widget.popup("您没有操作该渠道订单补退费的权限！");
                    return false;
                }
            }

            //判断是否只能对90天内的购物车做退补费操作
            if(null!=ret.isLimit90Day && ret.isLimit90Day=="1"){
                var date1 = new Date();
                var date2 = new Date(acceptDate)

                var s1 = date1.getTime(),s2 = date2.getTime();
                var total = (s1 - s2)/1000;

                var day = parseInt(total / (24*60*60));//计算整数天数
                var afterDay = total - day*24*60*60;//取得算出天数后剩余的秒数
                var hour = parseInt(afterDay/(60*60));//计算整数小时数
                var afterHour = total - day*24*60*60 - hour*60*60;//取得算出小时数后剩余的秒数
                var min = parseInt(afterHour/60);//计算整数分
                var afterMin = total - day*24*60*60 - hour*60*60 - min*60;//取得算出分后剩余的秒数

                if(day>90 || (day==90 && afterDay > 0)){
                    widget.popup("该订单生成时间已经已经超过90天，您没有操作该订单补退费的权限！");
                    return false;
                }
            }
            //收银台已竣工补退费--------------------------------------------------------------------


            var statusCd = $($tr.find("[link=customerOrder]")).data("statusCd");
            statusCd = statusCd.toString();
            var completeOrderStatus = data.completeOrderStatus;
            if($.inArray(statusCd, completeOrderStatus) == -1) {
                widget.popup("当前订单【"+custOrderNbr+"】状态不是已竣工状态！");
                return false;
            }
            var params = {
                custOrderNbr : custOrderNbr,
                isChange : 1
            }
            widget._createCashierDialog(params);
        },
        _chenckFeePriv: function(privCode) {
            var widget = this;
            var gSession = widget.gSession;
            var param = {
                "glbSessionId": gSession.glbSessionId,
                "sysUserId": gSession.systemUserId,
                "privCode" : privCode
            }
            var ret = {
                hasPriv : false
            };
            widget.callService("chenckFeePriv", param, function (res) {
                ret = res;
            },{async: false,mask: true,
                headers : {"regionId" : gSession.installArea}
            });
            return ret;
        },
        _initialize: function () {
            var widget = this, element = $(widget.el), global = widget.global;
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var nowDate = new Date();
            var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
            var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
            var startYear = nowDate.getFullYear() - 5, endYear = nowDate.getFullYear();
            var soConst = this.require("soConst"),isShowAmount = soConst.getMda("SWITCH_QUERY_ORDER_SHOW_AMOUNT")=="Y"?true:false;
            widget.model.set("isShowAmount",isShowAmount);

            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear:endYear
                });
            }
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear:endYear
                });
            }
            //日期控件设置默认时间
            widget.global.defaultBeginTime = widget._getFormatDate(widget.global.sevenDayCount); //默认是七天
            widget.global.defaultEndTime = widget._currentDate();
            widget._setDefaultDateTime();

            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
            var channelName = "";
            for(var channels=gSession.channels,i=0; channels.length && i<channels.length; i++) {
                if(gSession.curChannelId == channels[i].orgId) {
                    channelName = channels[i].channelName;
                    break;
                }
            }
            if(gSession.curChannelId && gSession.curChannelId != "") {
                element.find("#channelId").val(channelName).attr("value", gSession.curChannelId);
            } else {
                widget._chooseChannel(true);
            }
            element.find("#staffId").val(gSession.staffName).attr("value", gSession.staffId);

            //判断是否为首页近三个月退单页面请求
            var options = element.data('data');
            if(options&&options.backOrderReq&&options.backOrderReq!="null"){
                var backOrderReq = options.backOrderReq;
                if(backOrderReq.beginDate&&backOrderReq.beginDate!="null"){
                    widget._backOrder(backOrderReq.beginDate);
                }else{
                    widget._backOrder(widget._getFormatDate(widget.global.ninthDayCount));
                }
            };

            //nbrLabelsStr初始化
            var nbrLabels = widget.global.nbrLabels;
            var nbrLabelsStr = "#"+nbrLabels[0];
            for(var k=1;k<nbrLabels.length;k++) {
                nbrLabelsStr = nbrLabelsStr + ",#" + nbrLabels[k];
            }
            widget.global.nbrLabelsStr = nbrLabelsStr;
            if(isShowAmount){
                element.find("[data-toggle=tooltip]").tooltip();
            }
            global.emergOn = options.emergOn == "Y";
        },
        _backOrder:function(beginDate){
            var widget = this, el = $(widget.el);
            //设置退单参数
            var data = el.data("data");
            var statusCd =data.orderStatusTD;
            //设置页面参数，后触发搜索按钮
            //营业员选框，起始日期，订单状态选框及选择退单
            el.find("#c_staffId").attr("checked", "checked");
            widget._changeTimeQuanTum(widget.global.ninthDayCount);
            el.find("#c_statusCd").attr("checked","checked");
            el.find("#statusCd").val(statusCd);
            widget._customerOrderListQuery();
        },
        _getFormatDate: function(days) {
            var date = new Date();
            var yesterday_milliseconds = date.getTime()+days*1000*60*60*24;
            var yesterday = new Date();
            yesterday.setTime(yesterday_milliseconds);
            var strYear = yesterday.getFullYear();
            var strDay = yesterday.getDate();
            var strMonth = yesterday.getMonth()+1;
            if(strMonth<10)
            {
                strMonth="0"+strMonth;
            }
            if(strDay<10)
            {
                strDay="0"+strDay;
            }

            /*var hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
            var minute = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
            var second = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
            var time = hour+":"+minute+":"+second;*/
            var datastr = strYear+"-"+strMonth+"-"+strDay;
            return datastr;
        },
        global: {
            pageIndex: 1,
            pageSize: 5,
            preset: "date",
            chooseArea: "../comm/chooseArea",
            chooseChannel: "../comm/chooseChannel",
            chooseStaff: "../comm/chooseStaff",
            chooseCust: "../comm/chooseCust",
            cashier: "../action/cashier",
            defaultBeginTime:"",
            defaultEndTime:"",
            currentDayCount:0,
            sevenDayCount:-6,
            lastMonthDayCount:-29,
            ninthDayCount:-89,
            nbrLabels: ["c_orderItemNbr","c_custOrderNbr","c_orderItemId","c_extCustOrderId"],
            emergOn: false
        },
        _clearCond: function () {
            var widget = this, element = $(widget.el);
            var $checked = element.find(".form-group").find("input[type=checkbox]:checked");
            $.each($checked, function (i,chkInput) {
                chkInput.click();
                var $input = $(chkInput).closest(".form-group").find("input.form-control");
                $input.val("");
                $input.attr("value", null);
            })
            //时间设置为7天
            widget._changeTimeQuanTum(widget.global.sevenDayCount);
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _chooseChannelClick: function() {
            var widget = this,element = $(widget.el);
            widget._chooseChannel(false);
        },
        _chooseChannel: function(isNeedRefreshSession) {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseChannel,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);

                    if(isNeedRefreshSession) {
                        gSession.channelId   = data.orgId;
                        gSession.curChannelId= data.orgId;
                        gSession.curChannelNbr  = data.channelNbr;
                        gSession.channelName = data.channelName;
                        gSession.channelRegionId = data.channelRegionId;
                        gSession.channelRegionName = data.channelRegionName;
                        gSession.channelClass = data.channelClass;
                        gSession.orgId       = data.orgId;
                        gSession.orgName     = data.orgName;

                        gSession.orderArea   	 = data.channelRegionId; // 订单地区
                        gSession.installArea 	 = data.channelRegionId; // 安装地区
                        gSession.installAreaName = data.channelRegionName;
                        var param = {
                            curChannelId  : data.orgId,
                            curChannelNbr : data.channelNbr,
                            orderArea     : gSession.orderArea,
                            installArea   : gSession.installArea
                        };
                        widget.refreshSession(param);
                    }
                }
            });
        },
        _chooseStaff: function () {
            var widget = this,element = $(widget.el);
            var compCode = "chooseStaff";
            var dialogId = "chooseStaffDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseStaff,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data || data == "") {
                        return false;
                    }
                    element.find("#staffId").val(data.staffName).attr("value", data.staffId);
                }
            });
        },
        _chooseCust: function () {
            var widget = this,element = $(widget.el);
            var compCode = "chooseCust";
            var dialogId = "chooseCustDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseCust,
                params : {
                	widgetSource: widget.widgetName
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data || !data.validate) {
                        return false;
                    }
                    element.find("#custId").val(data.custName).attr("value", data.custId);
                }
            });
        },
        _nbrClick: function(e) {
            var widget = this, element = $(widget.el),
                nbrLabels = widget.global.nbrLabels,
                nbrLabelsStr = widget.global.nbrLabelsStr;
            var currentTarget = $(e.currentTarget);
            //界面样式渲染后才会調click事件，所以当状态是checked的时候才去清空其他选项
            var nbrChk = element.find(nbrLabelsStr).filter(":checked");
            var beginDate = element.find("#beginDate"),endDate =  element.find("#endDate");
            var dateTimeFlag = "on";
            if(!beginDate.prop("disabled")){
                widget.global.defaultBeginTime = beginDate.val();
                widget.global.defaultEndTime = endDate.val();
            }
            if(nbrChk.length != 0){
                // 本地化校验方法
                var $checked = widget._checkedNbrLabelsStr(nbrLabelsStr);
                $.each($checked, function (i,chkInput) {
                    $(chkInput).attr('checked', false);
                    // var $input = $(chkInput).closest(".form-group").find("input.form-control");
                    // $input.val("");
                    // $input.attr("value", null);
                });
                dateTimeFlag = "off";
            } else {
                dateTimeFlag = "on";
            }
            //时间控件开关
            widget._dateTimeSwitch(dateTimeFlag);
            //如果本次点击的是nbrLabels中的一个，
            // 需要判断是否有其他nbrLabels中的成员是checked的
            //如果有，则本次点击无效
            var currentTargetId = currentTarget.attr("id");
            if($.inArray(currentTargetId,nbrLabels)>-1) {
                var nbrFlag = false;
                $.each(nbrLabels,function(i,n){
                    if (n == currentTargetId) {
                        return true;
                    }
                    if (element.find("#" + n).is(":checked")) {
                        nbrFlag = true;
                        return false;
                    }
                });
                if(nbrFlag){
                    currentTarget.attr("checked", false);
                }
            }
        },
        _customerOrderListQuery: function () {
            var widget = this,element = $(widget.el);
            var params = widget._getConds(),isShowAmount = widget.model.get("isShowAmount");
            if (params) {
                widget.refreshPart("queryCustomOrderList", JSON.stringify(params), "#orderListResult", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if(isShowAmount){
                        $(res).find("[data-toggle=tooltip]").tooltip();
                    }
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if(_pageIndex > 1 && !params.pageInfo.rowCount) {
                                    params.pageInfo.rowCount = totalNumber;
                                }
                                if (widget._getConds()) {
                                    widget.refreshPart("queryCustomOrderList", JSON.stringify(params), "#orderList",function (res) {
                                        var re = $(res);
                                    	if(isShowAmount){
                                    		re.find("[data-toggle=tooltip]").tooltip();
                                        }
                                        widget._dealEmerg(re, params.qryScope);
                                    });
                                };
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }
                    widget._dealEmerg(r, params.qryScope);
                }, {
                    async: false
                });
            };
        },
        _queryCustomerOrderDetail: function () {
            var widget = this,element = $(widget.el);
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            var custOrderId = $($tr.find("[link=customerOrder]")).data("custOrderId");
            window.open("../../crm/so/olDetailInfo?custOrderId="+custOrderId);
        },
        _queryCustomerOrderDetailLink: function(e){
            var widget = this;
            var aEl = $(e.target).closest("a");
            var custOrderId = aEl.data("custOrderId");
            if(!custOrderId){
                widget.popup("订单不存在,请重新查询后再试.");
                return;
            }
            var data = {
                custOrderId:custOrderId
            };
            // widget.end("olDetailInfo",data); 这里没有配流程不能用框架的end
            window.open("../../crm/so/olDetailInfo?custOrderId="+custOrderId);
            widget._doLinkLogRecorads({
                custOrderId:custOrderId,
                qry1Value: custOrderId,
                point: "customerOrderLinkOrderNbr"
            })
        },
        _queryOrderItemDetailLink: function (e) {
            var widget = this;
            var aEl = $(e.target).closest("a");
            var custOrderId = aEl.parent().data("custOrderId");
            var custOrderNbr = aEl.parent().data("custOrderNbr");//购物车流水
            var orderItem = aEl.parent().data("orderItem");
            if (!custOrderId) {
                widget.popup("订单不存在,请重新查询后再试.");
                return;
            }
            if (!orderItem.orderItemId) {
                widget.popup("订单不存在,请重新查询后再试.");
                return;
            }
            var uri = "";
            if (orderItem && orderItem.orderItemCd && orderItem.orderItemCd != "") {
                switch (parseInt(orderItem.orderItemCd)) {
                	case 1100:
                		uri = "../../crm/so/acctOrderItemInfo";
                		break;
                    case 1200:
                        uri = "../../crm/so/boOfferDetailInfo";
                        break;
                    case 1300:
                        uri = "../../crm/so/boProdDetailInfo";
                        break;
                    case 1400:
                        uri = "../../crm/so/boMktDetailInfo";
                        break;
                    case 1500:
                    	uri = "../../crm/offerPkg/boOfferPkgDetailInfo";
                    	break;
                    default:
                    	break;
                }
            }
            if (uri == "") {
                widget.popup("订单项类型不存在,请重新查询后再试.");
                return;
            }

            var param = "?custOrderId="+custOrderId+"&orderItemId="+orderItem.orderItemId+
                    "&orderItemCd="+orderItem.orderItemCd+"&isIndependent="+orderItem.isIndependent+
                    "&offerType="+orderItem.offerType+"&custOrderNbr="+custOrderNbr;
            window.open(uri+param);
            widget._doLinkLogRecorads({
                custOrderId:custOrderId,
                orderItemId:orderItem.orderItemId,
                offerType:orderItem.offerType,
                custOrderNbr: custOrderNbr,
                orderItemCd: orderItem.orderItemCd,
                qry1Value: custOrderId,
                qry2Value: orderItem.orderItemId,
                point: "customerOrderLinkBusiType"
            })
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this,element = $(widget.el);
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            var paramCnt = 0;
            var regionId = element.find("#regionId").attr("value");
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.regionId = regionId;
            }
            params.isAcceptRegionId = "1";
            if (element.find("#c_channelId").is(":checked")) {
                paramCnt++;
                var channelId = element.find("#channelId").attr("value");
                if(widget._isNullStr(channelId)) {
                    widget.popup("请选择渠道！");
                    return false;
                } else {
                    params.channelId = channelId;
                }
            }

            if (element.find("#c_staffId").is(":checked")) {
                paramCnt++;
                var staffId = element.find("#staffId").attr("value");
                if(widget._isNullStr(staffId)) {
                    widget.popup("请选择营业员！");
                    return false;
                } else {
                    params.staffId = staffId;
                }
            }

            if (element.find("#c_custOrderNbr").is(":checked")) {
                paramCnt++;
                var custOrderNbr = $.trim(element.find("#custOrderNbr").val());
                if(widget._isNullStr(custOrderNbr)) {
                    widget.popup("请输入流水号！");
                    return false;
                } else {
                    params.custOrderNbr = custOrderNbr;
                }
            }

            if (element.find("#c_orderItemNbr").is(":checked")) {
                paramCnt++;
                var orderItemNbr = $.trim(element.find("#orderItemNbr").val());
                if(widget._isNullStr(orderItemNbr)) {
                    widget.popup("请输入订单项流水！");
                    return false;
                } else {
                    params.orderItemNbr = orderItemNbr;
                }
            }
            if (element.find("#c_orderItemId").is(":checked")) {
                paramCnt++;
                var orderItemId = $.trim(element.find("#orderItemId").val());
                if(widget._isNullStr(orderItemId)) {
                    widget.popup("请输入订单项Id！");
                    return false;
                }
				if(orderItemId.length >= 19){
					widget.popup("订单项Id输入长度超过限制的最大长度！");
					return false;
				}
                params.orderItemId = orderItemId;
            }
            if (element.find("#c_extCustOrderId").is(":checked")) {
                paramCnt++;
                var extCustOrderId = $.trim(element.find("#extCustOrderId").val());
                if(widget._isNullStr(extCustOrderId)) {
                    widget.popup("请输入集团编号！");
                    return false;
                } else {
                    params.extCustOrderId = extCustOrderId;
                }
            }

            if (element.find("#c_custId").is(":checked")) {
                paramCnt++;
                var custId = element.find("#custId").attr("value");
                if(widget._isNullStr(custId)) {
                    widget.popup("请选择客户！");
                    return false;
                } else {
                    params.custId = custId;
                }
            }

            if (element.find("#c_accNum").is(":checked")) {
                paramCnt++;
                var accNum = $.trim(element.find("#accNum").val());
                if(widget._isNullStr(accNum)) {
                    widget.popup("请输入号码！");
                    return false;
                } else {
                    params.accNum = accNum;
                }
            }

            //选择账户优惠必需选择接入号
            if (element.find("#c_isProdAcctRel").is(":checked")) {
                paramCnt++;
                params.isProdAcctRel = "Y";
                if (element.find("#c_accNum").is(":checked")) {
                    var accNum = $.trim(element.find("#accNum").val());
                    if (widget._isNullStr(accNum)) {
                        widget.popup("请输入号码！");
                        return false;
                    }
                }else{
                    widget.popup("选择账户优惠必需选择接入号！");
                    return false;
                }
            }
            if (element.find("#c_mktResInstNbr").is(":checked")) {
                paramCnt++;
                var mktResInstNbr = $.trim(element.find("#mktResInstNbr").val());
                if(widget._isNullStr(mktResInstNbr)) {
                    widget.popup("请输入终端串号！");
                    return false;
                } else {
                    params.mktResInstNbr = mktResInstNbr;
                }
            }

            var startTime = element.find("#beginDate").val();
            var endTime = element.find("#endDate").val();
            var accNumComp = element.find("#c_accNum");
            startTime = startTime ? startTime + " 00:00:00" : startTime;
            endTime = endTime ? endTime + " 23:59:59" : endTime;
            // 本地化校验方法
        	if(!accNumComp.is(":checked")&& element.find("input[type=checkbox]:checked").filter("#c_custId,#c_staffId,#c_channelId").length&&widget._unCheck()){
        		if (widget._isNullStr(startTime) || widget._isNullStr(endTime)) {
        			widget.popup("选择渠道、营业员、所属客户、号码这四个条件之一时， 开始时间、结束时间不能空!");
            		return false;
        		}
        	}
        	if (!widget._isNullStr(startTime) && !widget._isNullStr(endTime)) {
                if(startTime>=endTime){
                    widget.popup("结束时间应大于开始时间!");
                    return false;
                }
                var start  = new Date(startTime.replace(/-/g,"/")).getTime();
                var end = new Date(endTime.replace(/-/g,"/")).getTime();

                var flag = end - start  > 90*24*60*60*1000
                if (flag) {
                    widget.popup("开始、结束时间跨度不能超出90天!");
                    return false;
                }
			}
            if(!widget._isNullStr(startTime)||!widget._isNullStr(endTime)) {
                params.acceptDateScope = {};
                if (!widget._isNullStr(startTime)) {
                    params.acceptDateScope.beginDate = startTime;
                }
                if (!widget._isNullStr(endTime)) {
                    params.acceptDateScope.endDate = endTime;
                }
            }
            if (element.find("#c_statusCd").is(":checked")) {
                paramCnt++;
                var statusCd = element.find("#statusCd").val();
                if(widget._isNullStr(statusCd)) {
                    widget.popup("请选择订单状态！");
                    return false;
                }
                if (!element.find("#c_custId").is(":checked") && !element.find("#c_staffId").is(":checked") && !element.find("#c_channelId").is(":checked")) {
                	widget.popup("选择订单状态条件时，“渠 道、营业员、所属客户” 这3个条件里面必须选一个！");
                    return false;
                }
                params.statusCds = [statusCd];
            }

            var qryScope = $("#qryScope").val();
            if (widget._isNullStr(qryScope)) {
                widget.popup("请选择查询范围！");
                return false;
            } else {
                params.qryScope = qryScope;
            }
            paramCnt = widget._paramCntLocal(paramCnt);
            if(paramCnt < 1) {
                widget.popup("请至少选择一个查询条件！");
                return false;
            }
            return params;
        },
        _paramCntLocal : function (paramCnt){
            return paramCnt;
        },
        _qryTimeQuantum:function(e){
            var widget = this,element = $(widget.el),
                timeQuantum = $(e.currentTarget).attr("name"),
                dayCount;
            timeQuantum = timeQuantum.substring(timeQuantum.indexOf("_") + 1);
            switch(timeQuantum){
                case "1":
                    dayCount = widget.global.currentDayCount;
                    break;
                case "7":
                    dayCount = widget.global.sevenDayCount;
                    break;
                case "30":
                    dayCount = widget.global.lastMonthDayCount;
                    break
                case "90":
                    dayCount = widget.global.ninthDayCount;
                    break
                default:
                    dayCount = widget.global.currentDayCount;
            }
            widget._changeTimeQuanTum(dayCount);
        },
        _changeTimeQuanTum:function(dayCount){
            var widget = this,element = $(widget.el),beginDate = element.find('#beginDate'),endDate = element.find('#endDate');
            if(dayCount==widget.global.currentDayCount){
                beginDate.val(widget._currentDate());
            }else if(dayCount==widget.global.lastMonthDayCount){
                beginDate.val(widget._lastMonthDate());
            }else{
                beginDate.val(widget._getFormatDate(dayCount));
            }
            endDate.val(widget._getFormatDate(widget.global.currentDayCount));
        },
        //当天从0点开始
        _currentDate:function(){
            var widget = this, el = $(widget.el);
            var nowDate = widget._getFormatDate(widget.global.currentDayCount);
            return nowDate;
        },
        //设置默认时间
        _setDefaultDateTime : function(){
            var widget = this, element = $(widget.el),
                beginDate = element.find("#beginDate"),
                endDate = element.find("#endDate");
            beginDate.val(widget.global.defaultBeginTime);
            endDate.val(widget.global.defaultEndTime);
        },
        //时间控件开发方法
        _dateTimeSwitch:function(flag){
            var widget = this, element = $(widget.el),
                beginDate = element.find("#beginDate"),
                endDate = element.find("#endDate");
            if(flag==="on"){
                widget._setDefaultDateTime();
                beginDate.prop("disabled", false);
                endDate.prop("disabled", false);
                element.find("#qryTimeQuantum").find('button').removeAttr('disabled');
            }else{
                beginDate.val("");
                endDate.val("");
                element.find("#qryTimeQuantum").find('button').attr('disabled','disabled');
                beginDate.prop("disabled", true);
                endDate.prop("disabled", true);
            }
        },
        //获取上个月时间
        _lastMonthDate:function (){
            var Nowdate = new Date();
            var vYear = Nowdate.getFullYear();
            var vMon = Nowdate.getMonth() + 1;
            var vDay = Nowdate.getDate();
            //每个月的最后一天日期（为了使用月份便于查找，数组第一位设为0）
             var daysInMonth = new Array(0,31,28,31,30,31,30,31,31,30,31,30,31);
            if(vMon==1){
                vYear = Nowdate.getFullYear()-1;
                vMon = 12;
                }else{
                 vMon = vMon -1;
                }
            //若是闰年，二月最后一天是29号
            if(vYear%4 == 0 && vYear%100 != 0  || vYear%400 == 0 ){
                  daysInMonth[2]= 29;
            }
            if(daysInMonth[vMon] < vDay){
                     vDay = daysInMonth[vMon];
            }
            if(vDay<10){
                    vDay="0"+vDay;
            }
            if(vMon<10){
                    vMon="0"+vMon;
            }
            var date =vYear+"-"+ vMon +"-"+vDay;
            return date;
        },
        _accNumClick : function(){
            var widget = this, element = $(widget.el),
                accNumLabel = element.find("#c_accNum"),
                beginDate = element.find("#beginDate"),
                endDate = element.find("#endDate");
	    //如果nbrLabelsStr成员有checked状态，则返回
            var nbrChk = element.find(widget.global.nbrLabelsStr).filter(":checked");
            if(nbrChk.length){
                return;
            }
            if(accNumLabel.is(":checked")){
                //查询时间改为不限制
                beginDate.val("");
                endDate.val("");
            }else if(!beginDate.val()||!endDate.val()){
                //如果开始或结束时间是空的，默认选择为7天
                widget.global.defaultBeginTime = widget._getFormatDate(widget.global.sevenDayCount);
                widget.global.defaultEndTime = widget._currentDate();
                widget._setDefaultDateTime();
            }
        },
        _downloadPdf:function (){
            var widget = this, element = $(widget.el), gSession = widget.gSession;
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($($order).closest("tr"));
            var custOrderNbr = $tr.find("[link=customerOrder]").text(),
                custOrderId = $tr.find("[link=customerOrder]").data("custOrderId"),
                createStaff = $tr.find("[link=customerOrder]").data("createStaff");
            var params = {
                custOrderIds: [custOrderId],
                privParam : {
                    "glbSessionId": gSession.glbSessionId,
                    "sysUserId": gSession.systemUserId
                },
                staffParam : {
                    "createStaff" : createStaff,
                    "staffId" : gSession.staffId
                }
            };
            widget.callService("queryDocGrpNbr", params, function (re) {
                if(re.resultCode==0) {
                    // vita.ajax("../query/downloadPdfReceiptToPdf",{"olId":re.docGrpNbr},function(){})
                    var url = "../query/downloadPdfReceiptToPdf?olId=" + re.docGrpNbr;
                    var form = $("<form></form>").attr("action", url).attr("method", "post");
                    form.appendTo("body").submit().remove();
                }else{
                    widget.popup(re.resultMsg);
                }
            });
        },
        _qryArchives: function () {
            var widget = this,element = $(widget.el);
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            var custOrderNbr = $tr.find("[link=customerOrder]").text();
            var custOrderId = $($tr.find("[link=customerOrder]")).data("custOrderId");
            var data = element.data("data");
            var url = data.qryArchives + "?custOrderNbr=" + custOrderNbr + "&custOrderId=" + custOrderId;
            window.open(url);
        },

        //打印出库单
        _printOutStockOrder: function () {
            var widget = this,element = $(widget.el);
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length!=1) {
                widget.popup("请选择一条订单!");
                return false;
            }
            //先查询是否有出库单
            var custOrderData = orders.closest("tr").find("[link=customerOrder]").data();
            var params = {
                custOrderNbr: custOrderData.custOrderNbr,
                custOrderId: custOrderData.custOrderId
            }
            widget.callService("queryDeliveryCodesFromOrder",params,function(re){
                if (_.isEmpty(re.result)) {
                    widget.popup("该订单无出库单信息");
                    return;
                }
                window.open("/crm/so/outStockOrderReceipt/" + custOrderData.custOrderNbr);
            })
        },
        _viewOrder: function(){
        	var widget = this, el = $(widget.el);
        	var orders = el.find('table input[name="payment"]:checked');
        	if(orders.length!=1) {
        		widget.popup("请选择一条订单!");
        		return false;
        	}
        	var co = orders.closest("tr").find("[link=customerOrder]");
        	var custOrderId = co.data("custOrderId");
        	widget.callService("viewOrder", [custOrderId], function(url){
        		window.open(url);
        	});
        },
        _dealEmerg: function (dom, qryScope) {
        	var widget = this, global = widget.global;
        	if (!global.emergOn) {
        		return;
        	}
        	var radios = dom.find("[name=payment]");
        	if (!radios.length) {
        		return;
        	}
        	radios.each(function(i, obj) {
        		var i = $(obj), t = i.closest("td"),
        			custOrderId = t.parent().find("[link=customerOrder]").data("custOrderId");
        		if (!custOrderId) {
        			return true;
        		}
        		widget.callService("isEmergOrder", [custOrderId, qryScope || "10"], function (re) {
        			if (re == "Y") {
        				t.css("position", "relative").append("<div class='emerg-info'></div>");
        			}
        		});
        	});
        },
        _checkedNbrLabelsStr: function (nbrLabelsStr) {
            return  $(".form-group").find("input[type=checkbox]:checked").not(nbrLabelsStr);
        },
        _unCheck: function () {
            return  true;
        },
    });
    vita.widget.register("customerOrderListQuery", customerOrderListQuery, true);
})(window.vita);