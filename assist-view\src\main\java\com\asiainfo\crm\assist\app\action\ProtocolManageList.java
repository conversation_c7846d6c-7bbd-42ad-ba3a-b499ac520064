package com.asiainfo.crm.assist.app.action;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.util.ListUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IMenuCollectionSMO;
import com.asiainfo.crm.service.intf.IReceiptIvoiceQuerySMO;
import com.asiainfo.crm.service.intf.IReceiptQuerySMO;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import com.asiainfo.crm.service.intf.IStaffInfoQuerySMO;

/**
 * Created by wenhy on 2018/1/18.
 */
@Component("vita.protocolManageList")
public class ProtocolManageList extends AbstractComponent {

	private static final Logger logger = LoggerFactory.getLogger(ProtocolManageList.class);

	@Autowired
	private ISoQuerySMO soQuerySMO;

	@Autowired
	private IStaffInfoQuerySMO staffInfoQuerySMO;
	
	@Autowired
    private IReceiptQuerySMO receiptQuerySMO;
	
	@Autowired
	private IMenuCollectionSMO menuCollectionSMO;
	@Autowired
	private IReceiptIvoiceQuerySMO iReceiptIvoiceQuerySMO;

	@Override
	public Map achieveData(Object... params) throws Exception {
		Map options = new HashMap();
		String jsonString = params[0].toString();
		Map map = qryProtocolManageListInfo(jsonString);
		options.putAll(map);
		return options;
	}
	
	
	public Map qryProtocolManageListInfo(String jsonString) throws IOException{
		Map options = new HashMap();
		Map map = jsonConverter.toBean(jsonString, Map.class);
		boolean boo = paperlessAuthority(jsonString);
		String regionId = (String) map.get("regionId").toString();
//		String str = soQuerySMO.qryProtocolTempletList(Long.parseLong(regionId));
		String str = "";//soQuerySMO.qryProtocolTempletList(null);
		
		String regionIdStr = "";
		//查询地区
		if (MDA.ASSIST_RECEIPT_CALL_ADDRESS) {
			regionIdStr = iReceiptIvoiceQuerySMO.findByProperty();
			if (boo) {
				str = iReceiptIvoiceQuerySMO.qryProtocolTempletLists(Long.parseLong(regionId),true);
			}else{
				str = iReceiptIvoiceQuerySMO.qryProtocolTempletList(Long.parseLong(regionId));
			}
		}else{
			regionIdStr = receiptQuerySMO.findByProperty();
			if (boo) {
				str = soQuerySMO.qryProtocolTempletLists(Long.parseLong(regionId),true);
			}else{
				str = soQuerySMO.qryProtocolTempletList(Long.parseLong(regionId));
			}
		}
		List relTypeList = new ArrayList();
		if ("Y".equals(MDA.FULL_PT_CONTROL_SWITCH)){
			boolean isRole = fullProtocolTemplateAdminRole(jsonString);
			if (!isRole){
				relTypeList  = iReceiptIvoiceQuerySMO.qryProtocolTmplRegionRels(Long.parseLong(regionId));
			}
		}
		Map regionIdMap = jsonConverter.toBean(regionIdStr, Map.class);
		List reglist = (List) regionIdMap.get("resultObject");
		
		Map<String, Object> strMap = jsonConverter.toBean(str, Map.class);
		List list = new ArrayList<Object>();
		if (strMap.containsKey("resultObject")) {
			Map resultObject = (Map) strMap.get("resultObject");
			if (resultObject.containsKey("protocolTemplets")) {
				list = (List) resultObject.get("protocolTemplets");
				if (null != list && list.size() > 0) {
					for (int i = 0; i < list.size(); i++) {
						Map mapI = (Map) list.get(i);
						
						String statusCd = mapI.get("statusCd").toString();
						if ("1000".equals(statusCd)) {
							mapI.put("statusCds", "已上线");
						} else {
							mapI.put("statusCds", "下线");
						}
						mapI.put("regionIdLists", "reglist");
						String staffId = mapI.get("crateStaff").toString();
						Map staffIdMap = new HashMap<>();
						staffIdMap.put("staffId", staffId);
						if ("" != staffId) {
							String staffResult = staffInfoQuerySMO.qryStaffInfo(jsonConverter.toJson(staffIdMap));
							Map staffResultMap = jsonConverter.toBean(staffResult, Map.class);
							if (null != staffResultMap && staffResultMap.containsKey("resultObject")) {
								Map staffObj = (Map) staffResultMap.get("resultObject");
								if (null != staffObj && staffObj.containsKey("staffInfo")) {
									Map staffInfo = (Map) staffObj.get("staffInfo");
									if (null != staffInfo && staffInfo.containsKey("staffName")) {
										String staffName = (String) staffInfo.get("staffName");
										mapI.put("staffName", staffName);
									}
								}
							}
						}
						mapI.put("regionIdList", reglist);
						String regionName = "";
						if (!mapI.containsKey("regionName") && mapI.containsKey("regionId")) {
							for (int j = 0; j < reglist.size(); j++) {
								Map maps = (Map) reglist.get(j);
								if (maps.get("commonRegionId").equals(mapI.get("regionId"))) {
									regionName = maps.get("regionName").toString();
									mapI.put("regionName", regionName);
									break;
								}
							}
						}else {
							regionName = mapI.get("regionName").toString();
						}
						if ("四川省".equals(regionName)){
							mapI.put("regionNamePrivCode", "true");
						}
						if (boo) {
							mapI.put("privCode", "true");
						}

						// 不是当前地区的信息不允许编辑标识设置
						if (MDA.LOGIC_Y.equals(AssistMDA.PROTOCOL_MANAGE_DISABLE_SWITCH)) {
							if (!mapI.get("regionId").toString().equals(regionId)) {
								mapI.put("disable", "true");
							} else {
								mapI.put("disable", "false");
							}
						}
						if (!ListUtil.isListEmpty(relTypeList)){
							Long protocolTempletId = MapUtils.getLong(mapI,"protocolTempletId");
							Boolean relTypeDisable = false;
							for (int l = 0; l < relTypeList.size(); l++) {
								Map relTypeMap = (Map) relTypeList.get(l);
								Long id = MapUtils.getLong(relTypeMap,"protocolTempletId");
								String relTypeRegion  =  MapUtils.getString(relTypeMap,"regionId");
								if (id.equals(protocolTempletId)){
									relTypeDisable = true;
									//地区匹配的上 不限制
									if (regionId.equals(relTypeRegion)){
										relTypeDisable = false;
									}
								}
							}
							mapI.put("relTypeDisable", relTypeDisable);
						}
					}
					options.put("protocolManageLists", list);
					options.put("count", list.size());
				}
			}
		}
		
		if (boo) {
			options.put("privCode", "true");
			options.put("reglist", reglist);
		}else{
			options.put("privCode", "false");
		}
		options.put("regionId", regionId);
		return options;
	}
	
	/**
	 * 省级个性化协议模板管理权限判断
	 * @param jsonStr
	 * @return
	 * @throws IOException
	 */
	public boolean paperlessAuthority(String jsonStr) throws IOException{ 
		Map<String,Object> mapString = jsonConverter.toBean(jsonStr,Map.class);
        Map map = new HashMap();
        Map options = new HashMap();
        map.put("sysUserId",mapString.get("staffId"));
        map.put("privCode",MDA.USER_PRIV_RIGHTS.get("provincialLevelTemplate"));
        //根据员工id查权限
        String permissionReturn = menuCollectionSMO.checkSysUserPriv(jsonConverter.toJson(map));
        Map<String,Object> permissionReturnMap = (Map<String, Object>) resolveResult(permissionReturn);
        String stre = MapUtils.getString(permissionReturnMap,"isHave");
        if ("true".equals(stre.toString())){
            return true;
        }
        return false;
	}


	/**
	 * 全量协议模板管理员角色权限判断
	 * @param jsonStr
	 * @return
	 * @throws IOException
	 */
	public boolean fullProtocolTemplateAdminRole(String jsonStr) throws IOException{
		Map<String,Object> mapString = jsonConverter.toBean(jsonStr,Map.class);
		Map map = new HashMap();
		Map options = new HashMap();
		map.put("sysUserId",mapString.get("staffId"));
		map.put("privCode",MDA.USER_PRIV_RIGHTS.get("fullPTAdminRole"));
		//根据员工id查权限
		String permissionReturn = menuCollectionSMO.checkSysUserPriv(jsonConverter.toJson(map));
		Map<String,Object> permissionReturnMap = (Map<String, Object>) resolveResult(permissionReturn);
		String stre = MapUtils.getString(permissionReturnMap,"isHave");
		if ("true".equals(stre.toString())){
			return true;
		}
		return false;
	}
	
	/**
	 * 协议管理页面的预览
	 * @param str
	 * @return
	 * @throws IOException
	 */
	public Map qryProtocolAndOrderVo(String str) throws IOException {
		Map options = new HashMap<>();
		String result = "";
		if (MDA.ASSIST_RECEIPT_CALL_ADDRESS) {
			result = iReceiptIvoiceQuerySMO.qryProtocolAndOrderVo(str);
		}else{
			result = soQuerySMO.qryProtocolAndOrderVo(str);
		}
		
		Map resMap = jsonConverter.toBean(result, Map.class);
		if (resMap.containsKey("resultObject")) {
			Map resultObject = (Map) resMap.get("resultObject");
			if (resultObject.containsKey("protocolTemplet")) {
				Map protocolTemplet = (Map) resultObject.get("protocolTemplet");
				if (protocolTemplet.containsKey("content")) {
					String content = (String) protocolTemplet.get("content");
					String text = content.substring(38);
					options.put("result", text);
					return options;
				}
			}
		}
		options.put("result", null);
		return options;
	}
	
	/**
     * 更新协议模板
     */
    public String saveProtocol(String jsonString) throws Exception{
    	String ret = "";
    	String rets = "";
    	if (MDA.ASSIST_RECEIPT_CALL_ADDRESS) {
    		ret = iReceiptIvoiceQuerySMO.saveProtocolTemplet(jsonString);
    		Map map = jsonConverter.toBean(jsonString, Map.class);
			String str = jsonConverter.toJson(map.get("pams"));
			rets = iReceiptIvoiceQuerySMO.saveProtocolTempletDate(str);
		}else{
			ret = receiptQuerySMO.saveProtocolTemplet(jsonString);
			Map map = jsonConverter.toBean(jsonString, Map.class);
			String str = jsonConverter.toJson(map.get("pams"));
//			rets = receiptQuerySMO.saveProtocolTempletDate(str);
		}
    	JSONObject jsonObject = JSONObject.fromObject(ret+"///"+rets);
        return jsonObject.toString();
    }
    
    /**
     * 保存/更新协议模板关系
     */
    public String saveProtocolTempletConfig(String jsonString) throws Exception{
    	String ret = "";
    	if (MDA.ASSIST_RECEIPT_CALL_ADDRESS) {
    		ret = iReceiptIvoiceQuerySMO.saveProtocolTempletConfig(jsonString);
		}else{
			ret = receiptQuerySMO.saveProtocolTempletConfig(jsonString);
		}
        return ret;
    }
    
    /**
     * 删除协议模板关系
     */
    public String delProtocolTempletConfig(String jsonString) throws Exception{
    	String ret = "";
    	if (MDA.ASSIST_RECEIPT_CALL_ADDRESS) {
    		ret = iReceiptIvoiceQuerySMO.delProtocolTempletConfig(jsonString);
		}else{
			ret = receiptQuerySMO.delProtocolTempletConfig(jsonString);
		}
        return ret;
    }
    
	/**
	 * 查询
	 * @return
	 * @throws IOException 
	 */
    public Map qryProtocolManage(String str) throws IOException{
    	Map options = new HashMap<>();
    	Map parameter = jsonConverter.toBean(str, Map.class);
    	String custOrderNbr = parameter.get("custOrderNbr").toString();
    
    	Map map = qryProtocolManageListInfo(str);
    	List newList = new ArrayList<>();
    	List protocolManageLists = (List) map.get("protocolManageLists");
    	if (null != protocolManageLists && protocolManageLists.size() > 0) {
			for (int i = 0; i < protocolManageLists.size(); i++) {
				Map protocolManage = (Map) protocolManageLists.get(i);
				if (!"".equals(custOrderNbr) && "".equals(parameter.get("rengSe"))) {
					if (protocolManage.get("name").toString().indexOf(custOrderNbr) != -1 && protocolManage.get("statusCd").toString().equals(parameter.get("status").toString()) 
							|| "9999".equals(parameter.get("status")) && protocolManage.get("name").toString().indexOf(custOrderNbr) != -1) {
						newList.add(protocolManage);
					}
				}else if (!"".equals(parameter.get("rengSe")) && "".equals(custOrderNbr)) {
					if (protocolManage.get("regionId").toString().equals(parameter.get("rengSe").toString()) && protocolManage.get("statusCd").toString().equals(parameter.get("status").toString()) 
							|| "9999".equals(parameter.get("status").toString()) && protocolManage.get("regionId").toString().equals(parameter.get("rengSe").toString())) {
						newList.add(protocolManage);
					}
				}else if (!"".equals(parameter.get("rengSe")) && !"".equals(custOrderNbr)) {
					if (protocolManage.get("name").toString().indexOf(custOrderNbr) != -1 && protocolManage.get("regionId").toString().equals(parameter.get("rengSe").toString()) && protocolManage.get("statusCd").toString().equals(parameter.get("status").toString()) 
							|| "9999".equals(parameter.get("status").toString()) && protocolManage.get("name").toString().indexOf(custOrderNbr) != -1 && protocolManage.get("regionId").toString().equals(parameter.get("rengSe").toString())) {
						newList.add(protocolManage);
					}
				}else if ("".equals(parameter.get("rengSe")) && "".equals(custOrderNbr)) {
					if ( protocolManage.get("statusCd").toString().equals(parameter.get("status").toString())  || "9999".equals(parameter.get("status").toString())) {
						newList.add(protocolManage);
					}
				}
				
			}
		}
    	options.put("protocolManageLists", newList);
    	options.put("count", newList.size());
    	return options;
    }
}
