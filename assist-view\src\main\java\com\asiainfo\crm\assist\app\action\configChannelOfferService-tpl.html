<script type="text/javascript" src="../bundles/assist/common/jquery-form.js"></script>


<div data-widget="configChannelOfferService" style="height:100%">
    <div class="pace-done wrapper mini-rightmax no-minright">
        <div class="calcw_rightbox noneleft">
            <div class="calcw_rightcont ">
                <div class="container-fluid">

                    <div class="martb10">
                        <form role="form" class="form-inline">

                            <div class="form-group">
                                <label class="sr-only newlabel">渠道编号：</label>
                                <input type="text" id="channelId" placeholder="请输入查询内容"  class="form-control">
                            </div>


                            <button type="button" id="btn-query" class="btn btn-primary">查询</button>
                        </form>
                    </div>

                    <div class="wmin_content overflowh row" id="forbidChannelResult">
                        <div class="col-md-12">
                            <table class="table table-bordered" id="forbidChannelTable">
                                <thead>
                                <tr>
                                    <th>渠道编号</th>
                                    <th>业务动作编号</th>
                                    <th>渠道名称</th>
                                    <th>业务动作名称</th>
                                </tr>
                                </thead>
                                <tbody>
                                #if($options != "null" && $options.forbidChannelList && $options.forbidChannelList != "null"
                                && $options.forbidChannelList.size() > 0 )
                                #foreach($forbidChannel in $options.forbidChannelList)
                                <tr>
                                    <td>$!{forbidChannel.channelId}</td>
                                    <td>$!{forbidChannel.serviceOfferId}</td>
                                    <td>$!{forbidChannel.channelName}</td>
                                    <td>$!{forbidChannel.name}</td>
                                </tr>
                                #end
                                #elseif($options != "null" && $options.forbidChannelList && $options.forbidChannelList != "null"
                                && $options.forbidChannelList.size() == 0 )
                                <tr>
                                    <td align='center' colspan='4'>未查到相关数据</td>
                                </tr>
                                #end
                                </tbody>
                            </table>
                        </div>
                        <!--翻页start -->
                        <div id="showPageInfo">
                        </div>
                        #if($options.totalNumber && $options.totalNumber != "null")
                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                        #end
                        <!--翻页end -->
                    </div>


                    <div>
                        <h3>导入EXCEL文件批量功能</h3>
                    </div>

                    <div>
                        <form id="m_form" action="../action/uploadForbidChannelInfoExcel" method="post" enctype="multipart/form-data">
                            <input style="display: none" class="m_file" type="file" name="data"  accept="xls"/>
                            <button type="button" id="m_file" class="btn btn-default"><span>选择要上传的EXCEL文件</span></button>
                            <input style="display: none" class="route_input" type="text" value="" name ="jsonStr" />
                            <button type="button" id="upload_excel" class="btn btn-primary">提&nbsp;&nbsp;交</button>
                            <a href="javascript:;" id="out_excel" class="btn btn-primary">导出操作结果信息</a>
                        </form>

                    </div>

                    <div>
                        <h3 style="color: red; font-size: 18px;">注意：<br/></h3>
                        <p>1、操作类型1表示批量增加,2表示删除</p>
                        <p>2、业务动作编号和渠道编号必须是已存在的</p>
                        <p>3、文件内容参考下面列表</p>
                    </div>

                    <div class="wmin_content overflowh row">
                        <div class="col-md-12">
                            <table width="50%" class="table table-bordered">
                                <thead>
                                <tr>
                                    <th>A</th>
                                    <th>B</th>
                                    <th>C</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <!--<td>0</td>-->
                                    <td>渠道编号</td>
                                    <td>业务动作编号</td>
                                    <td>操作类型</td>
                                </tr>
                                <tr>
                                    <!--<td>1</td>-->
                                    <td>280802</td>
                                    <td>180001127</td>
                                    <td>1</td>
                                </tr>
                                <tr>
                                    <!--<td>2</td>-->
                                    <td>801101</td>
                                    <td>180001128</td>
                                    <td>2</td>
                                </tr>
                                </tbody>
                            </table>

                        </div>
                    </div>

                </div>

            </div>

        </div>
    </div>
</div>
