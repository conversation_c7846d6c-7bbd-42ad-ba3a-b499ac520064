(function (vita) {
    var invoicePrintLogDetail = vita.Backbone.BizView.extend({
        events: {
            "click #backBtn": "_back",
            "click #closeBtn" : "_closePage"
        },
        _initialize: function () {

        },
        _back: function () {
            this.end('back');
        },
        _closePage : function() {
            var widget = this, element = $(widget.el);
            var dialog = element.closest("[data-widgetfullname=vita-dialog]");
            if (dialog.length) {
                dialog.dialog("close");
            }
        }
    });
    vita.widget.register("invoicePrintLogDetail", invoicePrintLogDetail, true);
})(window.vita);