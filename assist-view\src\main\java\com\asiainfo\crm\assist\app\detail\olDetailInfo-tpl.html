<div data-widget="olDetailInfo" style="height: 100%">
    #if($options.detailInfo && $options.detailInfo != "null")
    #set($detailInfo = $options.detailInfo)
    #if($detailInfo.itemDetailInfos && $detailInfo.itemDetailInfos != "null")
    <p class="vita-data">
        {
            "data" : $detailInfo.itemDetailInfos,
            "custOrderId":"!$options.custOrderId"
        }
    </p>
    #end
    #end
    #macro( nullNotShow $val)
        #if($val && $val != "null")
            $!val
        #end
    #end
    <div class="calcw_rightbox noneleft">
    <div class="calctitle">
        <div class="titlefont">订单详情</div>
        <div class="toolr">
            <!--<button type="button" class="btn btn-primary btn-sm okbutt">确认</button>-->
            <!--<button id="closeBtn" type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>-->
        </div>
    </div>
    <div class="calcw_rightcont">
        #if($detailInfo && $detailInfo!= "null")
        #if($detailInfo.orderBaseInfoVo && $detailInfo.orderBaseInfoVo != "null")
        #set($baseInfo = $detailInfo.orderBaseInfoVo)
        <div class="container-fluid">
            <h5 class="meal_htitle nobrder"><i class="bot"></i>基本信息</h5>
            <table class="table table-bordered conttd-w">
                <tbody>
                <tr>
                    <td class="labellcont">订单流水</td>
                    <td>#nullNotShow($baseInfo.custOrderNbr)</td>
                    <td class="labellcont">受理地区</td>
                    <td>#nullNotShow($baseInfo.regionName)</td>
                </tr>
                <tr>
                    <td class="labellcont">受理渠道</td>
                    <td>#nullNotShow($baseInfo.createOrgName)</td>
                    <td class="labellcont">受理客户</td>
                    <td>
                        <a id="custDetail" href="javascript:void(0);" class="textcolorgreen" title="查看客户详情">
                            #nullNotShow($baseInfo.custName)
                        </a>
                        <p class="vita-data">{"custId":"$baseInfo.custId"}</p>
                        &nbsp;
                    </td>
                </tr>
                <tr>
                    <td class="labellcont">受理时间</td>
                    <td>#nullNotShow($baseInfo.acceptDate)</td>
                    <td class="labellcont">受理员工</td>
                    <td>#nullNotShow($baseInfo.createStaffName)</td>
                </tr>
                <tr>
                    <td class="labellcont">状态</td>
                    <td>#nullNotShow($baseInfo.statusCdName)</td>
                    <td class="labellcont">来源终端</td>
                    <td>#nullNotShow($baseInfo.sysSourceName)</td>
                </tr>
                #if($baseInfo.orderAttrVos && $baseInfo.orderAttrVos != "null" && $baseInfo.orderAttrVos.size()>0)
                #set($even = $baseInfo.orderAttrVos.size() % 2 == 1)
                <tr>
                    #foreach($attr in $baseInfo.orderAttrVos)
                    <td class="labellcont">#nullNotShow($attr.attrName)</td>
                        #if($attr.attrValueName && $attr.attrValueName != "null")
                            <td>#nullNotShow($attr.attrValueName)</td>
                        #else
                            <td>#nullNotShow($attr.value)</td>
                        #end
                    #if ( ($velocityCount % 2 == 0) && ($velocityCount - $arry.size() != 0))
                </tr>
                <tr>
                    #end
                    #end
                    #if($even)
                    <td class="labellcont">&nbsp;</td>
                    <td>&nbsp;</td>
                    #end
                </tr>
                #end
                </tbody>
            </table>
        </div>
        #end

        #if($detailInfo.itemDetailInfos && $detailInfo.itemDetailInfos != "null" && $detailInfo.itemDetailInfos.size() > 0)
        <div class="container-fluid">
            <h5 class="meal_htitle nobrder"><i class="bot"></i>订单项信息</h5>
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered" id="customerInfo">
                        <thead>
                        <tr>
                            <th>业务类型</th>
                            <th>受理时间</th>
                            <th>订单状态</th>
                            <th>受理详情</th>
                            <th class="hidden-xs hidden-sm">退单原因</th>
                            <th class="hidden-xs hidden-sm">暂缓原因</th>
                            <th class="hidden-xs hidden-sm">资料补录</th>
                            <th class="hidden-xs hidden-sm">第一协销人</th>
                            <th class="hidden-xs hidden-sm">第二协销人</th>
                        </tr>
                        </thead>
                        <tbody>
                        #foreach($order in $detailInfo.itemDetailInfos)
                        <tr>
                            #if($order.orderItemCd == "1300")
                            <td>
                                ├$!{order.serviceOfferName}-$!{order.applyObjSpecName}
                                #if($order.accNum && $order.accNum != "null")
                                【$order.accNum】
                                #end
                            </td>
                            #else
                                <td>$!{order.serviceOfferName}-$!{order.applyObjSpecName}</td>
                            #end
                            <td>$!order.acceptDate</td>
                            <td>$!order.statusCdName</td>

                            <td>
                                #if($order.orderItemCd == "1300")
                                <a href="javascript:void(0);" class="textcolorgreen" name="orderUrl">
                                    <p class="vita-data">{"flag":"prod","custOrderId":"$!order.custOrderId","orderItemId":"$!order.orderItemId"}</p>
                                    【$!{order.applyObjSpecName}-$!{order.serviceOfferName}】
                                </a>
                                #elseif($order.orderItemCd == "1200")
                                <a href="javascript:void(0);" class="textcolorgreen" name="orderUrl">
                                    <p class="vita-data">{"flag":"offer","custOrderId":"$!order.custOrderId","orderItemId":"$!order.orderItemId"}</p>
                                    【$!{order.applyObjSpecName}-$!{order.serviceOfferName}】
                                </a>
                                #else
                                    【$!{order.applyObjSpecName}-$!{order.serviceOfferName}】
                                #end
                            </td>

                            <td class="hidden-xs hidden-sm">#nullNotShow($order.cancelReason)</td>
                            <td class="hidden-xs hidden-sm">#nullNotShow($order.suspendReason)</td>
                            <td class="hidden-xs hidden-sm">
                                #if($order.repaired)
                                是
                                #else
                                否
                                #end
                            </td>
                            <td class="hidden-xs hidden-sm">
                                #foreach($name in $order.devStaffNames)
                                    #if($name.devStaffType == "1000")
                                        #nullNotShow($name.devStaffName)
                                    #end
                                #end
                            </td>
                            <td class="hidden-xs hidden-sm">
                                #foreach($name in $order.devStaffNames)
                                    #if($name.devStaffType == "2000")
                                        #nullNotShow($name.devStaffName)
                                    #end
                                #end
                            </td>
                        </tr>
                        #end
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        #end

        <!--订单属性信息-->
        <!--#if($detailInfo.orderAttrs && $detailInfo.orderAttrs != "null" && $detailInfo.orderAttrs.size() > 0)
        <div class="form_title nobrder">
            <div>
                <i class="bot"></i>订单属性信息
            </div>
        </div>
        #set($even = $detailInfo.orderAttrs.size() % 2 == 1)
        <div class="wmin_content overflowh">
            <div class="col-md-12">
                <table class="table table-bordered show-grid">
                    <tbody>
                    <tr>
                        #foreach($attr in $detailInfo.orderAttrs)
                            <td class="labellcont">$!{attr.itemSpecName}</td>
                            <td>$!attr.value</td>
                        #if ($velocityCount % 2 == 0)
                    </tr>
                    <tr>
                        #end
                        #end
                        #if($even)
                            <td class="labellcont"></td>
                            <td></td>
                        #end
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        #end-->

        <!--购物车动作信息-->
        <!--#if($detailInfo.orderChangeList && $detailInfo.orderChangeList != "null" && $detailInfo.orderChangeList.size() > 0)
        <div class="form_title nobrder">
            <div><i class="bot"></i>购物车动作信息</div>
        </div>
        <div class="wmin_content overflowh">
            <div class="col-md-12">
                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th>修改原因</th>
                        <th>处理渠道</th>
                        <th class="hidden-xs hidden-sm">处理员工</th>
                        <th class="hidden-xs hidden-sm">处理时间</th>
                        <th>处理备注</th>
                    </tr>
                    </thead>
                    <tbody>
                    #foreach($change in $detailInfo.orderChangeList)
                    <tr>
                        <td>$!change.reason</td>
                        <td>$!change.channelName</td>
                        <td class="hidden-xs hidden-sm">$!change.staffCode</td>
                        <td class="hidden-xs hidden-sm">$!change.date</td>
                        <th>$!change.remark</th>
                    </tr>
                    #end
                    </tbody>
                </table>
            </div>
        </div>
        #end-->

        <!--费用信息-->
        <div class="container-fluid">
            <h5 class="meal_htitle nobrder"><i class="bot"></i>费用信息</h5>
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <th class="hidden-xs hidden-sm">费用项目</th>
                            <th>费用名称</th>
                            <th>应收金额(元)</th>
                            <th>实收金额(元)</th>
                            <th>付费方式</th>
                            <th>税费(元)</th>
                            <th class="hidden-xs hidden-sm">付费时间</th>
                            <th class="hidden-xs hidden-sm">状态</th>
                            <th class="hidden-xs hidden-sm">发票编码</th>
                        </tr>
                        </thead>
                        <tbody>
                        #if($options.feeItems && $options.feeItems != "null" && $options.feeItems.size() > 0)
                        #foreach($feeItem in $options.feeItems)
                        <tr>
                            <td class="hidden-xs hidden-sm">#nullNotShow($feeItem.busiName)</td>
                            <td>#nullNotShow($feeItem.acctItemTypeName)</td>
                            <td>
                                #set($appChargeVal = $feeItem.realAmount / 100.00)
                                $appChargeVal
                            </td>
                            <td>
                                #set($chargVal = $feeItem.paidInAmount / 100.00)
                                $chargVal
                            </td>
                            <td>#nullNotShow($feeItem.chargeMethodName)</td>
                            <td>
                                #set($taxVal = $feeItem.tax / 100.00)
                                #nullNotShow($taxVal)
                            </td>
                            <td class="hidden-xs hidden-sm">#nullNotShow($feeItem.createDate)</td>
                            <td class="hidden-xs hidden-sm">#nullNotShow($feeItem.statusName)</td>
                            <td class="hidden-xs hidden-sm">#nullNotShow($feeItem.receiptId)</td>
                        </tr>
                        #end
                        #end
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!--销售单信息-->
        <!--#if($detailInfo.offerSaleInfos && $detailInfo.offerSaleInfos != "null" && $detailInfo.offerSaleInfos.size() > 0)
        <div class="form_title nobrder">
            <div><i class="bot"></i>销售单信息</div>
        </div>
        <div class="wmin_content overflowh">
            <div class="col-md-12">
                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th>受理单流水号</th>
                        <th>受理渠道</th>
                        <th class="hidden-xs hidden-sm">受理员工</th>
                        <th class="hidden-xs hidden-sm">客户</th>
                        <th class="hidden-xs hidden-sm">联系人</th>
                        <th class="hidden-xs hidden-sm">联系电话</th>
                        <th>预售单编号</th>
                        <th>经办人</th>
                        <th>优先级</th>
                        <th>状态</th>
                        <th class="hidden-xs hidden-sm">受理时间</th>
                        <th class="hidden-xs hidden-sm">状态时间</th>
                        <th class="hidden-xs hidden-sm">取消原因</th>
                        <th class="hidden-xs hidden-sm">备注</th>
                        <th class="hidden-xs hidden-sm">预受理信息</th>
                        <th class="hidden-xs hidden-sm">人工转正标识</th>
                    </tr>
                    </thead>
                    <tbody>
                    #foreach($saleInfo in $detailInfo.offerSaleInfos)
                    <tr>
                        <td>$!saleInfo.orderNbr</td>
                        <td>$!saleInfo.channelName</td>
                        <td class="hidden-xs hidden-sm">$!{saleInfo.staffName}【$!{saleInfo.staffNumber}】</td>
                        <td class="hidden-xs hidden-sm">$!saleInfo.partyName</td>
                        <td class="hidden-xs hidden-sm">$!saleInfo.linkMan</td>
                        <td class="hidden-xs hidden-sm">$!saleInfo.linkNumber</td>
                        <td>$!saleInfo.seqNum</td>
                        <td>$!saleInfo.handlePeopleName</td>
                        <td>$!saleInfo.priority</td>
                        <td>$!saleInfo.statusName</td>
                        <td class="hidden-xs hidden-sm">$!saleInfo.acceptTime</td>
                        <td class="hidden-xs hidden-sm">$!saleInfo.statusDate</td>
                        <td class="hidden-xs hidden-sm">$!saleInfo.reason</td>
                        <td class="hidden-xs hidden-sm">$!saleInfo.remark</td>
                        <td class="hidden-xs hidden-sm">$!saleInfo.orderInfos</td>
                        <td class="hidden-xs hidden-sm">
                            #if($saleInfo.manualFlag && $saleInfo.manualFlag != "null" && $saleInfo.manualFlag == "Y")
                                是
                            #else
                                否
                            #end
                        </td>
                    </tr>
                    #end
                    </tbody>
                </table>
            </div>
        </div>
        #end-->

        <!--无纸化信息-->
        <!--#if($detailInfo.printInfos && $detailInfo.printInfos != "null")
        #set($esInfo = $detailInfo.printInfos)
        <div class="form_title nobrder">
            <div><i class="bot"></i>无纸化信息</div>
        </div>
        <div class="wmin_content overflowh">
            <div class="col-md-12">
                <div class="show-grid">
                    <ul>
                        <li class="col-xs-6 col-sm-2 labellcont">电子签名业务类型</li>
                        <li class="col-xs-6 col-sm-4">$!esInfo.esBusiName</li>
                        <li class="col-xs-6 col-sm-2 labellcont">生成时间	</li>
                        <li class="col-xs-6 col-sm-4">$!esInfo.creatDt</li>
                        <li class="col-xs-6 col-sm-2 labellcont">电子签名状态	</li>
                        <li class="col-xs-6 col-sm-4">$!esInfo.esStatusName</li>
                        <li class="col-xs-6 col-sm-2 labellcont">电子档案查看</li>
                        <li class="col-xs-6 col-sm-4">
                            <a href="javascript:void(0)" class="textcolorgreen" onclick="window.open('https://www.baidu.com')">查看</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        #end-->

		<!-- 规则授信日志 -->
        <div class="container-fluid">
            <h5 class="meal_htitle nobrder"><i class="bot"></i>规则授信日志</h5>
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered">
                        <thead>
	                    <tr>
	                        <th>授信员工工号</th>
	                        <th>被授信员工工号</th>
	                        <th class="hidden-xs hidden-sm">授信规则信息</th>
	                        <th class="hidden-xs hidden-sm">授信日期</th>
	                        <th>状态</th>
	                    </tr>
                        </thead>
                        <tbody>
                        #if($options.ruleInfos && $options.ruleInfos != "null" && $options.ruleInfos.size() > 0)
                       	#foreach($rule in $options.ruleInfos)
	                    <tr>
	                        <td>$!rule.grantStaffNumber</td>
	                        <td>$!rule.receiveStaffNumber</td>
	                        <td class="hidden-xs hidden-sm">$!rule.ruleInfo</td>
	                        <td class="hidden-xs hidden-sm">$!rule.createDt</td>
	                        #if($rule.state && $rule.state != "null" && $rule.state == "ADD")
	                            <td class="hidden-xs hidden-sm">新增</td>
	                        #else
	                            <td class="hidden-xs hidden-sm">未知</td>
	                        #end
	                    </tr>
                        #end
                        #end
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        #end
    </div>
    </div>
</div>