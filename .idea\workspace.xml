<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ArtifactsWorkspaceSettings">
    <artifacts-to-build>
      <artifact name="so-web:war exploded" />
    </artifacts-to-build>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="60bd221c-92b6-47a4-afda-bf91c560ca94" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/common-view/src/main/resources/spring/root-context.xml" beforeDir="false" afterPath="$PROJECT_DIR$/common-view/src/main/resources/spring/root-context.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/common-view/src/main/resources/spring/service/crm-rmi.xml" beforeDir="false" afterPath="$PROJECT_DIR$/common-view/src/main/resources/spring/service/crm-rmi.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/common-view/src/main/resources/spring/web/vmResolver.xml" beforeDir="false" afterPath="$PROJECT_DIR$/common-view/src/main/resources/spring/web/vmResolver.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/configs-view/src/main/resources/BCommMDA.xml" beforeDir="false" afterPath="$PROJECT_DIR$/configs-view/src/main/resources/BCommMDA.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/configs-view/src/main/resources/al-dsession.properties" beforeDir="false" afterPath="$PROJECT_DIR$/configs-view/src/main/resources/al-dsession.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/configs-view/src/main/resources/uni-config.properties" beforeDir="false" afterPath="$PROJECT_DIR$/configs-view/src/main/resources/uni-config.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/configs-view/src/main/resources/uni-frontEnd-rmi.properties" beforeDir="false" afterPath="$PROJECT_DIR$/configs-view/src/main/resources/uni-frontEnd-rmi.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/receipt-web/so-receipt-web/src/main/resources/receipt-rmi.xml" beforeDir="false" afterPath="$PROJECT_DIR$/receipt-web/so-receipt-web/src/main/resources/receipt-rmi.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/soweb-compile/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/soweb-compile/pom.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="INCOMING_CHECK_STRATEGY" value="Never" />
  </component>
  <component name="LogFilters">
    <option name="FILTER_ERRORS" value="false" />
    <option name="FILTER_WARNINGS" value="false" />
    <option name="FILTER_INFO" value="true" />
    <option name="FILTER_DEBUG" value="true" />
    <option name="CUSTOM_FILTER" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\Program Files\apache-maven-3.2.5" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\m2\settings-AI.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2yiZUEnMXe3EXWkCSV7oeVODqps" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="abbreviatePackageNames" value="true" />
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
    <option name="showMembers" value="true" />
    <option name="showModules" value="false" />
    <option name="showVisibilityIcons" value="true" />
    <option name="sortByType" value="true" />
    <option name="sortKey" value="BY_TYPE" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.soweb-compile [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.soweb-compile [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.soweb-compile [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.soweb-compile [package].executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Tomcat Server.crm.executor&quot;: &quot;Debug&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/yaxin/山东ict/政企/web&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Artifacts&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.29450262&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;vcs.Subversion&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="crm" type="#com.intellij.j2ee.web.tomcat.TomcatRunConfigurationFactory" factoryName="Local" APPLICATION_SERVER_NAME="Tomcat 8.5.32" ALTERNATIVE_JRE_ENABLED="false">
      <option name="OPEN_IN_BROWSER_URL" value="http://so.crmtest.ctc.com:8450/crm/assist/vector?assist=index#index/main" />
      <option name="COMMON_VM_ARGUMENTS" value="-Xms1024m -Xmx1024m -XX:MaxNewSize=512m -XX:MaxPermSize=512m -Denv=DEV -Ddev_meta=http://************:9320 -Djava.net.preferIPv4Stack=true -Dapp.id=1090 -Dcmdb.client.ip=************ -Dcmdb.configService=http://************:9320 -Dcmdb.configClasspath=$PROJECT_DIR$/so-web/target/so-web/WEB-INF/classes" />
      <deployment>
        <artifact name="so-web:war exploded">
          <settings>
            <option name="CONTEXT_PATH" value="/crm" />
          </settings>
        </artifact>
      </deployment>
      <server-settings>
        <option name="BASE_DIRECTORY_NAME" value="29206d7c-b5de-499d-8183-e13ccb53207f" />
        <option name="HTTP_PORT" value="8450" />
      </server-settings>
      <predefined_log_file enabled="true" id="Tomcat" />
      <predefined_log_file enabled="true" id="Tomcat Catalina" />
      <predefined_log_file id="Tomcat Manager" />
      <predefined_log_file id="Tomcat Host Manager" />
      <predefined_log_file id="Tomcat Localhost Access" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="54037" />
      </RunnerSettings>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Cover">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Debug">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Profile">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Run">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <method v="2">
        <option name="Make" enabled="true" />
        <option name="BuildArtifacts" enabled="true">
          <artifact name="so-web:war exploded" />
        </option>
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.23339.11" />
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.23339.11" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="60bd221c-92b6-47a4-afda-bf91c560ca94" name="Changes" comment="" />
      <created>1750319367853</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750319367853</updated>
      <workItem from="1750319369126" duration="4464000" />
      <workItem from="1750323856210" duration="2522000" />
      <workItem from="1750383383936" duration="6926000" />
      <workItem from="1750768043723" duration="5852000" />
      <workItem from="1750835593715" duration="4147000" />
      <workItem from="1752481510954" duration="3318000" />
      <workItem from="1753869173727" duration="2091000" />
      <workItem from="1754039091299" duration="658000" />
      <workItem from="1754294393567" duration="4117000" />
      <workItem from="1754556766576" duration="1725000" />
      <workItem from="1756257635907" duration="3082000" />
      <workItem from="1756351038525" duration="1731000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>