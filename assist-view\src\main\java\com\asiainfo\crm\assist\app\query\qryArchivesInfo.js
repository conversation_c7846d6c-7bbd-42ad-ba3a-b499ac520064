(function (vita) {
    var qryArchivesInfo = vita.Backbone.BizView.extend({
        events: {
            "click #myTab li": "_qryInfo"//查看经办人照片或回执
        },
        _initialize: function () {
            var widget = this, el = $(widget.el);
            var data = el.data("data");
            var custOrderId = data.custOrderId;
            var custOrderNbr = data.custOrderNbr;
            var customerOrder = {"custOrderId": custOrderId, "custOrderNbr": custOrderNbr};
            widget.model.set("customerOrder", customerOrder);
            var li = el.find("#myTab li.active");
            var liId = li.attr("id");
            widget.global.currentClick = liId;
            var params = {};
            if (liId == "operTab") {
                params.operClick = widget.global.currentType;
            } else {
                params.receiptClick = widget.global.currentType;
            }
            widget._qryArchivesInfo(params);
        },
        global: {
            currentType: "Y",
            currentClick: ""
        },
        _qryInfo: function (e) {
            var widget = this, target = $(e.currentTarget);
            var targetId = target.attr("id");
            if (targetId == widget.global.currentClick) {
                return;
            }else {
                widget.global.currentClick = targetId;
            }
            var params = {};
            if (targetId == "operTab") {
                params.operClick = widget.global.currentType;
            } else {
                params.receiptClick = widget.global.currentType;
            }
            widget._qryArchivesInfo(params);
        },
        _qryArchivesInfo: function (params) {
            var widget = this;
            if (params) {
                var customerOrder = widget.model.get("customerOrder");
                params.custOrderId = customerOrder.custOrderId;
                params.custOrderNbr = customerOrder.custOrderNbr;
                widget.refreshPart("qryArchivesInfo", JSON.stringify(params), "#qryArchivesInfoDiv", function (res) {
                }, {
                    async: false
                });
            }
        }
    });
    vita.widget.register("qryArchivesInfo", qryArchivesInfo, true);
})(window.vita);