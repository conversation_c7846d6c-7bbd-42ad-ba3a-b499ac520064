package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created on 2019/4/23.
 */
@Component("vita.qryArchivesInfo")
public class QryArchivesInfo extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(QryArchivesInfo.class);

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        Map<String, Object> param = jsonConverter.toBean(params[0].toString(), Map.class);
        String custOrderId = (String) param.get("custOrderId");
        String custOrderNbr = (String) param.get("custOrderNbr");
        options.put("custOrderId", custOrderId);
        options.put("custOrderNbr", custOrderNbr);
        options.put("oper", "Y");
        options.put("receipt", AssistMDA.SHOW_RECEIPT_SWITCH);
        return options;
    }

    /**
     * 根据查询类型初始化相应的组件
     *
     * @param json
     * @return
     * @throws Exception
     */
    public Map qryArchivesInfo(String json) throws Exception {
        Map options = jsonConverter.toBean(json, Map.class);
        return options;
    }

}
