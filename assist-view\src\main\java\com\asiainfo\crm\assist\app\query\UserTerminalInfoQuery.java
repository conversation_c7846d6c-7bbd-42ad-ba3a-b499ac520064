package com.asiainfo.crm.assist.app.query;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.asiainfo.crm.service.intf.IResourceSMO;
import com.google.common.collect.Maps;

@Component("vita.userTerminalInfoQuery")
public class UserTerminalInfoQuery extends AbstractComponent {

	@Autowired
    private IResourceSMO resourceSMO;

    @Autowired
    private IProdInstSMO iProdInstSMO;
	
	@Override
	public Map achieveData(Object... params) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	public Map userTerminalInfoQuery(String jsonStr) throws Exception{
		//查询资源信息
		String materialInfoStr = resourceSMO.queryMaterialInfo(jsonStr);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(materialInfoStr);
        if(null != resultObjectMap && "0".equals(String.valueOf(resultObjectMap.get("handleResultCode")))
                && !"A".equals(String.valueOf(resultObjectMap.get("statusCd")))) {
        	//查询产品实例信息
            Map paramMap = jsonConverter.toBean(jsonStr, Map.class);

            Map paramMap1 = new HashMap();
            paramMap1.put("mktResInstNbr",paramMap.get("mktResInstNbr"));
            String resInstStr1 = iProdInstSMO.qryOfferResInstRelsLocal(jsonConverter.toJson(paramMap1));

            Map<String, Object> resInstMap = jsonConverter.toBean(resInstStr1, Map.class);

            if(resInstMap != null && resInstMap.get("resultObject") != null) {

                Map<String, Object> resultObject =(Map<String, Object>)resInstMap.get("resultObject");
                //System.out.println(resultObject);
                if(resultObject !=null&&resultObject.get("offerResInstRels") != null){
                    List<Map<String, Object>> prodInstMap = (List<Map<String, Object>>) resultObject.get("offerResInstRels");
                    if (prodInstMap.size() > 0) {
                        for (Map<String, Object> map : prodInstMap) {
                            boolean flag =false;
                            if(map.containsKey("offerInstId")){
                                String offerInstId =map.get("offerInstId").toString();
                                Map paramMap2 = new HashMap();
                                paramMap2.put("offerInstId",offerInstId);
                                String offerInst = iProdInstSMO.qryAccProdInstListLocal(jsonConverter.toJson(paramMap2));
                                Map<String, Object> resInstMap1 = jsonConverter.toBean(offerInst, Map.class);
                                if(resInstMap1 != null && resInstMap1.get("resultObject") != null&&"0".equals(String.valueOf(resInstMap1.get("resultCode")))) {
                                    Map<String, Object> resultObj =(Map<String, Object>)resInstMap1.get("resultObject");
                                    if(resultObj !=null&&resultObj.get("accProdInsts") != null&&(((List<Map<String, Object>>) resultObj.get("accProdInsts")).size()>0)){

                                        List<Map<String, Object>> accProdInsts = (List<Map<String, Object>>) resultObj.get("accProdInsts");
                                        for (Map<String, Object> accProdInst : accProdInsts) {
                                            if(accProdInst.containsKey("accNum")){
                                                flag = true;
                                                resultObjectMap.put("accNum", accProdInst.get("accNum"));
                                                resultObjectMap.put("mktResInstNbr", paramMap.get("mktResInstNbr"));
                                                break;
                                            }


                                        }


                                    }



                                }

                            }
                            if(flag){
                                break;
                            }

                        }

                    }

                }
            }
        }
        
        
		Map<String, Object> options = Maps.newHashMap();
		if(resultObjectMap.get("accNum")!=null){
			options.put("materialInfo", resultObjectMap);
		}
		
		return options;
	}
}
