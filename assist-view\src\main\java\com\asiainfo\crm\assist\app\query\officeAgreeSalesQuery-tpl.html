<div data-widget="officeAgreeSalesQuery" style="height:100%">
   <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
      <div class="box-maincont">
         <!--<div class="tab-pane fade in active" id="1">-->
         <div class="homenofood">
            <div class="page_main notopnav">
               <!--填单start-->
               <div class="col-lg-12">
                  <div class="box-item">
                     <div class="container-fluid row">
                        <div class="form_title">
                           <div><i class="bot"></i>查询条件</div>
                        </div>
                        <div class="wmin_content row">
                           <form class=" form-bordered">
                              <div class="form-group col-md-4 ">
                                 <label class="col-md-5 control-label lablep">
                                    地 区
                                 </label>
                                 <div class="col-md-7">
                                    <div class="input-group">
                                       <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                       <div class="input-group-btn">
                                          <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                       </div>
                                    </div>
                                 </div>
                              </div>

                              <div class="form-group col-md-4">
                                 <label class="col-md-5 control-label lablep">
                                    <label class="wu-radio full absolute" data-scope="">
                                       <input id="c_offerName" type="checkbox" name="product">
                                    </label> 销售品名称
                                 </label>
                                 <div class="col-md-7">
                                    <input id="offerName" type="text" class="form-control" placeholder="">
                                 </div>
                              </div>

                              <div class="form-group col-md-4">
                                 <label class="col-md-5 control-label lablep">
                                    <label class="wu-radio full absolute" data-scope="">
                                       <input id="c_custId" type="checkbox" name="payment">
                                    </label> 产权客户标识
                                 </label>
                                 <div class="col-md-7">
                                    <input id="custId" type="text" class="form-control" placeholder="">
                                 </div>
                              </div>

                              <div class="form-group col-md-4">
                                 <label class="col-md-5 control-label lablep">
                                    <label class="wu-radio full absolute" data-scope="">
                                       <input id="c_statusCd" type="checkbox" name="payment">
                                    </label> 状态:
                                 </label>
                                 <div class="col-md-7">
                                    <select id="statusCd" class="form-control">
                                       #if($options != "null" && $options.statusCdList !="null")
                                          #foreach($status in  $options.statusCdList.entrySet())
                                          <option value=$status.key>$status.value</option>
                                          #end
                                       #end
                                    </select>
                                 </div>
                              </div>

                              <div class="form-group col-md-4">
                                 <label class="col-md-5 control-label lablep">
                                    <label class="wu-radio full absolute" data-scope="">
                                       <input id="c_Remark" type="checkbox" name="payment">
                                    </label> 备注:
                                 </label>
                                 <div class="col-md-7">
                                    <input id="Remark" type="text" class="form-control" placeholder="">
                                 </div>
                              </div>

                              <div class="form-group col-md-11">
                                 <div class="col-md-12 searchbutt_r" align="right">
                                    <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                    <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                    <label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                    <button id="btn-clear" type="button" class="btn btn-white">清除</button>
                                 </div>
                              </div>
                           </form>
                        </div>
                     </div>

                     <div class="col-lg-12 mart10" id="orderListResult">
                        <table class="table table-hover">
                           <thead>
                            <tr>
                               <th>销售品标识</th>
                               <th>产品实例标识</th>
                               <th>费用标识</th>
                               <th>金额</th>
                               <th>币种</th>
                               <th>应收金额</th>
                               <th>优惠方式</th>
                               <th>优惠比例</th>
                               <th>是否一口价</th>
                               <th>产权客户标识</th>
                               <th>状态</th>
                               <th>状态修改时间</th>
                               <th>区域标识</th>
                               <th>账目类型</th>
                               <th>收款单位</th>
                               <th>委托收款单位</th>
                               <th>创建员工</th>
                               <th>创建时间</th>
                               <th>修改员工</th>
                               <th>修改时间</th>
                               <th>维护订单项标识</th>
                               <th>备注</th>
                            </tr>
                           </thead>
                           <tbody id="orderList">
                           #if($options !="null" && $options.orderList && $options.orderList !="null" && $options.orderList.size()>0)
                              #foreach($item in $options.orderList)
                              <tr>
                                 <td>$item.offerInstId</td>
                                 <td>$item.prodInstId</td>
                                 <td>$item.offerInstFeeInfoId</td>
                                 <td>$item.amount</td>
                                 <td>$item.currency</td>
                                 <td>$item.realAmount</td>
                                 <td>$item.ratioMethod</td>
                                 <td>$item.ratio</td>
                                 <td>$item.isFixedPrice</td>
                                 <td>$item.ownerCustId</td>
                                 <td>$item.statusCd</td>
                                 <td>$item.statusDate</td>
                                 <td>$item.regionId</td>
                                 <td>$item.acctItemTypeId</td>
                                 <td>$item.chargeOrg</td>
                                 <td>$item.commChargeOrg</td>
                                 <td>$item.createStaff</td>
                                 <td>$item.createDate</td>
                                 <td>$item.updateStaff</td>
                                 <td>$item.updateDate</td>
                                 <td>$item.lastOrderItemId</td>
                                 <td>$item.remark</td>
                           </tr>
                              #end
                           #elseif($options !="null" && $options.orderList && $options.orderList !="null" && $options.orderList.size() ==0)
                           <tr><td align='center' colspan='16'>未查询到数据！<td></tr>
                           #end
                           </tbody>
                        </table>
                     </div>
                        </div>
                     </div>

                  </div>
               </div>
               <!--填单end-->
            </div>
         </div>
      </div>
   </div>
</div>