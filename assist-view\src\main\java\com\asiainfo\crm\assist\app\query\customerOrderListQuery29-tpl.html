<div data-widget="customerOrderListQuery29" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <!--<div class="sypage-nav">-->
        <!--<div class="titlefont">-->
            <!--<ul id="myTab" class="nav nav-tabs">-->
                <!--<li class="active">-->
                    <!--<a href="#1" data-toggle="tab">受理单</a>-->
                <!--</li>-->
                <!--<li>-->
                    <!--<a href="#2" data-toggle="tab">销售单</a>-->
                <!--</li>-->
                <!--</li>-->
            <!--</ul>-->
        <!--</div>-->
    <!--</div>-->
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>通用订单查询（2.9）</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                地 区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_channelId" type="checkbox" name="payment">
                                                </label> 渠道
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="channelId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="channelIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_staffId" type="checkbox" name="payment">
                                                </label> 营业员
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="staffId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="staffIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custId" type="checkbox" name="payment">
                                                </label> 所属客户
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="custId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="custIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custOrderNbr" type="checkbox" name="payment">
                                                </label> 流水号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custOrderNbr" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_accNum" type="checkbox" name="payment">
                                                </label> 号码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="accNum" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_statusCd" type="checkbox" name="payment">
                                                </label> 订单状态
                                            </label>
                                            <div class="col-md-7">
                                                <select id="statusCd" class="form-control">
                                                    #if($options != "null" && $options.statusList != "null" &&
                                                    $options.statusList.size() > 0)
                                                    #foreach($status in $options.statusList)
                                                    <option value=$status.VALUE>$status.NAME</option>
                                                    #end
                                                    #end

                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_orderItemId" type="checkbox" name="payment">
                                                </label> 业务动作ID
                                            </label>
                                            <div class="col-md-7">
                                                <input id="orderItemId" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">查询范围</label>
                                            <div class="col-md-7">
                                                <select id="qryScope" class="form-control">
                                                    <option value="0">当前库</option>
                                                    <option value="1">历史库</option>
                                                    <option value="2">大数据库</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-12" id="qryTimeQuantum">
                                            <label class="col-md-2 control-label lablep">
                                                起止时间</label>
                                            <div class="col-md-10 form-inline">

                                                <div class="form-group">
                                                    <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="开始时间">
                                                </div>
                                                <div class="form-group">
                                                    <input name="endDate" id="endDate" type="text" class="form-control" placeholder="结束时间">
                                                </div>
                                                <button type="button" id="timeQuantum_1" name="timeQuantum_1" class="btn btn-gray btn-outline btn-sm">当天</button>
                                                <button type="button" id="timeQuantum_7" name="timeQuantum_7" class="btn btn-gray btn-outline btn-sm">七天</button>
                                                <button type="button" id="timeQuantum_30" name="timeQuantum_30" class="btn btn-gray btn-outline btn-sm">一个月</button>
                                                <button type="button" id="timeQuantum_90" name="timeQuantum_90" class="btn btn-gray btn-outline btn-sm">三个月</button>

                                            </div>
                                        </div>
                                        <div class="form-group col-md-11">
                                            <div class="col-md-12 searchbutt_r" align="right">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-clear" type="button" class="btn btn-white">清除</button>
                                            </div>
                                        </div>


                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <!--<div><i class="bot"></i>查询结果</div>-->
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12">
                                        <a id="viewInvoice" class="btn btn-gray btn-outline"> <i class="glyphicon fa-details text18"> </i> 查看发票
                                        </a>
                                        <a id="printInvoice" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 补打发票
                                        </a>
                                        <a id="printReceipt" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 打印回执
                                        </a>
                                        <a id="orderDetail" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 查看订单详情
                                        </a>
                                    </div>
                                    <div class="col-lg-12 mart10" id="orderListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>选择</th>
                                                <th>购物车流水</th>
                                                <th>受理渠道</th>
                                                <th>受理时间</th>
                                                <th>受理员工</th>
                                                <th>业务动作</th>
                                                <th>业务类型</th>
                                                <th>业务状态</th>
                                            </tr>
                                            </thead>
                                            <tbody id="orderList">
                                            #if($options != "null" && $options.customerOrders && $options.customerOrders != "null" &&
                                            $options.customerOrders.size() > 0)
                                            #foreach($customerOrder in $options.customerOrders)
                                            <tr>
                                                <td><label class="wu-radio full absolute" data-scope=""><input
                                                        type="radio" name="payment"></label></td>
                                                <td><a class="textcolorgreen" link="customerOrder" data-toggle="tooltip" data-placement="top" title="$!customerOrder.remark">$!{customerOrder.olNbr}</a>
                                                    <p class="vita-data">{"custOrderId" : "$!customerOrder.olId", "custOrderNbr": "$!customerOrder.olNbr", "statusCd":"$!customerOrder.olStatusCd", "isNeedPrint":"$!customerOrder.isNeedPrint", "printClassDom":"$!customerOrder.printClassDom", "partyId":"$!customerOrder.partyId"}</p>

                                                </td>
                                                <td>$!{customerOrder.channelName}</td>
                                                <td>$!{customerOrder.soDate}</td>
                                                <td>$!{customerOrder.staffName}</td>
                                                <td>
                                                    #if($customerOrder.orderItems != "null" && $customerOrder.orderItems.size() > 0)
                                                        #foreach($orderItem in $customerOrder.orderItems)
                                                        <p  style="height: 40px;">$!{orderItem.boId}</p>
                                                        #end
                                                    #end
                                                </td>
                                                <td>
                                                    #if($customerOrder.orderItems != "null" && $customerOrder.orderItems.size() > 0)
                                                        #foreach($orderItem in $customerOrder.orderItems)
                                                        #if($orderItem.flag && $orderItem.flag == "ohter")
                                                        <p  style="height: 40px;">$!{orderItem.boActionTypeName}
                                                        
                                                        </p>
                                                        <p class="vita-data">{"orderItem" : $orderItem,
                                                                            "custOrderId" : $!customerOrder.olId,
                                                                            "regionId" : $!customerOrder.regionId,
                                                                            "custOrderNbr":"$customerOrder.olNbr"
                                                            }</p>
                                                        #end
                                                        #if($orderItem.flag && $orderItem.flag == "cust")
                                                        <p  style="height: 40px;"><a class="textcolorgreen" link="orderItem">$!{orderItem.boActionTypeName}</a>
                                                        
                                                        </p>
                                                        <p class="vita-data">{"orderItem" : $orderItem,
                                                                            "custOrderId" : $!customerOrder.olId,
                                                                            "regionId" : $!customerOrder.regionId,
                                                                            "custOrderNbr":"$customerOrder.olNbr"
                                                            }</p>
                                                        #end
                                                        #if($orderItem.flag && $orderItem.flag == "offer")
                                                        <p  style="height: 40px;">$!{orderItem.boActionTypeName}【<a class="textcolorgreen" link="orderItem">$!{orderItem.offerSpecName}
                                                        </a>】
                                                        
                                                        </p>
                                                        <p class="vita-data">{"orderItem" : $orderItem,
                                                                            "custOrderId" : $!customerOrder.olId,
                                                                            "regionId" : $!customerOrder.regionId,
                                                                            "custOrderNbr":"$customerOrder.olNbr"
                                                            }</p>
                                                        #end
                                                        #if($orderItem.flag && $orderItem.flag == "prod")
                                                        <p  style="height: 40px;">$!{orderItem.boActionTypeName} - $!{orderItem.prodSpecName}【<a class="textcolorgreen" link="orderItem">$!{orderItem.accessNumber}
                                                        </a>】
                                                        
                                                        </p>
                                                        <p class="vita-data">{"orderItem" : $orderItem,
                                                                            "custOrderId" : $!customerOrder.olId,
                                                                            "regionId" : $!customerOrder.regionId,
                                                                            "custOrderNbr":"$customerOrder.olNbr"
                                                            }</p>
                                                        #end
                                                        
                                                            
                                                        #end
                                                    #end
                                                </td>
                                                <td>
                                                    #if($customerOrder.orderItems != "null" && $customerOrder.orderItems.size() > 0)
                                                        #foreach($orderItem in $customerOrder.orderItems)
                                                        <p  style="height: 40px;">$!{orderItem.statusName}</p>
                                                        #end
                                                    #end
                                                </td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.customerOrders && $options.customerOrders != "null" &&
                                            $options.customerOrders.size() == 0)
                                                <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
