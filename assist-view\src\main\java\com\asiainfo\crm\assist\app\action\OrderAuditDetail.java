package com.asiainfo.crm.assist.app.action;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderAuditSMO;
import com.asiainfo.crm.service.intf.IPaperlessSysRecodSMO;
import com.asiainfo.crm.service.intf.ISoSaveMultiSMO;
import com.asiainfo.crm.util.ListUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created on 2019/3/27.
 */
@Component("vita.orderAuditDetail")
public class OrderAuditDetail extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(OrderAuditDetail.class);

    @Autowired
    private IOrderAuditSMO orderAuditSMO;

    @Autowired
    private ISoSaveMultiSMO soSaveMultiSMO;

    @Autowired
    private IPaperlessSysRecodSMO saveMergeSignatureService;

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        options.put("auditOrderReason", AssistMDA.ORDER_AUDIT_FAIL_REASON);
        List<Map<String, String>> auditResultList = new ArrayList<>(2);
        Set<Map.Entry<String, String>> auditResultEntries = AssistMDA.ORDER_AUDIT_RESULT_ATTR.entrySet();
        for (Map.Entry<String, String> entry : auditResultEntries) {
            Map<String, String> map = new HashMap<>(2);
            map.put("key", entry.getKey());
            map.put("value", entry.getValue());
            if (entry.getKey().equals("2")) {
                map.put("selected", "selected");
            } else {
                map.put("selected", "false");
            }
            auditResultList.add(map);
        }
        options.put("auditResultList", auditResultList);
        String auditOrderInfoList = orderAuditSMO.qryNeedAuditOrderInfo(params[0].toString());
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(auditOrderInfoList);
        List<Map> auditOrderInfos = (List<Map>) MapUtils.getObject(resultObjectMap, "auditOrderInfoVos");
        if (!ListUtil.isListEmpty(auditOrderInfos) && !MapUtils.isEmpty(auditOrderInfos.get(0))) {
            options.put("auditOrderInfo", auditOrderInfos.get(0));
        }
        Map<String, Object> paramBean = jsonConverter.toBean(params[0].toString(), Map.class);
        String custOrderId = (String)paramBean.get("custOrderId");
        String accNbr = (String)paramBean.get("accNum");
        List<Map<String, Object>> photoList = this.getPhoto(custOrderId, accNbr);
        if (!ListUtil.isListEmpty(photoList)) {
            options.put("photoList", photoList);
        }
        return options;
    }

    /**
     * 订单审核
     *
     * @param json
     * @return
     * @throws Exception
     */
    public Map audit(String json) throws Exception {
        String auditOrderResult = soSaveMultiSMO.completeOrderAudit(json);
        Map<String, Object> retMap = (Map<String, Object>) resolveResult(auditOrderResult);
        String code = MapUtils.getString(retMap, "code");
        String desc = MapUtils.getString(retMap, "desc");
        String auditResult = MapUtils.getString(retMap, "auditResult");
        String auditResultName = MapUtils.getString(retMap, "auditResultName");
        Map<String, Object> options = Maps.newHashMap();
        options.put("code", code);
        options.put("desc", desc);
        options.put("auditResult", auditResult);
        options.put("auditResultName", auditResultName);
        return options;
    }

    /**
     * 从电子档案获取照片
     *
     * @param custOrderId
     * @param accNbr
     * @return
     * @throws Exception
     */
    public List<Map<String, Object>> getPhoto(String custOrderId, String accNbr) throws Exception {
        //查找的照片类型
        List<String> busiTypeMap = new ArrayList<>();
        busiTypeMap.add(AssistMDA.ORDER_AUDIT_PHOTO_ATTR.get("photoTypeA"));
        busiTypeMap.add(AssistMDA.ORDER_AUDIT_PHOTO_ATTR.get("photoTypeB"));
        Map<String, Object> paramMap = new HashMap<>(4);
        List<Map<String, Object>> returnList = new ArrayList<>();
        //附件分组查询
        List<Map<String, Object>> attachList = new ArrayList<>();
        for (String busiType : busiTypeMap) {
            Map<String, Object> t = new HashMap<>(4);
            t.put("busiType", busiType);
            //关联类型：接入号关联：ACC_NBR
            t.put("relaItem", AssistMDA.ORDER_AUDIT_PHOTO_ATTR.get("relaItem"));
            //分组标识
            t.put("relaValue", accNbr);
            attachList.add(t);
        }
        //测试数据
        paramMap.put("olId", custOrderId);
        paramMap.put("srcFlag", AssistMDA.ORDER_AUDIT_PHOTO_ATTR.get("srcFlag"));
        paramMap.put("attachList", attachList);

        //获取电子档案附件列表
        String archiveData = saveMergeSignatureService.attachsQuery(jsonConverter.toJson(paramMap));
        Map<String, Object> archiveDataMap = jsonConverter.toMap(archiveData, String.class, Object.class);
        //找到对应附件
        String resultCode = "resultCode";
        int result = MapUtils.getIntValue(archiveDataMap,resultCode,1);
        if (result == 0) {
            List<Map<String, Object>> resultList = (List<Map<String, Object>>) archiveDataMap.get("result");
            for (Map<String, Object> resultData : resultList) {
                Map<String, Object> dParamMap = new HashMap<>(8);
                dParamMap.put("busiType", resultData.get("busiType"));
                dParamMap.put("olId", custOrderId);
                dParamMap.put("srcFlag", AssistMDA.ORDER_AUDIT_PHOTO_ATTR.get("srcFlag"));
                Map<String, Object> mapCer = jsonConverter.toMap(saveMergeSignatureService.archivesDownload(jsonConverter.toJson(dParamMap)),
                        String.class, Object.class);
                mapCer.put("busiType", resultData.get("busiType"));
                mapCer.put("accNbr", resultData.get("relaValue"));
                returnList.add(mapCer);
            }
        }
        return returnList;
    }
}
