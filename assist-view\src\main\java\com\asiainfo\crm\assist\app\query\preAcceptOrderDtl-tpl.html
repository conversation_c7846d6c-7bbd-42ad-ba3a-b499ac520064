<div data-widget="preAcceptOrderDtl" style="height:100%">

    #macro( nullNotShow $val)
    #if($val && $val != "null")
    $!val
    #end
    #end

    <div class="calcw_rightbox noneleft">
        <div class="calctitle">
            <div class="titlefont">
                <ul class="nav nav-tabs">
                    <li style="font-weight: bold">预受理单详情</li>
                </ul>
            </div>
        </div>
        <div class="calcw_rightcont" style="padding-bottom: 0px">
            <div id="myTabContent" class="tab-content height100">
                <div class="tab-pane fade active in" id="1">
                    #if($options.prepareOrder && $options.prepareOrder != "null")
                    #set($offerDetail =$options.prepareOrder)
                    <!--  预受理单基本信息  -->
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>预受理单基本信息</h5>
                        <table class="table table-bordered conttd-w">
                            <tbody>
                            <tr>
                                <td class="labellcont">预受理单状态</td>
                                <td data-cust-view-code="offerName">#nullNotShow($offerDetail.statusCd)</td>
                                <td class="labellcont">预受理单id</td>
                                <td>#nullNotShow($offerDetail.prepareOrderId)</td>
                            </tr>
                            #if($options.prepareOrderDataList && $options.prepareOrderDataList != "null" && $options.prepareOrderDataList.size() > 0)
                            #set($busiData = $options.prepareOrderDataList[0].busiData)
                            #set($commonData = $busiData.commonData)
                            <tr>
                                <td class="labellcont">受理地区</td>
                                <td>$!{offerDetail.regionName}</td>
                                <td class="labellcont">受理渠道</td>
                                <td>$!{offerDetail.createOrgName}</td>
                            </tr>
                            <tr>
                                <td class="labellcont">客户id</td>
                                <td>
                                    #if($offerDetail.custId == -1)
                                    新增客户
                                    #else
                                    #nullNotShow($offerDetail.custId)
                                    #end
                                </td>
                                <td class="labellcont">客户姓名</td>
                                <td>#nullNotShow($busiData.custName)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">客户证件</td>
                                <td>$!{offerDetail.certTypeName}</td>
                                <td class="labellcont">客户证件号码</td>
                                <td>$!{offerDetail.certNum}</td>
                            </tr>
                            <tr>
                                <td class="labellcont">创建员工名称</td>
                                <td>#nullNotShow($busiData.createStaffName)</td>
                                <td class="labellcont">创建渠道</td>
                                <td>#nullNotShow($busiData.createOrgName)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">更新员工</td>
                                <td>#nullNotShow($busiData.updateStaffName)</td>
                                <td class="labellcont">修改渠道</td>
                                <td>#nullNotShow($busiData.updateOrgName)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">联系人</td>
                                <td>$!{offerDetail.linkMan}</td>
                                <td class="labellcont">联系电话</td>
                                <td>$!{offerDetail.linkNumber}</td>
                            </tr>

                            <tr>
                                <td class="labellcont">协销人ID</td>
                                <td>#nullNotShow($commonData.devStaffId)</td>
                                <td class="labellcont">协销人名称</td>
                                <td>#nullNotShow($commonData.devStaffName)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">经办人名称</td>
                                <td>#nullNotShow($commonData.handler)</td>
                                <td class="labellcont">经办人证件号码</td>
                                <td>#nullNotShow($commonData.handlerCertNum)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">经办人证件类型</td>
                                <td>#nullNotShow($commonData.handlerCertTypeName)</td>
                                <td class="labellcont">经办人id</td>
                                <td>#nullNotShow($commonData.handlerId)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">经办人电话</td>
                                <td>#nullNotShow($commonData.handlerPhone)</td>
                                <td class="labellcont">经办人证件地址</td>
                                <td>#nullNotShow($commonData.certInfo)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">费用信息</td>
                                <td>$!{offerDetail.charge}</td>
                                <td class="labellcont">预收款金额</td>
                                <td>$!{offerDetail.advanceCharge}</td>
                            </tr>
                            <tr>
                                <td class="labellcont">扣费金额</td>
                                <td>$!{offerDetail.backCharge}</td>
                                <td class="labellcont">产品备注</td>
                                <td>$!{offerDetail.remark}</td>
                            </tr>
                            #if($offerDetail.extPreOrderNbr && $offerDetail.extPreOrderNbr != "null")
                            <tr>
                                <td class="labellcont">集团外部流水</td>
                                <td>$!{offerDetail.extPreOrderNbr}</td>
                            </tr>
                            #end
                            #end
                            </tbody>
                        </table>
                    </div>
                    #end
                    <!--  对象信息-->
                    #if($options.prepareOrderObjList && $options.prepareOrderObjList != "null" && $options.prepareOrderObjList.size() > 0)
                    #set($objList = $options.prepareOrderObjList)
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>预受理单对象</h5>
                        <div class="row">
                            <div class="col-md-12">
                                <table class="table table-bordered">
                                    <thead>
                                    <tr>
                                        <th>对象名称</th>
                                        <th>对象ID</th>
                                        <th>对象类型</th>
                                        <th>业务动作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    #foreach($orderObj in $objList)
                                    <tr>
                                        <td>#nullNotShow($orderObj.objName)</td>
                                        <td>#nullNotShow($orderObj.objId)</td>
                                        <td>
                                            #if($orderObj.objType == "OFFER")
                                            主套餐
                                            #else
                                            产品
                                            #end
                                        </td>
<!--                                        <td>#nullNotShow($orderObj.serviceOfferId)</td>-->
                                        <td>订购销售品</td>
                                    </tr>
                                    #end
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    #end
                    <!--属性信息-->
                    #if($options.prepareOrderAttrList && $options.prepareOrderAttrList != "null" && $options.prepareOrderAttrList.size() > 0 )
                    #set($attrList = $options.prepareOrderAttrList)
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>预受理单属性</h5>
                        <div class="row">
                            <div class="col-md-12">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>属性名</th>
                                            <th>属性值</th>
                                            <th>属性值id</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    #foreach($attrObj in $attrList)
                                        <tr>
                                            <td>$!{attrObj.attrName}</td>
                                            <td>$!{attrObj.attrValue}</td>
                                            <td>$!{attrObj.attrValueId}</td>
                                        </tr>
                                    #end
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    #end

                    <!--产品信息 -->
                    #if($options.prepareOrderDataList && $options.prepareOrderDataList != "null" && $options.prepareOrderDataList.size() > 0)
                    #set($prepareOrderDataList = $options.prepareOrderDataList)
                    #foreach($preOrderData in $prepareOrderDataList)
                    #set($busiData=$preOrderData.busiData)
                    #set($accProdDatas = $busiData.accProdDatas)
                    #set($offerDatas = $busiData.offerDatas)
                    #if($accProdDatas && $accProdDatas != "null" && $accProdDatas.size() > 0)
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>产品信息</h5>
                        #foreach( $accProd in $accProdDatas)
                        <table class="table table-bordered conttd-w">
                            <tbody>
                            <tr>
                                <td class="labellcont">产品名称</td>
                                <td data-cust-view-code="offerName">#nullNotShow($accProd.prodName)</td>
                                <td class="labellcont">产品规格id</td>
                                <td>#nullNotShow($accProd.prodId)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">标准地址</td>
                                <td data-cust-view-code="offerName">#nullNotShow($accProd.addressStr)</td>
                                <td class="labellcont">安装地址</td>
                                <td>#nullNotShow($accProd.installAddressStr)</td>
                            </tr>
                            #if($accProd.accNums && $accProd.accNums.size() > 0)
                            #set($accNum = $accProd.accNums[0])
                            <tr>
                                <td class="labellcont">接入号</td>
                                <td data-cust-view-code="offerName">#nullNotShow($accNum.accNum)</td>
                                <td class="labellcont">号码实例id</td>
                                <td>#nullNotShow($accNum.anId)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">账号秘密</td>
                                <td data-cust-view-code="offerName">#nullNotShow($accNum.password)</td>
                                <td class="labellcont">号码类型</td>
                                <td data-cust-view-code="offerName">#nullNotShow($accNum.accNumType)</td>
                            </tr>
                            #end
                            </tbody>
                        </table>
                        <br/>
                        #end
                    </div>
                    #end
                    #if($offerDatas && $offerDatas != "null" && $offerDatas.size() > 0)
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>销售品信息</h5>
                        <div class="row">
                            <div class="col-md-12">
                                <table class="table table-bordered">
                                    <thead>
                                    <tr>
                                        <th>销售品名称</th>
                                        <th>销售品规格id</th>
                                        <th>业务动作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    #foreach($offer in $offerDatas)
                                    <tr>
                                        <td>#nullNotShow($offer.offerName)</td>
                                        <td>#nullNotShow($offer.offerId)</td>
<!--                                        <td>#nullNotShow($offer.serviceOfferId)</td>-->
                                        <td>订购销售品</td>
                                    </tr>
                                    #end
                                    </tbody>
                                </table>
                            </div>
                        </div>


                    </div>
                    #end
                    #end
                    #end


                    <!--转正后的订单号-->
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>转正信息</h5>
                        <table class="table table-bordered conttd-w">
                            <tbody>
                            #if($options.custOrderNbrs && $options.custOrderNbrs != "null" && $options.custOrderNbrs.size() > 0)
                                #set($custOrderNbrs = $options.custOrderNbrs)
                                #foreach($nbr in $custOrderNbrs)
                                <tr>
                                    <td class="labellcont">正式订单号</td>
                                    <td data-cust-view-code="custOrderNbr" style="width: 70%">
                                        <a name="custOrderNbr">$!nbr</a>
                                        <p class="vita-data">{ "custOrderNbr": $!nbr }</p>
                                    </td>
                                </tr>
                                #end
                            #else
                                <tr>
                                    <td class="labellcont">未有转正订单</td>
                                </tr>
                            #end
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>