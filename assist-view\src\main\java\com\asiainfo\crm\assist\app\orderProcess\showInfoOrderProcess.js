(function (vita) {

    var showInfoOrderProcess = vita.Backbone.BizView.extend({
        events: {
            "click .close" : "_closePage",
            "click #orderItemInfo": "_orderItemInfo",
            "click #warehousingInfo": "_warehousingInfo",
            "click #GDXJ": "_GDXJ",
            "click #asynMsgInfo": "_asynMsgInfo",


        },
        _closePage : function() {
            var widget = this,
                element = $(widget.el);
            var dialog = element.closest("[data-widgetfullname=vita-dialog]");
            if (dialog.length) {
                dialog.dialog("close");
            }
        },

        _GDXJ : function() {
            var widget = this,
                element = $(widget.el);
            widget.popup("暂无功能");
        },



        _warehousingInfo : function() {
            var widget = this,element = $(widget.el);
            var dialogId = "warehousingInfo";
            var url = './warehousingInfo';
            var params = {
            };
            params.custOrderId=$(widget.el).data("custOrderId")
            widget.callService("queryCustomerOrderLogs", JSON.stringify(params), function (res) {
                if (res.data!=null){
                    widget.dialog({
                        id : dialogId,
                        url : url,
                        left : "-200px",
                        params : {
                            data:JSON.stringify(res.data)
                        },
                    });
                }
            }, {async: false});
            $("#orderDiv").dialog("open")
        },

        _asynMsgInfo : function() {
            var widget = this,element = $(widget.el);
            var dialogId = "warehousingInfo";
            var url = './warehousingInfo';
            var params = {
            };
            params.custOrderId=$(widget.el).data("custOrderId")
            params.nodeCenter=$(widget.el).data("nodeCenter")
            params.relaMsgs=$(widget.el).data("relaMsgs")
            params.msgId=$(widget.el).data("msgId")
            params.msgTopic=$(widget.el).data("msgTopic")
            params.msgNbr=$(widget.el).data("msgNbr")
            params.orderItems=$(widget.el).data("orderItems")
            widget.callService("asynMsgInfo", JSON.stringify(params), function (res) {
                if (res.data!=null){
                    widget.dialog({
                        id : dialogId,
                        url : url,
                        left : "-200px",
                        params : {
                            data:JSON.stringify(res.data)
                        },
                    });
                }
            }, {async: false});
            $("#orderDiv").dialog("open")
        },


        _orderItemInfo : function() {
            var widget = this,element = $(widget.el);
            var dialogId = "olDetailInfo";
            var url = '../query/olDetailInfo';
            widget.dialog({
                id : dialogId,
                url : url,
                left : "-200px",
                params : {
                    custOrderId:$(widget.el).data("custOrderId")
                },
            });
        },
    });
    vita.widget.register("showInfoOrderProcess", showInfoOrderProcess, true);
})(window.vita);