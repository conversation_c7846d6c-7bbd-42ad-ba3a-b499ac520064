<div data-widget="boProdDetailInfo" style="height: 100%">
    <p class="vita-data">{"data" : {
        "custOrderId":"$!options.coId",
        "orderItemId":"$!options.orderItemId",
        "prodId":"$!options.prodId"
        }}
    </p>
    #macro( nullNotShow $val)
        #if($val && $val != "null")
            $!val
        #end
    #end

    #macro( table $arry $name $value $e $n)
    <table class="table table-bordered conttd-w">
        <tbody>
        <tr>
            #foreach($a in $arry)
            <td class="labellcont">#nullNotShow($a[$name])&nbsp;</td>
            <td>
                #if($a['operType'] && $n == 'Y')
                    #if($a['operType'] == "1000")
                        【新】
                    #elseif($a['operType'] == "1100")
                        【旧】
                    #else
                        【原】
                    #end
                #end
                #nullNotShow($a[$value])</td>
            #if ( ($velocityCount % 2 == 0) && ($velocityCount - $arry.size() != 0))
        </tr>
        <tr>
            #end
            #end
            #if($e)
            <td class="labellcont">&nbsp;</td>
            <td></td>
            #end
        </tr>
        </tbody>
    </table>
    #end

    <div class="calcw_rightbox noneleft">
        <div class="calctitle">
            <div class="titlefont">
                <ul id="prodTab" class="nav nav-tabs">
                    <li class="active"><a id="#1" href="javascript:void(0);">受理信息</a></li>
                    <li class=""><a id="#2" href="javascript:void(0);">工单信息</a></li>
                    <li class=""><a id="#3" href="javascript:void(0);">资源信息</a></li>
                </ul>
            </div>
            <div class="toolr">
                <!--<button id="closeBtn" type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>-->
            </div>
        </div>

        <div class="calcw_rightcont">
            #if($options.detailInfo && $options.detailInfo != "null")
            #set($prodItemVo = $options.detailInfo.prodOrderItemVo)
            #if($prodItemVo && $prodItemVo != "null")
            <div id="myTabContent" class="tab-content">
                <div class="tab-pane fade active in" id="1">
                    #set($baseInfo = $prodItemVo.orderItemDetailInfo)
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>基本信息</h5>
                        <table class="table table-bordered conttd-w">
                            <tbody>
                            <tr>
                                <td class="labellcont">订单ID</td>
                                <td>
                                    <a name="custOrderId" href="javascript:void(0);" class="textcolorgreen"
                                       title="查看订单详情">
                                        <p class="vita-data">{"custOrderId":"$!options.coId"}</p>
                                        $!options.coId
                                    </a>
                                </td>
                                <td class="labellcont">订单项ID</td>
                                <td>#nullNotShow($baseInfo.orderItemId)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">受理地区</td>
                                <td>#nullNotShow($baseInfo.acceptRegionName)</td>
                                <td class="labellcont">受理渠道</td>
                                <td>#nullNotShow($baseInfo.createOrgName)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">业务类型</td>
                                <td>#nullNotShow($baseInfo.serviceOfferName)-#nullNotShow($baseInfo.applyObjSpecName)</td>
                                <td class="labellcont">受理时间</td>
                                <td>#nullNotShow($baseInfo.acceptDate)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">受理员工</td>
                                <td>#nullNotShow($baseInfo.createStaffName)</td>
                                <td class="labellcont">状态</td>
                                <td>#nullNotShow($baseInfo.statusCdName)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">竣工时间</td>
                                <td>#nullNotShow($baseInfo.completeDate)</td>
                                <td class="labellcont">压单时间</td>
                                <td>#nullNotShow($baseInfo.retentionDate)</td>
                            </tr>
                            <tr>
                                <td class="labellcont">第一协销人</td>
                                <td>
                                    #foreach($name in $baseInfo.devStaffNames)
                                        #if($name.devStaffType == "1000")
                                            #nullNotShow($name.devStaffName)
                                        #end
                                    #end
                                </td>
                                <td class="labellcont">第二协销人</td>
                                <td>
                                    #foreach($name in $baseInfo.devStaffNames)
                                        #if($name.devStaffType == "2000")
                                            #nullNotShow($name.devStaffName)
                                        #end
                                    #end
                                </td>
                            </tr>
                            <tr>
                                <td class="labellcont">未竣工原因</td>
                                <td>#nullNotShow($baseInfo.uncompleteReason)</td>
                                <td class="labellcont"></td>
                                <td></td>
                            </tr>
                            #if($baseInfo.orderContactInfos && $baseInfo.orderContactInfos!="null" && $baseInfo.orderContactInfos.size()>0)
                                #foreach($contact in $baseInfo.orderContactInfos)
                                <tr>
                                    <td class="labellcont">联系人</td>
                                    <td>#nullNotShow($contact.contactName)</td>
                                    <td class="labellcont">联系号码</td>
                                    <td>#nullNotShow($contact.contactPhone)</td>
                                </tr>
                                #end
                            #end
                            </tbody>
                        </table>
                    </div>

                    <!--产品信息-->
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>产品信息</h5>
                        <div class="row">
                            <div class="col-md-12">
                                <table class="table table-bordered conttd-w">
                                    <tbody>
                                    <!--客户-->
                                    <tr>
                                        <td class="labellcont">客户名称</td>
                                        <td>
                                            #if($prodItemVo.ownerCustNames && $prodItemVo.ownerCustNames != "null" && $prodItemVo.ownerCustNames.size()>0)
                                            #foreach($cust in $prodItemVo.ownerCustNames)
                                                #if($!cust.operType == "1000")
                                                    【新】
                                                #elseif($!cust.operType == "1100")
                                                    【旧】
                                                #else
                                                    【原】
                                                #end
                                                <a name="custDetail" href="javascript:void(0);" class="textcolorgreen"
                                                   title="查看客户详情">
                                                    <p class="vita-data">{"custId":"$!cust.attrId"}</p>
                                                    #nullNotShow($cust.attrValueName)
                                                </a>
                                                <br/>
                                            #end
                                            #end
                                        </td>
                                        <td class="labellcont">产品状态</td>
                                        <td>
                                            #if($prodItemVo.statusCdNames && $prodItemVo.statusCdNames != "null" && $prodItemVo.statusCdNames.size()>0)
                                                #foreach($status in $prodItemVo.statusCdNames)
                                                    #if($!status.operType == "1000")
                                                    【新】
                                                    #elseif($!status.operType == "1100")
                                                    【旧】
                                                    #else
                                                    【原】
                                                    #end
                                                #nullNotShow($status.attrValueName)
                                                <br/>
                                                #end
                                            #end
                                        </td>
                                    </tr>

                                    <!--账户-->
                                    <tr>
                                        <td class="labellcont">账户名称</td>
                                        <td>
                                            #if($prodItemVo.acctNames && $prodItemVo.acctNames != "null" && $prodItemVo.acctNames.size()>0)
                                            #foreach($acctName in $prodItemVo.acctNames)
                                                #if($!acctName.operType == "1000")
                                                    【新】
                                                #elseif($!acctName.operType == "1100")
                                                    【旧】
                                                #else
                                                    【原】
                                                #end
                                                #nullNotShow($acctName.attrValueName)
                                                <br/>
                                            #end
                                            #end
                                        </td>

                                        <td class="labellcont">账户合同</td>
                                        <td>
                                            #if($prodItemVo.acctCds && $prodItemVo.acctCds != "null" && $prodItemVo.acctCds.size()>0)
                                            #foreach($acct in $prodItemVo.acctCds)
                                                #if($!acct.operType == "1000")
                                                【新】
                                                #elseif($!acct.operType == "1100")
                                                【旧】
                                                #else
                                                【原】
                                                #end
                                                <a href="javascript:void(0);" class="textcolorgreen" name="acctDetail"
                                                   title="查看账户详情">
                                                    <p class="vita-data">{"acctCd":"$acct.value"}</p>
                                                    #nullNotShow($acct.value)
                                                </a>
                                                <br/>
                                            #end
                                            #end
                                        </td>
                                    </tr>

                                    <!--付费方式-->
                                    <tr>
                                        <td class="labellcont">付费方式</td>
                                        <td>
                                            #if($prodItemVo.paymentMethodNames && $prodItemVo.paymentMethodNames != "null" && $prodItemVo.paymentMethodNames.size()>0)
                                            #foreach($fee in $prodItemVo.paymentMethodNames)
                                                #if($!fee.operType == "1000")
                                                    【新】
                                                #elseif($!fee.operType == "1100")
                                                    【旧】
                                                #else
                                                    【原】
                                                #end
                                                #nullNotShow($fee.attrValueName)
                                                <br/>
                                            #end
                                            #end
                                        </td>
                                        <td class="labellcont">地址</td>
                                        <td>
                                            #if($prodItemVo.addressDescs && $prodItemVo.addressDescs != "null" && $prodItemVo.addressDescs.size()>0)
                                            #foreach($address in $prodItemVo.addressDescs)
                                                #if($!address.operType == "1000")
                                                【新】
                                                #elseif($!address.operType == "1100")
                                                【旧】
                                                #else
                                                【原】
                                                #end
                                                #nullNotShow($address.attrValueName)
                                                <br/>
                                            #end
                                            #end
                                        </td>
                                    </tr>

                                    <!--主接入号-->
                                    <tr>
                                        <td class="labellcont">主接入号</td>
                                        <td>
                                            #if($prodItemVo.accNums && $prodItemVo.accNums != "null" && $prodItemVo.accNums.size()>0)
                                            #foreach($prodAns in $prodItemVo.accNums)
                                                #if($!prodAns.operType == "1000")
                                                    【新】
                                                #elseif($!prodAns.operType == "1100")
                                                    【旧】
                                                #else
                                                    【原】
                                                #end
                                                #nullNotShow($prodAns.attrValueName)
                                                <br/>
                                            #end
                                            #end
                                        </td>

                                        <td class="labellcont">非主接入号</td>
                                        <td>
                                            #if($prodItemVo.ordProdInstAccNums && $prodItemVo.ordProdInstAccNums != "null" && $prodItemVo.ordProdInstAccNums.size()>0)
                                                #foreach($prod2Ans in $prodItemVo.ordProdInstAccNums)
                                                #if($prod2Ans.accNumType != "1000")
                                                    #if($!prod2Ans.operType == "1000")
                                                    【新】
                                                    #elseif($!prod2Ans.operType == "1100")
                                                    【旧】
                                                    #else
                                                    【原】
                                                    #end
                                                    #nullNotShow($prod2Ans.accNum)
                                                 <br/>
                                            #end
                                            #end
                                            #end
                                        </td>
                                    </tr>

                                    #if($prodItemVo.useCustNames && $prodItemVo.useCustNames != "null" && $prodItemVo.useCustNames.size()>0)
                                    <tr>
                                        <td class="labellcont">使用人</td>
                                        <td>
                                            #foreach($userCust in $prodItemVo.useCustNames)
                                            #if($!userCust.operType == "1000")
                                                【新】
                                            #elseif($!userCust.operType == "1100")
                                                【旧】
                                            #else
                                                【原】
                                            #end
                                            #nullNotShow($userCust.attrValueName)
                                            <br/>
                                            #end
                                            #end
                                        </td>
                                        <td class="labellcont">&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!--附属销售品-->
                    #if($prodItemVo.optionals && $prodItemVo.optionals != "null" && $prodItemVo.optionals.size()>0)
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>附属销售品</h5>
                        <div class="row">
                            <div class="col-md-12">
                                <table class="table table-bordered conttd-tow">
                                    <tbody>
                                    <tr>
                                        <td class="labellcont">附属销售品</td>
                                        <td colspan="3">
                                            #foreach($option in $prodItemVo.optionals)
                                            <spec class="marl15">
                                                #if($!option.serviceOfferId == "3010100000")
                                                【订】
                                                #elseif($!option.serviceOfferId == "3030100000")
                                                【退】
                                                #else
                                                【原】
                                                #end
                                                #nullNotShow($option.offerName)
                                            </spec>
                                            #end
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    #end

                    <!--功能类产品-->
                    #if($prodItemVo.functionals && $prodItemVo.functionals != "null" && $prodItemVo.functionals.size()>0)
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>功能类产品</h5>
                        <div class="row">
                            <div class="col-md-12">
                                <table class="table table-bordered">
                                    <thead>
                                    <tr>
                                        <th width="20px"></th>
                                        <th>名称</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    #foreach($func in $prodItemVo.functionals)
                                    <tr>
                                        <td>
                                            #set($hasParam = $func.prodInstAttrs && $func.prodInstAttrs != "null" && $func.prodInstAttrs.size()>0)
                                            #if($hasParam)
                                            <i class="glyphicon fa-arrowr ivu-arrow ivu-noselect-arrow" name="paramA"></i>
                                            #end
                                        </td>
                                        <td>
                                            #if($!func.serviceOfferId == "4010400000")
                                            【开】
                                            #elseif($!func.serviceOfferId == "4020500000")
                                            【关】
                                            #else
                                            【原】
                                            #end#nullNotShow($func.prodName)
                                        </td>
                                    </tr>
                                    <!-- 参数展示 -->
                                    #if($hasParam)
                                    <tr style="display: none;" name="paramTr">
                                        <td colspan="2" class="ivu-down_cont">
                                            #foreach($attr in $func.prodInstAttrs)
                                            <div class="col-sm-4 textcont"><label>#nullNotShow($attr.attrName):</label><span>#nullNotShow($attr.attrValueName)</span></div>
                                            #end
                                        </td>
                                    </tr>
                                    #end
                                    #end
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    #end

                    <!--产品属性-->
                    #if($prodItemVo.prodInstAttrs && $prodItemVo.prodInstAttrs != "null" && $prodItemVo.prodInstAttrs.size()>0)
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>产品属性</h5>
                        <div class="row">
                            <div class="col-md-12">
                                #set($even = $prodItemVo.prodInstAttrs.size() % 2 == 1)
                                #table($prodItemVo.prodInstAttrs,"attrName","attrValueName",$even ,'Y')
                            </div>
                        </div>
                    </div>
                    #end

                    <!--订单属性-->
                    #if($baseInfo.orderItemAttrs && $baseInfo.orderItemAttrs != "null" && $baseInfo.orderItemAttrs.size()>0)
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>订单属性</h5>
                        <div class="row">
                            <div class="col-md-12">
                                #set($even = $baseInfo.orderItemAttrs.size() % 2 == 1)
                                #table($baseInfo.orderItemAttrs,"attrName","attrValueName",$even)
                            </div>
                        </div>
                    </div>
                    #end

                    <!--物品信息-->
                    #if($prodItemVo.ordProdResInstRels && $prodItemVo.ordProdResInstRels != "null" && $prodItemVo.ordProdResInstRels.size()>0)
                    <div class="container-fluid">
                        <h5 class="meal_htitle nobrder"><i class="bot"></i>物品信息</h5>
                        <div class="row">
                            <div class="col-md-12">
                                <table class="table table-bordered">
                                    <thead>
                                    <tr>
                                        <th>订单项</th>
                                        <th>串码</th>
                                        <th class="hidden-xs hidden-sm">物品名称</th>
                                        <th class="hidden-xs hidden-sm">数量</th>
                                        <th>状态</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    #foreach($coupon in $prodItemVo.ordProdResInstRels)
                                    <tr>
                                        <td>#nullNotShow($coupon.orderItemId)</td>
                                        <td>#nullNotShow($coupon.mktResInstNbr)</td>
                                        <td class="hidden-xs hidden-sm">#nullNotShow($coupon.mktResName)</td>
                                        <td class="hidden-xs hidden-sm">#nullNotShow($coupon.mktResNum)</td>
                                        <td>
                                            #if($coupon && $coupon.operType != "null" && $coupon.operType == "1000")
                                                新增
                                            #elseif($coupon && $coupon.operType != "null" && $coupon.operType == "1100")
                                                删除
                                            #else
                                            #end
                                        </td>
                                    </tr>
                                    #end
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    #end
                </div>
                <!--工单信息-->
                <div class="tab-pane fade" id="2"/>
                <!--资源信息-->
                <div class="tab-pane fade" id="3"/>
            </div>
            #end
            #end
        </div>
    </div>
</div>