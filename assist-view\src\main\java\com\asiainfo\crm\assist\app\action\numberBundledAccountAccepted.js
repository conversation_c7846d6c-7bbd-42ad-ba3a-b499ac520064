
/**
 * Created by Administrator on 2017-9-25.
 */
(function (vita) {
    var numberBundledAccountAccepted = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_qryInfo",
            "click #regionIdBtn": "_chooseArea",
            "click #queryCondition": "_onChangeQueryType",
            "click #bindCheck": "_addMemberInfo",
            "click #cancelBindCheck":"_cancelBindInfo"
        },
        _initialize: function () {
        	var widget = this,
            element = $(widget.el);
            //选择渠道
	        var compCode = "chooseChannel";
	        var dialogId = "chooseChannelDialog";
	        var option = {
	            id : dialogId,
	            url : widget.global.chooseChannel,
	            params : {
	            },
	            onClose : function(res) {
	                var comp = $(res).closest("[data-widget=" + compCode + "]");
	                var data = comp[compCode]("getValue");
	                if (!data) {
	                    return false;
	                }
	                element.find("#checkChannelId").val(data.channelName).attr("value", data.orgId);
	            }
	        }
        widget.dialog(option);
       },
        global:{
            pageIndex: 1,
            pageSize: 4,
            chooseArea: "../comm/chooseArea",
            chooseChannel: "../comm/chooseChannel"
        },
        _qryInfo: function () {
            var widget = this;
            var gsession = widget.gSession;
            debugger;
            var channelId = $("#checkChannelId").val();//渠道id
            var queryCondition = $("#queryCondition").val();
            var accessNumber = $("#accessNumber").val();//号码
            //地区ID
            var regionId = $("#regionId").attr("value");
            element = $(widget.el);
            //去除缓存值
            document.getElementById('numbers').value="";
            var numberType = "";
            var params = {};
            if (queryCondition == 1) {
                 numberType = $("#numberType").val();
                 if(numberType == 1){
                	 params = {
                             channelId:channelId,
                             queryCondition:queryCondition,
                             accessNumber:accessNumber,
                             numberType:numberType,
                             accNum:accessNumber,
                             regionId:regionId
                         };
                 }
                 if(numberType == 2){
                	 params = {
                             channelId:channelId,
                             queryCondition:queryCondition,
                             accessNumber:accessNumber,
                             numberType:numberType,
                             account:accessNumber,
                             regionId:regionId
                         };
                 }
            }
            if (queryCondition == 2) {
            	params ={
            			 channelId:channelId,
                         queryCondition:queryCondition,
                         accessNumber:accessNumber,
                         numberType:numberType,
                         acctCd:accessNumber,
                         regionId:regionId
            	}
            }    
            if(accessNumber==null||accessNumber==""){
                widget.popup("请输入查询内容");
                return;
            }
            debugger;
            widget.callService("numberBundledAccountAccepted", JSON.stringify(params),function (re) {
                debugger;
                element = $(widget.el);
                var managerNumValue =  element.find("#managerNum")[0] ;
                var offerName =  element.find("#offerName")[0] ;
                var currentSate =  element.find("#currentSate")[0] ;
                var custName =  element.find("#custName")[0] ;
                var acctNum =  element.find("#acctNum")[0] ;
                if(re.retCode=='0'){
                	managerNumValue.value = re.managerNum;
                	offerName.value = re.offerName;
                	currentSate.value = re.currentSate;
                	custName.value = re.custName;
                	acctNum.value = re.acctNum;
                }else{
                	//制空
                	managerNumValue.value = '';
                	offerName.value = '';
                	currentSate.value = '';
                	custName.value = '';
                	acctNum.value = '';
                }
            }, { async: false });
            widget._queryBind(accessNumber,queryCondition,regionId);
        },
        _onChangeQueryType:function () {
            var sel = $("#queryCondition").val();
            if (sel == 2){
                $("#numberType").attr("style","display: none");
            }else if (sel == 1){
                $("#numberType").attr("style","display: show");
            }
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        /*绑定详情查询*/
        _queryBind:function(accessNumber,queryCondition,regionId){
        	/*1:清楚绑定详情信息
        	 *2:新查询信息填充表格
        	 */
        	 var widget = this;
        	 var params = {}
        	 if(queryCondition == 1){
        		 params = {
                  		   acctNumber:accessNumber,
                  		   regionId :regionId,
                           pageInfo: {
                               pageIndex: widget.global.pageIndex,
                               pageSize: widget.global.pageSize
                           }
                    }
        	 }
        	 if(queryCondition == 2){
        		   params = {
                  		   acctCd:accessNumber,
                  		   regionId :regionId,
                           pageInfo: {
                               pageIndex: widget.global.pageIndex,
                               pageSize: widget.global.pageSize
                           }
                    }
        	 }
        	  widget.refreshPart("qryBindAcctInfo", JSON.stringify(params), "#bindAccountdiv", function (res) {
        		   debugger;
                   var paging = widget.require("paging"), r = $(res);
                   var totalNumber = r.find("#showPageInfo").data("totalNumber");
                   if (totalNumber > 0) {
                       var e = paging.new({
                           recordNumber: widget.global.pageSize,
                           total: totalNumber,
                           pageIndex: widget.global.pageIndex,
                           callback: function (_pageIndex, _recordNumber) {
                               params.pageInfo.pageIndex = _pageIndex;
                               params.pageInfo.pageSize = _recordNumber;
                               if (widget._getConds()) {
                                   widget.refreshPart("qryBindAcctInfo", JSON.stringify(params), "#bindAccountResult");
                               }
                               ;
                           }
                       });
                       r.find("#showPageInfo").append(e.getElement());
                   }

               }, {
                   async: false
               });
        },
        _addMemberInfo:function () {
        	var widget = this;
        	var accessNums = document.getElementById("numbers").value;
        	var accessNumber = $("#accessNumber").val();
        	var regionId = $("#regionId").attr("value");
        	if(accessNumber == null || accessNumber ==''){
        		widget.popup("请查询被绑定号码信息！");
        		return;
        	}
        	if(regionId == null || regionId =='' || regionId == undefined ){
        		widget.popup("请选择地区！");
        		return;
        	}
        	var res = widget._checkAccount(accessNums);
        	debugger;
        	if (res == false) return;
              var accessNumStrc = accessNums.split(",");
        	  var url = "../action/addMemberList?rand=" + Math.random() + "&accessNums="+accessNumStrc+ "&regionId="+regionId+"&manageNum="+accessNumber;
        	  var iWidth = 900; //弹出窗口的宽度;
              var iHeight = 600; //弹出窗口的高度;
              var iTop = (window.screen.availHeight - 30 - iHeight) / 2; //获得窗口的垂直位置;
              var iLeft = (window.screen.availWidth - 10 - iWidth) / 2; //获得窗口的水平位置;
              window.open(url, "捆绑号码", 'height=' + iHeight + ',width=' + iWidth + ',top=' + iTop + ',left=' + iLeft + ',toolbar=no,menubar=no,scrollbars=no, resizable=no,location=no, status=no', false);
             
        },
        _checkAccount:function (str) {
        	var widget = this;
        	if (str == null || str ==""){
        		widget.popup("请输入需要捆绑的账户接入号！");
        		return false;
        	}
        	var strc = str.split(",");
        	if (strc.length>20) {
        		widget.popup("输入接入号数目超过规定值，校验未通过，请检查！");
        		return false;
        	}
        	for (var i=0;i<strc.length;i++){
        		if (strc[i].trim()==""){
        			widget.popup ("有空字符串，请检查！");
        			return false;
        		}
        		if (strc[i].length>40){
        			widget.popup("输入接入号中存在长度超过40的账号，校验未通过，请检查！");
        				return false;
        		}
        	}
        	widget.popup("校验通过！");
        	return true;
        },
        _cancelBindInfo:function (e) {
           var widget = this;
      	   element = $(widget.el);
      	   debugger;
      	   var button = $(e.target).closest("button");
      	   var bindAccountInfo  = button.data("bindAccountInfo");
      	   widget.callService("updateNumberMsg", bindAccountInfo,function (re) {
                 if(re.resultCode=='0'){
              	   widget.popup("取消账户绑定成功");
                     return;
                 }else{
              	   widget.popup(re.resultDesc);
                     return;
                 }
             }, { async: false });
      	 widget._qryInfo();
        },
        _getConds : function(){
            var widget = this;
            var param = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            return param;
        }
    });
    vita.widget.register("numberBundledAccountAccepted", numberBundledAccountAccepted, true);
})(window.vita);