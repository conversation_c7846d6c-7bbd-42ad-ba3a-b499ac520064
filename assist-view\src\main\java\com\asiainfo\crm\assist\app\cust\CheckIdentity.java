package com.asiainfo.crm.assist.app.cust;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICustSaveMultiSMO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 类简述: 完成身份证校验
 * 调用客户中心身份证校验接口，支持实时校验和普通校验
 * 普通校验：如果已经校验过身份证，则从历史中查询，未校验过则调用国政通接口查询
 * 实时校验：直接调用国政通接口校验，不判断本地库。
 *
 * <AUTHOR>
 * @CreateDate 2018/8/11 13:41
 */
@Component("vita.checkIdentity")
public class CheckIdentity extends AbstractComponent {

    private static final Logger LOGGER = LoggerFactory.getLogger(CheckIdentity.class);

    @Autowired
    private ICustSaveMultiSMO custSaveMultiSMO;

    @Override
    public Map achieveData(Object... params) {
        return null;
    }

    /**
     * <AUTHOR>
     * @功能简述 执行校验
     * @Date 2018/8/13 15:15
     * @Param [jsonStr]
     * @return java.util.Map
     *<p>
     * 修改记录:(日期,修改人,描述) (可选)
     *</p>
     */
    public Map checkIdentityNum(String jsonStr) throws Exception{
        String checkRes = doCheck(jsonStr);
        Map map = dealRes(checkRes);
        return map;
    }

    /**
     * <AUTHOR>
     * @功能简述 调用客户中心，执行校验
     * @Date 2018/8/13 15:10
     * @Param [jsonStr]
     * @return java.lang.String
     *<p>
     * 修改记录:(日期,修改人,描述) (可选)
     *</p>
     */
    private String doCheck(String jsonStr) throws Exception{
        String checkRes;
        Map<String, Object> paramMap = jsonConverter.toBean(jsonStr, Map.class);
        boolean realTime = (boolean) paramMap.get("realTime");
        if(realTime){
            checkRes = custSaveMultiSMO.saveIdentityInfoRealTime(jsonStr);
        }
        else{
            checkRes = custSaveMultiSMO.saveIdentityInfo(jsonStr);
        }
        return checkRes;
    }

    /**
     * <AUTHOR>
     * @功能简述 处理校验结果
     * @Date 2018/8/13 15:12
     * @Param [res]
     * @return java.util.Map
     *<p>
     * 修改记录:(日期,修改人,描述) (可选)
     *</p>
     */
    private Map dealRes(String res) throws Exception{
        String strLostKey = "NaN";
        String strIsPass = "isPass";
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(res);
        String isPass = MapUtils.getString(resultObjectMap, strIsPass, strLostKey);
        if(isPass.equals(strLostKey)){
            resultObjectMap.put("msg", "客户服务中心异常，未返回isPass");
            resultObjectMap.put(strIsPass, "N");
        }
        if(isPass.equals("Y")){
            String cardImg = (String)resultObjectMap.get("cardImg");
            cardImg = "data:image/png;base64," + cardImg;
            resultObjectMap.put("cardImg", cardImg);
            String msg = (String)resultObjectMap.get("msg");
            if(msg == null){
                resultObjectMap.put("msg", "一致，校验成功！");
            }
        }
        resultObjectMap.put("tips", "可通过实时核查检验最新信息");
        Map<String, Object> options = Maps.newHashMap();
        options.put("checkRes", resultObjectMap);
        return resultObjectMap;
    }

//    private Map mockCheck(){
//        Map<String, Object> resultObjectMap = new HashMap<>();
//        resultObjectMap.put("isPass", "N");
//        resultObjectMap.put("msg", "Mock异常情况");
//        return resultObjectMap;
//    }

}
