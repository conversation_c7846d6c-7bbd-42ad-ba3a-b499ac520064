<div data-widget="busiChargeRule2Area">
    <div class="box-maincont">
        <div class="page_main notopnav">
            <div class="col-lg-12">
                <div class="box-item">
                    <div class="container-fluid row">
                        <div class="form_title">
                            <div><i class="bot"></i>查询条件</div>
                        </div>
                        <div class="wmin_content row">
                            <form class=" form-bordered">
                                <div class="form-group col-md-6">
                                    <label class="col-md-4 control-label lablep">状态</label>
                                    <div class="col-md-4">
                                        <select id="statusCd" class="form-control">
                                            <option value="1000">有效</option>
                                            <option value="1100">失效</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group col-md-6">
                                    <label class="col-md-4 control-label lablep">地区</label>
                                    <div class="col-md-4">
                                        <div class="input-group">
                                            <input id="queryLanId" type="text" class="form-control" placeholder="" readonly="readonly">
                                            <div class="input-group-btn">
                                                <button id="queryRegionIdBtn" class="btn btn-green" type="button">选择</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group col-md-4 ">
                                    <label class="col-md-2 control-label lablep"></label>
                                    <div class="col-md-9 text-right">
                                        <button type="button" class="btn btn-white" id="resetBtn">清除</button>
                                        <button type="button" class="btn btn-primary" id="queryBtn">查询</button>
                                        <button type="button" class="btn btn-primary" id="addBtn">添加</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="container-fluid row">
                        <div class="form_title">
                            <div><i class="bot"></i>查询结果</div>
                        </div>
                        <div class="wmin_content" id="loadData">
                            <div class="col-lg-12">
                                <a class="btn btn-gray btn-outline" id="editBtn">
                                    <i class="glyphicon icon-progress text18"> </i> 编辑
                                </a>
<!--                                <a style="pointer-events: none" class="btn btn-gray btn-outline"  id="deleteBtn">-->
<!--                                    <i class="glyphicon icon-batch text18" > </i> 删除-->
<!--                                </a>-->
                            </div>
                            <div class="col-lg-12 mart10" name="tableDiv">
                                <table class="table table-hover">
                                    <thead>
                                    <tr>
                                        <th>选择</th>
                                        <th>业务算费ID</th>
                                        <th>发布地区</th>
                                        <th>开始时间</th>
                                        <th>结束时间</th>
                                        <th>状态</th>
                                        <th>版本时间</th>
<!--                                        <th>操作</th>-->
                                    </tr>
                                    </thead>
                                    <tbody>
                                    #if($options.reqList!="null" && $options.reqList.size()>0)
                                    #foreach($fc in $options.reqList)
                                    <tr>
                                        <td>
                                            <label class="wu-radio full absolute" data-scope="">
                                                <input type="radio" name="payment">
                                                <p class="vita-data">{"Id":"$!fc.busiChargeId"}</p>
                                            </label>
                                        </td>
                                        <td>$!fc.busiChargeId</td>
                                        <td value='$!fc.regionId'>$!fc.regionName</td>
                                        <td>$!fc.startDate</td>
                                        <td>$!fc.endDate</td>
                                        <td>$!fc.statusCd</td>
                                        <td>$!fc.version</td>
<!--                                        <td><button type="button" class="btn btn-primary" value= $!fc.id id="checkZkValue">查看</button></td>-->
                                    </tr>
                                    #end
                                    #end
                                    </tbody>
                                </table>
                                #if($options.totalNumber)
                                <p class="vita-data" id = "_totalNumber">{"totalNumber":$options.totalNumber}</p>
                                #end
                            </div>
                            <div id="pageInfo">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--弹窗 start-->
    <div class="popup" style="display:none;">
        <div class="popup-mask active"></div>
        <div class="agile-popup right active" style="width: 75%">
            <div class="popup-container">
                <!--内容-->
                <div class="calctitle">
                    <div class="titlefont">添加</div>
                    <div class="toolr">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                    </div>
                </div>

                <div class="wmin_content row" style="margin-top: 10px">
                    <form class=" form-bordered" name="_popupForm">
                        <div class="hideAndShow form-group col-md-6" >
                            <label class="col-md-4 control-label lablep">业务算费ID</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_busiChargeId" placeholder="选填" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label class="col-md-4 control-label lablep">地区</label>
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input id="_lanId" type="text"   class="form-control" placeholder="" readonly="readonly">
                                    <div class="input-group-btn">
                                        <button id="_regionIdBtn" class="btn btn-green" type="button">选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">开始时间</label>
                            <div class="col-md-8">
                                <input  name="_startDate"  type="text" class="form-control" value=""
                                       id="_startDate" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label class="col-md-4 control-label lablep">结束时间</label>
                            <div class="col-md-8">
                                <input name="_endDate"  type="text" class="form-control" value=""
                                       id="_endDate" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label class="col-md-4 control-label lablep">状态</label>
                            <div class="col-md-8">
                                <select id="_statusCd" class="form-control">
                                    <option value="1000">有效</option>
                                    <option value="1100">失效</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group col-md-6 " >
                            <label class="col-md-4 control-label lablep">版本时间</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_version" required>
                            </div>
                        </div>
                        <div class="form-group col-md-12">
                            <div class="col-md-12 text-center">
                                <button type="button" class="btn btn-primary" id="addSubmitBtn">添加</button>
                                <button type="button" class="btn btn-primary" id="editSubmitBtn">修改</button>
                                <button type="button" class="btn btn-primary" id="closeBtn">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!--end-->


</div>
</div>
</div>
<style>
    .modal{z-index: 9910;}
    .agile-popup{z-index: 9000;}
</style>
