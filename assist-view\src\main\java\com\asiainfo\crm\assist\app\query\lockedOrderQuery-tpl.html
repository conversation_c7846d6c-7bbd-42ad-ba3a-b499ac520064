<div data-widget="lockedOrderQuery" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                地 区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_channelId" type="checkbox" name="payment">
                                                </label> 渠道
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="channelId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="channelIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">起始日期</label>
                                            <div class="col-md-7">
                                                <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">结束日期</label>
                                            <div class="col-md-7">
                                                <input name="endDate" id="endDate" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custOrderNbr" type="checkbox" name="payment">
                                                </label> 流水号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custOrderNbr" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_accNum" type="checkbox" name="payment">
                                                </label> 号码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="accNum" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <div class="col-md-12 searchbutt_r" align="center">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-clear" type="button" class="btn btn-white">清除</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询结果</div>
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12">
                                        <a id="cancelOrder" class="btn btn-gray btn-outline"> <i class="glyphicon fa-details text18"> </i> 撤单
                                        </a>
                                        <a id="orderDetail" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 查看订单详情
                                        </a>
                                    </div>
                                    <div class="col-lg-12 mart10" id="orderListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>选择</th>
                                                <th>购物车流水</th>
                                                <th>受理渠道</th>
                                                <th>受理时间</th>
                                                <th>受理员工</th>
                                                <th>业务动作</th>
                                                <th>业务类型</th>
                                                <th>业务状态</th>
                                            </tr>
                                            </thead>
                                            <tbody id="orderList">
                                            #if($options != "null" && $options.customerOrders && $options.customerOrders != "null" &&
                                            $options.customerOrders.size() > 0)
                                            #foreach($customerOrder in $options.customerOrders)
                                            <tr>
                                                <td><label class="wu-radio full absolute" data-scope=""><input
                                                        type="radio" name="payment"></label></td>
                                                <td><a class="textcolorgreen" link="customerOrder">$!{customerOrder.custOrderNbr}</a>
                                                    <p class="vita-data">{"custOrderId" : $!customerOrder.custOrderId, "custOrderNbr": $!customerOrder.custOrderNbr}</p>
                                                </td>
                                                <td>$!{customerOrder.createOrgName}</td>
                                                #if($customerOrder.orderItems != "null" && $customerOrder.orderItems.size() > 0)
                                                <td>$!{customerOrder.orderItems[0].acceptDate}</td>
                                                #else
                                                <td></td>
                                                #end
                                                <td>$!{customerOrder.createStaffName}</td>
                                                <td>
                                                    #if($customerOrder.orderItems != "null" && $customerOrder.orderItems.size() > 0)
                                                        #foreach($orderItem in $customerOrder.orderItems)
                                                        <p>$!{orderItem.orderItemNbr}</p>
                                                        #end
                                                    #end
                                                </td>
                                                <td>
                                                    #if($customerOrder.orderItems != "null" && $customerOrder.orderItems.size() > 0)
                                                        #foreach($orderItem in $customerOrder.orderItems)
                                                        <p>$!{orderItem.serviceOfferName}【<a class="textcolorgreen" link="orderItem">$!{orderItem.applyObjSpecName}
                                                            #if($orderItem.accNum && $orderItem.accNum != "")
                                                            _ $!{orderItem.accNum}
                                                            #end
                                                        </a>】
                                                            <p class="vita-data">{"orderItem" : $orderItem, "custOrderId" : $!customerOrder.custOrderId}</p>
                                                        </p>
                                                        #end
                                                    #end
                                                </td>
                                                <td>
                                                    #if($customerOrder.orderItems != "null" && $customerOrder.orderItems.size() > 0)
                                                        #foreach($orderItem in $customerOrder.orderItems)
                                                        <p>$!{orderItem.statusCdName}</p>
                                                        #end
                                                    #end
                                                </td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.customerOrders && $options.customerOrders != "null" &&
                                            $options.customerOrders.size() == 0)
                                                <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
