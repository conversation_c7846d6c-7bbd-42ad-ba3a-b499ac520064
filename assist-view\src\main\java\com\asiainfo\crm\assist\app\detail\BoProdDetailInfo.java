package com.asiainfo.crm.assist.app.detail;

import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.service.intf.IOrderQueryMultiSMO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


/**
 * 业务动作详情.
 *
 * <AUTHOR>
 */
@Component("vita.boProdDetailInfo")
public class BoProdDetailInfo extends AbstractSoComponent {

    @Autowired
    private IOrderQueryMultiSMO orderQueryMultiSMO;

    @Override
    public Map achieveData(Object... param) throws Exception {
        String p = (String) param[0];
        Map options = new HashMap();
        Map orders = this.qryBoDetailInfo(p);
        Map inParam = jsonConverter.toBean(p, Map.class);
        options.putAll(inParam);
        options.putAll(orders);
        return options;
    }

    public Map qryBoDetailInfo(String inStr) throws Exception {
        Map options = new HashMap();
        String retStr = orderQueryMultiSMO.queryOrderItemDetailInfo(inStr);
        Map resultObject = (Map)resolveResult(retStr);
        options.put("detailInfo", resultObject);
        return options;
    }
}
