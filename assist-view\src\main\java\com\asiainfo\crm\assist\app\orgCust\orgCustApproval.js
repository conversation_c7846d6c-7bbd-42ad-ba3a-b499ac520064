(function (vita) {
    var orgCustApproval = vita.Backbone.BizView.extend({
        events: {
            "click #btn-qryBatchOrg": "_qryBatchOrg",   //查询
            "click #approvalDo": "_approvalTrue",             //审批
            "click #approvalFl": "_approvalFalse",             //审批
            "click #fileDetail": "_fileDetail",         //文件查看
            "click [link=batchId]": function (e) {
                this._qryBatchOrgDetail(e);
            }
        },
        _initialize: function () {

            var widget = this,
                element = $(widget.el),gsession=widget.gSession;
            if(!gsession.curChannelId||!gsession.curChannelName){
                widget._chooseChannel();
            }
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var nowDate = new Date();
            var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
            var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
            var startYear = nowDate.getFullYear() - 5, endYear = nowDate.getFullYear();
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear: endYear
                });
            }
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear: endYear
                });
            }
            //日期控件设置默认时间
            widget.global.defaultBeginTime = widget._getFormatDate(widget.global.sevenDayCount); //默认是七天
            widget.global.defaultEndTime = widget._currentDate();
            widget._setDefaultDateTime();
        },
        _getFormatDate: function (days) {
            var date = new Date();
            var yesterday_milliseconds = date.getTime() + days * 1000 * 60 * 60 * 24;
            var yesterday = new Date();
            yesterday.setTime(yesterday_milliseconds);
            var strYear = yesterday.getFullYear();
            var strDay = yesterday.getDate();
            var strMonth = yesterday.getMonth() + 1;
            if (strMonth < 10) {
                strMonth = "0" + strMonth;
            }
            if (strDay < 10) {
                strDay = "0" + strDay;
            }
            var datastr = strYear + "-" + strMonth + "-" + strDay;
            return datastr;
        },
        _currentDate: function () {
            var widget = this, el = $(widget.el);
            var nowDate = widget._getFormatDate(widget.global.currentDayCount);
            return nowDate;
        },
        //设置默认时间
        _setDefaultDateTime: function () {
            var widget = this, element = $(widget.el),
                beginDate = element.find("#beginDate"),
                endDate = element.find("#endDate");
            beginDate.val(widget.global.defaultBeginTime);
            endDate.val(widget.global.defaultEndTime);
        },
        global: {
            orgCustApproval: "../query/orgCustApproval",
            batchOrgFiles: "../query/batchOrgFilesDownLoad",
            updateApprovalOptions: "../query/updateApprovalOptions",
            chooseChannel: "../comm/chooseChannel",
            pageIndex: 1,
            pageSize: 10,
            preset: "date",
            defaultBeginTime: "",
            defaultEndTime: "",
            currentDayCount: 1,
            sevenDayCount: -6
        },
        _getConds: function () {
            var widget = this, gsession = widget.gSession;
            var beginDate = $("#beginDate").val();//开始时间
            var endDate = $("#endDate").val();//结束时间
            beginDate = beginDate.replace(/-/g, "");
            endDate = endDate.replace(/-/g, "");
            var param = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                },
                staffId: gsession.staffId,
                regionId: gsession.staffRegionId,
                orgId: gsession.curChannelId,//受理渠道
                beginDate: beginDate,//开始时间
                endDate: endDate,//结束时间
                importBatchId: $("#batchId").val(),//批次号
                importSatffCode: $("#staffCode").val(),//导入员工工号
                auditStatus:$("#auditStatus").val()//审批状态
            };
            return param;
        },
        _qryBatchOrg: function () {
            var widget = this, element = $(widget.el);
            var gsession = widget.gSession;
            var param = widget._getConds();
            widget.refreshPart("queryOrgByObj", JSON.stringify(param), "#batctOrgTbody", function (res) {
                var paging = this.require("paging"), r = $(res);
                var data = $(res).find("#batchOrgList").data("data");
                var totalNumber = r.find("#showPageInfo").data("totalNumber");
                var e = paging.new({
                    recordNumber: widget.global.pageSize,
                    total: totalNumber,
                    pageIndex: widget.global.pageIndex,
                    callback: function (_pageIndex, _pageSize) {
                        debugger;
                        var params = widget._getConds();
                        params.pageInfo.pageIndex = _pageIndex;
                        params.pageInfo.pageSize = _pageSize;
                        widget.refreshPart("queryOrgByObj", JSON.stringify(params), "#batctOrgListR");
                    }
                });
                r.find("#showPageInfo").append(e.getElement());
            }, {
                async: false
            });
        },
        _approvalTrue: function () {
            var widget = this;
            var param = dealApproval(widget, 0);
            if (param == null||param=="null") {
                return;
            }
            widget.callService("updateApprovalOptions",param, function (re) {
                widget.popup(re.resultMsg);
                return;
            }, {mask:true})
        },
        _approvalFalse: function () {
            var widget = this;
            debugger;
            var param = dealApproval(widget, 1);
            if (param == null||param=="null") {
                return;
            }
            widget.callService("updateApprovalOptions",param, function (re) {
                debugger;
                alert(re.resultMsg);
                return;
            }, {mask:true,async:false})
        },

        _qryBatchOrgDetail: function (e) {
            var widget = this;
            var aEl = $(e.target).closest("a");
            var batchId = aEl.data("batchId");
            var params = {
                importBatchId: batchId//批次号
            };
            var option = {
                url: widget.global.updateApprovalOptions,
                params: params,
                onClose: function (res) {
                    return false;
                }
            };
            widget.dialog(option);
            return false;

        },
        _fileDetail: function (e) {
            var widget = this;
            var aEl = $(e.target).closest("button");
            var batchId = aEl.data("batchId");
            var param = {
                importBatchId: batchId//批次号
            };
            var dialogId = "batchOrgFilesLog";
            var title = "下载文件";
            widget.dialog({
                id: dialogId,
                title: title,
                url: widget.global.batchOrgFiles,
                params: param
            });
            return false;
        },
        _chooseChannel: function () {
            // var widget = this,element = $(widget.el);
            // var compCode = "chooseChannel";
            // var dialogId = "chooseChannelDialog";
            // widget.dialog({
            //     id : dialogId,
            //     url : widget.global.chooseChannel,
            //     params : {
            //     },
            //     onClose : function(res) {
            //         debugger;
            //         var comp = $(res).closest("[data-widget=" + compCode + "]");
            //         var data = comp[compCode]("getValue");
            //         if (!data) {
            //             return false;
            //         }
            //         widget.gSession.curChannelId = data.orgId;
            //         widget.gSession.curChannelName = data.channelName;
            //     }
            // });
        }
    });

    function dealApproval(widget, status) {
        debugger;
        var element = $(widget.el);
        var gsession = widget.gSession;
        var orgs = element.find('table input[name="payment"]:checked');
        if (orgs.length == 0) {
            widget.popup("请选择数据!");
            return null;
        }
        var params = [];

        $.each(orgs, function (i, org) {
            var $tr = $(org.closest("tr"));
            var statusCd = $($tr.find("[link=batchId]")).data("statusCd");
            if (statusCd&&statusCd==0) {
                widget.popup("第"+ (i + 1) +"行已经审批！");
                params = null;
                return null;
            }
            var batchId = $($tr.find("[link=batchId]")).data("batchId");
            var param = {
                importBatchId : batchId,
                auditStatus : status
            };
            params.push(param);
        })
        return JSON.stringify(params);
    }
    vita.widget.register("orgCustApproval", orgCustApproval, true);
})(window.vita);