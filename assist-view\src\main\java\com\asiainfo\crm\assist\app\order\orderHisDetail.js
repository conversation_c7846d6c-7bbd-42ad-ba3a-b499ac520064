(function(vita) {
    var orderHisDetail = vita.Backbone.BizView.extend({
        events : {
            "click #orderSearch" : "_orderSearch",
            "click #traceHis" : "_traceHisQuery",
            "click #orderDetail" : "_orderDetail",
            "click #paramDetail" : "_paramDetail"
        },
        global: {
            pageSize: 10,
            orderDetail : "../so/orderDetail",
            paramDetail : "../so/paramDetail",
        },
        _initialize : function() {
            var widget = this,
                el = $(widget.el);
            var datas = el.data("data");
            var totalNumber = parseInt(el.find("#orderDiv").data("totalNumber")) || 0;
            var param = widget._getConds();

            if (parseInt(totalNumber) > 10) {
                var datas = el.data("data");
                if(datas && datas.offerOrderItems && datas.offerOrderItems.length){
                    var offerOrderItems = datas.offerOrderItems;
                    var paging = widget.require("paging");
                    var e = paging.new({
                        recordNumber : widget.global.pageSize,
                        total : totalNumber,
                        pageIndex : 1,
                        callback : function (pageIndex, recordNumber) {
                            widget.refreshPart("getChainTraceCustomerOrder", [JSON.stringify(offerOrderItems), (pageIndex-1)*recordNumber + 1, pageIndex*recordNumber,param], "#orderDiv");
                        }
                    });
                    el.find("#showPageInfo").append(e.getElement());
                }
            }
        },
        _orderSearch : function(e) {
            var widget = this,
                el = $(widget.el);
            //var totalNumber = parseInt(el.find("#orderDiv").data("totalNumber")) || 0;
            var param = widget._getConds();
            //点击查询每次获取所有数据传给后端过滤
            el.find('#showPageInfo').empty();
            var datas = el.data("data");
            var currentItems = datas.offerOrderItems;
            widget.refreshPart("getFilterChainTraceCustomerOrder", [JSON.stringify(currentItems),param], "#orderDiv",
                "orders", function(res) {
                    var datas = $(res).data("data");
                    var totalNumber =datas.offerOrderItems.length;
                    if (parseInt(totalNumber) > 10) {
                        if (datas && datas.offerOrderItems && datas.offerOrderItems.length) {
                            var offerOrderItems = datas.offerOrderItems;
                            var paging = widget.require("paging");
                            var e = paging.new({
                                recordNumber: widget.global.pageSize,
                                total: totalNumber,
                                pageIndex: 1,
                                callback: function (pageIndex, recordNumber) {
                                    widget.refreshPart("getChainTraceCustomerOrder", [JSON.stringify(offerOrderItems), (pageIndex - 1) * recordNumber + 1, pageIndex * recordNumber, param], "#orderDiv");
                                }
                            });
                            el.find("#showPageInfo").append(e.getElement());
                        }
                    }
                }, {
                    mask : true
                });


        },
        /**
         * 获取查询条件参数对象
         */
        _getConds : function() {
            var widget = this,
                element = $(widget.el);

            var chainCreate = element.find("#chainCreate option:selected").val();
            var serviceOfferId = element.find("#serviceOfferId option:selected").val();
            var applyObjSpecName = element.find("#applyObjSpecName").val();
            var param = {
                "chainCreate" : chainCreate,
                "serviceOfferId" : serviceOfferId,
                "applyObjSpecName" : applyObjSpecName
            };
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(chainCreate);
            if (!isNullOrEmpty) {
                param.chainCreate = chainCreate;
            }

            isNullOrEmpty = soUtil.isNullOrEmpty(serviceOfferId);
            if (!isNullOrEmpty) {
                param.serviceOfferId = serviceOfferId;
            }

            isNullOrEmpty = soUtil.isNullOrEmpty(applyObjSpecName);
            if (!isNullOrEmpty) {
                param.applyObjSpecName = applyObjSpecName;
            }
            return param;
        },
        _traceHisQuery: function (e) {
            var widget = this,
                el = $(widget.el);
            //获取订单流水号
            var datas = el.data("data");
            var csNbr = datas.csNbr;
            widget.refreshPart("queryTraceHisList", csNbr, "#traceHisListResult", function (res) {
                var paging = this.require("paging"), r = $(res);
                var totalNumber = r.find("#showPageInfo").data("totalNumber");
                if (totalNumber > 0) {
                    var e = paging.new({
                        recordNumber: widget.global.pageSize,
                        total: totalNumber,
                        pageIndex: widget.global.pageIndex,
                        callback: function (_pageIndex, _recordNumber) {
                            var params = widget._getConds();
                            params.pageInfo.pageIndex = _pageIndex;
                            params.pageInfo.pageSize = _recordNumber;
                            if (params) {
                                widget.refreshPart("queryAgentAuditList", JSON.stringify(params), "#agentAuditTable");
                            }
                            ;
                        }
                    });
                    r.find("#showPageInfo").append(e.getElement());
                }

            }, { async: false});
        },
        _orderDetail : function(e){
            var widget = this,
                el = $(widget.el);
            var soUtil = widget.require("soUtil");
            var button = $(e.target).closest("a");
            var id = button.attr("id") || "";
            var datas = el.data("data");
            var csNbr = datas.csNbr;
            var tar = el.find("#orderDetail");
            var currentItem =$(e.currentTarget).data("offerOrderItem") ;
            var iUId = datas.iUId;

            var param = {
                "csNbr" : csNbr,
                "currentItem" : JSON.stringify(currentItem)
            };
            var isNullOrEmpty = soUtil.isNullOrEmpty(iUId);
            if (!isNullOrEmpty) {
                param.iUId = iUId;
            }
            if (id && widget.global[id]) {
                var dialogId = id + 'Dialog';
                var option = {
                    url : widget.global[id],
                    params : param,
                    id : dialogId,
                    onOpen : function(res){
                        var tar = $(res).closest("[data-widget=orderDetail]");
                        tar.orderDetail('setDatas',currentItem);
                    },
                    onClose : function(res) {
                        //widget._traceHisQuery();
                    }
                };
                widget.dialog(option);
            }
        },
        _paramDetail : function(e){
            var widget = this,
                el = $(widget.el);
            var button = $(e.target).closest("a");
            var id = button.attr("id") || "";
            var iUId = button.attr("value");
            var datas = el.data("data");
            var csNbr = datas.csNbr;
            var param = {
                "csNbr" : csNbr,
                "iUId" : iUId
            }
            if (id && widget.global[id]) {
                var dialogId = id + 'Dialog';
                var option = {
                    url : widget.global[id],
                    params : param,
                    id : dialogId,
                    onClose : function(res) {
                        widget._traceHisQuery();
                    }
                };
                widget.dialog(option);
            }
        }
    });
    vita.widget.register("orderHisDetail", orderHisDetail, true);
})(window.vita);

