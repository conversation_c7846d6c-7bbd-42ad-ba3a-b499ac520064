package com.asiainfo.crm.assist.app.query;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ai.vita.AbstractComponent;
import com.al.common.utils.StringUtil;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;

/**
 * Created on 2017/7/7.
 */
@Component("vita.transitOrderQuery")
public class TransitOrderQuery extends AbstractComponent {
	private static final  Logger logger = LoggerFactory.getLogger(TransitOrderQuery.class);
	
	@Autowired
	IOrderQuerySMO orderQuerySMO;
	
	public Map<?,?> achieveData(Object... params) throws Exception {
		return null;
	}
	
	public Map queryTransitList(String json) throws IOException{
		String resultJson = orderQuerySMO.qryCustomerOrderItemListByCustId(json);
		
		//System.out.println("请求参数：{}"+json);
		//System.out.println("返回参数：{}"+resultJson);
		Map<String, Object> retMap = new HashMap<String, Object>();
		long totalNumber = 0L;
        List orderList = new ArrayList();
        Map objMap = jsonConverter.toBean(resultJson, Map.class);
        String resultCode = MapUtils.getString(objMap, "resultCode");
        if (!StringUtil.isEmpty(resultCode)) {
            Map resultObject = (Map) objMap.get("resultObject");
            orderList = (List) resultObject.get("customerOrders");
            Map pageInfo = (Map) resultObject.get("pageInfo");
            String total = MapUtils.getString(pageInfo, "pageCount");
            if (!StringUtil.isEmpty(total)) {
                totalNumber = Long.valueOf(total);
            }
        }
        retMap.put("totalNumber", totalNumber);
        retMap.put("orderList", orderList);
        
		return retMap;
	}

}
