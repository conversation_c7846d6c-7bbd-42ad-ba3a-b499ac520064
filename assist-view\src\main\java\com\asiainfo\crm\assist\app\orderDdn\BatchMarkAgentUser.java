package com.asiainfo.crm.assist.app.orderDdn;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IStaffDataPrvQuerySMO;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by liyong on 2019/3/11
 */
@Component("vita.batchMarkAgentUser")
public class BatchMarkAgentUser extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(BatchMarkAgentUser.class);
    @Autowired
    private IStaffDataPrvQuerySMO staffDataPrvQuerySMO;
    @Override
    public Map achieveData(Object... params) throws Exception {
        Map retMap = new HashMap();
        return retMap;
    }

    public Map queryOrderDDNCfgList(String jsonStr) throws Exception {

      /*  String resultJson = custMultiSMO.qryTaxPayerForMulti(jsonStr);
        //处理返回的异常信息
        Map cartList = (Map) resolveResult(resultJson);

        System.out.println("请求参数：{}"+jsonStr);
        System.out.println("返回参数：{}"+resultJson);*/

//        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(resultJson);
        Map<String, Object> options = Maps.newHashMap();
        List taxPayerList =null;
    /*    List taxPayerList = (List) MapUtils.getObject(resultObjectMap, "taxPayers");
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");*/
        int total = 1;
        int pageIndex = 1;
        int pageCount = 1;
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", total);
        options.put("orderDDNSmsCfgList", taxPayerList);
        return options;

    }
}
