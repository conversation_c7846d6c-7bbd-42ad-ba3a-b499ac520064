package com.asiainfo.crm.assist.app.query;

import com.asiainfo.angel.json.JsonConverter;
import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.bcomm.exception.BError;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IResourceSMO;
import com.asiainfo.crm.service.intf.ISoConfigSMO;
import com.asiainfo.crm.service.intf.ISpecSMO;
import com.asiainfo.crm.util.ListUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 卡信息查询
 * liyc7
 * Created on 2019/3/6
 */
@Component("vita.prepareCardInfoQry")
public class PrepareCardInfoQry extends AbstractComponent {

    @Autowired
    private ISpecSMO iSpecSMO;

    @Autowired
    ISoConfigSMO iSoConfigSMO;

    @Autowired
    private IResourceSMO iResourceSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map<String,Object> options = new HashMap<>();
        List<Map<String,Object>> cardStatusList = getCardStatusResult(AssistMDA.CARD_STATUS_ATTR_ID);
        options.put("cardStatusList",cardStatusList);
        return options;
    }

    /**
     * 获取实例状态信息
     * @return
     */
    private List<Map<String, Object>> getCardStatusResult(Long attrId) throws Exception{
        Map<String,Object> paramMap = new HashMap<>();
        List<Integer> statusCds = new ArrayList<>();
        statusCds.add(1000);
        paramMap.put("attrId",attrId);
        paramMap.put("statusCds",statusCds);
        String cardStatusResult = iSpecSMO.qryAttrValuesByAttrId(jsonConverter.toJson(paramMap));
        Map<String,Object> resultObjectMap = (Map<String, Object>) resolveResult(cardStatusResult);
        return (List<Map<String, Object>>) resultObjectMap.get("attrValues");
    }

    /**
     * 查询卡信息列表
     */
    public Map qryPrepareCardInfoList(String json) throws Exception {

        String result = iResourceSMO.getTerminalDeviceAsLocal(json);

        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(result);
        Map<String, Object> options = Maps.newHashMap();
        //对结果进行二次处理
        List<Map<String,Object>> cardInfos = dealInfoResult(resultObjectMap);
        options.put("cardInfos",cardInfos);
//        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
//        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
//        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
//        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
//        options.put("pageCount",pageCount);
//        options.put("pageIndex",pageIndex);
//        options.put("totalNumber", total);

        return options;
    }

    /**
     * 针对卡信息查询结果进行二次处理方便前端展示
     * @param resultObjectMap
     * @return
     * @throws Exception
     */
    public List<Map<String,Object>> dealInfoResult(Map<String,Object> resultObjectMap) throws Exception{
        if (MapUtils.getString(resultObjectMap, "handleResultMsg") != null) {
            Map<String, Object> infoResult = jsonConverter.toMap(MapUtils.getString(resultObjectMap, "handleResultMsg"), String.class, Object.class);
            //获取卡状态
            List<Map<String, Object>> cardStatusList = getCardStatusResult(AssistMDA.CARD_STATUS_ATTR_ID);
            //获取库存状态
            List<Map<String, Object>> storeStatusList = getCardStatusResult(AssistMDA.STORE_STATUS_ATTR_ID);
            List<Map<String, Object>> infos = (List<Map<String, Object>>) infoResult.get("infos");
            for (Map<String, Object> info : ListUtil.nvlList(infos)) {
                //获取地区名称
                String regionId = MapUtils.getString(info, "regionId");
                Map<String, Object> regionParam = new HashMap<>();
                regionParam.put("regionId", regionId);
                Map<String, Object> regionResult = iSoConfigSMO.qryCommonRegionCnById(jsonConverter.toJson(regionParam));
                info.put("regionName", regionResult.get("regionName"));

                for (Map<String, Object> storeStatus : ListUtil.nvlList(storeStatusList)) {
                    if (MapUtils.getString(storeStatus, "attrValue").equals(MapUtils.getString(info, "cardStatusCd"))) {
                        info.put("cardStatusName", MapUtils.getString(storeStatus, "attrValueName"));
                    }
                }
                List<Map<String, Object>> itmes = (List<Map<String, Object>>) info.get("itmes");
                for (Map<String, Object> itme : ListUtil.nvlList(itmes)) {
                    String attrKey = MapUtils.getString(itme, "attrName");
                    info.put(MapUtils.getString(AssistMDA.CARD_NAME_VALUE_REL, attrKey), MapUtils.getString(itme, "attrValue"));
                }

                Map<String, Object> accProdInst = MapUtils.getMap(info, "accProdInsts");
                if (accProdInst != null) {
                    Map<String, Object> resultObject = MapUtils.getMap(accProdInst, "resultObject");
                    List<Map<String, Object>> accProdInsts = (List<Map<String, Object>>) resultObject.get("accProdInsts");
                    for (Map<String, Object> accPord : ListUtil.nvlList(accProdInsts)) {
                        info.put("accNum", MapUtils.getString(accPord, "accNum"));
                        for (Map<String, Object> cardStatus : ListUtil.nvlList(cardStatusList)) {
                            if (MapUtils.getString(cardStatus, "attrValue").equals(MapUtils.getString(accPord, "statusCd"))) {
                                info.put("statusName", MapUtils.getString(cardStatus, "attrValueName"));
                            }
                        }
                        break;
                    }
                }

            }
            return infos;
        }
        return null;
    }

}
