<div data-widget="transAcceptCertNumFive" style="overflow: auto; height: 100%;">

	<div class="box-maincont">
		<div class="homenofood">
			<div class="page-nav">
				<div class="row">
					<div class="pagenav_box">
						<div class="page_title">跨省一证五号受理</div>
					</div>
				</div>
			</div>
			<div class="page_main">
				<!--填单start-->
				<div class="col-lg-12">
					<div class="box-item">
						<div class="container-fluid row">
							<div class="form_title">
								<div>
									<i class="bot"></i>查询条件
								</div>
							</div>
							<div class="wmin_content row ">
								<form class=" form-bordered">

									<div class="form-group col-md-4">
										<label class="col-md-5 control-label lablep"> 证件类型</label>
										<div class="col-md-7">
											<div class="input-group">
												<div class="input-group-btn">
													<select id="custType"
														class="btn btn-default dropdown-toggle"
														data-toggle="dropdown" aria-haspopup="true"
														aria-expanded="false">
														
														<option value="1" selected="selected" certTypeName="身份证">身份证</option>
					
													</select>
												</div>
											</div>
										</div>
									</div>
									<div class="form-group col-md-5">
										<label class="col-md-5 control-label lablep"> <label
											class="wu-radio full absolute" data-scope=""> </label> 证件号码
										</label>
										<div class="col-md-8">
										 	<div class="input-group">
												<input id="custIdentity" type="text" class="form-control" aria-label="Text input with dropdown button">
												<p class="vita-bind" model="certNum"></p>
												<div  class="input-group-btn">
													<button id="btn-readCert" class="btn btn-green" type="button">读取</button>
												</div>
											</div>
										</div>
										
									</div>
									<div class="form-group col-md-4">
										<label class="col-md-5 control-label lablep"> <label
											class="wu-radio full absolute" data-scope=""> </label> 证件姓名
										</label>
										<div class="col-md-7">
											<input id="custName" type="text" class="form-control"
												placeholder="">
										</div>
									</div>
									<div class="form-group col-md-5">
										<label class="col-md-5 control-label lablep"> <label
											class="wu-radio full absolute" data-scope=""> </label> 证件地址
										</label>
										<div class="col-md-8">
											<input id="custAddress" type="text" class="form-control"
												placeholder="">
										</div>
									</div>
									
									<div class="form-group col-md-3">
										
										<div class="col-md-8">
											<button id="btn-qryCertNumFive" type="button"
												class="btn btn-primary">查询</button>
												<button id="btn-configChoose" type="button"
												class="btn btn-primary">确认选择</button>
										</div>
									</div>
									
								</form>
							</div>
						</div>



						<div class="container-fluid row">
							<div class="form_title">
								<div>
									<i class="bot"></i>查询结果
								</div>
							</div>
	
							<div class="container-fluid row" id="certNumTbody"
								>
								<div class="wmin_content" id="certNumFiveListR">
									<div class="col-lg-12 mart10" id="transCertNumFiveList">
										<p class="vita-data">{"data": $options}</p>
										<table class="table table-hover">
											<thead>
												<tr>
													<th></th>
													<th>省份</th>
													<th>地区</th>
													<th>号码</th>
													<th>关系类型</th>
													<th>状态</th>
													<th>处理状态</th>
												</tr>
											</thead>

											<tbody>
												#if($options != "null" && $options.transCertNumFiveList && $options.transCertNumFiveList != "null" && $options.transCertNumFiveList.size() > 0) 
												#foreach($transCertNumFive in $options.transCertNumFiveList)
												<tr>
													<td><input type="checkbox" name="checkCertFive">
														<a class="textcolorgreen" link="certNumFive"></a>
														<p class="vita-data">{"certNumFive" : $!{transCertNumFive}
															}</p></td>
													<td>$!{transCertNumFive.provinceName}</td>
													<td>$!{transCertNumFive.regionName}</td>
													<td>$!{transCertNumFive.phoneNum}</td>
													<td>$!{transCertNumFive.systemFlagName}</td>
													<td>#if($transCertNumFive.statusCd =="11") 实占 #else
														预占 #end</td>
													<td>$!{transCertNumFive.dealStatusName}</td>
													
												</tr>
												#end
												#elseif( $options.transCertNumFiveList == "null")
												<tr>
													<td align='center' colspan='7'>未查询到数据！
													<td>
												</tr>
												#end
											</tbody>
										</table>
									</div>
								</div>
									<!--翻页start-->
									<div id="showPageInfo" class="page-box"></div>
									#if($options.totalNumber && $options.totalNumber != "null")
									<p class="vita-data">{"totalNumber":$options.totalNumber}</p>
									#end
									<!--翻页end -->
							</div>
						</div>
					</div>
				</div>
			</div>
			<!--填单end-->
		</div>
	</div>
</div>

