<div data-widget="batchFluxPackage" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>批量订购流量包</div>
                                    <div class="form-group col-md-12 ">
                                        <div class="col-md-9 text-left">
                                            <br>
                                            <button type="button" class="btn btn-primary" id="batchImportPackage">批量导入</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="popup1" style="display:none;" >
        <div class="popup-mask active"></div>
        <div class="agile-popup right active" style="width: 75%">
            <div class="popup-container">
                <!--内容-->
                <div class="calctitle">
                    <div class="titlefont">导入Excel模板</div>
                    <div class="toolr">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                    </div>
                </div>

                <div class="wmin_content row" style="margin-top: 10px">
                    <form method="post" id="importForm" class="form-bordered" enctype="multipart/form-data">
                        <label class="col-md-4 control-label lablep">销售品规格：</label>
                        <div class="col-md-4">
                        <input type="text" id="offerId" name="offerId" value="">
                        </div>
                        <label class="col-md-4 control-label lablep">选择流量包：</label>
                        <div class="col-md-4">
                        <input type="text" id="offerName" name="offerName" disabled value="">
                        </div>
                <div class="form-group col-md-12 "></div>
                <label class="col-md-4 control-label lablep">EXCEL文件：</label>
                <div class="col-md-4">
                    <input id="upFile" name="upFile" value="" type="file">
                </div>

                <button type="button" class="btn btn-primary" id="chooseFluxPackage">选择订购流量包</button>
                <button type="button" class="btn btn-primary" id="submitImportBtn">导入</button>
                <button type="button" class="btn btn-primary" id="tempDownloadBtn">模板下载</button>
                    </form>
                <div class="form-group col-md-12">

                <label class="col-md-4 control-label lablep">注意事项：</label>
                <div class="col-md-9">
                    (1)仅支持上传xls、xlsx，单元格格式请设置为文本。
                    <br>
                    (2)严格按照模板导入信息。
                </div>
                    <div class="popup" style="display:none;float:left; width:100%;" >
                        <div >
                            <div class="calctitle">
                                <div class="titlefont">销售品列表</div>
                                <div class="toolr">
                                    <button type="button" class="btn btn-primary btn-sm okbutt" id="choosePackage">确认</button>
                                </div>
                            </div>
                            <div class="calcw_rightcont">
                                <div class="col-lg-12 mart10" id="itemTable">
                                    <table class="table table-hover" id="items">
                                        <thead>
                                        <tr>
                                            <th>选择</th>
                                            <th>销售品标识</th>
                                            <th>销售品名称</th>
                                        </tr>
                                        </tr>
                                        </thead>
                                        <tbody >
                                        #if($options != "null" && $options.items && $options.items != "null" && $options.items.size() > 0)

                                        #foreach($item in $options.items)
                                        <tr >
                                            <td><label class="wu-checkbox full absolute" data-scope="">
                                                <input type="radio" name="selectRadio"  />
                                                <p class="vita-data">{"data":$item,"totalNumber":$options.totalNumber}</p>
                                            </label></td>
                                            <td>$!item.offerId</td>
                                            <td>$!item.offerName</td>
                                        </tr>
                                        #end
                                        #end
                                        </tbody>
                                    </table>
                                    <!--翻页start -->
                                    <div class="page-box" id="showPageInfo">
                                    </div>
                                    <p class="vita-data">{"totalNumber":"$!options.totalNumber"}</p>
                                    <!--翻页end -->
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
            </div>
        </div>

    </div>

</div>


</div>
<style>
    .modal{z-index: 9910;}
    .agile-popup{z-index: 9000;}
</style>