package com.asiainfo.crm.assist.app.algorithmmanage;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ISecuritySmo;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Date 2022/1/20 16:04
 */
@Component("vita.algorithmUpdate")
public class AlgorithmUpdate extends AbstractComponent {

    @Autowired
    private ISecuritySmo securitySmo;

    @Override
    public Map achieveData(Object... params) throws Exception {
        String json = params[0].toString();
        Map options = jsonConverter.toBean(json, Map.class);
        String algoId = MapUtils.getString(options, "algoId");
        Map<String, Object> argMap = new HashMap<String, Object>();
        argMap.put("algoId", algoId);
        Map<String, Object> invokeResult = (Map<String, Object>) invokeMethod("algorithmList", "queryAlgorithms",
                new Object[]{jsonConverter.toJson(argMap)});
        List<Map> algorithms = (List<Map>) MapUtils.getObject(invokeResult, "algorithms");
        Map<String, Object> data = new HashMap<String, Object>();
        Map<String, Object> algorithmInfo = null;
        if (algorithms != null) {
            algorithmInfo = algorithms.get(0);
        }
        data.put("algorithmInfo", algorithmInfo);
        data.put("dataSecurityStatusCds", AssistMDA.DATA_SECURITY_STATUS_CDS);
        return data;
    }

    public String updateAlgorithm(String params) {
        String result = securitySmo.updateAlgorithm(params);
        return result;
    }
}
