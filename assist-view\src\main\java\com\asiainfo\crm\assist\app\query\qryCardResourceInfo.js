(function (vita) {
    var qryCardResourceInfo = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_cardResourceInfoQuery",
            "click #btn-clear": "_clearCond",
            "click input[type=checkbox]": "_nbrClick"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            //nbrLabelsStr初始化
            var nbrLabels = widget.global.nbrLabels;
            var nbrLabelsStr = "#" + nbrLabels[0];
            for (var k = 1; k < nbrLabels.length; k++) {
                nbrLabelsStr = nbrLabelsStr + ",#" + nbrLabels[k];
            }
            widget.global.nbrLabelsStr = nbrLabelsStr;
        },
        global: {
            pageIndex: 1,
            pageSize: 5,
            nbrLabels: ["c_accNum", "c_umiNum", "c_imsiNum"]
        },
        _clearCond: function () {
            var widget = this, element = $(widget.el);
            var checked = element.find(".form-group").find("input[type=checkbox]:checked");
            $.each(checked, function (i, chkInput) {
                chkInput.click();
                var input = $(chkInput).closest(".form-group").find("input.form-control");
                input.val("");
                input.attr("value", null);
            });
            var recordLog = element.find("#recordLog input[type=text]");
            recordLog.each(function () {
                $(this).val("");
                $(this).attr("value", null);
            });
        },
        _nbrClick: function (e) {
            var widget = this, element = $(widget.el),
                nbrLabels = widget.global.nbrLabels,
                nbrLabelsStr = widget.global.nbrLabelsStr;
            var currentTarget = $(e.currentTarget);
            //界面样式渲染后才会調click事件，所以当状态是checked的时候才去清空其他选项
            var nbrChk = element.find(nbrLabelsStr).filter(":checked");
            if (nbrChk.length != 0) {
                var $checked = $(".form-group").find("input[type=checkbox]:checked").not(nbrLabelsStr);
                $.each($checked, function (i, chkInput) {
                    $(chkInput).attr('checked', false);
                });
            }
            var currentTargetId = currentTarget.attr("id");
            if ($.inArray(currentTargetId, nbrLabels) > -1) {
                var nbrFlag = false;
                $.each(nbrLabels, function (i, n) {
                    if (n == currentTargetId) {
                        return true;
                    }
                    var checkObj = element.find("#" + n);
                    if (checkObj.is(":checked")) {
                        checkObj.attr("checked", false);
                    }
                });
            }
        },
        _cardResourceInfoQuery: function (e) {
            var widget = this;
            var btn = $(e.currentTarget);
            var logSwitch = btn.data("logSwitch");
            var params = widget._getConds(logSwitch);
            if (params) {
                widget._qryCardResourceInfo(widget, params);
                if (logSwitch && logSwitch == "Y") {
                    widget._saveQryCardResourceLog(widget, params);
                }
            }
        },
        _qryCardResourceInfo: function (widget, params) {
            widget.refreshPart("qryCardResourceInfo", JSON.stringify(params), "#cardResourceInfo",
                function () {
                }, {mask: true, async: false});
        },
        _saveQryCardResourceLog: function (widget, params) {
            if (widget._isNullStr(params.accNum)) {
                if (!widget._isNullStr(params.umiNum)) {
                    params.accNum = params.umiNum;
                } else if (!widget._isNullStr(params.imsiNum)) {
                    params.accNum = params.imsiNum;
                }
            }
            widget.callService("saveQryCardResourceLog", JSON.stringify(params),
                function () {
                }, {async: true});
        },
        _isNullStr: function (str) {
            if (str == null || str == "") {
                return true;
            }
            return false;
        },
        _isPattStr: function (str) {
            var reg = new RegExp(/^[A-Za-z0-9]+$/);
            if (!reg.test(str)) {
                return true;
            } else {
                return false;
            }
        },
        _getConds: function (logSwitch) {
            var widget = this, element = $(widget.el);
            var params = {
                "regionId": widget.gSession.staffRegionId,
                "createStaff": widget.gSession.staffId
            };
            var paramCnt = 0;
            var accNumChecked = element.find("#c_accNum").is(":checked");
            if (accNumChecked) {
                paramCnt++;
                var accNum = $.trim(element.find("#accNum").val());
                if (widget._isNullStr(accNum)) {
                    widget.popup("请输入用户号码！");
                    return false;
                } else if (widget._isPattStr(accNum)) {
                    widget.popup("请正确输入用户号码，允许英文或数字！");
                    return false;
                } else {
                    params.accNum = accNum;
                }
            }
            var umiNumChecked = element.find("#c_umiNum").is(":checked");
            if (umiNumChecked) {
                paramCnt++;
                var umiNum = $.trim(element.find("#umiNum").val());
                if (widget._isNullStr(umiNum)) {
                    widget.popup("请输入UIM号码！");
                    return false;
                } else if (widget._isPattStr(umiNum)) {
                    widget.popup("请正确输入UIM号码，允许英文或数字！");
                    return false;
                } else {
                    params.umiNum = umiNum;
                }
            }
            var imsiNumChecked = element.find("#c_imsiNum").is(":checked");
            if (imsiNumChecked) {
                paramCnt++;
                var imsiNum = $.trim(element.find("#imsiNum").val());
                if (widget._isNullStr(imsiNum)) {
                    widget.popup("请输入IMSI号码！");
                    return false;
                } else if (widget._isPattStr(imsiNum)) {
                    widget.popup("请正确输入IMSI号码，允许英文或数字！");
                    return false;
                } else {
                    params.imsiNum = imsiNum;
                }
            }
            if (paramCnt < 1) {
                widget.popup("请至少选择一个查询条件！");
                return false;
            }
            if (logSwitch == "Y") {
                var checker = $.trim(element.find("#checker").val());
                if (widget._isNullStr(checker)) {
                    widget.popup("请输入批准人！");
                    return false;
                } else {
                    params.approveStaff = checker;
                }
                var orderNum1 = $.trim(element.find("#orderNum1").val());
                if (widget._isNullStr(orderNum1)) {
                    widget.popup("请输入派单号1！");
                    return false;
                } else {
                    params.sendNumber = orderNum1;
                }
                var orderNum2 = $.trim(element.find("#orderNum2").val());
                if (!widget._isNullStr(orderNum2)) {
                    params.sendNumber2 = orderNum2;
                }
            }
            return params;
        }
    });
    vita.widget.register("qryCardResourceInfo", qryCardResourceInfo, true);
})(window.vita);