<div data-widget="materialSend" style="height:100%">
    <div id="myTabContent" class="tab-content">
            <div class="sypage-min">
                <div class="form_title">
                    <div>
                        <i class="bot"></i><strong class="text16">查询条件</strong>
                    </div>
                </div>
                <div class="sypagemin_content ">
                    <form>
                        <div class="form-group col-md-4 ">
                            <label class="col-md-5 control-label lablep">
                                地 区
                            </label>
                            <div class="col-md-7">
                                <div class="input-group">
                                    <input id="regionId" type="text" class="form-control" placeholder="">
                                    <div class="input-group-btn">
                                        <button class="btn btn-green" type="button">选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-md-4 ">
                            <label class="col-md-5 control-label lablep">
                                <label class="wu-radio full absolute" data-scope="">
                                    <input id="c_channelId" type="checkbox" name="channel">
                                </label> 渠道
                            </label>
                            <div class="col-md-7">
                                <div class="input-group">
                                    <input id="channelId" type="text" class="form-control" placeholder="">
                                    <div class="input-group-btn">
                                        <button class="btn btn-green" type="button">选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-md-4">
                            <label class="col-md-5 control-label lablep">
                                <label class="wu-radio full absolute" data-scope="">
                                    <input id="c_accNum" type="checkbox" name="accNum">
                                </label> 接入号
                            </label>
                            <div class="col-md-7">
                                <input id="accNum" type="text" class="form-control" placeholder="">
                            </div>
                        </div>
                        <div class="form-group col-md-4">

                            <label class="col-md-5 control-label lablep">
                                <label class="wu-radio full absolute" data-scope="">
                                    <input id="c_mktResNbr" type="checkbox" name="mktResNbr">
                                </label> 串号
                            </label>
                            <div class="col-md-7">
                                <input id="mktResNbr" type="text" class="form-control" placeholder="">
                            </div>
                        </div>
                        <div class="form-group col-md-4">
                            <label class="col-md-5 control-label lablep">起始日期</label>
                            <div class="col-md-7">
                                <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="">
                            </div>
                        </div>
                        <div class="form-group col-md-4">
                            <label class="col-md-5 control-label lablep">结束日期</label>
                            <div class="col-md-7">
                                <input name="endDate" id="endDate" type="text" class="form-control" placeholder="">
                            </div>
                        </div>
                        <div class="form-group col-md-4">
                            <div class="col-md-12 searchbutt_r">
                                <button id="btn-clear" type="button" class="btn btn-default">清除</button>
                                <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="form_title">
                    <div><i class="bot"></i><strong class="text16">物品列表</strong></div>
                </div>
                <div class="sypagemin_content " id="materialResult">
                    <div class="col-lg-12">
                        <a id="materialSend" class="btn btn-gray btn-outline"> <i class="glyphicon fa-details text18"> </i> 物品赠给
                        </a>
                    </div>
                    <div class="col-lg-12 mart10" id="materialList">
                        <table class="table table-hover">
                            <thead>
                            <tr>
                                <th>选择</th>
                                <th>客户名称</th>
                                <th>串    号</th>
                                <th>终端名称</th>
                                <th>捆绑号码</th>
                                <th>受理时间</th>
                            </tr>
                            </thead>
                            <tbody>
                            #if($options != "null" && $options.customerOrders && $options.customerOrders != "null" &&
                            $options.customerOrders.size() > 0)
                            #foreach($customerOrder in $options.customerOrders)
                            <tr>
                                <td><label class="wu-radio full absolute" data-scope=""><input
                                        type="radio" name="payment"></label></td>
                                <td>$customerOrder.custOrderNbr</td>
                                <td>$customerOrder.createOrgId</td>
                                <td>$customerOrder.orderItems[0].acceptDate</td>
                                <td>$customerOrder.orderHandlerId</td>
                                <td>$customerOrder.serviceOfferId</td>
                                <td>
                                    #foreach($orderItem in $customerOrder.orderItems)
                                    $orderItem.serviceOfferName【<a href="$orderItem.orderItemId">$orderItem.applyObjSpecName</a>】
                                    <p></p>
                                    #end
                                </td>
                                <td>$customerOrder.statusCdName</td>
                            </tr>
                            #end
                            #end
                            </tbody>
                        </table>
                    </div>
                    <!--翻页start -->
                    <div id="showPageInfo">
                    </div>
                    #if($options.totalNumber && $options.totalNumber != "null")
                    <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                    #end
                </div>
            </div>
        </div>
    </div>
</div>
