package com.asiainfo.crm.assist.app.query;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.appframe.ext.flyingserver.org.apache.commons.collections.MapUtils;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IBillServiceSMO;

import net.sf.json.JSONArray;
@Component("vita.billMaintain")
public class BillMaintain extends AbstractComponent{

	@Autowired
	private IBillServiceSMO billServiceSMO;
	
	@Override
	public Map achieveData(Object... params) throws Exception {
		
		return null;
	}

	
	@SuppressWarnings({"rawtypes", "unchecked" })
	public Map<?, ?> billQuery(String params) throws Exception {
		Map paramMap = jsonConverter.toBean(params, Map.class);
		String condChoose = MapUtils.getString(paramMap, "condChoose");
		String condDesc = MapUtils.getString(paramMap, "condDesc");
		String sendJosn ="{\""+condChoose+"\" : "+condDesc+"}";
		String responseSOAP = billServiceSMO.qryOrdBillList(sendJosn);
        List resultObjectList = (List) resolveResult(responseSOAP);
		Map options = new HashMap();
		options.put("billData", resultObjectList);
		return options;
	}
	
	
	@SuppressWarnings({"rawtypes", "unchecked" })
	public Map<?, ?> showordBillObjVos(String params) throws Exception {
		Map paramMap = jsonConverter.toBean(params, Map.class);
		Map options = new HashMap();
		options.put("billData", paramMap);
		return options;
	}
	
	
	@SuppressWarnings({"rawtypes" })
	public String billUpdate(String params) throws Exception {
		Map paramMap = jsonConverter.toBean(params, Map.class);
		List data = (List) paramMap.get("billData");
		JSONArray json = JSONArray.fromObject(data); 
		String responseSOAP = billServiceSMO.saveArchGrpDataMaintain(json.toString());
		return responseSOAP;
	}
	
}
