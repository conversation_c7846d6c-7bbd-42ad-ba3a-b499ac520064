<div data-widget="valueAddedTaxLogDetail" style="height: 100%">
    #macro(nullNotShow $val)
        #if($val && $val != "null")
            $!val
        #end
    #end
    <div class="calcw_rightbox noneleft">
        <div class="calctitle">
            <div class="titlefont">专票日志详情</div>
            <div class="toolr">
                <button id="closeBtn" type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
            </div>
        </div>
        <div class="calcw_rightcont" >
            #if($options.detailInfo && $options.detailInfo != "null")
            #set($detailInfo = $options.detailInfo)
            <div class="container-fluid">
                <h5 class="meal_htitle nobrder"><i class="bot"></i>日志信息</h5>
                <div class="row">
                    <div class="col-md-12 ">
                        <table class="table table-bordered conttd-w">
                            <tbody>
                            <tr>
                                <td class="labellcont">购物车ID</td>
                                <td>#nullNotShow($detailInfo.custOrderId)&nbsp;</td>
                                <td class="labellcont">购物车流水</td>
                                <td>#nullNotShow($detailInfo.custOrderNbr)&nbsp;</td>
                            </tr>
                            <tr>
                                <td class="labellcont">入参(发起报文)</td>
                                <td colspan="3">
                                    <textarea class="form-control" rows="3">$detailInfo.reqXml</textarea>
                                </td>
                            </tr>
                            <tr>
                                <td class="labellcont">出参(返回报文)</td>
                                <td colspan="3">
                                    <textarea class="form-control" rows="3">$detailInfo.resXml</textarea>
                                </td>
                            </tr>
                            <tr>
                                <td class="labellcont">备注</td>
                                <td>#nullNotShow($detailInfo.remarks)&nbsp;</td>
                                <td class="labellcont">创建时间</td>
                                <td>#nullNotShow($detailInfo.createDate)&nbsp;</td>
                            </tr>
                            <tr>
                                <td class="labellcont">处理状态</td>
                                <td>
                                    #if($options != "null" && $options.stateMap != "null" && $options.stateMap.size() > 0)
                                        #foreach($item in $!options.stateMap.entrySet())
                                            #if($!{item.key} == $!{detailInfo.state})
                                                $!{item.value}
                                            #end
                                        #end
                                    #end
                                </td>
                                <td class="labellcont">处理时间</td>
                                <td>#nullNotShow($detailInfo.dealDate)&nbsp;</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            #end
        </div>
    </div>
</div>