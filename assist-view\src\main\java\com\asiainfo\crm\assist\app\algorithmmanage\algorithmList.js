(function (vita) {
    var algorithmList = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_queryAlgorithms",
            "click #btn-clear": "_clearCond",
            "click #btn-add": "_toAddAlgorithm",
            "click button[name=modifyBtn]": "_toUpdateAlgorithm",
            "click button[name=archiveBtn]": "_archiveAlgorithm"
        },
        global: {
            pageIndex: 1,
            pageSize: 5,
            preset: "date",
            toAddAlgorithm: "../query/toAlgorithmAdd",
            toUpdateAlgorithm: "../query/toAlgorithmUpdate"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el), datetime = widget.require("datetime");
            var beginDate = element.find("input[name='beginDate']");
            var endDate = element.find("input[name='endDate']");
            datetime.register(beginDate, {
                preset: widget.global.preset,
                dateFormat: "yy-mm-dd"
            });
            datetime.register(endDate, {
                preset: widget.global.preset,
                dateFormat: "yy-mm-dd"
            });
            widget._queryAlgorithms();
        },
        _queryAlgorithms: function () {
            var widget = this, element = $(widget.el);
            var params = widget._getConds();
            widget.refreshPart("queryAlgorithms", JSON.stringify(params), "#algorithmListResult", function (res) {
                var paging = widget.require("paging"), r = $(res);
                var totalNumber = r.find("#showPageInfo").data("totalNumber");
                if (totalNumber > 0) {
                    var e = paging.new({
                        recordNumber: widget.global.pageSize,
                        total: totalNumber,
                        pageIndex: widget.global.pageIndex,
                        callback: function (_pageIndex, _recordNumber) {
                            params.pageInfo.pageIndex = _pageIndex;
                            params.pageInfo.pageSize = _recordNumber;
                            if (_pageIndex > 1 && !params.pageInfo.rowCount) {
                                params.pageInfo.rowCount = totalNumber;
                            }
                            if (widget._getConds()) {
                                widget.refreshPart("queryAlgorithms", JSON.stringify(params), "#algorithmList", function (res) {

                                }, {mask: true});
                            }
                        }
                    });
                    r.find("#showPageInfo").append(e.getElement());
                }

            }, {mask: true});
        },
        _getConds: function () {
            var widget = this, element = $(widget.el);
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            var algoNbr = $.trim(element.find("#algoNbr").val());
            if (!widget._isNullOrEmpty(algoNbr)) {
                params.algoNbr = algoNbr;
            }
            var algoKey = $.trim(element.find("#algoKey").val());
            if (!widget._isNullOrEmpty(algoKey)) {
                params.algoKey = algoKey;
            }
            var statusCd = element.find("#statusCd").val();
            if (!widget._isNullOrEmpty(statusCd)) {
                params.statusCd = statusCd;
            }
            widget._appendDateScopeCond(params);
            return params;
        },
        _clearCond: function () {
            var widget = this, element = $(widget.el);
            var $input = element.find(".form-group").find("input.form-control");
            $input.val("");
            var $select = element.find(".form-group").find("select.form-control");
            $select.val("");
        },
        _appendDateScopeCond: function (params) {
            var widget = this, element = $(widget.el);
            var startTime = element.find("#beginDate").val();
            var endTime = element.find("#endDate").val();
            startTime = startTime ? startTime + " 00:00:00" : startTime;
            endTime = endTime ? endTime + " 23:59:59" : endTime;
            if (!widget._isNullOrEmpty(startTime) && !widget._isNullOrEmpty(endTime)) {
                if (startTime >= endTime) {
                    widget.popup("结束时间应大于开始时间!");
                    return false;
                }
            }
            if (!widget._isNullOrEmpty(startTime) || !widget._isNullOrEmpty(endTime)) {
                params.acceptDateScope = {};
                if (!widget._isNullOrEmpty(startTime)) {
                    params.acceptDateScope.beginDate = startTime;
                }
                if (!widget._isNullOrEmpty(endTime)) {
                    params.acceptDateScope.endDate = endTime;
                }
            }
        },
        _isNullOrEmpty: function (condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty;
        },
        _toAddAlgorithm: function (e) {
            var widget = this;
            var option = {
                url: widget.global.toAddAlgorithm,
                onClose: function (res) {
                    widget._queryAlgorithms();
                    //var algorithmAdd = $(res).closest("[data-widget=algorithmAdd]");
                }
            };
            widget.dialog(option);
        },
        _archiveAlgorithm: function (e) {
            var widget = this;
            var tarEl = $(e.target).closest("button");
            var algoId = tarEl.data("algoId");
            var params = {
                "algoId": algoId,
                "statusCd": "1100"
            };
            widget.callService("archiveAlgorithm", JSON.stringify(params), function (res) {
                var ret = JSON.parse(res);
                var resultMsg = null;
                if (ret.resultCode == 0) {
                    var resultObject = ret.resultObject;
                    if (resultObject.code != 0) {
                        widget.popup(resultObject.msg || "归档失败");
                        return false;
                    }
                } else {
                    resultMsg = ret.resultMsg || "归档失败";
                }
                if (widget._checkValue(resultMsg)) {
                    widget.popup(resultMsg);
                    return false;
                }
                widget.popup("归档成功",function (){
                    widget._queryAlgorithms();
                });
            }, {
                async: true
            });
        },
        _toUpdateAlgorithm: function (e) {
            var widget = this;
            var tarEl = $(e.target).closest("button");
            var algoId = tarEl.data("algoId");
            var params = {
                "algoId": algoId
            };
            var options = {
                url: widget.global.toUpdateAlgorithm,
                params: params,
                onClose: function (res) {
                    widget._queryAlgorithms();
                    //var algorithmUpdate = $(res).closest("[data-widget=algorithmUpdate]");
                }
            };
            widget.dialog(options);
        },
        _checkValue: function (condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty ? false : true;
        }
    });
    vita.widget.register("algorithmList", algorithmList, true);
})(window.vita);