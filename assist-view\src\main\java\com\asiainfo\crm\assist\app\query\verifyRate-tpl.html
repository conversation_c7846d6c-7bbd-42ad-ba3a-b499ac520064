<div data-widget="verifyRate" style="height:100%">

    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                               	 地 区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="acceptRegionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 " id="bearerNoDev">
                                            <label class="col-md-5 control-label lablep">
                                                	承装号码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="bearerNo" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 " id="broadBandNoDev">
                                            <label class="col-md-5 control-label lablep">
                                                	宽带账号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="broadBandNo" type="text" class="form-control" placeholder="" >
                                            </div>
                                        </div>
                                        
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_bearerNo"  type="radio" name="queryType" value="1">
                                                </label> 承装号码
                                            </label>
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_broadBandNo"  type="radio" name="queryType" value="3">
                                                </label> 宽带账号
                                            </label>
                                        </div>
                                     
                                        <div class="form-group col-md-12">
                                            <div class="col-md-12 searchbutt_r" align="right">
                                                <button id="btn-query" type="button" class="btn btn-primary">查询</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="wmin_content overflowh row">
			                    <div class="col-md-12" id="verifyRateResult">
			                        <table class="table table-bordered">
			                            <thead>
			                            <tr>
			                                <th width="50%">属性名称</th>
			                                <th>属性值</th>
			                            </tr>
			                            </thead>
			                            <tbody>
			                            <tr>
			                                <td>用户名称</td>
			                                <td id="forcastUserNameBy2an">$!{options.USERNAME}</td>
			                            </tr>
			                            <tr>
			                                <td>用户地址</td>
			                                <td id="forcastUserAddressBy2an">$!{options.PRODADDR}</td>
			                            </tr>
			                            <tr>
			                                <td>接入方式</td>
			                                <td id="typename">$!{options.TYPENAME}</td>
			                            </tr>
			                            <tr>
			                                <td>LOID</td>
			                                <td id="loid">$!{options.LOID}</td>
			                            </tr>
			                            <tr>
			                                <td>是否能提速</td>
			                                #if($options != "null" && $options.IsIncreaseRate == 1)
	                                            <td id="IsIncreaseRate">可提速</td>
	                                        #end
	                                        #if($options != "null" && $options.IsIncreaseRate == 0)
	                                            <td id="IsIncreaseRate">不能提速</td>
	                                        #end
	                                        #if($options != "null" && $options.IsIncreaseRate == -1)
	                                            <td id="IsIncreaseRate">数据异常</td>
	                                        #end
			                                
			                            </tr>
			                            <!--  
			                            <tr>
			                                <td>下行实开带宽(M)</td>
			                                <td id="ConfigRateDn">$!{options.ConfigRateDn}</td>
			                            </tr>
			                            <tr>
			                                <td>下行带宽承载能力(M)</td>
			                                <td id="MaxAttainRateDn">$!{options.MaxAttainRateDn}</td>
			                            </tr>
			                            -->
			                            <tr>
			                                <td>下行可提升带宽数(M)</td>
			                                <td id="IncreaseRateDn">$!{options.IncreaseRateDn}</td>
			                            </tr>
			                            </tbody>
			                        </table>
			                      
                                        <div id="resultInfo">
                                        <p class="vita-data">{"data":$options}</p> 
                                        </div>
                                        
                                  
                         
			                    </div>
			                </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
