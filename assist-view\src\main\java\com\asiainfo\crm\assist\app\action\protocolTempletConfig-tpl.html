<div data-widget="protocolTempletConfig" class="overflow_hidden" style="height:100%">
<p class="vita-data">{"data":$options}</p>
	<div class="calcw_rightbox noneleft">
        <div class="calctitle">
            <div class="titlefont">
                <ul id="myTab" class="nav nav-tabs">
                    <li class="active" id="userTab"><a  data-toggle="tab" >协议模板控制配置</a></li>
                </ul>
            </div>
            <div class="toolr">
               <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
            </div>
        </div>
        <div class="calcw_rightcont marltb15" id="protocolTable">
			    <div class="form_title">
			        <div><i class="bot"></i><strong class="text16">协议模板已有控制列表</strong></div>
			    </div>
               <div class="col-lg-12 mart10">
	                <a class="btn btn-gray btn-outline" id="addConfig">
	                    <i class="glyphicon fa-add text16"> </i> 新增
	                </a>
	                <a class="btn btn-gray btn-outline" id="modifyConfig">
	                    <i class="glyphicon fa-add text16"> </i> 修改
	                </a>
	                <a class="btn btn-gray btn-outline" id="delConfig">
	                    <i class="glyphicon fa-delete text16"> </i> 删除
	                </a>
            </div>

            <div class="col-lg-12 mart10">
                <table class="table table-hover" id="itemTable">
                    <thead>
                    <tr>
                        <th>选择</th>
	                    <th>销售品标识</th>
	                    <th>业务动作标识</th>
	                    <th>优先级</th>
	                    <th>控制方式</th>
	                    <th>生效时间</th>
	                    <th>失效时间</th>
	                    <th>状态</th>
                    </tr>
                    </thead>
                    <tbody>
                  #if($options != "null" && $options.items && $options.items != "null" && $options.items.size() > 0)
				
					#foreach($item in $options.items)
                    <tr>
                        <td><label class="wu-checkbox full absolute" data-scope="">
                        <input type="radio" name="moduleRadio" />                    
                        <p class="vita-data">{"data":$item,"totalNumber":$options.totalNumber}</p>
                        </label></td>
                        <td>$!item.objId</td>
                        <td>$!item.serviceOfferId</td>
                        <td>$!item.priorityLevel</td>
                        <td>
                        	#if($item.controlType && $item.controlType == '1000')
			           			缺省勾选,可人工取消
			           	   	#else
			           	   		必须勾选,不可人工取消
			           	   	#end 
                        </td>
                        <td>#if($item.effDate && $item.effDate != 'null' && $item.effDate.length() >= 10)
			           		$!item.effDate.substring(0,10)
			           	   #end 
			           	</td>
                        <td>#if($item.expDate && $item.expDate != 'null' && $item.expDate.length() >= 10)
			           		$!item.expDate.substring(0,10)
			           	   #end 
			           	</td>
			           	<td>
			           		#if($item.statusCd && $item.statusCd == '1000')
			           			有效
			           	   	#else
			           	   		无效
			           	   	#end 
			           	</td>
                    </tr>
                    #end
                   #end
                    </tbody>
                </table>
                <!--翻页start -->
                <div class="page-box" id="showPageInfo">
                </div>
                <p class="vita-data">{"totalNumber":"$!options.totalNumber"}</p>
                <!--翻页end -->
            </div>
        </div>
    </div>
</div>
