package com.asiainfo.crm.assist.app.detail;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IAcctQuerySMO;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


/**
 * 业务动作详情.
 *
 * <AUTHOR>
 */
@Component("vita.acctDetailInfo")
public class AcctDetailInfo extends AbstractSoComponent {

    @Autowired
    private IAcctQuerySMO acctQuerySMO;

    @Override
    public Map achieveData(Object... param) throws Exception {
        String p = (String) param[0];
        Map options = new HashMap();
        Map orders = this.qryAcctDetailInfo(p);
        Map inParam = jsonConverter.toBean(p,Map.class);
        options.putAll(inParam);
        options.putAll(orders);
        return options;
    }

    public Map qryAcctDetailInfo(String inStr) throws Exception {
        Map options = new HashMap();
        String retStr = acctQuerySMO.qryAccountByAcctId(inStr);
        Map edOrderMap = jsonConverter.toBean(retStr, Map.class);
        String resultCode = MapUtils.getString(edOrderMap, "resultCode");
        if (!StringUtil.isEmpty(resultCode) && resultCode.equals(MDA.RESULT_SUCCESS.toString())) {
            Map resultObject = (Map) edOrderMap.get("resultObject");
            options.put("detailInfo", resultObject);
        }
        return options;
    }
}
