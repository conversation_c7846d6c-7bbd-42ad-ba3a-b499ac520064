(function (vita) {
    var depositChargeDetail = vita.Backbone.BizView.extend({
        events : {
            "click #btn-query": "_commDepositDetailQuery",
            "click #regionIdBtn": "_chooseArea"
        },
        global : {
            pageIndex : 1, //当前页
            pageSize : 5, //每页记录数
            initPageIndexFlag : "Y",//初始化当前页标识
            chooseArea: "../comm/chooseArea"
        },
        _initialize: function () {

        },
        _commDepositDetailQuery : function () {

            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var params = {};
          
            var accNumVal=$("#accNumInput").val();
            if(widget._isNullStr(accNumVal)){
                widget.popup("请输入接入号码！");
                return false;
            } else {
                params.accNum = accNumVal;
            }
            widget._refreshDepositDetailList(params);
        },
        _refreshDepositDetailList: function (params) {
            var widget = this;
            widget.refreshPart("qryDepositDetail", JSON.stringify(params), "#depositDetailAll", function (re) {
                var r = $(re);
            }, {
                async: false,
                mask:true,
                headers : {
                    "regionId" : widget.gSession.installArea
                }
            });
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        }
    });
    vita.widget.register("depositChargeDetail", depositChargeDetail, true);
})(window.vita);