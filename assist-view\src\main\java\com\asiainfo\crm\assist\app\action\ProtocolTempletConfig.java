package com.asiainfo.crm.assist.app.action;

import java.util.HashMap;
import java.util.Map;

import com.asiainfo.crm.common.AbstractComponent;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.service.intf.IReceiptQuerySMO;

/**
 * 配置协议模板关联组件
 * @author:  zhoujun
 */
@Component("vita.protocolTempletConfig")
public class ProtocolTempletConfig extends AbstractComponent {

	@Autowired
    private IReceiptQuerySMO receiptQuerySMO;
	
	@Override
	public Map achieveData(Object... params) throws Exception {
		String paramStr = (String) params[0];
		Map options = jsonConverter.toMap(paramStr, String.class, Object.class);
		Map pageInfo = new HashMap();
		pageInfo.put("pageIndex", 1);
		pageInfo.put("pageSize", 10);
		options.put("pageInfo", pageInfo);
		options.putAll(qryItems(jsonConverter.toJson(options)));
		return options;
	}
	
	/**
	 * 获取数据
	 * @param jsonString JSOn字符串参数
	 * @return 返回MAP
	 * @throws Exception
	 */
	public Map qryItems(String jsonString) throws Exception {
		Map options = new HashMap();
		String retStr  = receiptQuerySMO.qryProtocolTempletConfig(jsonString);
		Map retMap = (Map) resolveResult(retStr);
		options.put("totalNumber", MapUtils.getString(retMap, "rowCount"));
		options.put("items", MapUtils.getObject(retMap, "rows"));
		return options;
	}
	
	/**
	 * 删除协议模板关联配置
	 */
    public String del(String jsonString) throws Exception {
		String returnStr = receiptQuerySMO.delProtocolTempletConfig(jsonString);
        return returnStr;
    }

}
