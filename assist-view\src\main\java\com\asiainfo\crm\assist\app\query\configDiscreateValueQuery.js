(function (vita) {
    var configDiscreateValueQuery = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_configDiscreateValueQuery",
            //   "click #newDisValue": "_prom",
           "click #newDisValue": "_newDisValue",
            "click #changeDisValue": "_changeDisValue",
            "click #upDisValue": "_upDisValue",
            "click #downDisValue": "_downDisValue"
        },

        _initialize: function () {
            var widget = this, element = $(widget.el);
            // var gSession = widget.gSession;
            // element.find("#regionId").val(gSession.managerAreaId).attr("value", gSession.managerAreaId);
        },
        global: {
            pageIndex: 1,//当前页
            pageSize: 12,//每页记录数
            preset: "date",
            chooseArea: "../comm/chooseArea" //选择区域
        },
        _configDiscreateValueQuery : function(){
        	debugger;
            var widget = this;
            var gsession = widget.gSession;
           var regionId = $("#regionIds").val();


            var params = {
                    applyRegionId: $("#regionIds").val(),
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize,

                }
            };
            if($("#status").val() != 2){
                params.statusCd = $("#status").val();
            }else{
                params.statusCd="isAll";
            }

            debugger;
            widget.refreshPart("configDiscreateValueQuery", JSON.stringify(params), "#configDispersedListResult", function (re) {
                debugger;
                var r = $(re), paging = this.require("paging");
                var e = paging.new({
                    recordNumber : widget.global.pageSize,
                    total : r.find("#showPageInfo").data("totalNumber"),
                    pageIndex : widget.global.pageIndex,
                    callback : function (pageIndex, pageSize,rowCount,pageCount) {
                        debugger;
                        var params = {
                                applyRegionId: $("#regionIds").val(),
                                pageInfo: {
                                    pageIndex: pageIndex,
                                    pageSize: widget.global.pageSize,

                                }
                        };



                        if($("#status").val() != 2){
                            params.statusCd = $("#status").val()
                        }
                        widget.refreshPart("configDiscreateValueQuery", JSON.stringify(params), "#configDispersedList");
                    }
                });
                r.find("#showPageInfo").append(e.getElement());
            }, { async: false });
        },
        _newDisValue:function () {
        	debugger;
            var widget = this;
            var gsession = widget.gSession;
            var name = prompt("请输入项目名称", "");
            if (name == null || name.trim() == ""){
                widget.popup("项目名称不能为空")
                return;
            }
            var params = {
                    applyRegionId: $("#regionIds").val(),
                    attrValue: name,
            }
            widget.callService("saveDiscreateValue",JSON.stringify(params),function (ret) {
                if (ret != null && ret != ""){
                    widget.popup("成功")
                    $("#btn-query").click();
                    return;
                }else {
                    widget.popup("失败")
                    return;
                }
            })
        },
        _changeDisValue:function () {
        	debugger;
            var widget = this,element = $(widget.el);
            var gsession = widget.gSession;
            var markDdiscrete = element.find('table input[name="checkbox"]:checked');
            if (markDdiscrete.length == 0){
                widget.popup("请选择一个要修改的项目")
                return;
            }
            var name = prompt("请输入新的项目名称", "");
            if (name == null || name.trim() == ""){
                widget.popup("项目名称不能为空")
                return;
            }
            var $markDdiscrete = markDdiscrete[0];
            var $tr = markDdiscrete[0].parentElement.parentElement;
            //var $tr = $($markDdiscrete.closest("tr"));
            var params = {
                attrValueId:$($tr).find("[link=customerConfig]").data("attrValueId"),
                attrValue:name,
                applyRegionId: $("#regionIds").val(),
                //staffLanId :gsession.staffLanId
            }
            widget.callService("updateDiscreateValue",JSON.stringify(params),function (ret) {
                if (ret != null && ret != ""){
                    widget.popup("成功")
                    $("#btn-query").click();
                    return;
                }else {
                    widget.popup("失败")
                    return;
                }
            })
        },
        _upDisValue:function () {
        	debugger;
            var widget = this,element = $(widget.el);
            var gsession = widget.gSession;
            var markDdiscrete = element.find('table input[name="checkbox"]:checked');
            if (markDdiscrete.length == 0){
                widget.popup("请选择一个项目")
                return;
            }
           // var markDdiscretes = markDdiscrete[0];
            var $tr = markDdiscrete[0].parentElement.parentElement;
            //var $tr = $($markDdiscrete.closest("tr"));
            var statusCd = $($tr).find("a[link=customerConfigs]").data("statusCd");
            if(statusCd == 1000){
                widget.popup("请选择一个失效的项目")
                return;
            }
            var params = {
                attrValueId:$($tr).find("[link=customerConfig]").data("attrValueId"),
                statusCd:1000,
                applyRegionId: $("#regionIds").val(),
                //staffLanId :gsession.staffLanId
            }
            widget.callService("updateDiscreateValue",JSON.stringify(params),function (ret) {
                if (ret != null && ret != ""){
                    widget.popup("成功")
                    $("#btn-query").click();
                    return;
                }else {
                    widget.popup("失败")
                    return;
                }

            })
        },
        _downDisValue:function() {
        	debugger;
        var widget = this,element = $(widget.el);
        var gsession = widget.gSession;
        var markDdiscrete = element.find('table input[name="checkbox"]:checked');
        if (markDdiscrete.length == 0){
            widget.popup("请选择一个项目")
            return;
        }
        var $markDdiscrete = markDdiscrete[0];
       // var $tr = $($markDdiscrete.closest("tr"));
        var $tr = markDdiscrete[0].parentElement.parentElement;
        var statusCd = $($tr).find("[link=customerConfigs]").data("statusCd");
        if(statusCd == 1200){
            widget.popup("请选择一个有效的项目")
            return;
        }
        var params = {
            attrValueId:$($tr).find("[link=customerConfig]").data("attrValueId"),
            statusCd:1200,
            applyRegionId: $("#regionIds").val(),
            //staffLanId :gsession.staffLanId
        }
        widget.callService("updateDiscreateValue",JSON.stringify(params),function (ret) {
            debugger;
                if (ret != null && ret != ""){
                widget.popup("成功")
                    $("#btn-query").click();
                return;
                }else {
                widget.popup("失败")
                return;
                }
        })
    },
        //获得
        _getConds: function () {
            debugger;
            var widget = this;
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            return params;
        }
    });
    vita.widget.register("configDiscreateValueQuery", configDiscreateValueQuery, true);
})(window.vita);