(function (vita) {
    var remoteReplCard = vita.Backbone.BizView.extend({
        events: {
            "click #btn-readCert": "_readCert",
            "click #btn-qryCrtfctNmbr": "_qryCrtfctNmbr",
            "click [name='btn-replCard']": "_replCardOrder",
        },
        _initialize: function () {
            var widget = this,
                element = $(widget.el);
        },
        global: {
            scanCertUrl: "../comm/scanCert",
            pageIndex: 1,
            pageSize: 15
        },
        _readCert: function () {

            var widget = this;
            var option = {
                url : widget.global.scanCertUrl+"?certOperType=" + "add",
                onClose : function(res) {
                    debugger;
                    var scanCert = $(res).closest("[data-widget=scanCert]");
                    if (scanCert.length) {

                        var cust = scanCert.scanCert("getValue");
                        widget.model.set("certInfo",cust);
                        if (!cust) {
                            return false;
                        }
                        widget._setCustomerInfo(cust);
                    }
                }
            };
            widget.dialog(option);
            return false;

        },
        _setCustomerInfo: function (data) {
            var widget = this,
                global = widget.global,
                element = $(widget.el);
            element.find("#certName").val(data.partyName);
            element.find("#certName").attr("disabled", true);
            element.find("#certNum").val(data.certNumber);
            element.find("#certNum").attr("disabled", true);
            element.find("#certAddress").val(data.certAddress);
            element.find("#certAddress").attr("disabled", true);
        },
        _qryCrtfctNmbr: function () {
            var widget = this, element = $(widget.el);
            var gsession = widget.gSession;
            var param = widget._getConds();
            if (!param.custIdentity) {
                widget.popup("请输入证件号")
                return;
            }
            widget.refreshPart("qryCrtfctNmbr", JSON.stringify(param), "#certNumTbody", function (res) {
                var r = $(res), paging = this.require("paging");
                var e = paging.new({
                    recordNumber: widget.global.pageSize,
                    total: r.find("#showPageInfo").data("totalNumber"),
                    pageIndex: widget.global.pageIndex,
                    callback: function (pageIndex, pageSize) {
                        var param = widget._getConds(pageIndex);
                        widget.refreshPart("qryCrtfctNmbr", JSON.stringify(param), "#certNumTbody");
                    }
                });
                r.find("#showPageInfo").append(e.getElement());

            }, {
                async: false
            });

        },
        _getConds: function (pageIndex) {
            var widget = this;
            var gsession = widget.gSession;
            var pIndex = pageIndex?pageIndex:widget.global.pageIndex;
            var param = {
                pageInfo: {
                    pageIndex: pIndex,
                    pageSize: widget.global.pageSize,
                },
                regionId: gsession.staffRegionId,//地区id
                custType: $("#certType").val(),//证件类型
                custIdentity:$("#certNum").val(),//证件号码
                custName: $("#certName").val(),//客户名
                custAddress: $("#certAddress").val(),//证件地址
                staffId: gsession.staffId,//员工id
                glbSessionId: gsession.glbSessionId
            };
            return param;
        },
        _replCardOrder:function (eve) {
            var widget = this,tar = $(eve.currentTarget);
            var phoneNumInfo = tar.data();
            var params = this._getConds();
            params.replPhoneNum = phoneNumInfo.phoneNum;
            params.replPhoneNumLanId = phoneNumInfo.lanId;
            delete params["pageInfo"];
            //跳转ie，重新传一个sessionId
            params.ieSessionId= widget.gSession.glbSessionId;
            var option = {
                url : "../query/remoteReplCardOrder",
                id : "replCardOrder",
                params : params
            };
            var soConst = this.require("soConst");
            var jumpSwitch = soConst.getMda("REMOTE_REPL_CARD_JUMP_SWITCH");
            if(jumpSwitch=="Y"){
            	widget._replCardOrderToIe(option);
            }else{
            	widget.dialog(option);
            }
        },
        _replCardOrderToIe:function (option) {
        		var widget = this,el = $(widget.el);
                var oForm = document.createElement("form");
                    oForm.id=option.id;
                    oForm.method="get";
                    oForm.action=widget._getUrl("/query/remoteReplCardOrder");
                    oForm.target="replCardOrderIfm";
                    var params = option.params;
                    if (params)
                    {
                    	$.each(params, function(key, value) {
                    		console.log(key, value);
                    		var oInput = document.createElement("input");
                    		oInput.type="hidden";
                    		oInput.name=key;
                    		oInput.value=value;
                    		oForm.appendChild(oInput);
                    		　
                    		})
                    }
                    oForm.onSubmit=function(){
                        window.open('about:blank',"replCardOrderIfm",'width=700,height=400,menubar=no,scrollbars=no');
        			};
        			document.getElementById("replCardOrderDiv").appendChild(oForm);
        			oForm.submit();
        			oForm.remove();
                   
        },
        _getUrl:function (url){
            var curPath=window.location.href;
            var pathName=window.location.pathname;
            var pathIdex=curPath.indexOf(pathName);
            var hostPaht=curPath.substring(0,pathIdex);
            var projectName=pathName.substring(0,pathName.substr(1).indexOf('/')+1);
            return ("crmopenie:"+hostPaht+projectName+url);
        }
    });
    vita.widget.register("remoteReplCard", remoteReplCard, true);
})(window.vita);