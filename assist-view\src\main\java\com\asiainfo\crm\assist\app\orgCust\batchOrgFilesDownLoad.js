(function (vita) {
    var batchOrgFilesDownLoad = vita.Backbone.BizView.extend({
        events: {
            "click [link=downloadFile]": function (e) {
                this._download(e);
            }
        },
        _initialize: function () {
            var widget = this;
             ele = $(widget.el);
            var params = {
                pageInfo: {
                    pageIndex: 1,
                    pageSize: 10
                },
                importBatchId: ele.data("importBatchId")
            };
            widget.refreshPart("queryFiles", JSON.stringify(params), "#auditAdjustInfoListResult", function (res) {
                var paging = widget.require("paging"), r = $(res);
                var totalNumber = r.find("#showPageInfo").data("totalNumber");
                if (totalNumber > 0) {
                    var e = paging.new({
                        recordNumber: widget.global.pageSize,
                        total: totalNumber,
                        pageIndex: widget.global.pageIndex,
                        callback: function (_pageIndex, _recordNumber) {
                            params.pageInfo.pageIndex = _pageIndex;
                            params.pageInfo.pageSize = _recordNumber;
                            if (widget._getConds()) {
                                widget.refreshPart("queryFiles", JSON.stringify(params), "#batctOrgListR");
                            }
                        }
                    });
                    r.find("#showPageInfo").append(e.getElement());
                }

            }, {
                async: false
            });
        },
        global:{
            pageIndex: 1,
            pageSize: 10
        },
        _getConds: function () {
            var widget = this;
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            return params;
        },
        _download : function(e) {
            var widget = this,
                aEls = $(e.target).closest("a");
            debugger;
            var aEl = aEls.data("data");
            var filePath = aEl.importFilePath;
            var importBatchFileId = aEl.importBatchFileId;
            var fileName = aEl.fileName;
            var url = "../query/downLoadOrgFile?filePath=" + filePath +"&importBatchFileId=" + importBatchFileId +"&fileName=" + fileName;
            location.href=url;
        },

    });
    vita.widget.register("batchOrgFilesDownLoad", batchOrgFilesDownLoad, true);
})(window.vita);