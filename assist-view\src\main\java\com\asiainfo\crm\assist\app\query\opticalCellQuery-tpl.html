<div data-widget="opticalCellQuery"
	class="pace-done wrapper mini-rightmax" style="height: 100%">
	<div class="pace-done wrapper mini-rightmax no-minright">
		<!--控制测边栏mini-rightmax-->
		<!--控制底部按钮nobuttnfood-->
		<div class="box-maincont">
			<div class="page-nav">
				<div class="row">
					<div class="pagenav_box">
						<div class="titlefont paddt8">
							<div class="wmin_content row">
								<form class=" form-bordered">
									<div class="form-group col-md-6">
										<label class="col-md-5 control-label lablep"> 地区 </label>
										<div class="col-md-7">
											<div class="input-group" align="right">
												<input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
												<div class="input-group-btn">
													<button id="channelIdBtn" class="btn btn-green" type="button">选择</button>
												</div>
											</div>
										</div>
									</div>
									<div class="form-group col-md-6">
										<label class="col-md-5 control-label lablep"> 小区名称 </label>
										<div class="col-md-7">
											<div class="input-group" align="right">
												<input id="villageName" type="text" class="form-control"
													placeholder=""">
											</div>
										</div>
										<button id="qryBtn" type="button"
												class="btn btn-primary">查&nbsp;&nbsp;询</button>
										</div>
									</div>
								</form>

								<div class="container-fluid row" id="materialCodeResult">
									<div class="wmin_content">
										<div class="col-lg-12 mart10">
											<table class="table table-hover">
												<thead>
													<tr align="center">
														<th class="td_label">所属子区域</th>
														<th class="td_label">所属本地网</th>
														<th class="td_label">小区名称</th>
														<th class="td_label">所属分公司</th>
														<th class="td_label">覆盖用户数</th>
														<th class="td_label">小区位置</th>
														<th class="td_label">是否放号</th>
														<th class="td_label">小区类型</th>
													</tr>
												</thead>
												<tbody id="opticalCellQueryList">
													#if($options != "null" && $options.list && $options.list != "null" &&
	                                               	$options.list.size() > 0)
	                                                #foreach($lis in $options.list)
	                                                <tr>
	                                                    <td>$!{lis.mgmtarea_name}</td>
	                                                    <td>$!{lis.localnet_name}</td>
	                                                    <td>$!{lis.name}</td>
	                                                    <td>$!{lis.company_name}</td>
	                                                    <td>$!{lis.user_num}</td>
	                                                    <td>$!{lis.lan_pos}</td>
	                                                    <td>$!{lis.isnumed}</td>
	                                                    <td>$!{lis.type}</td> 
	                                                </tr>
	                                                #end
	                                                #elseif($options != "null" && $options.list && $options.list != "null" &&
	                                                $options.list.size() == 0)
	                                                <tr><td align='center' colspan='7'>未查询到数据！<td></tr>
	                                                #end
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>



