package com.asiainfo.crm.assist.app.query.prepareorderinfo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.Constant;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.ICommQuerySMO;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.IStaffDataPrvQuerySMO;
import com.asiainfo.crm.util.ListUtil;
import com.asiainfo.crm.util.center.OrderUtil;

@Component("vita.prepareOrderInfoData")
public class PrepareOrderInfoData extends AbstractComponent {

	@Autowired
	private IOrderQuerySMO orderQuerySMO;
    
	@Autowired
	private ICommQuerySMO commQuerySMO;
	
	@Autowired
	private IStaffDataPrvQuerySMO staffDataPrvQuerySMO;

	@Override
	public Map achieveData(Object... params) throws Exception {
		String json = (String) params[0];
		Map options = qryPrepareOrderData(json);
		return options;
	}

	public Map qryPrepareOrderData(String json) throws Exception {
		String re = orderQuerySMO.findPrepareOrderDataByProperty(json);
		List<Map> prepareOrderDatas = (List) resolveResult(re);
		Map result = new HashMap();
		if (ListUtil.isListEmpty(prepareOrderDatas)) {
			return result;
		}
		String busiDataStr = MapUtils.getString(prepareOrderDatas.get(0), "busiData");
		if (StringUtils.isBlank(busiDataStr)) {
			return result;
		}
		Map busiMap = jsonConverter.toBean(busiDataStr, Map.class);
		if (MapUtils.isNotEmpty(busiMap)) {
			Map paramsMap = jsonConverter.toBean(json, Map.class);
			Map nonLocalCustInfo = MapUtils.getMap(busiMap, "nonLocalCustInfo");
			String certType = MapUtils.getString(nonLocalCustInfo, "certType");
			String regionId = MapUtils.getString(nonLocalCustInfo, "regionId");
	        if (!StringUtil.isEmpty(certType)) {
	        	String certTypeName = MDA.ORDER_HANDLER_IDENTITYS.get(certType);
	            nonLocalCustInfo.put("certTypeName", certTypeName);
	        }
            if (!StringUtil.isEmpty(regionId)) {
            	nonLocalCustInfo.put("regionName", qryRegionName(regionId));
            }
            String acceptRegionId = MapUtils.getString(busiMap, "acceptRegionId");
            if (!StringUtil.isEmpty(acceptRegionId)) {
            	result.put("acceptRegionName", qryRegionName(acceptRegionId));
            }
            result.put("createOrgName", busiMap.get("createOrgName"));
            result.put("remark", MapUtils.getString(busiMap, "remark", ""));
			result.put("nonLocalCustInfo", nonLocalCustInfo);
			
			String linkMan = MapUtils.getString(busiMap, "linkMan");
			String linkNumber = MapUtils.getString(busiMap, "linkNumber");
			String accNbr = MapUtils.getString(nonLocalCustInfo, "accNbr");
			String certNum = MapUtils.getString(nonLocalCustInfo, "certNum");
			String custName = MapUtils.getString(nonLocalCustInfo, "custName");
			boolean isAllow = allowPermission(json);
			if (!OrderUtil.isMarkSensitive("prepareOrderInfoData") && !isAllow) {
				encryptName(result, "linkMan", linkMan);
				encryptName(nonLocalCustInfo, "custName", custName);
				encryptCert(nonLocalCustInfo, "certNum", certNum);
				encryptPhone(result, "linkNumber", linkNumber);
				encryptPhone(nonLocalCustInfo, "accNbr", accNbr);
			} else {
	            result.put("linkMan", busiMap.get("linkMan"));
	            result.put("linkNumber", busiMap.get("linkNumber"));
			}
			Long prepareOrderId = MapUtils.getLong(paramsMap, "prepareOrderId");
			supplementPrepareOrderDoc(result, prepareOrderId);
		}
		return result;
	}
	
	private void supplementPrepareOrderDoc(Map result, Long prepareOrderId) throws Exception {
		if (Constant.LOGIC_STR_N.equals(AssistMDA.ALLOPATRY_PIC_ON)) {
			return;
		}
		Map param = new HashMap();
		param.put("prepareOrderId", prepareOrderId);
		param.put("scope", "prepareOrderDoc");
		String res = orderQuerySMO.queryPrepareOrderInfoByScopeInfo(jsonConverter.toJson(param));
		Map resultMap = (Map) resolveResult(res);
		Map detailInfoVo = MapUtils.getMap(resultMap, "detailInfoVo");
		List<Map> prepareOrderDocs = (List) MapUtils.getObject(detailInfoVo, "prepareOrderDocList");
		result.put("prepareOrderDocs", prepareOrderDocs);
		supplementDocInfo(prepareOrderDocs);
	}
	
	public void supplementDocInfo(List<Map> prepareOrderDocs) throws Exception {
//		//补充逻辑(本地化该方法)：展示现场照片信息
//		for (Map prepareOrderDoc: ListUtil.nvlList(prepareOrderDocs)) {
//			
//			//1.根据docKey调用无纸化系统接口
//			String docKey = MapUtils.getString(prepareOrderDoc, "docKey");
//			
//			//2.将照片base64编码值赋值到docImg字段值
//			String docImg = "";
//			prepareOrderDoc.put("docImg", docImg);
//			
//		}
	}
	
	private void encryptName(Map result, String key, String value) {
		if (StringUtils.isBlank(value)) {
			result.put(key, "****");
		} else {
			result.put(key, encrypt(value, "name"));
		}
	}
	
	private void encryptCert(Map result, String key, String value) {
		if (null == value || value.length() < 5) {
			result.put(key, "****");
		} else {
			result.put(key, encrypt(value, "cert"));
		}
	}
	
	private void encryptPhone(Map result, String key, String value) {
		if (StringUtils.isBlank(value)) {
			result.put(key, "****");
		} else {
			result.put(key, encrypt(value, "phone"));
		}
	}
	
	/**
     * 查询权限
     *
     * @param regionId
     * @return
     * @throws Exception
     */
	private boolean allowPermission(String json) throws Exception {
		Map paramsMap = jsonConverter.toBean(json, Map.class);
		paramsMap.put("privCode", MDA.USER_PRIV_RIGHTS.get("custDetailQry"));
		String re = staffDataPrvQuerySMO.checkSysUserPriv(jsonConverter.toJson(paramsMap));
		Map resultMap = (Map) resolveResult(re);
		Boolean isHave = MapUtils.getBoolean(resultMap, "isHave");
		return isHave;
	}

    /**
     * 查询地区名称
     *
     * @param regionId
     * @return
     * @throws Exception
     */
    private String qryRegionName(String regionId) throws Exception {
        Map<String, Object> regionIdMap = new HashMap(1);
        regionIdMap.put("commonRegionId", regionId);
        String re = commQuerySMO.getRegionCnByCond(jsonConverter.toJson(regionIdMap));
		List<Map> regionList = (List) resolveResult(re);
		if (ListUtil.isListEmpty(regionList)) {
			return "";
		}
		return MapUtils.getString(regionList.get(0), "regionName", "");
    }
	
}
