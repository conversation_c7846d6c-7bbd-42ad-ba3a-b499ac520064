package com.asiainfo.crm.assist.app.detail;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.service.intf.ISoReceiptSMO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component("vita.invoicePrintLogDetail")
public class InvoicePrintLogDetail extends AbstractSoComponent {

    @Autowired
    private ISoReceiptSMO soReceiptSMO;

    @Override
    public Map achieveData(Object... param) throws Exception {
        String p = (String) param[0];
        Map options = new HashMap();
        Map inParam = jsonConverter.toBean(p,Map.class);
        Map orders = qryInvoicePrintLogDetailInfo(inParam);
        options.putAll(inParam);
        options.putAll(orders);
        return options;
    }

    public Map qryInvoicePrintLogDetailInfo(Map inParam) throws Exception {
        Map options = new HashMap();
        String logId = String.valueOf(inParam.get("logId"));
        String retStr = soReceiptSMO.queryReceiptLogByLogId(logId);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(retStr);
        options.put("detailInfo", resultObjectMap);
        options.put("logTypeMap", AssistMDA.INVOICE_LOG_TYPES);
        options.put("statusCdMap", AssistMDA.INVOICE_LOG_STATUS_CDS);
        return options;
    }
}
