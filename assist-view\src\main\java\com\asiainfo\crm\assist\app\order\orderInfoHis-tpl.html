<link rel="stylesheet" type="text/css" href="../bundles/assist/app/order/ActionChain.css">
<div data-widget="orderInfoHis" class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <p class="vita-data">{"data":$options}</p>
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item dzlaction">
                            <div class="container-fluid row">
                                <Div class="Order-number "><span style="float: left;padding-top: 2px;">订单流水：</span>
                                    <div class="form-inline">
                                        <div class="input-group">
                                            <input id="csNbr" type="text" style="width:360px;"  class="form-control" placeholder="" value="$options.csNbr">
                                               <span class="input-group-btn">
                                                <button class="btn btn-primary" type="button" id="loadOrder">加载</button>
                                              </span>
                                        </div>
                                    </div>

                                </Div>
                                <div class="btn-group btn-group-justified width-m5 " role="group" aria-label="...">
                                    <div class="btn-group  " role="group" >
                                        <button id="orderInfo" type="presentation" class="btn btn-default redbg color3 active" href="#11" role="tab" data-toggle="tab">订单项信息</button>
                                    </div>
                                    <div class="btn-group" role="group">
                                        <button id="traceHis" type="presentation" class="btn btn-default redbg color3" href="#12" role="tab" data-toggle="tab">调用历史</button>
                                    </div>
                                  <!--  <div class="btn-group" role="group">
                                        <button id="orderItem" type="presentation" class="btn btn-default redbg color3" href="#13" role="tab" data-toggle="tab">订单项关系</button>
                                    </div>-->
                                </div>
                                <div class="tab-content" >
                                    <div role="tabpanel" class="tab-pane active" id="11">
                                        <div class="form_title">
                                            <div><i class="bot"></i>查询条件</div>
                                        </div>
                                        <div class="form-bordered " style="margin-bottom:30px;">

                                            <div class="col-md-12">
                                                <div class="form-group col-md-4">
                                                    <label class="col-md-4 control-label lablep">
                                                        来源
                                                    </label>
                                                    <div class="col-md-8">
                                                        <select class="form-control" id="chainCreate">
                                                            <option value="">-请选择-</option>
                                                            <option value="false">前端</option>
                                                            <option value="true">动作链</option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <div class="form-group col-md-4">
                                                    <label class="col-md-4 control-label lablep">
                                                        服务提供</label>
                                                    <div class="col-md-8">
                                                        <select class="form-control " id="serviceOfferId">
                                                            <option value="">-请选择-</option>
                                                            #if($options != "null" && $options.serviceOfferTypes && $options.serviceOfferTypes != "null" && $options.serviceOfferTypes.size() > 0)
                                                            #foreach($serviceOffer in $options.serviceOfferTypes)
                                                            <option value="$serviceOffer.serviceOfferId">$serviceOffer.serviceOfferName</option>
                                                            #end
                                                            #end
                                                        </select>

                                                    </div>
                                                </div>
                                                <div class="form-group col-md-4">

                                                    <div class="col-xs-10"><input id="applyObjSpecName" type="text" class="form-control" placeholder="请输入订单项名称"></div>

                                                    <div class="col-xs-2">
                                                        <button id="orderSearch"  type="button" class="btn btn-primary">查询</button>
                                                    </div>



                                                </div>

                                            </div>
                                            <!--<div class="col-md-12">
                                                <div class="col-md-8">
                                                    <label class="col-md-4 control-label lablep textcolorgreen">
                                                        订单流水</label>
                                                    <div class="col-md-8">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control" placeholder="" value="$options.csNbr">
                                               <span class="input-group-btn">
                                                <button class="btn btn-primary" type="button">确定</button>
                                              </span>
                                                        </div>
                                                    </div>

                                                </div>
                                                <div class="col-md-4 ">


                                                </div>
                                            </div>
-->
                                        </div>
                                        <div class="form_title">
                                            <div><i class="bot"></i>查询结果
                                                <div class="floatr  textcolorred"><span class="glyphicon glyphicon-list"></span>
                                                    <span class="text14">层级展示</span>
                                                </div></div>
                                        </div>
                                        <div class="container-fluid row" id="orderTable">
                                            <div class="wmin_content">
                                                <div class="col-lg-12 " id="orderDiv">
                                                    <p class="vita-data">{"data":$options}</p>
                                                    <table class="table table-hover">
                                                        <thead>
                                                        <tr>
                                                            <th>序号</th>
                                                            <th>订单项名称</th>
                                                            <th>服务提供</th>
                                                            <th>来源</th>
                                                            <th>动作描述</th>
                                                        </tr>
                                                        </thead>
                                                        <tbody >
                                                        #if($options != "null" && $options.offerOrderItems && $options.offerOrderItems != "null" &&
                                                        $options.offerOrderItems.size() > 0)
                                                        #foreach($offerOrderItem in $options.offerOrderItems)
                                                        #if( !($velocityCount >= 11) )
                                                        <tr>
                                                            <td>$foreach.count</td>
                                                            <td>
                                                                <a id="orderDetail" >$!{offerOrderItem.applyObjSpecName}</a>
                                                                <p class="vita-data">{"offerOrderItem":$offerOrderItem}</p>
                                                            </td>
                                                            <td>$!{offerOrderItem.serviceOfferName}</td>
                                                            <td>#if($offerOrderItem.chainCreate)动作链#else前端#end</td>
                                                            <td>$!{offerOrderItem.remark}</td>
                                                        </tr>
                                                        #end
                                                        #end
                                                        #elseif($options != "null" && $options.offerOrderItems && $options.offerOrderItems != "null" &&
                                                        $options.offerOrderItems.size() == 0)
                                                        <tr>
                                                            <td align='center' colspan='8'>未查到相关数据</td>
                                                        </tr>
                                                        #end
                                                        </tbody>
                                                    </table>
                                                </div>
                                                #if($options.totalNumber && $options.totalNumber != "null")
                                                <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                                #end
                                            </div>
                                            #if($options.totalNumber && $options.totalNumber != "null" && $options.totalNumber >10)
                                            <!--翻页start -->
                                            <div id="showPageInfo"></div>
                                            <!--翻页end -->
                                            #end
                                        </div>

                                    </div>
                                    <div role="tabpanel" class="tab-pane " id="12">
                                        <div class="container-fluid row mart15">
                                            <div class="wmin_content">
                                                <div class="col-lg-12 " id="traceHisListResult">
                                                    <table class="table table-hover">
                                                        <thead>
                                                        <tr>
                                                            <th>序号</th>
                                                            <th>iUId</th>
                                                            <th>调用时间</th>
                                                            <th>出入参</th>
                                                            <th>耗时</th>
                                                            <th>是否并行</th>
                                                            <th>订单</th>
                                                        </tr>
                                                        </thead>
                                                        <tbody >
                                                        #if($options != "null" && $options.traceHisList && $options.traceHisList != "null" &&
                                                        $options.traceHisList.size() > 0)
                                                        #foreach($traceHis in $options.traceHisList)
                                                        <tr>
                                                            <td>$foreach.count</td>
                                                            <td>$!{traceHis.iUId}</td>
                                                            <td>$!{traceHis.invokeTime}</td>
                                                            <td>
                                                                <a id="paramDetail" value="$traceHis.iUId">出入参详情</a>
                                                                <p class="vita-data">{"iUId":$traceHis.iUId</p>
                                                            </td>
                                                            <td>$!{traceHis.elapsedTime}</td>
                                                            <td>#if($traceHis.parallel)是#else否#end</td>
                                                            <td>
                                                                <a id="orderHisDetail" value="$traceHis.iUId">订单详情</a>
                                                                <p class="vita-data" >{"iUId":$traceHis.iUId</p>
                                                            </td>
                                                        </tr>
                                                        #end
                                                        #elseif($options != "null" && $options.traceHisList && $options.traceHisList != "null" &&
                                                        $options.traceHisList.size() == 0)
                                                        <tr>
                                                            <td align='center' colspan='8'>未查到相关数据</td>
                                                        </tr>
                                                        #end
                                                        </tbody>
                                                    </table>
                                                    <div class="row">
                                                        <!--翻页start -->
                                                       <!-- <div class="page-box ">
                                                            <div class="floatl pagel_font">展示第<span>1</span>条到第<span>1</span>条，共<span>23</span>条</div>
                                                            <div class="floatr">
                                                                <ul class="pagination">
                                                                    <li><a href="#" aria-label="Previous"><span aria-hidden="true">Previous</span></a></li>
                                                                    <li><a href="#" class="active">1</a></li>
                                                                    <li><a href="#">2</a></li>
                                                                    <li><a href="#">3</a></li>
                                                                    <li><a href="#">4</a></li>
                                                                    <li><a href="#">5</a></li>
                                                                    <li><a href="#" aria-label="Next"><span aria-hidden="true">Next</span></a></li>
                                                                </ul>
                                                            </div>-->
                                                        </div>
                                                        <!--翻页end -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div role="tabpanel" class="tab-pane " id="13"></div>
                                </div>

                            </div>
                        </div>
                        <!--填单end-->


                    </div>

                </div>
            </div>
        </div>
</div>
