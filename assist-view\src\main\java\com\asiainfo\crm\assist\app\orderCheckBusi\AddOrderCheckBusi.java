package com.asiainfo.crm.assist.app.orderCheckBusi;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderCheckBusiSMO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

@Component("vita.addOrderCheckBusi")
public class AddOrderCheckBusi extends AbstractComponent {
    @Autowired
    private IOrderCheckBusiSMO orderCheckBusiSMO;
    @Override
    public Map achieveData(Object... objects) throws Exception {
        return null;
    }

    public String saveOrderCheckBusi(String params) {
        String result = orderCheckBusiSMO.saveOrderCheckBusi(params);
        return result;
    }
}
