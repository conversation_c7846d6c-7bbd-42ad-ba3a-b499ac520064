(function (vita) {
    var releaseRs = vita.Backbone.BizView.extend({
        events: {
            "click #releaseRsBtn": "_releaseRsBtnClick",
            "click #regionIdBtn": "_chooseArea",
            "click #accNumType": "_accNumTypeCheck"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);

        },
        global: {
            pageIndex: 1,
            pageSize: 100,
            chooseArea: "../comm/chooseArea"
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },

        _accNumTypeCheck: function () {

            var accNumType = $("#accNumType").val();
            if("1" == accNumType) {
                $("#queryLabel").text("接入号码")
            }
            if("2" == accNumType) {
                $("#queryLabel").text("账号")
            }
            if("3" == accNumType) {
                $("#queryLabel").text("串号")
            }
            if("4" == accNumType) {
                $("#queryLabel").text("UIM卡")
            }
        },
 
        _releaseRsBtnClick: function () {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var params = {};

            var regionId = $("#regionId").attr("value");
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return;
            } else {
                params.busiRegionId = regionId;
                params.regionId = regionId;
            }
            
            var accNumType =  $("#accNumType").val();//资源类型 1、接入号码 2、账号 3、串号 4、UIM卡
            
            var rsNum = $("#rsNum").val();
            if (widget._isNullStr(rsNum)) {
                widget.popup("请输入需要释放的资源号码！");
                return;
            } else {
                params.rsNum = rsNum;
            }
            params.accNumType = accNumType;
            var channelId = gSession.curChannelId;
            var staffId = gSession.staffId;
            
            params.channelId = channelId;
            params.staffId = staffId;
            
            $("#releaseRsBtn").attr("disabled", true);
            
            widget.callService("releaseRs",JSON.stringify(params),function (ret) {
                if (0 == ret.retCode){
                    widget.popup(ret.retDesc);
                    $("#rsNum").val("");
                    $("#releaseRsBtn").attr("disabled", false);
                    return;
                }else {
                    widget.popup(ret.retDesc);
                    $("#rsNum").val("");
                    $("#releaseRsBtn").attr("disabled", false);
                    return;
                }
            })

        },

        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        }
    });
    vita.widget.register("releaseRs", releaseRs, true);
})(window.vita);