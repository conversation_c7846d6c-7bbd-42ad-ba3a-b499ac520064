<div data-widget="acctDetailInfo" style="height: 100%">
    <div class="calcw_rightbox noneleft">

        #macro( nullNotShow $val)
            #if($val && $val != "null")
                $!val
            #end
        #end

        #macro( eachRangeValue $valueRange $value)
            #if($valueRange && $valueRange != "null" && $valueRange.size()>0)
                #foreach($range in $valueRange)
                    #if($range.value == $value)
                        $range.name
                    #end
                #end
            #end
        #end

        <div class="calctitle">
            <div class="titlefont">账户详情</div>
            <div class="toolr">
                <button id="closeBtn" type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
            </div>
        </div>
        <div class="calcw_rightcont" >
            #if($options.detailInfo && $options.detailInfo != "null")
            #set($detailInfo = $options.detailInfo)
            #if($detailInfo.account && $detailInfo.account != "null")
            #set($baseInfo = $detailInfo.account)
            <div class="container-fluid">
                <h5 class="meal_htitle nobrder"><i class="bot"></i>基本信息</h5>
                <div class="row">
                    <div class="col-md-12 ">
                        <table class="table table-bordered conttd-w">
                            <tbody>
                            <tr>
                                <td class="labellcont">账户合同号</td>
                                <td>#nullNotShow($baseInfo.acctCd)&nbsp;</td>
                                <td class="labellcont">账户属主</td>
                                <td>#nullNotShow($baseInfo.custName)&nbsp;</td>
                            </tr>
                            <tr>
                                <td class="labellcont">账户名</td>
                                <td>#nullNotShow($baseInfo.acctName)&nbsp;</td>
                                <td class="labellcont">地区</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td class="labellcont">账户状态</td>
                                <td>#nullNotShow($baseInfo.acctStatusName)&nbsp;</td>
                                <td class="labellcont">支付号码</td>
                                <td>#nullNotShow($baseInfo.prodInstId)&nbsp;</td>
                            </tr>
                            <tr>
                                <td class="labellcont">信用等级</td>
                                <td>&nbsp;</td>
                                <td class="labellcont">账户类型</td>
                                <td>#eachRangeValue($baseInfo.acctBillingTypeValueRange,$baseInfo.acctBillingType)&nbsp;</td>
                            </tr>
                            <tr>
                                <td class="labellcont">生效时间</td>
                                <td>#nullNotShow($baseInfo.effDate)&nbsp;</td>
                                <td class="labellcont">失效时间</td>
                                <td>#nullNotShow($baseInfo.expDate)&nbsp;</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            #end

            <!--账户/外部支付账户关联-->
            #if($detailInfo.paymentPlan && $detailInfo.paymentPlan != "null")
            #set($payment = $detailInfo.paymentPlan)
            <div class="container-fluid">
                <h5 class="meal_htitle nobrder"><i class="bot"></i>账户/外部支付账户关联</h5>
                <div class="row">
                    <div class="col-md-12">
                        <table class="table table-bordered conttd-w">
                            <tbody>
                            <tr>
                                <td class="labellcont">外部支付账户</td>
                                <td>#nullNotShow($payment.payAcctId)&nbsp;</td>
                                <td class="labellcont">优先次序</td>
                                <td>#nullNotShow($payment.priority)&nbsp;</td>
                            </tr>
                            <tr>
                                <td class="labellcont">外部支付账户类型</td>
                                <td>#eachRangeValue($payment.paymentMethodValueRange,$payment.paymentMethod)&nbsp;</td>
                                <td class="labellcont">支付类型</td>
                                <td>#eachRangeValue($payment.payAcctTypeValueRange,$payment.payAcctType)&nbsp;</td>
                            </tr>
                            <tr>
                                <td class="labellcont">起始时间</td>
                                <td>#nullNotShow($payment.effDate)&nbsp;</td>
                                <td class="labellcont">结束时间</td>
                                <td>#nullNotShow($payment.expDate)&nbsp;</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            #end

            <!--外部支付账户-->
            #if($detailInfo.extAcct && $detailInfo.extAcct != "null")
            #set($payAcct = $detailInfo.extAcct)
            <div class="container-fluid">
                <h5 class="meal_htitle nobrder"><i class="bot"></i>外部支付账户</h5>
                <div class="row">
                    <div class="col-md-12">
                        <table class="table table-bordered conttd-w">
                            <tbody>
                            <tr>
                                <td class="labellcont">付费人</td>
                                <td>#nullNotShow($payAcct.payAcctName)&nbsp;</td>
                                <td class="labellcont">是否快支</td>
                                <td>#eachRangeValue($payAcct.ifContractQuickPayValueRange,$payAcct.ifContractQuickPay)&nbsp;</td>
                            </tr>
                            #if($payAcct.payChannel == 1)
                            <tr>
                                <td class="labellcont">银行编码</td>
                                <td>&nbsp;</td>
                                <td class="labellcont">银行账号</td>
                                <td>&nbsp;</td>
                            </tr>
                            #end
                            <tr>
                                <td class="labellcont">开户人</td>
                                <td>&nbsp;</td>
                                <td class="labellcont">支付方式</td>
                                <td>#eachRangeValue($payAcct.payChannelValueRange,$payAcct.payChannel)&nbsp;</td>
                            </tr>
                            <tr>
                                <td class="labellcont">起始时间</td>
                                <td>#nullNotShow($payAcct.effDate)&nbsp;</td>
                                <td class="labellcont">结束时间</td>
                                <td>#nullNotShow($payAcct.expDate)&nbsp;</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            #end

            #end
        </div>
    </div>
</div>