package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.service.intf.ICustSMO;
import com.asiainfo.crm.service.intf.ICustSaveMultiSMO;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component("vita.disputeUpload")
public class disputeUpload extends AbstractSoComponent {
	@Autowired
	private ICustSaveMultiSMO custSaveMultiSMO;
	@Autowired
	protected ICustSMO custSMO;
	@Override
	public Map achieveData(Object... objects) throws Exception {
		String jsonString = objects[0].toString();
		Map options = new HashMap();
		Map inParams = jsonConverter.toBean(jsonString, Map.class);
		String certNum = MapUtils.getString(inParams, "certNum");
		String custName = MapUtils.getString(inParams, "custName");
		String picPath = MapUtils.getString(inParams, "picPath");
		String certType = MapUtils.getString(inParams, "certType");
		String certTypeName = MapUtils.getString(inParams, "certTypeName");
		String qryCustCompCfgRsp = custSMO.qryCustCompCfgInfo();
		Map custCompCfgMap = (Map) resolveResult(qryCustCompCfgRsp);


		options.putAll(custCompCfgMap);
		options.put("certNum",certNum);
		options.put("custName",custName);
		options.put("picPath",picPath);
		options.put("certType",certType);
		options.put("certTypeName",certTypeName);

		return options;
	}
	public String partyCheckArgue(String jsonString) throws Exception {
		String resString = custSaveMultiSMO.partyCheckArgue(jsonString);
		return resString;
	}
}


