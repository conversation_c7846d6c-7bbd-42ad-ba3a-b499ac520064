<script src="../bundles/vita/plugs/highmeterjs/base64.min.js"></script>
<script src="../bundles/vita/plugs/sha1.js"></script>
<div data-widget="custAuth">

    #if($options)
    <p class="vita-data">{"data":$options}</p>
    #end

    <div class="calcw_rightbox noneleft">
        <!--此处控制noneleft右边宽-->
        <div class="calctitle">
            <div class="titlefont">
                <ul id="myTab" class="nav nav-tabs">
                    #if($options != "null" && $options.custAuthTypes && $options.custAuthTypes != "null" &&
                    $options.custAuthTypes.size() > 0)
                    #foreach($custAuth in $options.custAuthTypes)
                    #if($!custAuth.custAuthCd == "3")
                    <li class="active" id="tab$!custAuth.custAuthCd"><a href="javascript:void(0);">$!custAuth.custAuthName</a>
                    </li>
                    #else
                    <li id="tab$!custAuth.custAuthCd"><a href="javascript:void(0);">$!custAuth.custAuthName</a></li>
                    #end
                    #end
                    #end
                </ul>
            </div>
            <div class="toolr">
                <button id="custAuthSubmitBtn" type="button" class="btn btn-primary btn-sm okbutt">确认</button>
                <button id="closeCustAuthBtn" type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">×</span></button>
            </div>
        </div>
        <div class="calcw_rightcont">
            <div id="myTabContent" class="tab-content">

                #if($options != "null" && $options.custAuthTypes && $options.custAuthTypes != "null" &&
                $options.custAuthTypes.size() > 0)
                #foreach($custAuth in $options.custAuthTypes)
                #if($!custAuth.custAuthCd == "1")
                <div id="content$!custAuth.custAuthCd" class="tab-pane fade" style="display:none;">
                    <div class="nav_pills_box">
                        #if($options && $options.useDeskTop=="0")
                        <ul class="nav nav-pills">
                            <li id="dkq" class="active"><a data-toggle="tab" aria-expanded="true">读卡器读卡</a></li>
                            <!--<li id="nfc" class=""><a  data-toggle="tab" aria-expanded="false">手机NFC读卡</a></li>-->
                        </ul>
                        #else
                        <i class="fa-lightn glyphicon textcolorgreen text18 mar15"></i>智慧桌面读卡
                        #end
                    </div>
                    #if($options && $options.useDeskTop=="0")
                    <object type="application/cert-reader" id="CertCtl" width=0 height=0></object>
                    #end
                    <div class="tab-content">
                        <div class="mart15 tab-pane fade active in" id="nav-pills-tab-1">
                            <div class=" col-md-12">
                                <span id="controlsIdentify">控件识别结果：未检测到控件。</span><a id="hdSrc" class="form_alink"
                                                                                    style="display:none;">鸿达驱动下载 </a><a
                                    id="krSrc" class="form_alink" style="display:none;">卡尔驱动下载 </a><a id="ynSrc"
                                                                                                      class="form_alink"
                                                                                                      style="display:none;">因纳伟盛驱动下载</a><span
                                    id="loadInfo" class="mar15" style="display:none;">下载安装后，请重启浏览器</span>
                                <button id="scanCert" type="button" class="btn btn-primary btn-sm">读取</button>
                            </div>
                            <div class="mincontent ">
                                <form class="form-bordered">
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">姓名</label>
                                        <div class="col-md-10">
                                            <input id="Name" type="text" class="form-control" placeholder="" value=""
                                                   readonly="readonly">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">证件号</label>
                                        <div class="col-md-10">
                                            <input id="CardNo" type="text" class="form-control" placeholder="" value="" readonly="readonly">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">性别</label>
                                        <div class="col-md-10">
                                            <input id="Sex" type="text" class="form-control" placeholder="" value=""
                                                   readonly="readonly">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">民族</label>
                                        <div class="col-md-10">
                                            <input id="Nation" type="text" class="form-control" placeholder="" value=""
                                                   readonly="readonly">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">出生年月</label>
                                        <div class="col-md-10">
                                            <input id="Born" type="text" class="form-control" placeholder=""
                                                   readonly="readonly">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">地址</label>
                                        <div class="col-md-10">
                                            <input id="Address" type="text" class="form-control" placeholder=""
                                                   readonly="readonly">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">签发机关</label>
                                        <div class="col-md-10">
                                            <input id="Police" type="text" class="form-control" placeholder=""
                                                   readonly="readonly">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">期限起始</label>
                                        <div class="col-md-10">
                                            <input id="ActivityLFrom" type="text" class="form-control" placeholder=""
                                                   readonly="readonly">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">期限失效</label>
                                        <div class="col-md-10">
                                            <input id="ActivityLTo" type="text" class="form-control" placeholder=""
                                                   readonly="readonly">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">身份证照片</label>
                                        <div class="col-md-10">
                                            <div class="pic_box"><img id="IdPhoto"></div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!--<div class="mart15 tab-pane fade" id="nav-pills-tab-2">
                            <div class=" col-md-12">
                                <span class="mar15">手机连接NFC：连接成功！</span>
                                <button type="button" class="btn btn-primary btn-sm">NFC绑定</button>
                                <button type="button" class="btn btn-primary btn-sm">NFC读取</button>
                                <button type="button" class="btn btn-primary btn-sm">NFC返回</button>
                            </div>
                            <div class="mincontent ">
                                <form class="form-bordered">
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">姓名</label>
                                        <div class="col-md-10">
                                            <input type="text" class="form-control" placeholder="" value="张海涛">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">性别</label>
                                        <div class="col-md-10">
                                            <input type="text" class="form-control" placeholder="" value="男">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">民族</label>
                                        <div class="col-md-10">
                                            <input type="text" class="form-control" placeholder="" value="汉">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">出生年月</label>
                                        <div class="col-md-10">
                                            <input type="text" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">地址</label>
                                        <div class="col-md-10">
                                            <input type="text" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">签发机关</label>
                                        <div class="col-md-10">
                                            <input type="text" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">期限起始</label>
                                        <div class="col-md-10">
                                            <input type="text" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">期限失效</label>
                                        <div class="col-md-10">
                                            <input type="text" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep">身份证照片</label>
                                        <div class="col-md-10">
                                            <div class="pic_box"><img ></div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>-->
                    </div>
                </div>

                #elseif($!custAuth.custAuthCd == "2")
                <div id="content$!custAuth.custAuthCd" class="tab-pane fade" style="display:none;">
                    <div class="col-md-12">
                        <h5 class=" meal_htitle">
                            <label>证件号码:</label>
                            <span>$!options.certNum</span>
                        </h5>
                    </div>
                    <div class="mincontent ">
                        <form class="form-bordered">
                            <div class="form-group col-md-12">
                                <label class="col-md-2 control-label lablep"><span
                                        class="textcolorred">*</span>手机号码</label>
                                <div class="col-md-10">
                                    <div class="input-group">
                                        <input id="accNum2" type="text" class="form-control" placeholder="请输入手机号码">
                                        <div class="input-group-btn">
                                            <button id="sendMsgNum" class="btn btn-green borderl" type="button">获取验证码
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group col-md-12">
                                <label class="col-md-2 control-label lablep"><span
                                        class="textcolorred">*</span>手机验证码</label>
                                <div class="col-md-10">
                                    <div class="input-group">
                                    <input id="checkMsgNum" type="text" class="form-control" placeholder="请输入手机验证码">
                                    <div class="input-group-btn">
                                        <button id="validateMsgBtn" class="btn btn-green borderl" type="button">校验
                                        </button>
                                    </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                #elseif($!custAuth.custAuthCd == "3")
                <div id="content$!custAuth.custAuthCd" class="tab-pane fade in active">
                    <div class="col-md-12">
                        <h5 class=" meal_htitle">
                            <label>证件号码:</label>
                            <span>$!options.certNum</span>
                        </h5>
                    </div>
                    <div class="mincontent ">
                        <form class="form-bordered">

                            <div class="form-group col-md-12">
                                <label class="col-md-2 control-label lablep"><span
                                        class="textcolorred">*</span>手机号码</label>
                                <div class="col-md-10">
                                    <div class="input-group">
                                        <input id="accNum3" type="text" class="form-control" placeholder="请输入手机号码" readonly="readonly">
                                        <div class="input-group-btn">
                                            <button id="prodPwdBtn" class="btn btn-green borderl" type="button">获取验证码
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group col-md-12">
                                <label class="col-md-2 control-label lablep"><span
                                        class="textcolorred">*</span>手机验证码</label>
                                <div class="col-md-10">
                                 <div class="input-group">
                                    <input id="checkMsgNum3" type="text" class="form-control" placeholder="请输入手机验证码">
                                     <div class="input-group-btn">
                                        <button id="prodPwdReleaseBtn" class="btn btn-green borderl" type="button">校验
                                        </button>
                                        </div>
                                   	</div>
                                </div>
                            </div>

                            <!-- 										<div class="form-group col-md-12"> -->
                            <!-- 											<label class="col-md-2 control-label lablep"></label> -->
                            <!-- 											<div class="col-md-10"> -->
                            <!-- 												<button id="prodPwdBtn" class="btn btn-primary" type="submit">校验</button> -->
                            <!-- 											</div> -->
                            <!-- 										</div> -->
                        </form>
                    </div>
                </div>
                #end
                #end
                #end

            </div>
        </div>
    </div>
</div>