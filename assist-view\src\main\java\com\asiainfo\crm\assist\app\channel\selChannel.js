(function(vita){
	var selChannel = vita.Backbone.BizView.extend({
		events : {
			"click #searchBtn" : "_queryList",
			"click #searchDel": "_searchParamsDel",
			"click .close" : "_closePage",
			"click #submit " : "_sumbitData",
			"click .pagination li" : "_pageClick"
		},
		_initialize : function() {
			debugger;
			var widget = this,
			element = $(widget.el);
			var widget = this,
			element = $(widget.el);
		//	gSession = widget.gSession;
			var page = widget.require("pageUtil");
			widget.pageInfo = page.new({
				pageSize : widget.global.pageSize
			});
			/**
			 * 设置之前已经被选择维度，进行过滤
			 */
			var data = element.data("data");
			widget.model.set("regionId", data.regionId);
			//初始化查询
			widget._queryList();
		},
		_closePage : function(){
			var widget = this,
			element = $(widget.el);
			var dialog = element.closest("[id=" + widget.widgetName + "Dialog]");
			if (dialog.length) {
				dialog.dialog("close");
			}
		},
		global : {
			pageSize : 20 //每页记录数
		},
		/**
		 * 获取查询条件参数对象
		 */
		_getConds : function() {
			var widget = this,
				element = $(widget.el);
	//			gSession = widget.gSession;
			var param = {
					"pageInfo" : {
						"pageIndex" : widget.pageInfo.getCurPage(),
						"pageSize" : widget.global.pageSize
					}
				};
			
			var orgName = widget.model.get("orgName");
			debugger;
			var soUtil = widget.require("soUtil");
			var isNullOrEmpty = soUtil.isNullOrEmpty(orgName);
			if (!isNullOrEmpty) {
				param.channelName = orgName;
			}
			
			var regionId = widget.model.get("regionId");
			isNullOrEmpty = soUtil.isNullOrEmpty(regionId);
			if (!isNullOrEmpty) {
				param.regionId = regionId;
			}
			
			return param;
		},
		/**
		 * 清空查询条件
		 */
		_searchParamsDel : function() {
			var widget = this,
				element = $(widget.el);
	//			gSession = widget.gSession;
			widget.model.set("orgName","");
		},
		_queryList : function() {
			var widget = this,
				element = $(widget.el);
			var param = widget._getConds();
			widget.refreshPart("qryList", param, ".table-hover",
				"items", function(res) {
					var totalNumber = $(res).find("input").eq(0).data("totalNumber");
					if (parseInt(totalNumber) > 0) {
						widget.pageInfo.setPageControlPart(element, 'showPageInfo', totalNumber);
					}
				}, {
					mask : true
				});
		},
		setDatas : function(isBatchSelect){
			debugger;
			var widget = this, element = $(widget.el);
			widget.model.set("isBatchSelect",isBatchSelect);
		},
		_sumbitData : function (e){
			var widget = this,
				element = $(widget.el);
			validate = false;
			debugger;
			//该字段是否为多选类型
			var isBatchSelect = widget.model.get("isBatchSelect");
			var checkUseredList = element.find('#items').find('input:checked');
			var idArray = [];
			var orgs = [];
			$.each(checkUseredList,function(i,item){
				idArray.push($(item).data('data').orgId);
				orgs.push($(item).data('data'));
			});
			if(idArray.length == 0){
				widget.popup("请选择渠道！");
				return false;
			}
			//如果不是多选，判断单选
			if(isBatchSelect != "YES" && idArray.length > 1){
				widget.popup("只能选择一个渠道!");
				return false;
			}
			validate = true;
			if(isBatchSelect != "YES"){
				widget.model.set({
					validate : validate,
					orgId : orgs[0].orgId,
					channelName : orgs[0].channelName
				});
			}else{
				widget.model.set({
					validate : validate,
					orgs : orgs
				});
			}
			widget._closePage();
			return false;
		},
		getValue : function() {
			var widget = this;
			var valueJSON = widget.model.toJSON();
			return valueJSON;
		},
		_pageClick : function(e) {
			var widget = this;
			var pageIndex = widget.pageInfo.getPageIndex(e);
			widget._queryList();
			return false;
		}
	});
	 vita.widget.register("selChannel", selChannel, true); 
})(window.vita);
