package com.asiainfo.crm.assist.app.order;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderSMO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/5/16 15:52
 * @Description TODO
 **/

@Component("vita.paramDetail")
public class ParamDetail extends AbstractComponent{
    @Autowired
    private IOrderSMO orderSMO;

    @Override
    public Map achieveData(Object... objects) throws Exception {
        String csNbr = String.valueOf(objects[0]);
        String iUId = String.valueOf(objects[1]);
        String resultJson = orderSMO.qryTraceHisDetail(csNbr,iUId);
        HashMap<String,Object> map = (HashMap<String, Object>) jsonConverter.toBean(resultJson,Map.class);
        HashMap<String,Object> resultMap = (HashMap<String, Object>) map.get("resultObject");
        Map options = new HashMap();
        options.put("afterChain",resultMap.get("afterChain"));
        options.put("beforeChain",resultMap.get("beforeChain"));
        options.put("invokeParam",resultMap.get("invokeParam"));
        options.put("chainResult",resultMap.get("chainResult"));
        return options;
    }
}
