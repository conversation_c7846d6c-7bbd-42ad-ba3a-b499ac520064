<div data-widget="remoteReplCard" style="overflow: auto; height: 100%;">

	<div class="box-maincont">
		<div class="homenofood">
			<div class="page-nav">
			</div>
			<div class="page_main">
				<!--填单start-->
				<div class="col-lg-12">
					<div class="box-item">
						<div class="container-fluid row">
							<div class="form_title">
								<div>
									<i class="bot"></i>查询条件
								</div>
							</div>
							<div class="wmin_content row ">
								<form class=" form-bordered">

									<div class="form-group col-md-4">
										<label class="col-md-5 control-label lablep"><span class="textcolorred">*</span> 证件类型</label>
										<div class="col-md-5">
											<select id="certType" class="form-control" >
												<option value="1" selected="selected" certTypeName="身份证">身份证</option>
											</select>
										</div>
									</div>
									<div class="form-group col-md-6">
										<label class="col-md-5 control-label lablep"> <span class="textcolorred">*</span>证件号码</label>
										<div class="col-md-5">
											<input id="certNum" type="text" class="form-control" placeholder="" value="342222198909126090">
										</div>
										<div class="col-md-3">
											<button id="btn-readCert" type="button" class="btn btn-primary">读卡</button>
											<button id="btn-qryCrtfctNmbr" type="button" class="btn btn-primary">查询</button>
										</div>
									</div>
									<div class="form-group col-md-4">
										<label class="col-md-5 control-label lablep"> <span class="textcolorred">*</span> 证件姓名</label>
										<div class="col-md-5">
											<input id="certName" type="text" class="form-control"  placeholder="" value="张丽峰">
										</div>
									</div>
									<div class="form-group col-md-6">
										<label class="col-md-5 control-label lablep"> <span class="textcolorred">*</span>证件地址</label>
										<div class="col-md-5">
											<input id="certAddress" type="text" class="form-control"  placeholder="" value="内蒙古和林格尔县公喇嘛乡郭家营村一队18号3户">
										</div>
										<div class="col-md-3"></div>
									</div>
								</form>
								<form id="replCardOrder"></form>
								<div stye="width:0px;hright:0px;" id="replCardOrderDiv">
								<iframe id="replCardOrderIfm" name="replCardOrderIfm" style="with:0px;height:0px;border:0px"></iframe>
								</div>
							</div>
						</div>


						<div class="container-fluid row">
							<div class="form_title">
								<div>
									<i class="bot"></i>查询结果
								</div>
							</div>
							<div class="container-fluid row" id="certNumTbody">
								<div class="wmin_content" id="certNumFiveListR">
									<div class="col-lg-12 mart10" id="certNumFiveList">
										<p class="vita-data">{"data": $options}</p>
										<table class="table table-hover">
											<thead>
												<tr id="certNumTable" >
													<th>省份</th>
													<th>地区</th>
													<th>号码</th>
													<th>关系类型</th>
													<th>操作</th>
												</tr>
											</thead>
											<tbody>
											#if($options  && $options.crtfctNmbrList)
												#foreach($crtfctNmbr in $options.crtfctNmbrList)
												<tr>
													<td>$crtfctNmbr.lanId</td>
													<td>$crtfctNmbr.lanId</td>
													<td>$crtfctNmbr.phoneNum</td>
													<td>$crtfctNmbr.systemFlagName</td>
													<td>
														<button name="btn-replCard" type="button" class="btn btn-primary">异地补换卡</button>
														<p class="vita-data">{"lanId":"$!crtfctNmbr.lanId","phoneNum":"$!crtfctNmbr.phoneNum"}</p>
													</td>
												</tr>

												#end
											#else
											<tr>
												<td align='center' colspan='7'>未查询到数据！
												<td>
											</tr>
											#end
											</tbody>
										</table>
									</div>
								</div>
								<!--翻页start-->
								<div id="showPageInfo" class="page-box"></div>
								#if($options.totalNumber && $options.totalNumber != "null")
								<p class="vita-data">{"totalNumber":$options.totalNumber}</p>
								#end
								<!--翻页end -->
							</div>
						</div>
					</div>
				</div>
			</div>
			<!--填单end-->
		</div>
	</div>
</div>

