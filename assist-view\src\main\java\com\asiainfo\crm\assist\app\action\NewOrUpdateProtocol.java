package com.asiainfo.crm.assist.app.action;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import net.sf.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IReceiptIvoiceQuerySMO;
import com.asiainfo.crm.service.intf.IReceiptQuerySMO;
import com.asiainfo.crm.service.intf.ISoQuerySMO;

/**
 * Created by wenhy on 2018/1/18.
 */
@Component("vita.newOrUpdateProtocol")
public class NewOrUpdateProtocol extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(NewOrUpdateProtocol.class);
    
    @Autowired
	private ISoQuerySMO soQuerySMO;
    
    @Autowired
    private IReceiptQuerySMO receiptQuerySMO;
	@Autowired
	private IReceiptIvoiceQuerySMO iReceiptIvoiceQuerySMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
    	Map options = new HashMap();
		String jsonString = params[0].toString();
		Map map = jsonConverter.toBean(jsonString, Map.class);
		if (!map.containsKey("str")) {
			return null;
		}
		options.put("str", map.get("str").toString());
		options.put("name", map.get("name").toString());
        return options;
    }

    /**
     * 更新协议模板
     */
    public String saveProtocol(String jsonString) throws Exception{
    	String ret = "";
    	if (MDA.ASSIST_RECEIPT_CALL_ADDRESS) {
    		ret = iReceiptIvoiceQuerySMO.saveProtocolTemplet(jsonString);
    	}else{
    		ret = receiptQuerySMO.saveProtocolTemplet(jsonString);
    	}
    	JSONObject jsonObject = JSONObject.fromObject(ret);
        return jsonObject.toString();
    }
    
    /**
     * 查询协议内容
     * @param str
     * @return
     * @throws IOException
     */
    public Map showProtocol(String str) throws IOException {
		Map options = new HashMap<>();
		String result = "";
		if (MDA.ASSIST_RECEIPT_CALL_ADDRESS) {
			result = iReceiptIvoiceQuerySMO.qryProtocolAndOrderVo(str);
		}else{
			result = soQuerySMO.qryProtocolAndOrderVo(str);
		}
				
		Map resMap = jsonConverter.toBean(result, Map.class);
		if (resMap.containsKey("resultObject")) {
			Map resultObject = (Map) resMap.get("resultObject");
			if (resultObject.containsKey("protocolTemplet")) {
				Map protocolTemplet = (Map) resultObject.get("protocolTemplet");
				if (protocolTemplet.containsKey("content")) {
					String content = (String) protocolTemplet.get("content");
					String text = content.substring(38);
					options.put("result", text);
					return options;
				}
			}
		}
		options.put("result", null);
		return options;
	}
}
