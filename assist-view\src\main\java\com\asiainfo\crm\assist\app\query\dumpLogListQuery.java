package com.asiainfo.crm.assist.app.query;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;

/**
 * Created on 2019/8/14
 */
@Component("vita.dumpLogListQuery")
public class dumpLogListQuery extends AbstractComponent {

	   private static final Logger logger = LoggerFactory.getLogger(QryPrepareOrderInfo.class);

	    @Autowired
	    private IOrderQuerySMO orderQuerySMO;

	    @Override
	    public Map achieveData(Object... params) throws Exception {
	    	return null;
	    }

	    /**
	     * 转储日志查询
	     */
	    public Map dumpLogListQuery(String json) throws Exception {
	    	String dumpLogList = orderQuerySMO.getOrderCTransLogByCond(json);
	    	List resultObjectMap =  (List) resolveResult(dumpLogList);
	        Map options = new HashMap();
	        options.put("dumpLogList", resultObjectMap);
	        return options;
	    }
	    
}
