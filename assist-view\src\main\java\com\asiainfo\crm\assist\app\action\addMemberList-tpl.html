<div data-widget="addMemberList" style="height:100%">
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--�start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row" id="addListDiv">
                                <div class="form_title">
                                    <div><i class="bot"></i>待捆绑号码详情</div>
                                </div>
                                <div class="wmin_content row" >
                                   <div class="col-lg-12 mart10" >
                                        <table class="table table-hover" >
                                            <thead>
                                            <tr>
                                                <th>接入号</th>
                                                <th>账户合同号</th>
                                                <th>绑定号码地区</th>
                                                <th>账户客户名称</th>
                                                <th>账户当前状态</th>
                                                <th id = "control">操作</th>
                                            </tr>
                                            </thead>
                                            <tbody id="member">
                                            #if($options != "null" && $options.resultCode && $options.resultCode != "null" &&
	                                            $options.resultCode == 0)
	                                            #if($options.orderInfos && $options.orderInfos != "null" &&
	                                            $options.orderInfos.size() > 0)
	                                            #foreach($orderInfo in $options.orderInfos)
	                                            <tr>
	                                                <td>$!{orderInfo.bindNumber}</td>
	                           						<td>$!{orderInfo.acctCd}</td>
	                                                <td>$!{orderInfo.regionName}</td>
	                                                <td>$!{orderInfo.acctName}</td>
	                                                <td>$!{orderInfo.currentSate}</td>
	                                                <td>
	                                                  <button id ='bindCheck' type="button" class="btn btn-primary btn-sm okbutt">
	                                                   <p style ="display: none"class="vita-data">{"orderInfo":$!{orderInfo}}</p>绑定
	                                                   </button>
	                                                  </td>
	                                            </tr>
	                                            #end
	                                            #elseif($options != "null" && $options.orderInfos && $options.orderInfos != "null" &&
	                                            $options.orderInfos.size() == 0)
	                                                <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
	                                            #end
	                                         #else
	                                         	#if($options != "null" && $options.resultDesc && $options.resultDesc != "null")
	                                        	 <tr><td align='center' colspan='8'>$options.resultDesc<td></tr>
	                                        	#end
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                         <!--翻页end -->
                                    </div>
                                </div>
                            </div>
							<!--  
                            <div class="container-fluid row" id="delListDiv">
                                 <div class="calctitle">
									<div class="toolr">
										<button id ='bindCheck' type="button" class="btn btn-primary btn-sm okbutt">确认</button>
										<button type="button" class="close" data-dismiss="modal" aria-label="Close">
											<span aria-hidden="true">×</span>
										</button>
									</div>
								</div>
                            </div>
                            -->
                        </div>
                    </div>
                   <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
