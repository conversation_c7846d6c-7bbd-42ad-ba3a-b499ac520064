/**
 * Created by Administrator on 2017-7-26.
 */
(function (vita) {
    var configProdlnst = vita.Backbone.BizView.extend({
        events: {
            "click #btn-queryCust": "_userQueryCust",//查询
            "click #regionIdBtn": "_chooseArea",//地区查询
            "click #btn-addCust": "_addRelationship",//新增
            "click #failureCorrelation": "_failureCorrelation",//失效操作
            // "click #m_file": "_selectExcel",//
            "click #route_input": "_fileOnChange",//
            "click #button": "_button",
            "click #btn-querys": "_btnQquerys",
            "click #upLoadTemplet": "_upLoadTemplet"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el),data = element.data();
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
            //获取MDA里配置的值
            element.find("#statusType option:eq(3)").attr("value",data.itvId);
            $("#accNumTypeDiv").hide();
        },
        global: {
            pageIndex: 1,
            pageSize: 3,
            preset: "date",
            chooseArea: "../comm/chooseArea",
            isChanged : false,
        },
        _fileOnChange:function () {
            isChanged = true;
            var filename = $(".m_file").get(0).files[0].name;
            $('#m_file span').html(filename);
        },
        // _selectExcel:function () {
        //     return $('.m_file').click();
        // },
        _button:function () {
            var widget = this,element = $(widget.el);
            var gsession = widget.gSession;
            var m_fileVal = $('.m_file').val();
            if(m_fileVal=="") {
                alert('请选择文件！');
                return ;
            }
            if('-1' == m_fileVal.indexOf('.xls')) {
                alert('只能上传Excel文件！');
                return ;
            }
            /*var bacthRemark = $.trim($('#batchAddRemark').val());
            if(bacthRemark==null||bacthRemark=="") {
                alert("批量备注不能为空");
                return;
            }*/
            var regionId = $("#regionId").attr("value");
            if (regionId == null || regionId.trim() == ""){
                widget.popup("地区不能为空")
                return;
            }
            var params = {
                prodName:$("#status").val(),//产品规格
                prodId:379,//产品规格id
                statusStaff:gsession.staffId, //员工编号
                regionName:$("#regionId").val(),//地区id
                regionId:$("#regionId").attr("value"),                      //地区名
                // remark:$("#batchAddRemark").val(), //备注
                typeName:"产品"
                // prodInstId:$("#batchAddBoType").find("option:selected").attr("prodInstId"),
                // serviceOfferName:$("#batchAddBoType").val(),//动作名称
                // serviceOfferId:$("#batchAddBoType").find("option:selected").attr("serviceOfferId"),//$("#addBoType").attr("id"),        //动作id
            }
            $(".route_input").val(JSON.stringify(params));
            var url ="../action/uploadConfigExcels";
            $('#a_form').ajaxSubmit({
                type:"post",url:url, scriptCharset: 'utf-8',
                success:function (date) {
                    var jsonData = JSON.parse(date);
                   if(jsonData.resultObject.resultCode == 0){
                       var dataInfo = jsonData.resultObject.configDataInfoVo
                       localStorage.setItem('prodServLimitCpgId',dataInfo.prodServLimitCpgId);
                       alert('操作批次号：'+dataInfo.prodServLimitCpgId+' 成功'+
                           dataInfo.successSum+'个; 失败'+dataInfo.errorSum+'个');
                   } else if(jsonData.resultObject.resultCode == -1){
                       alert('一次上传不能超过500条');
                     }else {
                       alert('上传失败，请检查内容是否合法');
                   }
                }
            })
        },
        _btnQquerys:function(){
            var widget = this;
            var gsession = widget.gSession;
            var url = "../action/pcExcelPrintConfg";
            var marking = localStorage.getItem('prodServLimitCpgId');
            var params = {
                serviceOfferName: marking//唯一标识
            }
            if(!marking){
                widget.popup("请先上传文件再导出结果")
                return;
            }
            debugger;
           // var iii = url+"?jsonStr="+JSON.stringify(params)
            location.href=url+"?jsonStr="+marking;
        },

        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _userQueryCust:function () {
            debugger;
            var widget = this,element = $(widget.el);
            var gsession = widget.gSession;
            var userNum = $("#userNum").val();
            var regionId = $("#regionId").attr("value");
            if (userNum == null || userNum.trim() == ""){
                widget.popup("计费号信息不能为空");
                return;
            }
            if (regionId == null || regionId.trim() == ""){
                widget.popup("地区不能为空")
                return;
            }
            var params = {
                useNnumber:userNum,
                sadffID:regionId,
                accNum:userNum,
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            }
            debugger;
            widget.refreshPart("configProdlnstQuery", JSON.stringify(params), "#configProdlnstResult", function (re) {
                debugger;
                var r = $(re), paging = this.require("paging");
                var e = paging.new({
                    recordNumber : widget.global.pageSize,
                    total : r.find("#showPageInfo").data("totalNumber"),
                    pageIndex : widget.global.pageIndex,
                    callback : function (pageIndex, pageSize) {
                        debugger;
                        var params = {
                            useNnumber:userNum,
                            sadffID:regionId,
                            pageInfo: {
                                pageIndex: pageIndex,
                                pageSize: widget.global.pageSize
                            }
                        };
                        widget.refreshPart("configProdlnstQuery", JSON.stringify(params), "#configProdlnstList");

                    }
                });
                debugger;
                //var totalNum = r.find("#showPageInfo").data("totalNumber");
                // if(r.find("#showPageInfo").data("totalNumber") > 0) {
                // }
                r.find("#showPageInfo").append(e.getElement());
                // $("#configNum").val($("#userNum").val())
                // addBoType
            }, { async: false });
        },
        _addRelationship: function () {
            var widget = this,element = $(widget.el);
            var gsession = widget.gSession;
            if($("#configNum").val() == null || $("#configNum").val().trim() == "" ){
                widget.popup("计费号信息不能为空");
                return;
            }
            if($("#remarks").val() == null || $("#remarks").val().trim() == "" ){
                widget.popup("备注不能为空");
                return;
            }
            debugger;
            var params = {
                // prodName:$("#status").val(),//产品规格
                prodName:$("#statusType").find("option:selected").text(),
                prodId:$("#statusType").val(),//产品规格id
                statusStaff:gsession.staffId, //员工编号
                regionName:$("#regionId").val(),//地区id
                regionId:$("#regionId").attr("value"),                      //地区名
                remark:$("#remarks").val(), //备注
                accNum:$("#configNum").val(),//用户号码
                serviceOfferName:$("#addBoType").val(),//动作名称
                serviceOfferId :$("#addBoType").find("option:selected").attr("prodInstId"),//$("#addBoType").attr("id"),        //动作id
                //prodInstId  :$("#addBoType").find("option:selected").attr("prodInstId"),//$("#addBoType").attr("id"),        //动作id
                typeName:"产品",
                state:1,
                ownerCustName:$("#projectDetailsname").val()
            }
            widget.callService("addProdlnst", JSON.stringify(params), function(res) {
                if(res.result==0){
                    widget.popup("新增成功");
                    $("#btn-queryCust").click();
                    return;
                }else{
                    widget.popup("新增失败，该产品的接入号码不存在");
                }

            }, {
                async : false
            });
        },
        _failureCorrelation:function () {
            var widget = this,element = $(widget.el);
            var gsession = widget.gSession;
            debugger;
            var markDdiscrete = element.find('table input[name="checkbox"]:checked');
            if (markDdiscrete.length == 0){
                widget.popup("请选择待失效操作关系");
                return;
            }
            if (markDdiscrete.length > 1){
                widget.popup("只能选择一条待失效操作");
                return;
            }
            var $markDdiscrete = markDdiscrete[0];
            var $tr = $($markDdiscrete).closest("tr");
            var params = {
                prodSerLinitCfgId:$($tr.find("[link=customerConfig]")).data("prodServLimitCpgId"),

                staffId :gsession.staffId
            }
                widget.callService("delectProdlnst", params, function() {
                        widget.popup("成功")
                    $("#btn-queryCust").click()
            },{ async : false })
        },
        _upLoadTemplet: function () {
            var widget = this;
            var gsession = widget.gSession;
            var url = "../action/upLoadTemplet";
            location.href=url;
        }
    });
    vita.widget.register("configProdlnst", configProdlnst, true);
})(window.vita);