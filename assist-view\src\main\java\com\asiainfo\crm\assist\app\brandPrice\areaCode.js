(function(vita){
    var areaCode = vita.Backbone.BizView.extend({
        events : {
            "click #qryCommonLogSearchBtn" : "_qryAreaCodeList",
            "click #searchDel": "_searchParamsDel",
            "click [name='moduleRadio'] " : "_clickRadio",
        },
        _initialize : function() {
            var widget = this,
                element = $(widget.el);
            data = element.data("data");
            widget.model.set(data);
            //初始化查询
            widget._qryAreaCodeList();
        },
        global : {
            pageIndex: 1,
            pageSize: 5, //每页记录数
        },
        _clickRadio : function(e){
            var widget = this,
                element = $(widget.el);
            var radio = $(e.target).closest('input');
            var data = radio.data('data');
            widget.model.set("selectedAreaCode",data);
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        /**
         * 获取查询条件参数对象
         */
        _getConds : function(pageIndex) {
            var widget = this,
                element = $(widget.el);
            gSession = widget.gSession;
            var param = {
                "pageInfo" : {
                    "pageIndex" : pageIndex,
                    "pageSize" : widget.global.pageSize
                }
            };
            var areaCode = widget.model.get("areaCode");
            if (!widget._isNullOrEmpty(areaCode)) {
                param.areaCode = areaCode;
            }
            return param;
        },
        _isNullOrEmpty: function (condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty;
        },
        /**
         * 清空查询条件
         */
        _searchParamsDel : function() {
            var widget = this,
                element = $(widget.el);
            widget.model.set("areaCode","");
            element.find("#areaCode").val("");
        },

        _qryAreaCodeList : function(e) {
            var widget = this, element = $(widget.el);
            var param = widget._getConds(widget.global.pageIndex);
            element.find('#showPageInfo').empty();
            widget.refreshPart("qryAreaCode", param, ".table-hover",
                "areaCodes", function(res) {
                    var paging = this.require("paging");
                    var totalNumber = $(res).find("input").eq(1).data("totalNumber");
                    if (parseInt(totalNumber) > 0) {
                        var e = paging.new({
                            recordNumber : widget.global.pageSize,
                            total : totalNumber,
                            pageIndex : widget.global.pageIndex,
                            callback : function(pageIndex){
                                //判断当前页
                                if(pageIndex == null || pageIndex == undefined || isNaN(pageIndex)){
                                    pageIndex = 1;
                                }
                                widget.model.set("pageIndex",pageIndex);
                                widget.refreshPart("qryAreaCode", widget._getConds(pageIndex), ".table-hover");
                            }
                        });
                        element.find('#showPageInfo').append(e.getElement());
                    }
                }, {
                    mask : true
                });
        },
    });
    vita.widget.register("areaCode", areaCode, true);
})(window.vita);
