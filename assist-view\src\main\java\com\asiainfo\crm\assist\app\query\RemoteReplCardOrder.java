package com.asiainfo.crm.assist.app.query;

import com.al.common.utils.DateUtil;
import com.al.common.utils.StringUtil;
import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.Constant;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.ICertNumFiveQuerySMO;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.service.intf.IMenuCollectionSMO;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.asiainfo.crm.service.intf.IStaffInfoQuerySMO;
import com.asiainfo.crm.util.ListUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

/**
 * Created by fengjie on 2018/6/22.
 */
@Component("vita.remoteReplCardOrder")
public class RemoteReplCardOrder extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(RemoteReplCardOrder.class);

    @Autowired
    private IProdInstSMO prodInstSMO;
    @Autowired
    private IStaffInfoQuerySMO staffInfoQuerySMO;
    @Autowired
    private ICustMultiSMO custMultiSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map<String,Object> options = Maps.newHashMap();
        Map<String, Object> paramMap = (Map<String, Object>) params[0];
        Map<String, Object> scope = Maps.newHashMap();
        Map<String, Object> param = Maps.newHashMap();
        List<Map<String, Object>> scopes = new ArrayList<>();
        scope.put("scope", "prodInst");
        scope.put("pageIndex", 1);
        scope.put("pageSize", 10);
        scopes.add(scope);
        param.put("scopeInfos", scopes);
        param.put("accNum",MapUtils.getString(paramMap,"replPhoneNum"));

        String json = this.jsonConverter.toJson(param);
        String prodInst = this.prodInstSMO.qryAccProdInstDetailLocal(json);
        Map<String, ?> instObjectMap = (Map<String, Object>) resolveResult(prodInst);

        String busiType = "4G";
        if (MapUtils.isNotEmpty(instObjectMap)) {
            Map<String, ?> instInfo = (Map<String, ?>) MapUtils.getObject(instObjectMap, "prodInstDetail");
            String prodInstId = MapUtils.getString(instInfo, "prodInstId");
            param = Maps.newHashMap();
            param.put("prodInstId",prodInstId);
            json = this.jsonConverter.toJson(param);
            prodInst = this.prodInstSMO.checkProdIsFourGeneration(json);
            instObjectMap = (Map<String, Object>) resolveResult(prodInst);
            if (MapUtils.isNotEmpty(instObjectMap)) {
                json = MapUtils.getString(instObjectMap, "isFourGeneration");
                busiType = Constant.LOGIC_STR_Y.equals(json)?"4G":"3G";
            }
        }
        options.put("busiType",busiType);
        return options;
    }

    public Map queryStaffNumber(String jsonString) throws Exception {
        Map options = new HashMap();
        options.put("resultCode", "-1");
        String staffString = staffInfoQuerySMO.checkCosponsor(jsonString);
        Map resultObject = (Map) resolveResult(staffString);
        String isCosponsor = (String)resultObject.get("isCosponsor");
        options.put("isCosponsor", isCosponsor);
        Map staffInfo = (Map) resultObject.get("staffInfo");

        if (staffInfo != null) {
            this.queryChannelInfo(staffInfo);
            options.put("staffInfo", staffInfo);
            options.put("resultCode", "0");
        }
        return options;
    }

    public void queryChannelInfo(Map staffInfo) throws Exception {
        // TODO组织权限中心未提供接口营销权限临时处理
//		staffInfo.put("soOrSale", "1");
        List channels = (List) staffInfo.get("channels");
        if (channels != null && !channels.isEmpty()) {
            Map channel = (Map) channels.get(0);
            String orgId = MapUtils.getString(channel, "orgId");
            if (!StringUtil.isEmpty(orgId)) {
                Map param = new HashMap();
                param.put("orgId", orgId);
                String channelParam = jsonConverter.toJson(param);
                // String resString =
                // staffSMO.queryChannelInfo(channelParam);
                String resString = "{\n" + " \"resultCode\":\"0\",\n" + " \"resultMsg\":\"处理成功!\",\n"
                        + " \"resultObject\":{\n" + "  \"channelInfo\":{\n" + "   \"isNbygType\":\"0\",\n"
                        + "   \"isShZxProp\":\"0\",\n" + "   \"isXyYytType\":\"0\",\n" + "   \"soOrSale\":\"1\",\n"
                        + "   \"isZqShZxType\":\"0\",\n" + "   \"isZqYytType\":\"0\",\n"
                        + "   \"isZqZyZxType\":\"0\",\n" + "   \"isZxForm\":\"0\",\n" + "   \"isZyZxProp\":\"1\"\n"
                        + "  }\n" + " }\n" + "}";
                Map resultObject = (Map) resolveResult(resString);
                Map channelInfo = (Map) resultObject.get("channelInfo");
                staffInfo.putAll(channelInfo);
            }
        }
    }

    /**
     * 异地补办卡提交
     * */
    public Map remoteReplCardSubmit(String jsonString) throws Exception {
        Map options = new HashMap();
        options.put("retCode", "-1");
        Map params = new HashMap();
        Map reqParams = jsonConverter.toBean(jsonString, Map.class);
        Map orderPymntMap = (Map)reqParams.get("orderPymnt");
        Map custOrderInfo = (Map)reqParams.get("custOrderInfo");
        params.put("regionId", custOrderInfo.get("commonRegionId"));
        reqParams.remove("orderPymnt");
        changeAttrValue(reqParams);
        params.put("nonlocalReplaceUIMReq", reqParams);
        //调用异地补办卡提交
        String respStr = custMultiSMO.nonlocalReplaceUIM(jsonConverter.toJson(params));
        Map resultObject = (Map) resolveResult(respStr);
        if (resultObject instanceof  Map && resultObject.containsKey("nonlocalReplaceUIMRes")) {
            Map uimResMap = (Map)resultObject.get("nonlocalReplaceUIMRes");
            orderPymntMap.put("preHandleFlag", AssistMDA.REPL_CARD_SUBMIT_MAP.get("preHandleFlag"));
            orderPymntMap.put("statusCd", AssistMDA.REPL_CARD_SUBMIT_MAP.get("statusCd"));
            orderPymntMap.put("custOrderId", uimResMap.get("custOrderId"));
            if(!orderPymntMap.containsKey("extCustOrderId")){
            	orderPymntMap.put("extCustOrderId", uimResMap.get("custSoNumber"));
            }
            if(!orderPymntMap.containsKey("acceptTime")){
            	orderPymntMap.put("acceptTime", DateUtil.getNowDefault());
            }
            //根据接入号查询客户信息
            Map custParams = new HashMap();
            custParams.put("queryType", "accNbr");
            custParams.put("value", orderPymntMap.get("accNum"));
            custParams.put("glbSessionId", orderPymntMap.get("glbSessionId"));
            orderPymntMap.remove("accNum");
            orderPymntMap.remove("glbSessionId");
            String resString = custMultiSMO.qryCustomerListForMulti(jsonConverter.toJson(custParams));
            Map<String, List> custResultObject = (Map) resolveResult(resString);
            List<Map> customers = custResultObject.get("customers");
            if(customers != null && !customers.isEmpty()){
            	orderPymntMap.put("custId", customers.get(0).get("custId"));
            }
            
            //异地补办卡订单缴费通知
            String orderPymntStr= custMultiSMO.orderPaymentNotice(jsonConverter.toJson(orderPymntMap));
            Map orderPymntResultObject = (Map) resolveResult(orderPymntStr);
            if (resultObject instanceof  Map && orderPymntResultObject.containsKey("orderPymntNtcRes")) {
                options.put("retCode", "0");
                options.put("retObj", (Map)orderPymntResultObject.get("orderPymntNtcRes"));
            }
        }
        
        return options;
    }
    
    /**
     * 根据前台传的参数，转换成attrInfos
     * @param reqParams
     */
    private void changeAttrValue(Map reqParams){
    	Map inputInfo = (Map)reqParams.get("inputInfo");
    	if(inputInfo!=null){
    		List<Map<String,Object>> AttrInfoList = new ArrayList<Map<String,Object>>();
	    	Set<Entry<String, Object>> inputInfoEntries = inputInfo.entrySet();
	    	Set<Entry<String, String>> attrEntries = AssistMDA.REPL_CARD_ATTR_MAP.entrySet();
	    	if(inputInfo!=null&&inputInfoEntries!=null){
		    	for (Entry<String, Object> entry : inputInfoEntries) {
		    		Map<String, Object> attrInfo = new HashMap<String,Object>();
		    		for (Entry<String, String> attrEntry : attrEntries) {
		    			if(attrEntry.getKey().equals(entry.getKey())&&entry.getValue()!=""&&entry.getValue()!=null){
		    				attrInfo.put("AttrSpecId", AssistMDA.REPL_CARD_ATTR_MAP.get(entry.getKey()));
			    			attrInfo.put("AttrValue", entry.getValue());
		    			}
		    		}
		    		if(attrInfo.containsKey("AttrSpecId")){
		    			AttrInfoList.add(attrInfo);
		    		}
		    	}
	    	}
	    	reqParams.remove("inputInfo");
	    	Map prodInfo = (Map)reqParams.get("prodInfo");
	    	Map mktResouceInfo = (Map)prodInfo.get("mktResouceInfo");
	    	mktResouceInfo.put("mktResCd", AssistMDA.REPL_CARD_SUBMIT_MAP.get("mktResCd"));
	    	mktResouceInfo.put("attrInfos", AttrInfoList);
    	}
    }
    /**
     * 异地补办卡订单缴费通知
     * */
    public Map orderPaymentNotice(String jsonString) throws Exception {
        Map options = new HashMap();
        options.put("retCode", "-1");
        String respStr = custMultiSMO.orderPaymentNotice(jsonString);
        Map resultObject = (Map) resolveResult(respStr);
        if (resultObject instanceof  Map && resultObject.containsKey("orderPymntNtcRes")) {
            options.put("retCode", "0");
            options.put("retObj", (Map)resultObject.get("orderPymntNtcRes"));
        }
        return options;
    }

}
