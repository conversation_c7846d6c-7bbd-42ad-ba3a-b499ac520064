<div data-widget="configDiscreateValueQuery" style="height: 100%">
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="wmin_content row">
                                    <div class="form-group col-md-4 ">
                                        <label class="col-md-5 control-label lablep">
                                            地 区：
                                        </label>
                                        <div class="col-md-7">
                                            <div class="input-group">
                                                <input id="regionId" type="text" value="$options.managerAreaId"
                                                       class="form-control" placeholder="" readonly="readonly">
                                                <input id="regionIds" type="hidden" value="$options.staffLanId">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label class="col-md-5 control-label lablep">状 态:</label>
                                        <div class="col-md-7">
                                            <select id="status" class="form-control">
                                                <option value="2">所有状态</option>
                                                <option value="1000">在用</option>
                                                <option value="1200">停用</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <div class="col-md-12 searchbutt_r" align="center">
                                            <label>&nbsp&nbsp&nbsp</label>
                                            <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="container-fluid row">
                                <div class="box-item">
                                    <a id="newDisValue" class="btn btn-gray btn-outline"><i
                                            class="glyphicon fa-details text18"></i>
                                        新增项目
                                    </a>
                                    <a id="changeDisValue" class="btn btn-gray btn-outline"> <i
                                            class="glyphicon fa-request text18"> </i>
                                        变更项目
                                    </a>
                                    <a id="upDisValue" class="btn btn-gray btn-outline"> <i
                                            class="glyphicon fa-request text18"> </i>
                                        上线项目
                                    </a>
                                    <a id="downDisValue" class="btn btn-gray btn-outline"> <i
                                            class="glyphicon fa-request text18"> </i>
                                        下线项目
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="container-fluid row">
                            <div class="form_title">
                                <!--<div><i class="bot"></i>查询结果</div>-->
                            </div>
                            <div class="wmin_content">
                                <div class="box-item">
                                <div class="wmin_content row">
                                    <div class="col-lg-12">
                                    </div>
                                    <div class="container-fluid row">
                                        <div class="col-lg-12 mart10" id="configDispersedListResult">
                                            <div class="col-lg-12 mart10" id="configDispersedList">
                                            <table class="table table-hover">
                                                <thead>
                                                <tr>
                                                    <th>选择</th>
                                                    <th>项目ID</th>
                                                    <th>项目名称</th>
                                                    <th>项目状态</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                #if($options != "null" && $options.configDispersedList &&
                                                $options.configDispersedList != "null" &&
                                                $options.configDispersedList.size() > 0)
                                                #foreach($configList in $options.configDispersedList)
                                                <tr>
                                                    <td><input id="c_accNum" type="radio" name="checkbox">
                                                        <a class="textcolorgreen" link="customerConfig"></a>
                                                        <p class="vita-data">{"attrValueId" :
                                                            $!configList.attrValueId}</p>
                                                    </td>
                                                    <td>$!{configList.attrValueId}</td>
                                                    <td>$!{configList.attrValueName}</td>
                                                    <td><a class="textcolorgreen" link="customerConfigs"></a>
                                                        <p class="vita-data">{"statusCd" : $!configList.statusCd}</p>
                                                        #if($configList.statusCd != "null" && $configList.statusCd ==
                                                        "1000")
                                                        在用
                                                        #else
                                                        失效
                                                        #end
                                                    </td>
                                                </tr>
                                                #end
                                                #elseif($options == "null" && $options.configDispersedList &&
                                                $options.configDispersedList == "null" &&
                                                $options.configDispersedList.size() == 0)
                                                <tr>
                                                    <td align='center' colspan='7'>未查询到数据！
                                                    <td>
                                                </tr>
                                                #end
                                                </tbody>
                                            </table>
                                            </div>
                                            <!--翻页start -->
                                            <div id="showPageInfo">
                                            </div>
                                            #if($options.totalNumber && $options.totalNumber != "null")
                                            <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                            #end
                                            <!--翻页end -->
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--填单end-->
            </div>
        </div>
    </div>
</div>
</div>
