<div data-widget="ICTInputItem" class="calcw_rightbox noneleft">
    <p class="vita-data">{
        "subItemCode":#if($options.subItemCode) "$options.subItemCode" #else "" #end
        }</p>
    <div class="calctitle">
        <div class="titlefont"><i class="bot"></i><strong class="text16">收入计划</strong></div>
        <div class="toolr">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close" id='btnClose'><span aria-hidden="true">×</span></button>
        </div>
    </div>
    <div class="calcw_rightbox noneleft">
        <div class="ibox">
            <div class="pad_tabtitle">
                <ul id="myTab" class="nav nav-tabs">
                    <li class="active" id="tab1"><a href="javascript:void(0)" data-toggle="tab" aria-expanded="true">周期性</a></li>
                    <li  id="tab2"><a href="javascript:void(0);" data-toggle="tab" aria-expanded="false">非周期性</a></li>

                </ul>
            </div>
        </div>
    <div class="calcw_rightcont" style="padding-bottom: 0px">
        <div id="myTabContent" class="tab-content height100" >
            <div id="content1" class="tab-pane fade in active" style="display: block">
                <div class="mincontent ">
                    <table class="table table-hover1" >
                        <thead>
                        <tr>
                            <th>子项目编码</th>
                            <th>产品收入项</th>
                            <th>销售品</th>
                            <th>是否网元</th>
                            <th>含税金额(元)</th>
                            <th>日期</th>
                            <th>状态</th>
                        </tr>
                        </thead>
                        <tbody >
                                #if($options != "null" && $options.ICTSubProjectDeail && $options.ICTSubProjectDeail != "null" && $options.ICTSubProjectDeail.size() > 0)
                                #foreach($contract in $options.ICTSubProjectDeail)
                                <tr >
                                <td>#if($contract.subItemCode && $contract.subItemCode != 'null')
                                    $!contract.subItemCode
                                    #end
                                    <p class="vita-data">{"totalNumber" : $options.totalNumber}</p>
                                </td>
                                <td>#if($contract.inputItemCode  && $contract.inputItemCode != 'null')
                                    $!contract.inputItemCode
                                    #end
                                </td>
                                <td>#if($contract.offerId && $contract.offerId != 'null')
                                    $!contract.offerId
                                    #end
                                </td>
                                <td>
                                    #if($contract.spTypeCd && $contract.spTypeCd != 'null')
                                    $!contract.spTypeCd
                                    #end
                                </td>
                                <td>#if($contract.confirmFee && $contract.confirmFee !='null')
                                    $!contract.confirmFee
                                    #end
                                </td>
                                <td>#if($contract.createDate && $contract.createDate != 'null')
                                    $!contract.createDate
                                    #end
                                </td>
                                <td>#if($contract.status && $contract.status != 'null')
                                    $!contract.status
                                    #end
                                </td>
                                </tr>
                            #end
                            #end
                        </tbody>
                    </table>
                    <div class="page-box" id="showPageInfo1">
                    </div>
                </div>
            </div>
            <div id="content2" class="tab-pane fade" style="display: block">
                <div class="mincontent ">
                    <table class="table table-hover2" >
                        <thead>
                        <tr>
                            <th>子项目编码</th>
                            <th>产品收入项</th>
                            <th>销售品</th>
                            <th>是否网元</th>
                            <th>含税金额(元)</th>
                            <th>开始日期</th>
                            <th>结束日期</th>
                            <th>状态</th>
                        </tr>
                        </thead>
                        <tbody >
                        #if($options != "null" && $options.ICTSubProjectDeail && $options.ICTSubProjectDeail != "null" && $options.ICTSubProjectDeail.size() > 0)
                        #foreach($contract in $options.ICTSubProjectDeail)
                        <tr >
                            <td>#if($contract.subItemCode != 'null')
                                $!contract.subItemCode
                                #end
                                <p class="vita-data">{"totalNumber" : $options.totalNumber}</p>
                            </td>
                            <td>#if($contract.inputItemCode != 'null')
                                $!contract.inputItemCode
                                #end
                            </td>
                            <td>#if($contract.offerId != 'null')
                                $!contract.offerId
                                #end
                            </td>
                            <td>
                                #if($contract.spTypeCd != 'null')
                                $!contract.spTypeCd
                                #end
                            </td>
                            <td>#if($contract.confirmFee !='null')
                                $!contract.confirmFee
                                #end
                            </td>
                            <td>#if($contract.beginDate != 'null')
                                $!contract.beginDate
                                #end
                            </td>
                            <td>#if($contract.endDate != 'null')
                                $!contract.endDate
                                #end
                            </td>
                            <td>#if($contract.status != 'null')
                                $!contract.status
                                #end
                            </td>
                        </tr>
                        #end
                        #end
                        </tbody>
                    </table>
                    <div class="page-box" id="showPageInfo2">
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
</div>