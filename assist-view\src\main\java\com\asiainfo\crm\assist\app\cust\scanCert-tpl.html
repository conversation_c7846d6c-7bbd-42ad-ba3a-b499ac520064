<script src="../bundles/vita/plugs/highmeterjs/base64.min.js"></script>
<script src="../bundles/vita/plugs/sha1.js"></script>
<div data-widget="scanCert" style="height:100%">
	<p class="vita-data">{"data":$options}</p>	
	<div class="calctitle">
        <div class="titlefont">
            #if($options && $options.useDeskTop=="0")
            <ul id="myTab" class="nav nav-tabs">
                <li class="active"><a href="#11" data-toggle="tab">读卡器读卡</a></li>
            </ul>
            #else
            <i class="fa-lightn glyphicon textcolorgreen text18 mar15"></i>智慧桌面读卡
            #end
        </div>
        <div class="toolr">            
            <button id="submitBtn" type="button" class="btn btn-primary btn-sm okbutt">确认</button>
            <button id="closeBtn" type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
        </div>
    </div>
       <div class="calcw_rightcont">

            <div id="myTabContent" class="tab-content">
                <div class=" mart15 tab-pane fade in active" id="11">
                    <div class=" col-md-12">
                        <span id="controlsIdentify">控件识别结果：未检测到控件。</span>
                        <a id="hdSrc" class="form_alink" style="display:none;">鸿达驱动下载  </a><a id="krSrc" class="form_alink" style="display:none;">卡尔驱动下载   </a><a id="ynSrc" class="form_alink" style="display:none;">因纳伟盛驱动下载</a><span id="loadInfo" class="mar15" style="display:none;">下载安装后，请重启浏览器</span><button id="scanCert" type="button" class="btn btn-primary btn-sm">读取</button>
                        <!-- <a class="form_alink">鸿达驱动下载</a><a class="form_alink">卡尔驱动下载</a><a class="form_alink">因纳伟盛驱动下载</a><span class="mar15">下载安装后，请重启浏览器</span><button id="scanCert" type="button" class="btn btn-primary btn-sm">读取</button> -->
                    </div>
                    <div class="mincontent ">
                        <form class="form-bordered">
                            <div class="form-group col-md-12">
                                <label class="col-md-2 control-label lablep">姓名</label>
                                <div class="col-md-10">
                                    <input id="Name" type="text" class="form-control" placeholder="" value="" readonly>
                                </div>
                            </div>
                    <div class="form-group col-md-12">
                        <label class="col-md-2 control-label lablep">证件号</label>
                        <div class="col-md-10">
                            <input id="CardNo" type="text" class="form-control" placeholder="" value="" readonly>
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label class="col-md-2 control-label lablep">性别</label>
                        <div class="col-md-10">
                            <input id="Sex" type="text" class="form-control" placeholder="" value="" readonly>
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label class="col-md-2 control-label lablep">民族</label>
                        <div class="col-md-10">
                            <input id="Nation" type="text" class="form-control" placeholder="" value="" readonly>
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label class="col-md-2 control-label lablep">出生年月</label>
                        <div class="col-md-10">
                            <input id="Born" type="text" class="form-control" placeholder="" readonly>
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label class="col-md-2 control-label lablep">地址</label>
                        <div class="col-md-10">
                            <input id="Address" type="text" class="form-control" placeholder="" readonly>
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label class="col-md-2 control-label lablep">签发机关</label>
                        <div class="col-md-10">
                            <input id="Police" type="text" class="form-control" placeholder="" readonly>
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label class="col-md-2 control-label lablep">期限起始</label>
                        <div class="col-md-10">
                            <input  id="ActivityLFrom" type="text" class="form-control" placeholder="" readonly>
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label class="col-md-2 control-label lablep">期限失效</label>
                        <div class="col-md-10">
                            <input id="ActivityLTo" type="text" class="form-control" placeholder="" readonly>
                        </div>
                    </div>
					#if($options.newPermantOn == "Y")
						<div class="form-group col-md-12" style="display:none;">
							<label class="col-xs-2 control-label lablep textcolorred">旧卡证件</label>
							<div class="col-xs-10">
								<input id="oldCert" type="text" class="form-control" placeholder="" readonly>
							</div>
						</div>
					#end
                    <div class="form-group col-md-12">
                        <label class="col-md-2 control-label lablep">身份证照片</label>
                        <div class="col-md-10">
                            <div  class="pic_box"><img  id="IdPhoto"></div>
                        </div>
                    </div>	

                        </form>
                    </div>

                </div>
       <div class="mart15 tab-pane fade" id="12">
                    <div class=" col-md-12">
                        <span class="mar15">手机连接NFC：连接成功！</span>
                        <button type="button" class="btn btn-primary btn-sm">NFC绑定</button>
                        <button type="button" class="btn btn-primary btn-sm">NFC读取</button>
                        <button type="button" class="btn btn-primary btn-sm">NFC返回</button>
                    </div>
                    <div class="mincontent ">
                        <form class="form-bordered">
                            <div class="form-group col-md-12">
                                <label class="col-md-2 control-label lablep">姓名</label>
                                <div class="col-md-10">
                                    <input type="text" class="form-control" placeholder="" value="张海涛">
                                </div>
                            </div>
                            <div class="form-group col-md-12">
                                <label class="col-md-2 control-label lablep">性别</label>
                                <div class="col-md-10">
                                    <input type="text" class="form-control" placeholder="" value="男">
                                </div>
                            </div>
                            <div class="form-group col-md-12">
                                <label class="col-md-2 control-label lablep">民族</label>
                                <div class="col-md-10">
                                    <input type="text" class="form-control" placeholder="" value="汉">
                                </div>
                            </div>
                            <div class="form-group col-md-12">
                                <label class="col-md-2 control-label lablep">出生年月</label>
                                <div class="col-md-10">
                                    <input type="text" class="form-control" placeholder="">
                                </div>
                            </div>
                            <div class="form-group col-md-12">
                                <label class="col-md-2 control-label lablep">地址</label>
                                <div class="col-md-10">
                                    <input type="text" class="form-control" placeholder="">
                                </div>
                            </div>
                            <div class="form-group col-md-12">
                                <label class="col-md-2 control-label lablep">签发机关</label>
                                <div class="col-md-10">
                                    <input type="text" class="form-control" placeholder="">
                                </div>
                            </div>
                            <div class="form-group col-md-12">
                                <label class="col-md-2 control-label lablep">期限起始</label>
                                <div class="col-md-10">
                                    <input type="text" class="form-control" placeholder="">
                                </div>
                            </div>
                            <div class="form-group col-md-12">
                                <label class="col-md-2 control-label lablep">期限失效</label>
                                <div class="col-md-10">
                                    <input type="text" class="form-control" placeholder="">
                                </div>
                            </div>
                            <div class="form-group col-md-12">
                                <label class="col-md-2 control-label lablep">身份证照片</label>
                                <div class="col-md-10">
                                    <div class="pic_box"><img></div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        #if($options && $options.useDeskTop=="0")
        <object type="application/cert-reader"  id="CertCtl" width=0 height=0> </object>
        #end
</div>
 