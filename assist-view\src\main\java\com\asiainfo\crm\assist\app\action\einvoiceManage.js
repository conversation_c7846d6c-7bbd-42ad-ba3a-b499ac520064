(function (vita) {
    var einvoiceManage = vita.Backbone.BizView.extend({
        events: {
            "click #btn-sign": "_einvoiceSign",
            "click #btn-unSign": "_einvoiceUnSign",
            "click #btn-queryCust": "_queryCust",
            "click #btn-custSign": "_custSign",
            "click #btn-custUnSign": "_custUnSign",
            "click tr[name='trClick']": "_trClick",
            "click [name='payment'] " : "_clickRadio"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
        },
        _einvoiceSign: function () {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var custOrderNbr = $("#custOrderNbr").val();
            if(custOrderNbr == null || custOrderNbr == "") {
                widget.popup("请输入订单流水号！");
                return ;
            }
            var params = {
                custOrderNbr : custOrderNbr,
                staffId : gSession.staffId
            }
            widget.callService("einvoiceSign", params, function (ret) {
                debugger;
                if (ret.resultCode == "0") {
                    widget.popup("订单流水【"+custOrderNbr+"】标记为已开票成功！");
                } else if (ret.resultCode == "XB_RECEIPT_10003") {
                    widget.popup(ret.resultMsg, function () {
                        widget._einvoiceSignConfirm(params);
                    }, null);
                } else {
                    widget.popup("订单流水【"+custOrderNbr+"】标记为已开票失败：" + ret.resultMsg);
                }
            }, { async : false });
        },
        _einvoiceSignConfirm: function(params) {
            debugger;
            var widget = this;
            widget.callService("einvoiceSignConfirm", params, function (ret) {
                if (ret.resultCode == "0") {
                    widget.popup("订单流水【"+params.custOrderNbr+"】标记为已开票成功！");
                } else {
                    widget.popup("订单流水【"+params.custOrderNbr+"】标记为已开票失败：" + ret.resultMsg);
                }
            }, { async : false });
        },
        _einvoiceUnSign: function () {
            debugger;
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var custOrderNbr = $("#custOrderNbr").val();
            if(custOrderNbr == null || custOrderNbr == "") {
                widget.popup("请输入订单流水号！");
                return ;
            }
            var params = {
                custOrderNbr : custOrderNbr,
                staffId : gSession.staffId
            }
            widget.callService("einvoiceUnSign", params, function (ret) {
                if (ret.resultCode == "0") {
                    widget.popup("订单流水【"+custOrderNbr+"】撤销标记成功！");
                } else {
                    widget.popup("订单流水【"+custOrderNbr+"】撤销标记失败：" + ret.resultMsg);
                }
            }, { async : false });
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _queryCust: function () {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var paramCnt = 0;
            var params = {
                    staffId:gSession.staffId
                };
            if ($("#c_accNum").is(":checked")) {
                paramCnt++;
                var accNum = $("#accNum").val();
                if (widget._isNullStr(accNum)) {
                    widget.popup("请输入接入号！");
                    return false;
                } else {
                    params.accNum = accNum;
                }
            }
            var numReg = /^[0-9]*$/;
            if ($("#c_custId").is(":checked")) {
                paramCnt++;
                var custId = $("#custId").val();
                if (widget._isNullStr(custId)) {
                    widget.popup("请输入客户ID！");
                    return false;
                } else if(!numReg.test(custId)) {
                    widget.popup("请输入正确的数字！");
                    return false;
                } else {
                    params.custIds = [custId];
                }
            }
            if(paramCnt < 1){
                widget.popup("请至少选择一个条件！");
            }
            widget.refreshPart("queryCust", JSON.stringify(params), "#custListResult", function (re) {
            	widget.model.set("selectedModule",null);
            }, { async: false });
        },
        _trClick: function (e) {
            var widget = this;
            $(e.target).find("input[name='payment']").click();
        },
        _custSign: function (e) {
            debugger;
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var custId = widget.model.get("selectedModule");
            if(custId == null || custId == undefined){
                widget.popup("请先选择一个客户!");
                return false;
            }
            
            var params = {
                staffId : gSession.staffId,
                custId : custId,
                markFlag : 1
            };
            widget.callService("markSpecCust", params, function (ret) {
                widget.popup(ret.markRemark);
                widget._queryCust();
            }, { async : false });
        },
        _clickRadio : function(e){
			var widget = this,
				element = $(widget.el);
			debugger;
			var radio = $(e.target).closest('input');
			var data = radio.data('custId');
			widget.model.set("selectedModule",data);
		},
        _custUnSign: function (e) {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var custId = widget.model.get("selectedModule");
            if(custId == null || custId == undefined){
                widget.popup("请先选择一个客户!");
                return false;
            }

            var params = {
                staffId : gSession.staffId,
                custId : custId,
                markFlag : 0
            };
            widget.callService("markSpecCust", params, function (ret) {
                widget.popup(ret.markRemark);
                widget._queryCust();
            }, { async : false });
        }
    });
    vita.widget.register("einvoiceManage", einvoiceManage, true);
})(window.vita);