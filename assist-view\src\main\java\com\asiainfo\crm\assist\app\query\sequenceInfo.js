(function (vita) {
    var sequenceInfo = vita.Backbone.BizView.extend({
        events: {
            "click #queryBtn": "_querySequenceList",
            "click #resetBtn": "_clearConditions",
            "click #regionIdBtn": "_chooseArea",
            "click #_regionIdBtn": "_chooseAddArea",
            "click #addBtn": "_addPopup",
            "click #editBtn": "_editPopup",
            "click #deleteBtn": "_deleteSubmit",
            "click #addSubmitBtn": "_addSubmit",
            "click #editSubmitBtn": "_editSubmit",
            "click button.close": "_closeBtnClick",
            "click #closeBtn": "_closeBtnClick",
            "click #batchImport": "_batchImport",
            "click #submitImportBtn": "_submitImportBtn",
            "click #tempDownloadBtn": "_tempDownloadBtn",
            "click #queryRegionIdBtn": "_chooseQryAddArea",
            "click #upLoadRegionIdBtn": "_chooseUploadAddArea",
            "click #checkZkValue": "_checkZkValue"
        },
        _initialize: function () {
            var widget = this,
                el = $(widget.el);
            //时间控件类型初始化
            var datetime = widget.require("datetime");

            var time = el.find("#qureyCreateData,#queryUpdateDate");
            if (time.length) {
                datetime.register(time, {
                    preset: widget.global.datetime
                });
            }
        },
        global: {
            totalNumber: 0,
            pageIndex: 1,
            pageSize: 5,
            datetime: 'time',
            date: 'date',
            chooseArea: "../comm/chooseArea",
        },

        /**
         * 打开添加弹框
         */
        _addPopup: function () {
            var widget = this,
                el = $(widget.el);
            widget._cleanPopupData();
            widget._openPopup("add");
        },
        _openPopup: function (action) {
            var widget = this,
                el = $(widget.el);
            switch (action) {
                case "add":
                    el.find(".titlefont").html("添加");
                    el.find("#_regionIdBtn").removeAttr("disabled");
                    el.find(".hideAndShow").hide();
                    el.find("#addSubmitBtn").show();
                    el.find("#editSubmitBtn").hide();
                    el.find(".popup").show();
                    break;
                case "edit":
                    el.find(".titlefont").html("编辑");
                    el.find(".hideAndShow").show();
                    el.find("#_sequenceInfoIdVal").attr("disabled","disabled");
                    el.find("#_statusTimeVal").attr("disabled","disabled");
                    el.find("#_upDateVal").attr("disabled","disabled");
                    el.find("#_createDataVal").attr("disabled","disabled");
                    el.find("#_statusCodeVal").attr("disabled","disabled");
                    el.find("#addSubmitBtn").hide();
                    el.find("#editSubmitBtn").show();
                    el.find(".popup").show();
                    break;
                default:
                    break;
            }
        },
        /**
         * 删除
         * @private
         */
            _deleteSubmit: function () {
            var widget = this,el = $(widget.el);
            var row = el.find("table").find("input[name='payment']:checked");
            if (row.length > 0 && row.length<2) {
                var sequenceInfoId = row.data("fcSequenceInfoId");
                widget.callService("deleteByIdSequenceInfo", sequenceInfoId, function (res) {
                        if("0" == res.resultCode) {
                            widget.popup("删除成功");
                            this._judgeIsLastPageIndex();
                            this._querySequenceList();
                        }
                }, {async: false, mask: false});
            } else {
                widget.popup("请选择要删除的一行!");
                return;
            }
        },
        /**
         * 新增
         * @private
         */
        _addSubmit: function () {
            var widget = this;
            var param = widget._getPopupData();
            if(!this._checkParam(param)){
                return;
            }
            widget.callService("createSequenceInfo", JSON.stringify(param), function (res) {
                    if("0" == res.resultCode){
                        widget.popup("添加成功");
                        widget._closeBtnClick();
                        widget._querySequenceList();
                    }else{
                        widget.popup("添加失败，请稍后重试");
                    }
            }, {async: false, mask: false});
        },
        /**
         * 编辑更新
         * @private
         */
        _editSubmit: function () {
            var widget = this,el = $(widget.el);
            var param = widget._getPopupData();
            param["sequenceInfoId"] = el.find("#_sequenceInfoIdVal").val().trim()
            if(!this._checkParam(param)){
                return;
            }
            widget.callService("updateSequenceInfo", JSON.stringify(param), function (res) {
                if (res) {
                    if("0" == res.resultCode){
                        widget.popup("修改成功");
                        widget._closeBtnClick();
                        widget._querySequenceList();
                    }
                }
            }, {async: false, mask: false});
        },
        /**
         * 打开编辑弹框
         */
        _editPopup: function () {
            var widget = this,
                el = $(widget.el);
            var payment = el.find("table").find("input[name='payment']:checked");
            if (payment.length > 0) {
                widget._cleanPopupData();
                var fcRuleId = payment.data("fcRuleId");
                var tr = payment.parent().parent().parent();
                el.find("#_sequenceInfoIdVal").val(tr.find("td:eq(1)").text());
                el.find("#_sequenceNameVal").val(tr.find("td:eq(2)").text());
                el.find("#_tableNameVal").val(tr.find("td:eq(3)").text());
                el.find("#_columnNameVal").val(tr.find("td:eq(4)").text());
                el.find("#_maxNumVal").val(tr.find("td:eq(5)").text());
                el.find("#_columnSizeVal").val(tr.find("td:eq(6)").text());
                el.find("#_sequenceSizeVal").val(tr.find("td:eq(7)").text());
                el.find("#_lanId").val(tr.find("td:eq(8)").text());
                el.find("#_thresholdVal").val(tr.find("td:eq(9)").text());
                el.find("#_centerVal").val(tr.find("td:eq(10)").text());
                el.find("#_statusCodeVal").val(tr.find("td:eq(12)").text());
                el.find("#_remarkVal").val(tr.find("td:eq(14)").text());
                widget._openPopup("edit");
            } else {
                widget.popup("请选择要编辑的一行!");
                return;
            }
        },
        /**
         * 弹出框关闭事件
         */
        _closeBtnClick: function () {
            var widget = this,
                el = $(widget.el);
            el.find(".popup").hide();
            el.find(".popup1").hide();
            el.find(".zkPopup").hide();
        },
        /**
         * 弹出导入窗口
         */
        _batchImport: function () {
            var widget = this,
                el = $(widget.el);
                el.find(".popup1").show();
            },
        /**
         * exel批量导入
         */
        _submitImportBtn: function () {
            var widget = this,
                el = $(widget.el);
            var upFile = $.trim(el.find("#upFile").val());
            if (upFile === "") {
                widget.popup("请选择要导入的Excel文件！");
                return;
            } else {
                var fileType = upFile.substr(upFile.lastIndexOf(".") + 1);
                if (fileType == "xls" || fileType == "xlsx") {
                    var loading = vita.widget.require("loading");
                    el.find("#importForm").ajaxSubmit({
                        type : 'post',
                        dataType : 'json',
                        url : '../query/insertBatchSequenceInfo',
                        timeout: 300000,  // 5分钟
                        beforeSubmit:function(){
                            loading.open();
                        },
                        success : function(data) {
                            loading.close();
                            if(data == 0){
                                el.find(".popup1").hide();
                                el.find("#upFile").val("")
                                widget._querySequenceList();
                                widget.popup("批量导入成功");
                            } else {
                                widget.popup("导入失败，请检查表格数据");
                            }
                        },
                        error : function(data, status, e) {
                            loading.close();
                            widget.popup("请求可能发生异常，请稍后再试！");
                        }
                    });
                } else {
                    widget.popup("导入文件类型错误！");
                    return;
                }
                }
        },
        /**
         * 下载exel模板
         */
        _tempDownloadBtn: function(){
            var widget = this;
            window.open("../query/downLoadSeqInfoTemplateExcel")
        },
        /**
         * 清除弹框数据
         */
        _cleanPopupData: function () {
            var widget = this,
                el = $(widget.el);
            el.find("form[name=_popupForm]").resetForm();
            el.find("#_lanId").val('').attr("value","");
        },

        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#lanId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _chooseAddArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#_lanId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        /**
         * 重置查询条件
         */
        _clearConditions: function () {
            var widget = this,
                el = $(widget.el);
            el.find("#querySequenceName").val('');
            el.find("#queryLanId").val('').attr("value","");

        },
        /**
         * 获取弹出框数据
         * @private
         */
        _getPopupData: function () {
            var widget = this,
                el = $(widget.el);
            var lanId = $("#_lanId").attr("value")
            var params = {
                "seqName": el.find("#_sequenceNameVal").val().trim(),
                "tName": el.find("#_tableNameVal").val().trim(),
                "columnName": el.find("#_columnNameVal").val().trim(),
                "maxNum": el.find("#_maxNumVal").val().trim(),
                "columnSize": el.find("#_columnSizeVal").val().trim(),
                "seqSize":el.find("#_sequenceSizeVal").val().trim(),
                "threshold": el.find("#_thresholdVal").val().trim(),
                "center": el.find("#_centerVal").val().trim(),
                "remark": el.find("#_remarkVal").val().trim(),
                "regionId": lanId,
                "createStaff": widget.gSession.staffId,
                "updateStaff": widget.gSession.staffId,

            };
            return params;
        },
        _checkParam: function (param){
            var widget = this,
                el = $(widget.el);
            var soUtil = widget.require("soUtil");
            if(soUtil.isNullOrEmpty(param.seqName) || soUtil.isNullOrEmpty(param.maxNum)){
                widget.popup("参数必填，请检查后重新提交!");
                return false;
            }else if(!(/^[1-9]{0,2}$/.test(param.columnSize) && /^[1-9]{0,2}$/.test(param.seqSize) && /^([0-9]{0,3})+(.[0-9]{0,2})?$/.test(param.threshold))) {
                widget.popup("参数大小错误，请检查后重新提交!");
                return false;
            }
            return true;
        },
        _judgeIsLastPageIndex: function(){
            var widget = this;
            var totalNumber = widget.global.totalNumber;
            var pageIndex = widget.global.pageIndex;
            var pageSize = widget.global.pageSize
            var totalPage= 0;
            if ((totalNumber % pageSize) == 0) {
                totalPage = Math.floor(totalNumber / pageSize)
            }else {
                totalPage = Math.floor(totalNumber / pageSize) + 1;
            }
            if(pageIndex == totalPage && ((totalNumber-1) % pageSize) == 0){
                pageIndex = pageIndex-1
            }
            widget.global.pageIndex = pageIndex;
            return;
        },
        /**
         * 查询
         */
        _querySequenceList: function () {
            var widget = this,
                el = $(widget.el);
            var param = widget._getConds(widget.global.pageIndex);
            widget.refreshPart("qryPageInfoSequenceInfo", param, "#loadData",
                "reqList", function (res) {
                    var totalNumber = $(res).find("table").data("totalNumber");
                    widget.global.totalNumber = totalNumber;
                    if (parseInt(totalNumber) > 0) {
                    	var r = $(res), paging = widget.require("paging");
                        var e = paging.new({
                            recordNumber : widget.global.pageSize,
                            total : totalNumber,
                            pageIndex : widget.global.pageIndex,
                            callback : function (pageIndex, recordNumber) {
                                widget.global.pageIndex = pageIndex;
                                var inParam = widget._getConds(widget.global.pageIndex);
                                widget.refreshPart("qryPageInfoSequenceInfo", inParam, "div[name=tableDiv]",null,null,{mask: true});
                            }
                        });
                        r.find("#pageInfo").append(e.getElement());
                    }
                }, {mask: true});
        },
        _getConds: function (pageIndex) {
            var widget = this, el = $(widget.el);
            var regionId = $("#queryLanId").attr("value");
            var param = {
                "seqName": el.find("#querySequenceName").val().trim(),
                "regionId":regionId,
                "pageInfo": {
                    "pageIndex": pageIndex,
                    "pageSize": widget.global.pageSize,
                }
            };
            return param;
        },
        _chooseQryAddArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#queryLanId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _checkZkValue: function(e){
            var widget = this,el = $(widget.el);
            var aEl = $(e.currentTarget);
            var seqName = aEl.val()
            widget.callService("getCurrentSeq",seqName,function (res) {
                if (res) {
                    if("0" == res.resultCode){
                        el.find(".zkPopup").show();
                        el.find("#zkSequenceInfo").text(res.resultObject);
                    }
                }
            }, {async: false, mask: false});
        },
        _chooseUploadAddArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#upLoadLanId").val(data.regionName)
                        el.find("#orgId").val(data.commonRegionId);
                    }
                }
            });
        },

    });
    vita.widget.register("sequenceInfo", sequenceInfo, true);
})
(window.vita);
