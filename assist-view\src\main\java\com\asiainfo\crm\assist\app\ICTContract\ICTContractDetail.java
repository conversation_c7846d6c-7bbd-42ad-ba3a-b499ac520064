package com.asiainfo.crm.assist.app.ICTContract;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IAssetCenterSMO;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类简述: ICT合同详情查询
 */
@Component("vita.ICTContractDetail")
public class ICTContractDetail extends AbstractComponent {

    private static final Logger LOGGER = LoggerFactory.getLogger(ICTContractDetail.class);

    @Autowired
    private IAssetCenterSMO assetCenterSMO;

    @Override
    public Map achieveData(Object... params) throws IOException {
        String paramStr = (String)params[0];
        Map paramMap = jsonConverter.toBean(paramStr, Map.class);
        Map options = new HashMap();
        Map ictProjectParam=new HashMap();
        ictProjectParam.put("ictItemCode",paramMap.get("ictItemCode"));
        options.put("ICTProjectDeail",queryICTProjectDetail(ictProjectParam));

        Map ictContractParam=new HashMap();
        ictContractParam.put("contractCode",paramMap.get("contractCode"));
        options.put("ICTContractDeail",queryICTContractDetail(ictContractParam));
        options.put("params",paramMap);
        return options;

    }
    public Map queryICTProjectDetail(Map param) throws IOException {
        String re=assetCenterSMO.qryTransIctItemByCode(jsonConverter.toJson(param));
        Map<String, Object> resultObject = (Map) resolveResult(re);
        if(resultObject==null || resultObject.isEmpty()){
            return new HashMap();
        }
        if(resultObject ==null || resultObject.isEmpty() ||resultObject.get("transIctItem") ==null){
            return new HashMap();
        }
        Map items = (Map) resultObject.get("transIctItem");
        if(items==null || items.isEmpty() || items.get("ictContract") ==null ){
            return new HashMap();
        }
        Map ictContract= (Map) items.get("ictContract");
        BigDecimal b   =   new   BigDecimal(100);
        Double tradeSum=ictContract.get("tradeSum")!=null ? (Double)ictContract.get("tradeSum") :0;
        if(tradeSum != null){
            BigDecimal a   =   new   BigDecimal(tradeSum);
            a=a.divide(b,2,BigDecimal.ROUND_HALF_UP);
            ictContract.put("tradeSum" ,a);
        }
        return items;
    }
    public Map qrySubIctContractByItem(String jsonStr) throws IOException {
        Map options = new HashMap();
        String re=assetCenterSMO.qrySubIctContractByItem(jsonStr);
        Map<String, Object> resultObject = (Map) resolveResult(re);
        List<Map> items = (List<Map>) resultObject.get("subIctContracts");
        Map<String, Integer> pageInfo = (Map) resultObject.get("pageInfo");
        List contracts = new ArrayList();
        long rowCount = 0L;
        if(items != null && !items.isEmpty()){
            rowCount = Long.valueOf(pageInfo.get("rowCount"));
            options.put("totalNumber", rowCount);
            options.put("ICTSubProjectDeail", items);
            return options;
        }
        options.put("totalNumber", rowCount);
        options.put("ICTSubProjectDeail", contracts);
        return options;
    }
    public Map queryICTContractDetail(Map param) throws IOException {
        String re=assetCenterSMO.qryIctContractByCode(jsonConverter.toJson(param));
        Map<String, Object> resultObject = (Map) resolveResult(re);
        if(resultObject==null || resultObject.isEmpty() || resultObject.get("ictContract") ==null ){
            return new HashMap();
        }
        Map items = (Map) resultObject.get("ictContract");
        BigDecimal b   =   new   BigDecimal(100);
        Double tradeSum=items.get("tradeSum")!=null ? (Double)items.get("tradeSum") :0;
        if(tradeSum != null){
            BigDecimal a   =   new   BigDecimal(tradeSum);
            a=a.divide(b,2,BigDecimal.ROUND_HALF_UP);
            items.put("tradeSum" ,a);
        }
        return items;
    }
}
