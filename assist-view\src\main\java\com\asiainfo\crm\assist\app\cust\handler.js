(function(vita) {
	var handler = vita.Backbone.BizView.extend({
		events : {
			"click #chooseCustBtn": "_showOldCustPage"//经办人
		},
		_initialize : function() {
		},
		global : {
			partyTypes: 1,
			chooseCust: {
				compId: "chooseCust",
				compUrl: "../comm/chooseCust",
				dialogId: "Dialog"
			}
		},
		getValue : function() {
			var widget = this,
				element = $(widget.el);
			widget.model.set({
				"remark":element.find("#remark").val()
			})
			return widget.model.toJSON();
		},
		_showOldCustPage: function (e) {

			this._showCustDialog(e, "chooseCust", this._dealOldCustInfo);
		},
		_showCustDialog: function (e, name, callback) {

			var widget = this;
			var comp = widget.global[name].compId;
			var param = {
				"control" : "true"
			};
			var option = {
				url: widget.global[name].compUrl,
				params : param,
				onClose: function (res) {
					var compWidget = $(res).closest("[data-widget=" + comp + "]");
					if (compWidget.length) {

						var data = compWidget[comp]("getValue");
						if (!data || !data.custId) {
							return false;
						}
						if (data.certAddr == null || data.certAddr == ""
							|| data.certNum == null || data.certNum == ""
							|| data.certTypeName == null || data.certTypeName == ""
							|| data.custName == null || data.custName == "") {
							widget.popup("证件信息不完整，请确认证件名称，证件类型。证件号码，证件地址等是否齐全");
							return false;
						}
						if (data.partyType != widget.global.partyTypes) {
							widget.popup("经办人必须为个人客户");
							return false;
						}
						if (data.isRealname != widget.global.partyTypes) {
							widget.popup("经办人必须为实名客户");
							return false;
						}
						if ($.isFunction(callback)) {
							callback.call(widget, data);
						}
					}
				},
				headers : {
					"regionId" : widget.gSession.installArea
				}
			};
			var dialog = widget.global[name].dialogId;
			if (dialog) {
				option.id = comp + dialog;
			}
			widget.dialog(option);
		},
		_dealOldCustInfo: function (data) {
			//console.info(data)
			var widget = this;
			$("#handlerTypeName").val("");
			$("#handlerAddr").val("");
			$("#handlerName").val("");
			if (data.defaultCertType && data.defaultCertType != null && data.defaultCertType != ""&&data.defaultCertType==1) {
				if (data.certNum && data.certNum != null && data.certNum != "") {
					if(widget.countIdentAge(data.certNum)){
						widget.popup("当前经办人的年龄小于16岁，不能作为经办人");
						return false;
					}
				}
			}
			if (data.custName && data.custName != null && data.custName != "") {
				$("#handlerName").val(data.custName);
				widget.model.set("handler",data.custName);
			}
			if (data.certTypeName && data.certTypeName != null && data.certTypeName != "" && data.certNum != null && data.certNum != "") {
				$("#handlerTypeName").val(data.certTypeName+"/"+data.certNum);
				widget.model.set({
					"handlerCertType":data.defaultCertType,
					"handlerCertTypeName":data.certTypeName,
					"handlerCertNum":data.certNum
				});
			}
			if (data.certAddr && data.certAddr != null && data.certAddr != "") {
				$("#handlerAddr").val(data.certAddr);
				widget.model.set({
					"certInfo":data.certAddr
				});
			}
			if (data.custId && data.custId != null && data.custId != "") {
				widget.model.set({
					"handlerId":data.custId
				});
			}else{
				widget.model.set("handlerId","");
			}

			widget.model.set({
				"handlerPhone" :data.contactPhone
			});
		},
		/**
		 * 计算身份证件年龄
		 */
		countIdentAge : function(idCard) {

			var widget = this,
				global = widget.global,
				element = $(widget.el),
				professionName = null;
			//获取当前系统时间
			var nowTime=widget.querySysdate();

			var year=nowTime.substring(0,4)
			var month=nowTime.substring(5,7);
			var date=nowTime.substring(8,10);
			//根据身份证获取出生年月
			var birthday = "";
			var age="";
			birthday = idCard.substr(6,8);
			birthday = birthday.replace(/(.{4})(.{2})/,"$1-$2-");
			//获取年龄
			age = year - idCard.substring(6, 10) - 1;
			if (idCard.substring(10, 12) <month ||( idCard.substring(10, 12)==month&&idCard.substring(12, 14) <= date)) {
				age++;
			}
			if(age<16){
				return true;
			}else{
				return false;
			}

		},
		querySysdate: function () {

			var widget = this,
				global = widget.global,
				element = $(widget.el),
				professionName = null;
			gSession = widget.gSession;
			var soUtil=widget.require("soUtil");
			var resultMsg = "";
			var param = {
				"glbSessionId": gSession.glbSessionId
			}

			var retVal=null;
			widget.callService("querySysdate", param, function (res) {
				var ret = JSON.parse(res);

				if (ret.resultCode == 0) {
					retVal= ret.resultObject;
				}else{
					resultMsg = ret.resultMsg;
				}
				var isNullOrEmpty = soUtil.isNullOrEmpty(resultMsg);
				if (!isNullOrEmpty) {
					widget.popup(resultMsg);
					return false;
				}
			}, {
				async: false,
				mask: true
			});
			return retVal;
		}


	});
	vita.widget.register("handler", handler, true);
})(window.vita);