<div data-widget="iTVOrderQuery" style="height:100%">
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">iTV接入号</label>
                                            <div class="col-md-7">
                                                <input id="iTVAccessNum" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <div class="col-md-12 searchbutt_r" align="center">
                                                <button id="btn-query" type="button" class="btn btn-primary">查询</button>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4"></div>
                                        
                                        <div class="form-group col-md-12">
                                            <div class="col-md-12" style="left: 65px;top: 25px;">
                                                iTV影视大包订购历史数据列表（备注：iTV平台接口只返回六个月内的有效订购关系）
                                            </div>
                                        </div>
                      
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <!--<div><i class="bot"></i>查询结果</div>-->
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12 mart10" id="iTVOrderListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>产品编号</th>
                                                <th>产品名称</th>
                                                <th>产品金额(元)</th>
                                                <th>生效时间</th>
                                                <th>失效时间</th>
                                                <th>订购时间</th>
                                            </tr>
                                            </thead>
                                            <tbody id="orderList">
                                            #if($options != "null" && $options.resultCode && $options.resultCode != "null" &&
	                                            $options.resultCode == 0)
	                                            #if($options.orderInfos && $options.orderInfos != "null" &&
	                                            $options.orderInfos.size() > 0)
	                                            #foreach($orderInfo in $options.orderInfos)
	                                            <tr>
	                                                <td>$!{orderInfo.productID}</td>
	                           						<td>$!{orderInfo.productName}</td>
	                                                <td>$!{orderInfo.fee}</td>
	                                                <td>$!{orderInfo.startTime}</td>
	                                                <td>$!{orderInfo.endTime}</td>
	                                                <td>$!{orderInfo.orderTime}</td>
	                                            </tr>
	                                            #end
	                                            #elseif($options != "null" && $options.orderInfos && $options.orderInfos != "null" &&
	                                            $options.orderInfos.size() == 0)
	                                                <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
	                                            #end
	                                         #else
	                                         	#if($options != "null" && $options.resultDesc && $options.resultDesc != "null")
	                                        	 <tr><td align='center' colspan='8'>$options.resultDesc<td></tr>
	                                        	#end
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start 
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end-->
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
