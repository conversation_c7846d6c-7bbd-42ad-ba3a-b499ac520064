(function(vita){
	var protocolTempletConfig = vita.Backbone.BizView.extend({
		events : {
			"click #addConfig" : "_add",
			"click #modifyConfig" : "_modify",
			"click .close" : "_closeBtn",
			"click #delConfig" : "_del",
			"click [name='moduleRadio'] " : "_clickRadio"
		},
		_initialize : function() {
			var widget = this,
			element = $(widget.el);
            var data = element.data("data");
			widget.global.pageSize = data.pageInfo ? data.pageInfo.pageSize : 10;
			widget.model.set("protocolTempletId",data.protocolTempletId);
			//初始化查询
            widget._createPageInfo();
		},
		_clickRadio : function(e){
			var widget = this,
				element = $(widget.el);
			var radio = $(e.target).closest('input');
			var data = radio.data('data');
			widget.model.set("selectedModule",data);
		},
		_closeBtn : function() {
			var widget = this,
				element = $(widget.el);
			element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
		},
		global : {
			addConfig :"../action/saveProtocolTempletConfig",
			modifyConfig : "../action/saveProtocolTempletConfig"
		},
		/**
		 * 获取查询条件参数对象
		 */
		_getConds : function(pageIndex) {
			var widget = this,
				element = $(widget.el);
			var pageInfo= {
						"pageIndex" : pageIndex,
						"pageSize" : widget.global.pageSize
					};
			var param = {
					"pageInfo" :pageInfo
				};
			var soUtil = widget.require("soUtil");
			var protocolTempletId = widget.model.get('protocolTempletId');
			var isNullOrEmpty = soUtil.isNullOrEmpty(protocolTempletId);
			if (!isNullOrEmpty) {
				param.protocolTempletId = protocolTempletId;
			}
			return param;
		},
		_queryList : function(pageIndex) {
            var widget = this,
                element = $(widget.el),
                showPageInfo = element.find("#showPageInfo");
            pageIndex = pageIndex || showPageInfo.find("a.active").text();
			var param = widget._getConds(pageIndex);
			widget.refreshPart("qryItems", param, "#protocolTable", function(res) {
                    widget._createPageInfo(pageIndex,$(res));
				}, {
					mask : true
				});
		},
		_createPageInfo : function(pageIndex){
            var widget = this, element = arguments[1] || $(widget.el);
            var showPageInfo = element.find("#showPageInfo"),
                totalNumber = showPageInfo.data("totalNumber");
            widget.model.set("selectedModule",null);
            var page = widget.require("paging");
            var e = page.new({
                pageIndex : pageIndex || 1,
                recordNumber : widget.global.pageSize,
                total : totalNumber,
                callback : function(pageIndex){
                    var param = widget._getConds(pageIndex);
                    widget.refreshPart("qryItems", param, "#itemTable",function(res) {
                            widget.model.set("selectedModule",null);
                        }, {mask : true});
                }
            });
            showPageInfo.empty().append(e.getElement());
		},
		_add : function(e){
			var widget = this,
				element = $(widget.el);
			 
			var button = $(e.target).closest("a");
            var params = {
                "protocolTempletId": widget.model.get("protocolTempletId")
            };
			var id = button.attr("id") || "";
			if (id && widget.global[id]) {
				var dialogId = id + 'Dialog';
				var option = {
					url : widget.global[id],
					params : params,
					id : dialogId,
					onClose : function(res) {
						widget._queryList();
					}
				};
				widget.dialog(option);
			}
		
		},
		_modify : function(e){

			var widget = this,
				element = $(widget.el);
			var module = widget.model.get("selectedModule");
			if(module == null || module == undefined){
				widget.popup("请选择一条信息！");
				return false;
			}
			var button = $(e.target).closest("a");
			var id = button.attr("id") || "";
			if (id && widget.global[id]) {
				var dialogId = id + 'Dialog';
				var option = {
					url : widget.global[id],
					params : module,
					id : dialogId,
					onClose : function(res) {
						widget._queryList();
					}
				};
				widget.dialog(option);
			}
		
		},
		_del : function(e) {
			var widget = this, gsession = widget.gSession,
				element = $(widget.el);
			var module = widget.model.get("selectedModule");
			if(module == null || module == undefined){
				widget.popup("请选择一条信息！");
				return false;
			}

			var param = {
					"protocolTempletConfigId" : module.protocolTempletConfigId,
					"protocolTempletId" : module.protocolTempletId,
					"createStaff" : gsession.staffId
		    	};
		    	widget.callService("del", JSON.stringify(param),function(res) {
					var ret = JSON.parse(res);
					if (ret.resultCode == 0) {
						widget.popup("成功！",function(){
							widget._queryList(1);
						});
					}else{
						widget.popup("失败！");
					}				
				}, {
					async : false
				});
		}
		 
	});
	 vita.widget.register("protocolTempletConfig", protocolTempletConfig, true); 
})(window.vita);
