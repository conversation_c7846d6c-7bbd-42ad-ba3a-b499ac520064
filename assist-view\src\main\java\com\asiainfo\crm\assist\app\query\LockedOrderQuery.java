package com.asiainfo.crm.assist.app.query;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.asiainfo.crm.service.intf.IAgencyCapitalSMO;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICommQuerySMO;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.google.common.collect.Maps;

/**
 * Created on 2017/7/7.
 */
@Component("vita.lockedOrderQuery")
public class LockedOrderQuery extends AbstractComponent {

    private final Logger logger = LoggerFactory.getLogger(CustomerOrderListQuery.class);

    @Autowired
    private IOrderQuerySMO orderQuerySMO;

    @Autowired
    private ICommQuerySMO commQuerySMO;

    @Autowired
    private ISoQuerySMO soQuerySMO;

    @Autowired
    private IAgencyCapitalSMO agencyCapitalSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map retMap = new HashMap();
        return retMap;
    }

    /**
     * 查询订单列表
     * @param json
     * @return
     * @throws IOException
     */
    public Map queryCustomOrderList(String json) throws Exception {
        String customerOrderItemList = orderQuerySMO.qryCustomerOrderItemListByCond(json);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(customerOrderItemList);
        Map<String, Object> options = Maps.newHashMap();
        List customerOrders = (List) MapUtils.getObject(resultObjectMap, "customerOrders");
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", total);
        options.put("customerOrders", customerOrders);
        return options;
    }

    /**
     * 查询订单详情
     * @param json
     * @return
     * @throws Exception
     */
    public Map queryCustomOrderDetail(String json) throws Exception {
        String customOrderDetail = orderQuerySMO.queryCustomerOrderDetailsByCodId(json);
        return (Map) resolveResult(customOrderDetail);
    }

    public Map cancelLockedOrder(String jsonStr) throws Exception {
        String retStr = agencyCapitalSMO.saveCancelLockedOrder(jsonStr);
        return (Map) resolveResult(retStr);
    }

}
