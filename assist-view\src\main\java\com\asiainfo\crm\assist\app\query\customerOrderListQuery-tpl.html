<div data-widget="customerOrderListQuery" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright">
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                地 区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_channelId" type="checkbox" name="payment">
                                                </label> 渠道
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="channelId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="channelIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_staffId" type="checkbox" name="payment">
                                                </label> 营业员
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="staffId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="staffIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custId" type="checkbox" name="payment">
                                                </label> 所属客户
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="custId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="custIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custOrderNbr" type="checkbox" name="payment">
                                                </label> 流水号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custOrderNbr" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_accNum" type="checkbox" name="payment">
                                                </label> 号码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="accNum" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_statusCd" type="checkbox" name="payment">
                                                </label> 订单状态
                                            </label>
                                            <div class="col-md-7">
                                                <select id="statusCd" class="form-control">
                                                    #if($options != "null" && $options.statusList != "null" &&
                                                    $options.statusList.size() > 0)
                                                    #foreach($status in $options.statusList)
                                                    <option value=$status.attrValue>$status.attrValueName</option>
                                                    #end
                                                    #end

                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_orderItemNbr" type="checkbox" name="payment">
                                                </label> 订单项流水
                                            </label>
                                            <div class="col-md-7">
                                                <input id="orderItemNbr" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_orderItemId" type="checkbox" name="payment">
                                                </label> 订单项Id
                                            </label>
                                            <div class="col-md-7">
                                                <input id="orderItemId" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_extCustOrderId" type="checkbox" name="payment">
                                                </label> 集团编号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="extCustOrderId" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">查询范围</label>
                                            <div class="col-md-7">
                                                <select id="qryScope" class="form-control">
                                                    <option value="10">在途库</option>
                                                    <option value="11">历史库</option>
                                                    <option value="12">二级库</option>
                                                </select>
                                            </div>
                                        </div>
                                        
										<div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_mktResInstNbr" type="checkbox" name="payment">
                                                </label> 终端串号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="mktResInstNbr" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_isProdAcctRel" type="checkbox" name="payment">
                                                </label> 账户优惠
                                            </label>
                                          <!--  <div class="col-md-7">
                                                <input id="isProdAcctRel" type="text" class="form-control" placeholder="">
                                            </div>-->
                                        </div>

                                        <div class="form-group col-md-12" id="qryTimeQuantum">
                                            <label class="col-md-2 control-label lablep">
                                                起止时间</label>
                                            <div class="col-md-10 form-inline">

                                                <div class="form-group">
                                                    <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="开始时间">
                                                </div>
                                                <div class="form-group">
                                                    <input name="endDate" id="endDate" type="text" class="form-control" placeholder="结束时间">
                                                </div>
                                                <button type="button" id="timeQuantum_1" name="timeQuantum_1" class="btn btn-gray btn-outline btn-sm">当天</button>
                                                <button type="button" id="timeQuantum_7" name="timeQuantum_7" class="btn btn-gray btn-outline btn-sm">七天</button>
                                                <button type="button" id="timeQuantum_30" name="timeQuantum_30" class="btn btn-gray btn-outline btn-sm">一个月</button>
                                                <button type="button" id="timeQuantum_90" name="timeQuantum_90" class="btn btn-gray btn-outline btn-sm">三个月</button>

                                            </div>
                                        </div>
                                        <div class="form-group col-md-11">
                                            <div class="col-md-12 searchbutt_r" align="right">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-clear" type="button" class="btn btn-white">清除</button>
                                            </div>
                                        </div>


                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <!--<div><i class="bot"></i>查询结果</div>-->
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12"  id="custoomerOrderLabels">
                                        <a id="viewInvoice" class="btn btn-gray btn-outline"> <i class="glyphicon fa-details text18"> </i> 查看发票
                                        </a>
                                        <a id="printInvoice" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 补打发票
                                        </a>
                                        <a id="printReceipt" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 打印回执
                                        </a>
                                        <a id="returnFee" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 未竣工补/退费
                                        </a>
                                        <a id="orderDetail" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 查看订单详情
                                        </a>
                                        <a id="afterPayCash" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 先装后付收费
                                        </a>
                                        <a id="repairCharge" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 未收费订单补收
                                        </a>
                                        <a id="commitFee" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 收费确认
                                        </a>
                                        <a id="repealCharge" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 已竣工补/退费
                                        </a>
                                         <a id="printReceipts" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 回执协议补录
                                        </a>
                                        <a id="downloadPdf" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 回执PDF下载
                                        </a>
                                        #if($options != "null" && $options.qryArchivesSwitch && $options.qryArchivesSwitch != "null" && $options.qryArchivesSwitch == "Y")
                                        <a id="qryArchives" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 查看电子档案
                                        </a>
                                        #end
                                        #if($options != "null" && $options.orderRevokeSwitch && $options.orderRevokeSwitch != "null" && $options.orderRevokeSwitch == "Y")
                                        <a id="revokeOrder" class="btn btn-gray btn-outline" style="display: none"> <i class="glyphicon fa-cancel text18"> </i> 订单作废
                                        </a>
                                        #end
                                        <a id="printOutStockOrder" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 出库单打印
                                        </a>
                                        #if($options.viewOrderOn == "Y")
                                        	<a id="viewOrder" class="btn btn-gray btn-outline">
                                        		<i class="glyphicon fa-request text18"> </i> 订单溯源
                                        	</a>
                                        #end
                                    </div>
                                    <div class="col-lg-12 mart10" id="orderListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>选择</th>
                                                <th>购物车流水</th>
                                                <th>受理渠道</th>
                                                <th>受理时间</th>
                                                <th>受理员工</th>
                                                <th>订单项Id</th>
                                                <th>业务类型</th>
                                                <th>业务状态</th>
												<th>修改渠道</th>
                                                <th>修改员工</th>
                                                <th>修改员工工号</th>
                                            </tr>
                                            </thead>
                                            <tbody id="orderList">
                                            #if($options != "null" && $options.customerOrders && $options.customerOrders != "null" &&
                                            $options.customerOrders.size() > 0)
                                            #foreach($customerOrder in $options.customerOrders)
                                            <tr>
                                                <td><label class="wu-radio full absolute" data-scope=""><input
                                                        type="radio" name="payment"></label></td>
                                                <td>
                                                    <a class="textcolorgreen" link="customerOrder" data-toggle="tooltip" data-placement="top" data-original-title="费用信息：应收:$!customerOrder.payInfo.amountTotal;实收:$!customerOrder.payInfo.realAmountTotal">$!{customerOrder.custOrderNbr}</a>
                                                    <p class="vita-data">{"custOrderId" : $!customerOrder.custOrderId, "custOrderNbr": "$!customerOrder.custOrderNbr", "statusCd":$!customerOrder.statusCd, "acceptDate":"$!customerOrder.acceptDate", "createOrgId":$!customerOrder.createOrgId}</p>
                                                    <br>
                                                    #if($customerOrder.statusCd && $customerOrder.statusCd =="201800")
                                                     【人脸识别卡单】
                                                    #end
                                                </td>
                                                <td>$!{customerOrder.createOrgName}</td>
                                                #if($customerOrder.orderItems != "null" && $customerOrder.orderItems.size() > 0)
                                                <td>$!{customerOrder.orderItems[0].acceptDate}</td>
                                                #else
                                                <td></td>
                                                #end
                                                <td>$!{customerOrder.createStaffName}</td>
                                                <td>
                                                    #if($customerOrder.orderItems != "null" && $customerOrder.orderItems.size() > 0)
                                                        #foreach($orderItem in $customerOrder.orderItems)
                                                        <p  style="height: 40px;">$!{orderItem.orderItemId}</p>
                                                        #end
                                                    #end
                                                </td>
                                                <td>
                                                    #if($customerOrder.orderItems != "null" && $customerOrder.orderItems.size() > 0)
                                                        #foreach($orderItem in $customerOrder.orderItems)
                                                        <p  style="height: 40px;">$!{orderItem.serviceOfferName}【<a class="textcolorgreen" link="orderItem">$!{orderItem.applyObjSpecName}
                                                            #if($orderItem.accNum && $orderItem.accNum != "")
                                                            _ $!{orderItem.accNum}
                                                            #end
                                                        </a>】
                                                            <p class="vita-data">{"orderItem" : $orderItem,
                                                                            "custOrderId" : $!customerOrder.custOrderId,
                                                                            "regionId" : $!orderItem.regionId,
                                                                            "custOrderNbr":"$customerOrder.custOrderNbr"
                                                            }</p>
                                                        </p>
                                                        #end
                                                    #end
                                                </td>
                                                <td>
                                                    #if($customerOrder.orderItems != "null" && $customerOrder.orderItems.size() > 0)
                                                        #foreach($orderItem in $customerOrder.orderItems)
                                                        <p  style="height: 40px;">$!{orderItem.statusCdName}</p>
                                                        #end
                                                    #end
                                                </td>
												<td>$!{customerOrder.updateOrgName}</td>
                                                <td>$!{customerOrder.updateStaffName}</td>
                                                <td>$!{customerOrder.updateStaff}</td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.customerOrders && $options.customerOrders != "null" &&
                                            $options.customerOrders.size() == 0)
                                                <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
