(function (vita) {
    var agentAuditListQuery = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_agentAuditQuery",
            "click #btn-clear": "_clearCond",
            "click #regionIdBtn": "_chooseArea",
            "click #channelIdBtn": "_chooseChannel",
            "click .agencyInfoId":"_queryChannelOfAuditGroup"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
            var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd"
                });
            }
            ;
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd"
                });
            }
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
        },
        global: {
            pageIndex: 1,
            pageSize: 10,
            preset: "date",
            chooseArea: "../comm/chooseArea",
            chooseChannel: "../comm/chooseChannel",
            auditChannel: "../query/auditChannel"
        },
        _clearCond: function () {
            var $checked = $(".form-group").find("input[type=checkbox]:checked");
            $.each($checked, function (i,chkInput) {
                chkInput.click();
                var $input = $(chkInput).closest(".form-group").find("input.form-control");
                $input.val("");
                $input.attr("value", null);
                if(chkInput.id == "c_date") {
                    var endDate = $(chkInput).closest(".form-group").next().find("input.form-control");
                    endDate.val("");
                    endDate.attr("value", null);
                }
            })
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _chooseChannel: function () {
            var widget = this,element = $(widget.el);
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseChannel,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);
                }
            });
        },
        _queryChannelOfAuditGroup:function (e) {
            var widget = this,element = $(widget.el);
            var dialogId = "auditChannel";
            var agencyInfoId = $(e.target).data("agencyInfoId");

            widget.dialog({
                id : dialogId,
                url : widget.global.auditChannel,
                params : {
                    agencyInfoId:agencyInfoId
                }
            });
        },
        _agentAuditQuery: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("queryAgentAuditList", JSON.stringify(params), "#agentAuditListResult", function (res) {
                    var paging = this.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                var params = widget._getConds();
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if (params) {
                                    widget.refreshPart("queryAgentAuditList", JSON.stringify(params), "#agentAuditTable");
                                }
                                ;
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }

                }, { async: false});
            }
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this;
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            var paramCnt = 0;
            var regionId = $("#regionId").attr("value");
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.regionId = regionId;
            }

            if ($("#c_channelId").is(":checked")) {
                paramCnt++;
                var channelId = $("#channelId").attr("value");
                if(widget._isNullStr(channelId)) {
                    widget.popup("请选择渠道！");
                    return false;
                } else {
                    params.channelId = channelId;
                }
            }


            if ($("#c_auditName").is(":checked")) {
                paramCnt++;
                var auditName = $("#auditName").val();
                if(widget._isNullStr(auditName)) {
                    widget.popup("请输入稽核厅名称！");
                    return false;
                } else {
                    params.auditName = auditName;
                }
            }

            if($("#c_date").is(":checked")) {
                paramCnt++;
                var beginDate = $("#beginDate").val();
                var endDate = $("#endDate").val();
                if (widget._isNullStr(beginDate) || widget._isNullStr(endDate)) {
                    widget.popup("请选择日期范围！");
                    return false;
                } else {
                    params.startDt = beginDate;
                    params.endDt = endDate;
                }
            }
            return params;
        }
    });
    vita.widget.register("agentAuditListQuery", agentAuditListQuery, true);
})(window.vita);