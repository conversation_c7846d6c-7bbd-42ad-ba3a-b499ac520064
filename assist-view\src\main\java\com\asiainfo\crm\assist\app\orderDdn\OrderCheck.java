package com.asiainfo.crm.assist.app.orderDdn;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.asiainfo.appframe.ext.flyingserver.org.apache.commons.collections.MapUtils;
import com.asiainfo.crm.common.AbstractComponent;

import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.IOrderSMO;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 *
 */
@Component("vita.orderCheck")
public class OrderCheck extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(OrderCheck.class);
    @Autowired
    private IOrderQuerySMO iOrderQuerySMO;
    @Autowired
    private IOrderSMO iOrderSMO ;


    @Override
    public Map achieveData(Object... params) throws Exception {

        return null;
    }

    public Map byCoId(String params) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        Map req = jsonConverter.toBean(params,Map.class);
        String retStr;
        String custOrderId = null;
        String analysisStr = null;
        if ("0".equals(req.get("func"))){
            //0 根据coId 查询
            retStr= iOrderQuerySMO.queryCustomerOrderDetailsByCodId(req.get("str").toString());
            Map<String,Object> paramTow = new HashMap<String,Object>();
            paramTow.put("coId",req.get("str").toString());
            analysisStr= iOrderSMO.analysisOrder(req.get("str").toString());
            custOrderId = req.get("str").toString();
        }else {
            //1 根据csNbr 查询
            Map<String,Object> paramOne = new HashMap<String,Object>();
            paramOne.put("coNbr",req.get("str").toString());
            retStr = iOrderQuerySMO.queryCustomerOrderDetails(JSON.toJSONString(paramOne));
            Map map = jsonConverter.toBean(retStr, Map.class);
            Map resultObjectMap = (Map) MapUtils.getObject(map, "resultObject");
            if ((null != resultObjectMap || "".equals(resultObjectMap))&&
                    null!=MapUtils.getObject(resultObjectMap, "custOrderId")){
                analysisStr= iOrderSMO.analysisOrder(resultObjectMap.get("custOrderId").toString());
                custOrderId = resultObjectMap.get("custOrderId").toString();
            }
        }
        Map mapDeal = jsonConverter.toBean(analysisStr, Map.class);
        Map resultObjectMapDeal = (Map) MapUtils.getObject(mapDeal, "resultObject");
        List errItems = (List) MapUtils.getObject(resultObjectMapDeal, "errItems");
        options.put("errItems", errItems);
        if (null != custOrderId){
            String msgContent= iOrderSMO.qureyItemInfos(custOrderId);
            analysis(msgContent,options);
        }
        Map map = jsonConverter.toBean(retStr, Map.class);
        Map resultObjectMap = (Map) MapUtils.getObject(map, "resultObject");
        if (null != resultObjectMap || "".equals(resultObjectMap)){
            options.put("isData",false);
            options.put("isMessageAnalysis",true);
        }else {
            options.put("isData",true);
            options.put("isMessageAnalysis",false);
        }
        List prodOrderItems = (List) MapUtils.getObject(resultObjectMap, "prodOrderItems");
        options.put("prodOrderItems", prodOrderItems);
        List offerOrderItems = (List) MapUtils.getObject(resultObjectMap, "offerOrderItems");
        options.put("offerOrderItems", offerOrderItems);
        options.put("statusCd", MapUtils.getObject(resultObjectMap, "statusCd"));
        options.put("statusDate",MapUtils.getObject(resultObjectMap, "statusDate"));
        options.put("custOrderId",MapUtils.getObject(resultObjectMap, "custOrderId"));
        options.put("custId", MapUtils.getObject(resultObjectMap, "custId"));
        options.put("custOrderNbr", MapUtils.getObject(resultObjectMap, "custOrderNbr"));
        options.put("offerObjInstId",MapUtils.getObject(resultObjectMap, "offerObjInstId"));
        options.put("isTemplate",MapUtils.getObject(resultObjectMap, "isTemplate"));
        options.put("isSync", MapUtils.getObject(resultObjectMap, "isSync"));
        options.put("isPay", MapUtils.getObject(resultObjectMap, "isPay"));
        options.put("str", custOrderId);
        options.put("func", "coId");
        return  options;

    }



    /**
     * @Description: 分析日志内容
     * @Param:
     * @return:
     * @Author: leftHand
     * @Date: 2022-08-15
     */
    public JSONArray analysis(String jsonString, Map<String, Object> options) {
        JSONArray res = new JSONArray();
        JSONArray resultObject = JSON.parseObject(jsonString).getJSONArray("resultObject");
        if (null==resultObject){
            return res;
        }
        for (int i = 0; i < resultObject.size(); i++) {

            JSONObject item = (JSONObject) resultObject.get(i);
            String msgContent = (String) item.get("msgContent");
            String msgTopic = (String) item.get("msgTopic");
            String msgName = (String) item.get("msgName");
            Integer result = (Integer) item.get("result");
            List exceptions = (List) item.get("exceptions");
            JSONObject temp = new JSONObject();
            temp.put("msgTopic", msgTopic);
            temp.put("exceptions",exceptions);
            temp.put("msgName", msgName);
            res.add(temp);
        }
        options.put("analysisList",res);
        return res;
    }


}
