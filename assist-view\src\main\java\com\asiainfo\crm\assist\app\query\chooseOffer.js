(function(vita){
	var chooseOffer = vita.Backbone.BizView.extend({
		events : {
			"click #searchBtn" : "_queryList",
			"click #searchDel": "_searchParamsDel",
			"click .close" : "_closePage",
			"click #submit " : "_sumbitData"
		},
		_initialize : function() {
			debugger;
			var widget = this,
			element = $(widget.el);

			/**
			 * 设置之前已经被选择维度，进行过滤
			 */
			var data = element.data("data");
			widget.model.set("regionId", data.regionId);

            widget.global.pageSize = data.pageInfo ? data.pageInfo.pageSize : 10;
            widget._createPageInfo();
        },
		_closePage : function(){
			var widget = this,
			element = $(widget.el);
			var dialog = element.closest("[id=" + widget.widgetName + "Dialog]");
			if (dialog.length) {
				dialog.dialog("close");
			}
		},
		global : {
		},
		_createPageInfo : function(el) {
            var widget = this, element = el||$(widget.el),
                showPageInfo = element.find("#showPageInfo");
            var paging = widget.require("paging");
            var e = paging.new({
                pageIndex: 1,
                recordNumber: widget.global.pageSize,
                total: showPageInfo.data("totalNumber"),
				callback : function(pageIndex){
                    var param = widget._getConds(pageIndex);
                    widget.refreshPart("qryList", param, ".table-hover",
                        "items", function(res) {}, {
                            mask : true
                        });
				}
            });
            showPageInfo.empty().append(e.getElement());
        },
		/**
		 * 获取查询条件参数对象
		 */
		_getConds : function(pageIndex) {
			var widget = this,
				element = $(widget.el);
			var param = {
					"pageInfo" : {
						"pageIndex" : pageIndex||1,
						"pageSize" : widget.global.pageSize
					}
				};
			var offerName = widget.model.get("offerName");
            offerName = offerName ? offerName.trim() : "";
			var soUtil = widget.require("soUtil");
			var isNullOrEmpty = soUtil.isNullOrEmpty(offerName);
			if (!isNullOrEmpty) {
				param.offerName = offerName;
			}
			
			var regionId = widget.model.get("regionId");
			isNullOrEmpty = soUtil.isNullOrEmpty(regionId);
			if (!isNullOrEmpty) {
				param.regionId = regionId;
			}
			return param;
		},
		/**
		 * 清空查询条件
		 */
		_searchParamsDel : function() {
			var widget = this,
				element = $(widget.el);
	//			gSession = widget.gSession;
			widget.model.set("offerName","");
		},
		_queryList : function() {
			var widget = this,
				element = $(widget.el);
			var param = widget._getConds();
            widget.refreshPart("qryList", param, "#itemTable", function(res) {
                widget._createPageInfo($(res));
                }, {
                    mask : true
                });
		},
		setDatas : function(isBatchSelect){
			var widget = this, element = $(widget.el);
			widget.model.set("isBatchSelect",isBatchSelect);
		},
		_sumbitData : function (e){
			var widget = this,
				element = $(widget.el);
			validate = false;
			//该字段是否为多选类型
			var isBatchSelect = widget.model.get("isBatchSelect");
			var checkeds = element.find('#items').find('input:checked');
			var idArray = [];
			var items = [];
			$.each(checkeds,function(i,item){
				idArray.push($(item).data('data').offerId);
				items.push($(item).data('data'));
			});
			if(idArray.length == 0){
				widget.popup("请选择销售品！");
				return false;
			}
			//如果不是多选，判断单选
			if(isBatchSelect != "YES" && idArray.length > 1){
				widget.popup("只能选择一个销售品!");
				return false;
			}
			validate = true;
			if(isBatchSelect != "YES"){
				widget.model.set({
					validate : validate,
					offerId : items[0].offerId,
					offerName : items[0].offerName
				});
			}else{
				widget.model.set({
					validate : validate,
					items : items
				});
			}
			widget._closePage();
			return false;
		},
		getValue : function() {
			var widget = this;
			var valueJSON = widget.model.toJSON();
			return valueJSON;
		}
	});
	 vita.widget.register("chooseOffer", chooseOffer, true); 
})(window.vita);
