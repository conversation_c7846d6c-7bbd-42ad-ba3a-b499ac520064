(function (vita) {
    var customerOrderListQuery29 = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_customerOrderListQuery29",
            "click #btn-clear": "_clearCond",
            "click #regionIdBtn": "_chooseArea",
            "click #channelIdBtn": "_chooseChannelClick",
            "click #staffIdBtn": "_chooseStaff",
            "click #custIdBtn": "_chooseCust",
            "click [link=customerOrder]": "_queryCustomerOrderDetailLink",
            "click [link=orderItem]": "_queryOrderItemDetailLink",

            "click #viewInvoice": "_viewInvoice",
            "click #printInvoice": "_printInvoice",
            "click #printReceipt": "_printReceipt",
            "click #orderDetail": "_queryCustomerOrderDetail",
//          "click #c_custOrderNbr": "_custOrderNbrClick",
            "click input[type=checkbox]": "_nbrClick",
            "click #qryTimeQuantum button" : "_qryTimeQuantum",
            "click #c_accNum" : "_accNumClick"
            // "click #c_orderItemNbr" : "_orderItemNbrClick",
            // "click #c_custOrderNbr" : "_custOrderNbrClick"
        },
        _viewInvoice: function () {
            var widget = this,element = $(widget.el);
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            var statusCd = $($tr.find("[link=customerOrder]")).data("statusCd");
            if(statusCd == 'D'){
            	widget.popup("购物车已作废，无法查看发票！");
            	return false;
			}
            var custOrderId = $($tr.find("[link=customerOrder]")).data("custOrderId");
            var regionId = element.find("#regionId").attr("value");
            var data = element.data("data");
            var url = data.viewInvoice;
            url += "?olId="+custOrderId+"&vitaAreaId="+regionId+"&isInnerNet="+true;
            window.open(url);
        },
        _printInvoice: function () {
            var widget = this,element = $(widget.el);
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            
            var statusCd = $($tr.find("[link=customerOrder]")).data("statusCd");
            if(statusCd == 'D'){
            	widget.popup("购物车已作废，无法重打发票！");
            	return false;
			}
            var isNeedPrint = $($tr.find("[link=customerOrder]")).data("isNeedPrint");
            if(isNeedPrint == '1'){
            	widget.popup("该购物车没有费用，不能重打发票！");
            	return false;
			}
            var printClassDom = $($tr.find("[link=customerOrder]")).data("printClassDom");
            if(printClassDom == '-2'){
            	widget.popup("注意参加光网智慧小区团购活动，不允许打发票！");
            	return false;
			}
            var custOrderId = $($tr.find("[link=customerOrder]")).data("custOrderId");
            var custOrderNbr = $($tr.find("[link=customerOrder]")).data("custOrderNbr");
            var partyId = $($tr.find("[link=customerOrder]")).data("partyId");
            var regionId = element.find("#regionId").attr("value");
            var data = element.data("data");
            var url = data.invoice;
            url += "?olId="+custOrderId+"&olNbr="+custOrderNbr+"&printFlag=2&printClass=1&vitaAreaId="+regionId+"&partyId="+partyId;
    
            window.open(url);
        },

        
        _printReceipt: function () {
            var widget = this,element = $(widget.el);
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            
            var $order = $(orders[0]);
            var $tr = $($($order).closest("tr"));
            
            var statusCd = $($tr.find("[link=customerOrder]")).data("statusCd");
            if(statusCd == 'D'){
            	widget.popup("购物车已作废，无法进行重打回执！");
            	return false;
			}
            var custOrderId = $($tr.find("[link=customerOrder]")).data("custOrderId");
            var regionId = element.find("#regionId").attr("value");
            var url = element.data("data").receipt;
            url = url + "?custOrderNbr=" + custOrderId + "&regionId=" + regionId;

            window.open(url);
        },

        _initialize: function () {
            var widget = this, element = $(widget.el);
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var nowDate = new Date();
            var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
            var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
            var startYear = nowDate.getFullYear() - 5, endYear = nowDate.getFullYear();
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear:endYear
                });
            }
            ;
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear:endYear
                });
            }
            //日期控件设置默认时间
            widget.global.defaultBeginTime = widget._getFormatDate(widget.global.sevenDayCount); //默认是七天
            widget.global.defaultEndTime = widget._currentDate();
            widget._setDefaultDateTime();

            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
            var channelName = "";
            for(var channels=gSession.channels,i=0; channels.length && i<channels.length; i++) {
                if(gSession.curChannelId == channels[i].orgId) {
                    channelName = channels[i].channelName;
                    break;
                }
            }
            if(gSession.curChannelId && gSession.curChannelId != "") {
                element.find("#channelId").val(channelName).attr("value", gSession.curChannelId);
            } else {
                widget._chooseChannel(true);
            }
            element.find("#staffId").val(gSession.staffName).attr("value", gSession.staffId);

      

            //nbrLabelsStr初始化
            var nbrLabels = widget.global.nbrLabels;
            var nbrLabelsStr = "#"+nbrLabels[0];
            for(var k=1;k<nbrLabels.length;k++) {
                nbrLabelsStr = nbrLabelsStr + ",#" + nbrLabels[k];
            }
            widget.global.nbrLabelsStr = nbrLabelsStr;
        },

        _getFormatDate: function(days) {
            var date = new Date();
            var yesterday_milliseconds = date.getTime()+days*1000*60*60*24;
            var yesterday = new Date();
            yesterday.setTime(yesterday_milliseconds);
            var strYear = yesterday.getFullYear();
            var strDay = yesterday.getDate();
            var strMonth = yesterday.getMonth()+1;
            if(strMonth<10)
            {
                strMonth="0"+strMonth;
            }
            if(strDay<10)
            {
                strDay="0"+strDay;
            }

            /*var hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
            var minute = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
            var second = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
            var time = hour+":"+minute+":"+second;*/
            var datastr = strYear+"-"+strMonth+"-"+strDay;
            return datastr;
        },
        global: {
            pageIndex: 1,
            pageSize: 5,
            preset: "date",
            chooseArea: "../comm/chooseArea",
            chooseChannel: "../comm/chooseChannel",
            chooseStaff: "../comm/chooseStaff",
            chooseCust: "../comm/chooseCust",
            defaultBeginTime:"",
            defaultEndTime:"",
            currentDayCount:0,
            sevenDayCount:-6,
            lastMonthDayCount:-29,
            ninthDayCount:-89,
            nbrLabels: ["c_custOrderNbr","c_orderItemId"]
        },
        _clearCond: function () {
            var widget = this, element = $(widget.el);
            var $checked = element.find(".form-group").find("input[type=checkbox]:checked");
            $.each($checked, function (i,chkInput) {
                chkInput.click();
                var $input = $(chkInput).closest(".form-group").find("input.form-control");
                $input.val("");
                $input.attr("value", null);
            })
            //时间设置为7天
            widget._changeTimeQuanTum(widget.global.sevenDayCount);
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _chooseChannelClick: function() {
            var widget = this,element = $(widget.el);
            widget._chooseChannel(false);
        },
        _chooseChannel: function(isNeedRefreshSession) {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseChannel,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);

                    if(isNeedRefreshSession) {
                        gSession.channelId   = data.orgId;
                        gSession.curChannelId= data.orgId;
                        gSession.curChannelNbr  = data.channelNbr;
                        gSession.channelName = data.channelName;
                        gSession.channelRegionId = data.channelRegionId;
                        gSession.channelRegionName = data.channelRegionName;
                        gSession.channelClass = data.channelClass;
                        gSession.orgId       = data.orgId;
                        gSession.orgName     = data.orgName;

                        gSession.orderArea   	 = data.channelRegionId; // 订单地区
                        gSession.installArea 	 = data.channelRegionId; // 安装地区
                        gSession.installAreaName = data.channelRegionName;
                        var param = {
                            curChannelId  : data.orgId,
                            curChannelNbr : data.channelNbr,
                            orderArea     : gSession.orderArea,
                            installArea   : gSession.installArea
                        };
                        widget.refreshSession(param);
                    }
                }
            });
        },
        _chooseStaff: function () {
            var widget = this,element = $(widget.el);
            var compCode = "chooseStaff";
            var dialogId = "chooseStaffDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseStaff,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data || data == "") {
                        return false;
                    }
                    element.find("#staffId").val(data.staffName).attr("value", data.staffId);
                }
            });
        },
        _chooseCust: function () {
            var widget = this,element = $(widget.el);
            var compCode = "chooseCust";
            var dialogId = "chooseCustDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseCust,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data || !data.validate) {
                        return false;
                    }
                    element.find("#custId").val(data.custName).attr("value", data.custId);
                }
            });
        },
        _nbrClick: function(e) {
            var widget = this, element = $(widget.el),
                nbrLabels = widget.global.nbrLabels,
                nbrLabelsStr = widget.global.nbrLabelsStr;
            var currentTarget = $(e.currentTarget);
            //界面样式渲染后才会調click事件，所以当状态是checked的时候才去清空其他选项
            var nbrChk = element.find(nbrLabelsStr).filter(":checked");
            var beginDate = element.find("#beginDate"),endDate =  element.find("#endDate");
            var dateTimeFlag = "on";
            if(!beginDate.prop("disabled")){
                widget.global.defaultBeginTime = beginDate.val();
                widget.global.defaultEndTime = endDate.val();
            }
            if(nbrChk.length != 0){
                var $checked = $(".form-group").find("input[type=checkbox]:checked").not(nbrLabelsStr);
                $.each($checked, function (i,chkInput) {
                    $(chkInput).attr('checked', false);
                    // var $input = $(chkInput).closest(".form-group").find("input.form-control");
                    // $input.val("");
                    // $input.attr("value", null);
                });
                dateTimeFlag = "off";
            } else {
                dateTimeFlag = "on";
            }
            //时间控件开关
            widget._dateTimeSwitch(dateTimeFlag);
            //如果本次点击的是nbrLabels中的一个，
            // 需要判断是否有其他nbrLabels中的成员是checked的
            //如果有，则本次点击无效
            var currentTargetId = currentTarget.attr("id");
            if($.inArray(currentTargetId,nbrLabels)>-1) {
                var nbrFlag = false;
                $.each(nbrLabels,function(i,n){
                    if (n == currentTargetId) {
                        return true;
                    }
                    if (element.find("#" + n).is(":checked")) {
                        nbrFlag = true;
                        return false;
                    }
                });
                if(nbrFlag){
                    currentTarget.attr("checked", false);
                }
            }
        },
        _customerOrderListQuery29: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("queryCustomOrderList", JSON.stringify(params), "#orderListResult", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    debugger;
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if(_pageIndex > 1 && !params.pageInfo.rowCount) {
                                    params.pageInfo.rowCount = totalNumber;
                                }
                                if (widget._getConds()) {
                                    widget.refreshPart("queryCustomOrderList", JSON.stringify(params), "#orderList");
                                }
                                ;
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }

                }, {
                    async: false
                });
            }
            ;
        },
        _queryCustomerOrderDetail: function () {
            var widget = this,element = $(widget.el);
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            var custOrderId = $($tr.find("[link=customerOrder]")).data("custOrderId");
            var custOrderNbr = $($tr.find("[link=customerOrder]")).data("custOrderNbr");
            var regionId = element.find("#regionId").attr("value");
            var qryHisType = $("#qryScope").val();
            window.open("../../crm/so/qryCartBaseInfo?olId="+custOrderId+ "&olNbr=" + custOrderNbr+ "&regionId=" + regionId+ "&qryHisType=" + qryHisType);
        },
        _queryCustomerOrderDetailLink: function(e){
            var widget = this,element = $(widget.el);
            var aEl = $(e.target).closest("a");

            var custOrderId = aEl.data("custOrderId");
            var custOrderNbr = aEl.data("custOrderNbr");
            if(!custOrderId){
                widget.popup("订单不存在,请重新查询后再试.");
                return;
            }
            var regionId = element.find("#regionId").attr("value");
            var qryHisType = $("#qryScope").val();

            // widget.end("olDetailInfo",data); 这里没有配流程不能用框架的end
            window.open("../../crm/so/qryCartBaseInfo?olId="+custOrderId+ "&olNbr=" + custOrderNbr+ "&regionId=" + regionId+ "&qryHisType=" + qryHisType);
        },
        _queryOrderItemDetailLink: function (e) {
            var widget = this,element = $(widget.el);
            var aEl = $(e.target).closest("a");
  
            var custOrderId = aEl.parent().data("custOrderId");
            var custOrderNbr = aEl.parent().data("custOrderNbr");//购物车流水
            var orderItem = aEl.parent().data("orderItem");
            if (!custOrderId) {
                widget.popup("订单不存在,请重新查询后再试.");
                return;
            }
            if (!orderItem.boId) {
                widget.popup("订单不存在,请重新查询后再试.");
                return;
            }
            var uri = "../../crm/so/qrySoInfo";
            
            var flag = orderItem.flag;
            var param = "";
            var regionId = element.find("#regionId").attr("value");
            var qryHisType = $("#qryScope").val();
            if("cust" == flag){
            	param = "?olId="+custOrderId+"&olNbr="+custOrderNbr+
                "&boId="+orderItem.boId+"&offerId=null"+
                "&flag=cust&tab=0"+"&regionId="+regionId+"&qryHisType="+qryHisType;
            	
            }
            if("prod" == flag){
            	param = "?olId="+custOrderId+"&olNbr="+custOrderNbr+
                "&boId="+orderItem.boId+"&offerId=null"+
                "&flag=prod&tab=0"+"&prodId="+orderItem.prodId+"&prodSpecId="+orderItem.prodSpecId+"&regionId="+regionId+"&qryHisType="+qryHisType;
            	
            }
            if("offer" == flag){
            	param = "?olId="+custOrderId+"&olNbr="+custOrderNbr+
                "&boId="+orderItem.boId+"&offerId="+orderItem.offerId+
                "&flag=offer&tab=0"+"&regionId="+regionId+"&qryHisType="+qryHisType;
            	
            }
            window.open(uri+param);

            
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this,element = $(widget.el);
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            var paramCnt = 0;
            var regionId = element.find("#regionId").attr("value");
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.regionId = regionId;
            }

            if (element.find("#c_channelId").is(":checked")) {
                paramCnt++;
                var channelId = element.find("#channelId").attr("value");
                if(widget._isNullStr(channelId)) {
                    widget.popup("请选择渠道！");
                    return false;
                } else {
                    params.channelId = channelId;
                }
            }

            if (element.find("#c_staffId").is(":checked")) {
                paramCnt++;
                var staffId = element.find("#staffId").attr("value");
                if(widget._isNullStr(staffId)) {
                    widget.popup("请选择营业员！");
                    return false;
                } else {
                    params.staffId = staffId;
                }
            }

            if (element.find("#c_custOrderNbr").is(":checked")) {
                paramCnt++;
                var custOrderNbr = $.trim(element.find("#custOrderNbr").val());
                if(widget._isNullStr(custOrderNbr)) {
                    widget.popup("请输入流水号！");
                    return false;
                } else {
                    params.olNbr = custOrderNbr;
                }
            }

            if (element.find("#c_orderItemId").is(":checked")) {
                paramCnt++;
                var orderItemId = $.trim(element.find("#orderItemId").val());
                if(widget._isNullStr(orderItemId)) {
                    widget.popup("请输入订单项Id！");
                    return false;
                } else {
                    params.busiActionId = orderItemId;
                }
            }

            if (element.find("#c_custId").is(":checked")) {
                paramCnt++;
                var custId = element.find("#custId").attr("value");
                if(widget._isNullStr(custId)) {
                    widget.popup("请选择客户！");
                    return false;
                } else {
                    params.partyId = custId;
                }
            }

            if (element.find("#c_accNum").is(":checked")) {
                paramCnt++;
                var accNum = $.trim(element.find("#accNum").val());
                if(widget._isNullStr(accNum)) {
                    widget.popup("请输入号码！");
                    return false;
                } else {
                    params.qryNumber = accNum;
                }
            }

            params.queryType = "orderQquery";

            var startTime = element.find("#beginDate").val();
            var endTime = element.find("#endDate").val();
            var accNumComp = element.find("#c_accNum");
            startTime = startTime ? startTime + " 00:00:00" : startTime;
            endTime = endTime ? endTime + " 23:59:59" : endTime;
        	if(!accNumComp.is(":checked")&& element.find("input[type=checkbox]:checked").filter("#c_custId,#c_staffId,#c_channelId").length){
        		if (widget._isNullStr(startTime) || widget._isNullStr(endTime)) {
        			widget.popup("选择渠道、营业员、所属客户、号码这四个条件之一时， 开始时间、结束时间不能空!");
            		return false;
        		}
        	}
        	if (!widget._isNullStr(startTime) && !widget._isNullStr(endTime)) {
                if(startTime>=endTime){
                    widget.popup("结束时间应大于开始时间!");
                    return false;
                }
                var start  = new Date(startTime.replace(/-/g,"/")).getTime();
                var end = new Date(endTime.replace(/-/g,"/")).getTime();

                var flag = end - start  > 90*24*60*60*1000
                if (flag) {
                    widget.popup("开始、结束时间跨度不能超出90天!");
                    return false;
                }
			}
            if(!widget._isNullStr(startTime)||!widget._isNullStr(endTime)) {
                if (!widget._isNullStr(startTime)) {
                    params.startDt = startTime;
                }
                if (!widget._isNullStr(endTime)) {
                    params.endDt = endTime;
                }
            }
            if (element.find("#c_statusCd").is(":checked")) {
                paramCnt++;
                var statusCd = element.find("#statusCd").val();
                if(widget._isNullStr(statusCd)) {
                    widget.popup("请选择订单状态！");
                    return false;
                }
//                if (!element.find("#c_custId").is(":checked") && !element.find("#c_staffId").is(":checked") && !element.find("#c_channelId").is(":checked")) {
//                	widget.popup("选择订单状态条件时，“渠 道、营业员、所属客户” 这3个条件里面必须选一个！");
//                    return false;
//                }
                if(statusCd=='B'){
                	params.boStatusCd= 'B';
                }else{
                	var statusCds=new Array();
					statusCds[0]=statusCd;
					params.statusCds=statusCds;
                }
                
            }
            
            params.qryBusiOrder="1";

            var qryScope = $("#qryScope").val();
            if (widget._isNullStr(qryScope)) {
                widget.popup("请选择查询范围！");
                return false;
            } else {
                params.qryHisType = qryScope;
            }
            if(paramCnt < 1) {
                widget.popup("请至少选择一个查询条件！");
                return false;
            }
            return params;
        },
        _qryTimeQuantum:function(e){
            var widget = this,element = $(widget.el),
                timeQuantum = $(e.currentTarget).attr("name"),
                dayCount;
            timeQuantum = timeQuantum.substring(timeQuantum.indexOf("_") + 1);
            switch(timeQuantum){
                case "1":
                    dayCount = widget.global.currentDayCount;
                    break;
                case "7":
                    dayCount = widget.global.sevenDayCount;
                    break;
                case "30":
                    dayCount = widget.global.lastMonthDayCount;
                    break
                case "90":
                    dayCount = widget.global.ninthDayCount;
                    break
                default:
                    dayCount = widget.global.currentDayCount;
            }
            widget._changeTimeQuanTum(dayCount);
        },
        _changeTimeQuanTum:function(dayCount){
            var widget = this,element = $(widget.el),beginDate = element.find('#beginDate'),endDate = element.find('#endDate');
            if(dayCount==widget.global.currentDayCount){
                beginDate.val(widget._currentDate());
            }else if(dayCount==widget.global.lastMonthDayCount){
                beginDate.val(widget._lastMonthDate());
            }else{
                beginDate.val(widget._getFormatDate(dayCount));
            }
            endDate.val(widget._getFormatDate(widget.global.currentDayCount));
        },
        //当天从0点开始
        _currentDate:function(){
            var widget = this, el = $(widget.el);
            var nowDate = widget._getFormatDate(widget.global.currentDayCount);
            return nowDate;
        },
        //设置默认时间
        _setDefaultDateTime : function(){
            var widget = this, element = $(widget.el),
                beginDate = element.find("#beginDate"),
                endDate = element.find("#endDate");
            beginDate.val(widget.global.defaultBeginTime);
            endDate.val(widget.global.defaultEndTime);
        },
        //时间控件开发方法
        _dateTimeSwitch:function(flag){
            var widget = this, element = $(widget.el),
                beginDate = element.find("#beginDate"),
                endDate = element.find("#endDate");
            if(flag==="on"){
                widget._setDefaultDateTime();
                beginDate.prop("disabled", false);
                endDate.prop("disabled", false);
                element.find("#qryTimeQuantum").find('button').removeAttr('disabled');
            }else{
                beginDate.val("");
                endDate.val("");
                element.find("#qryTimeQuantum").find('button').attr('disabled','disabled');
                beginDate.prop("disabled", true);
                endDate.prop("disabled", true);
            }
        },
        //获取上个月时间
        _lastMonthDate:function (){
            var Nowdate = new Date();
            var vYear = Nowdate.getFullYear();
            var vMon = Nowdate.getMonth() + 1;
            var vDay = Nowdate.getDate();
            //每个月的最后一天日期（为了使用月份便于查找，数组第一位设为0）
             var daysInMonth = new Array(0,31,28,31,30,31,30,31,31,30,31,30,31);
            if(vMon==1){
                vYear = Nowdate.getFullYear()-1;
                vMon = 12;
                }else{
                 vMon = vMon -1;
                }
            //若是闰年，二月最后一天是29号
            if(vYear%4 == 0 && vYear%100 != 0  || vYear%400 == 0 ){
                  daysInMonth[2]= 29;
            }
            if(daysInMonth[vMon] < vDay){
                     vDay = daysInMonth[vMon];
            }
            if(vDay<10){
                    vDay="0"+vDay;
            }
            if(vMon<10){
                    vMon="0"+vMon;
            }
            var date =vYear+"-"+ vMon +"-"+vDay;
            return date;
        },
        _accNumClick : function(){
            var widget = this, element = $(widget.el),
                accNumLabel = element.find("#c_accNum"),
                beginDate = element.find("#beginDate"),
                endDate = element.find("#endDate");
	    //如果nbrLabelsStr成员有checked状态，则返回
            var nbrChk = element.find(widget.global.nbrLabelsStr).filter(":checked");
            if(nbrChk.length){
                return;
            }
         
            widget.global.defaultBeginTime = widget._getFormatDate(widget.global.sevenDayCount);
            widget.global.defaultEndTime = widget._currentDate();
            widget._setDefaultDateTime();
           
        }
    });
    vita.widget.register("customerOrderListQuery29", customerOrderListQuery29, true);
})(window.vita);