(function(vita){
    var addOrderCheckBusi = vita.Backbone.BizView.extend({
        events : {
            "click #saveOrderCheckBusiBtn":"_saveOrderCheckBusi",
            "click #closeCustAuthBtn" : "_closePage",
            "click #regionIdBtn": "_chooseArea"
        },
        _initialize:function(){
            var widget = this,
                element = $(widget.el);
            var gSession = widget.gSession;
        },
        global:{
            pageIndex:1,
            pageSize:5,
            chooseArea: "../comm/chooseArea",
            preset : "date"
        },
        _saveOrderCheckBusi: function () {
            var isImmd = true;
            if (isImmd) {
                var widget = this,
                    gSession = widget.gSession,
                    element = $(widget.el);
                if(!widget._validate()){
                    return false;
                }
                var serviceName = widget.model.get("serviceName");
                var statusCd = element.find("#statusCd").val();
                var checkSite = element.find("#checkSite").val();
                var checkType = element.find("#checkType").val();
                var specType = element.find("#specType").val();
                var tacheCode = element.find("#tacheCode").val();
                var regionId = element.find('#applyRegionId').attr("value");
                var param = {
                    "serviceName" :serviceName,
                    "regionId" :regionId,
                    "statusCd" :statusCd,
                    "checkSite" : checkSite,
                    "checkType" : checkType,
                    "specType" : specType,
                    "tacheCode" : tacheCode,
                }
                widget.callService("saveOrderCheckBusi", param, function(res) {
                    var ret = JSON.parse(res);
                    if (ret.resultCode == 0) {
                        widget.popup("新增成功！",function(){
                            widget._closePage();
                        });
                    }else{
                        widget.popup("新增失败！"+ret.resultMsg,function(){
                            widget._closePage();
                        });
                    }

                }, {
                    async : true,
                    mask : true
                });
            }
        },
        _checkValue: function (condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty ? false : true;
        },
        //取消
        _closePage : function() {
            var widget = this,
                element = $(widget.el);
            element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
        },
        _validate : function(){
            var widget = this, element = $(widget.el);
            var serviceName = widget.model.get("serviceName");
            if (serviceName == "" || serviceName == null) {
                alert("【稽核业务名称】不能为空");
                $("#serviceName").focus;
                return false;
            }
            return true;
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#applyRegionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        }
    });
    vita.widget.register("addOrderCheckBusi", addOrderCheckBusi, true);
})(window.vita);
