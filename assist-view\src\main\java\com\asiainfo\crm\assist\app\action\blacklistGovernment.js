(function (vita) {
    var blacklistGovernment = vita.Backbone.BizView.extend({
        events: {
            "click #qryButton": "_epayBonusSendQuery",
            "click #savaButton": "_savaButton",
            "click #deleteButton" : "_deleteButton"
        },
        _checkAllResult: function (e) {
            var widget = this,element = $(widget.el);
            var $target = e.target;
            var $allBounsInputs = element.find('table input[name="payment"]');
            $.each($allBounsInputs, function (i,$check) {
                if(!$target.checked && $check.checked){
                    $check.click();
                } else if($target.checked && !$check.checked) {
                    $check.click();
                }
            });
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
        },
        global: {
            pageIndex: 1,
            pageSize: 10,
            preset: "date"
        },
        _deleteButton: function(){
        	var widget = this,element = $(widget.el);
	       	var idArray = "";
	       	var checkFlag = "";
	            element.find("#blacklistGovernmentResult :checkbox").each(function (i, oi) {
	                var o = $(oi);
	                if(o.is(":checked")){
	                	 checkFlag=o.data("data").checkFlag;
	                    var custId=o.data("data").custId;
	                    idArray=idArray+custId+",";
	                }
	            });
	         idArray = idArray.substring(0,idArray.length-1);
	         if (1 != checkFlag) {
        		 widget.popup("请选择黑名单客户进行操作！");
                 return;
			}
	         if (idArray == "") {
            	 widget.popup("请选择需要移出黑名单的客户！");
                 return;
			}
	         var params={
	                 ids : idArray
	             };
	         widget.callService("deleteBlacklistGovernment", JSON.stringify(params), function(res) {
	        	 if (res != null && res != ""){
                	 widget.popup("操作成功！");
                	 $("#qryButton").click();
                     return;
                 }else{
                     widget.popup("操作失败");
                 }
             }, {
                 async : false
             });
        },
        _savaButton: function(){
        	 var widget = this,element = $(widget.el);
        	 var gSession = widget.gSession;
        	 var idArray = "";
        	 var checkFlag = "";
             element.find("#blacklistGovernmentResult :checkbox").each(function (i, oi) {
                 var o = $(oi);
                 if(o.is(":checked")){
                	 checkFlag=o.data("data").checkFlag;
                     var custId=o.data("data").custId;
                     idArray=idArray+custId+",";
                 }
             });
             idArray = idArray.substring(0,idArray.length-1);
             if (1 == checkFlag) {
        		 widget.popup("请选择非黑名单客户进行操作！");
                 return;
			}
             if (idArray == "") {
            	 widget.popup("请选择需要加入黑名单的客户！");
                 return;
			}
             var n = window.prompt('请输入操作原因！');
             if ("" == n) {
          	    widget.popup("操作原因不能为空！");
          		return;
      		}else if (null == n) {
      			return;
  			}
             var params={
                 ids : idArray,
                 staffId : gSession.staffId,
                 specialListReason : n
             };
             widget.callService("addBlacklistGovernment", JSON.stringify(params), function(res) {
            	 if (res != null && res != ""){
                	 widget.popup("操作成功！");
                	 $("#qryButton").click();
                     return;
                 }else{
                     widget.popup("操作失败");
                 }
             }, {
                 async : false
             });
        },
        _epayBonusSendQuery: function () {
            var widget = this;
            var params = widget._getConds();
            if (!params) {
				return;
			}
            
            debugger
            if (params) {
                widget.refreshPart("queryBlacklistGovernment", JSON.stringify(params), "#blacklistGovernmentResult", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if (widget._getConds()) {
                                    widget.refreshPart("queryBlacklistGovernment", JSON.stringify(params), "#blacklistGovernment");
                                }
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }
                }, {
                    async: false
                });
            }
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this;
            var certNum = $("#certNum").val();
            certNum = certNum.replace(/\s*/g,"");
            if (certNum == "") {
            	widget.popup("请输入证件号码！");
            	return false;
			}
            var params = {
            		certNum: $("#certNum").val(),
            		certType : $('#certType').val()
            };
            return params;
        }
    });
    vita.widget.register("blacklistGovernment", blacklistGovernment, true);
})(window.vita);