(function (vita) {
    var algorithmChoose = vita.Backbone.BizView.extend({
        events: {
            "click #searchBtn": "_queryAlgorithms",
            "click input[name=algorithmRadio]": "_selectAlgorithm",
            "click #submitBtn": "_handleSubmit",
            "click #closeBtn": "_handleClosePage"
        },
        global: {
            pageIndex: 1,
            pageSize: 5,
            preset: "date"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            widget._queryAlgorithms();
        },
        _queryAlgorithms: function () {
            var widget = this, element = $(widget.el);
            var params = widget._getConds();
            widget.refreshPart("queryAlgorithms", JSON.stringify(params), "#algorithmListResult", function (res) {
                var paging = widget.require("paging"), r = $(res);
                var totalNumber = r.find("#showPageInfo").data("totalNumber");
                if (totalNumber > 0) {
                    var e = paging.new({
                        recordNumber: widget.global.pageSize,
                        total: totalNumber,
                        pageIndex: widget.global.pageIndex,
                        callback: function (_pageIndex, _recordNumber) {
                            params.pageInfo.pageIndex = _pageIndex;
                            params.pageInfo.pageSize = _recordNumber;
                            if (_pageIndex > 1 && !params.pageInfo.rowCount) {
                                params.pageInfo.rowCount = totalNumber;
                            }
                            if (widget._getConds()) {
                                widget.refreshPart("queryAlgorithms", JSON.stringify(params), "#algorithmList", function (res) {

                                }, {mask: true});
                            }
                        }
                    });
                    r.find("#showPageInfo").append(e.getElement());
                }

            }, {mask: true});
        },
        _getConds: function () {
            var widget = this, element = $(widget.el);
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            var algoNbr = $.trim(element.find("#algoNbr").val());
            if (!widget._isNullOrEmpty(algoNbr)) {
                params.algoNbr = algoNbr;
            }
            var algoKey = $.trim(element.find("#algoKey").val());
            if (!widget._isNullOrEmpty(algoKey)) {
                params.algoKey = algoKey;
            }
            var statusCd = element.find("#statusCd").val();
            if (!widget._isNullOrEmpty(statusCd)) {
                params.statusCd = statusCd;
            }
            return params;
        },
        _clearCond: function () {
            var widget = this, element = $(widget.el);
            var $input = element.find(".form-group").find("input.form-control");
            $input.val("");
            var $select = element.find(".form-group").find("select.form-control");
            $select.val("");
        },
        _isNullOrEmpty: function (condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty;
        },
        _selectAlgorithm: function (e) {
            console.log("test...");//TODO TEST
            var widget = this;
            var targetDom = $(e.target).closest("label");
            var algorithmInfo = targetDom.data("algorithmInfo");
            widget.model.set(algorithmInfo);
        },
        _handleSubmit: function () {
            var widget = this;
            widget._handleClosePage();
        },
        _handleClosePage: function () {
            var widget = this, element = $(widget.el);
            element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
        },
        getValue : function() {
            var widget = this;
            var algorithmInfo = widget.model.toJSON();
            return algorithmInfo;
        }
    });
    vita.widget.register("algorithmChoose", algorithmChoose, true);
})(window.vita);