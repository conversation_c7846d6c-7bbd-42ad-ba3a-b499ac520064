package com.asiainfo.crm.assist.app.cust;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.service.intf.ICustSMO;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("vita.handler")
public class Handler extends AbstractComponent {

    @Override
    public Map achieveData(Object... objects) throws Exception {
        return null;
    }


}
