package com.asiainfo.crm.assist.app.query;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.Constant;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.IOrderSaveSmo;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import com.asiainfo.crm.service.intf.IStaffInfoQuerySMO;
import com.asiainfo.crm.service.intf.IStaffOrgAreaSMO;
import com.asiainfo.crm.util.ListUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

/**
 * Created on 2019/4/10.
 */
@Component("vita.qryPrepareOrderInfo")
public class QryPrepareOrderInfo extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(QryPrepareOrderInfo.class);

    @Autowired
    private IOrderSaveSmo orderSaveSMO;

    @Autowired
    private IOrderQuerySMO orderQuerySMO;

    @Autowired
    private IStaffOrgAreaSMO staffOrgAreaSMO;

    @Autowired
    private ISoQuerySMO soQuerySMO;

    @Autowired
    private IStaffInfoQuerySMO staffInfoQuerySMO;

    @Autowired
    private ICustMultiSMO custMultiSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        List<Map<String, String>> statusList = new ArrayList<>(8);
        Set<Map.Entry<String, String>> statusEntries = AssistMDA.PREPARE_ORDER_TYPE.entrySet();
        for (Map.Entry<String, String> entry : statusEntries) {
            Map<String, String> map = new HashMap<>(2);
            map.put("key", entry.getKey());
            map.put("value", entry.getValue());
            statusList.add(map);
        }
        options.put("statusList", statusList);
        options.put("allopatryType", AssistMDA.ALLOPATRY_OPEN_ORDER_TYPE);
        return options;
    }

    /**
     * 预受理查询
     */
    public Map qryPrepareOrderInfo(String json) throws Exception {
        String prepareOrderList = orderQuerySMO.qryPrepareOrders(json);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(prepareOrderList);
        Map<String, Object> options = new HashMap<>(4);
        List<Map> prepareOrders = Lists.newArrayList();
        List<Map> prepareOrderDatas = (List) MapUtils.getObject(resultObjectMap, "prepareOrderVos");
        for (Map prepareOrderData : prepareOrderDatas) {
            Map prepareOrder = Maps.newHashMap();
            prepareOrder.putAll(prepareOrderData);
            String createOrgId = MapUtils.getString(prepareOrderData, "createOrgId");
            if (!StringUtil.isEmpty(createOrgId)) {
                prepareOrder.put("createOrgName", qryChannelName(createOrgId));
            }
            String regionId = MapUtils.getString(prepareOrderData, "regionId");
            if (!StringUtil.isEmpty(regionId)) {
                prepareOrder.put("regionName", qryRegionName(regionId));
            }
            String createStaff = MapUtils.getString(prepareOrderData, "createStaff");
            if (!StringUtil.isEmpty(createStaff)) {
                prepareOrder.put("staffName", qryStaffName(createStaff));
            }
            String custId = MapUtils.getString(prepareOrderData, "custId");
            String custName = MapUtils.getString(prepareOrderData, "custName");
            if (!StringUtil.isEmpty(custId) && StringUtil.isEmpty(custName)) {
                prepareOrder.put("custName", qryCustName(custId));
            }
            String orderType = MapUtils.getString(prepareOrderData, "orderType");
            if (AssistMDA.ALLOPATRY_OPEN_ORDER_TYPE.equals(orderType)) {
            	prepareOrder.put("isInfo", Constant.LOGIC_STR_Y);
            }
            prepareOrders.add(prepareOrder);
        }
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap, "pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount", pageCount);
        options.put("pageIndex", pageIndex);
        options.put("totalNumber", total);
        options.put("prepareOrders", prepareOrders);
        return options;
    }

    /**
     * 查询渠道名称
     *
     * @param createOrgId
     * @return
     * @throws Exception
     */
    private String qryChannelName(String createOrgId) throws Exception {
        String channelName = "";
        List<String> orgIds = new ArrayList<>(1);
        orgIds.add(createOrgId);
        Map<String, Object> channelParam = new HashMap<>(1);
        channelParam.put("orgIds", orgIds);
        String result = staffOrgAreaSMO.queryChannelListByIds(jsonConverter.toJson(channelParam));
        List resultList = (List) resolveResult(result);
        if (!ListUtil.isListEmpty(resultList)) {
            Map<String, Object> orgRetMap = (Map) resultList.get(0);
            channelName = MapUtils.getString(orgRetMap, "channelName", "");
        }
        return channelName;
    }

    /**
     * 查询地区名称
     *
     * @param regionId
     * @return
     * @throws Exception
     */
    private String qryRegionName(String regionId) throws Exception {
        Map<String, Object> regionIdMap = new HashMap(1);
        regionIdMap.put("regionId", regionId);
        String ret = soQuerySMO.qryCommonRegionByIds(regionIdMap);
        String regionStr = (String) resolveResult(ret);
        Map<String, Object> regionMap = jsonConverter.toBean(regionStr, Map.class);
        String regionName = MapUtils.getString(regionMap, "regionName", "");
        return regionName;
    }

    /**
     * 查询员工名称
     *
     * @param createStaff
     * @return
     * @throws Exception
     */
    private String qryStaffName(String createStaff) throws Exception {
        Map param = new HashMap(1);
        param.put("staffId", createStaff);
        String staffInfoRet = staffInfoQuerySMO.qryStaffInfo(jsonConverter.toJson(param));
        Map staffInfoMap = (Map) resolveResult(staffInfoRet);
        Map staffMap = MapUtils.getMap(staffInfoMap, "staffInfo");
        String staffName = MapUtils.getString(staffMap, "staffName", "");
        return staffName;
    }

    /**
     * 查询客户名称
     *
     * @param custId
     * @return
     * @throws Exception
     */
    private String qryCustName(String custId) throws Exception {
        Map<String, Object> custParams = new HashMap<>(1);
        custParams.put("custId", custId);
        String custStr = custMultiSMO.qryCustomerDetailForMulti(jsonConverter.toJson(custParams));
        Map cust = (Map) resolveResult(custStr);
        Map customer = (Map) cust.get("customerDetail");
        String custName = MapUtils.getString(customer, "custName", "");
        if (!StringUtil.isEmptyStr(custName) && custName.length() > 1) {
            custName = custName.substring(0, 1) + "***";
        }
        return custName;
    }
    
}
