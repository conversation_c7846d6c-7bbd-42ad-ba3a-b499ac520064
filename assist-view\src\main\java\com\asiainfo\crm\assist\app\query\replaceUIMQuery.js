(function (vita) {
    var replaceUIMQuery = vita.Backbone.BizView.extend({
        events: {
            "click #queryRegionIdBtn": "_chooseAreaOne",
            "click #btn-query": "_replaceUIM",
            "click #btn-clear": "_clearCondOne",
            "click #BKXX": "_BKXX",
            "click #printDelivery": "_printDelivery",
            "click #returnInfo": "_returnInfo",

        },
        global: {
            pageIndex: 1,
            pageSize: 5,
            preset: "date",
            chooseArea: "../comm/chooseArea",
            chooseChannel: "../comm/chooseChannel",
            chooseStaff: "../comm/chooseStaff",
            chooseCust: "../comm/chooseCust",
            defaultBeginTime:"",
            defaultEndTime:"",
            currentDayCount:0,
            sevenDayCount:-6,
            lastMonthDayCount:-29,
            ninthDayCount:-89,
            nbrLabels: ["c_custOrderNbr","c_orderItemId"]
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var nowDate = new Date();
            var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
            var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
            var startYear = nowDate.getFullYear() - 5, endYear = nowDate.getFullYear();
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear:endYear
                });
            }
            ;
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear:endYear
                });
            }
            //日期控件设置默认时间
            widget.global.defaultBeginTime = widget._getFormatDate(widget.global.sevenDayCount); //默认是七天
            widget.global.defaultEndTime = widget._currentDate();
            widget._setDefaultDateTime();
            //nbrLabelsStr初始化
            var nbrLabels = widget.global.nbrLabels;
            var nbrLabelsStr = "#"+nbrLabels[0];
            for(var k=1;k<nbrLabels.length;k++) {
                nbrLabelsStr = nbrLabelsStr + ",#" + nbrLabels[k];
            }
            widget.global.nbrLabelsStr = nbrLabelsStr;
        },
        _replaceUIM: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("replaceUIMQry", JSON.stringify(params), "#replaceUIMListResult", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    debugger;
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if(_pageIndex > 1 && !params.pageInfo.rowCount) {
                                    params.pageInfo.rowCount = totalNumber;
                                }
                                widget.global.pageSize =_recordNumber
                                widget.global.pageIndex =_pageIndex
                                if (widget._getConds()) {
                                    widget.refreshPart("replaceUIMQry", JSON.stringify(params), "#infoList");
                                }
                                ;
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }
                }, {
                    async: false
                });
            }
            ;
        },
        _returnInfo: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                var paramsArray = new Array();
                for (key in params) {
                    if ("pageInfo" != key) {
                        let value = params[key];
                        if (!!value) {
                            paramsArray.push(key + "=" + value);
                        }
                    }
                }
                let paramStr = paramsArray.join("&")
                window.location.href = "./returnInfo?" + paramStr;
            }
        },

        _clearCondOne: function () {
            var widget = this, element = $(widget.el);
            var $checked = element.find(".form-group").find("input[type=checkbox]:checked");
            $.each($checked, function (i,chkInput) {
                chkInput.click();
                var $input = $(chkInput).closest(".form-group").find("input.form-control");
                $input.val("");
                $input.attr("value", null);
            })
            //时间设置为7天
           // widget._changeTimeQuanTum(widget.global.sevenDayCount);
        },
        _chooseAreaOne: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _getFormatDate: function(days) {
            var date = new Date();
            var yesterday_milliseconds = date.getTime()+days*1000*60*60*24;
            var yesterday = new Date();
            yesterday.setTime(yesterday_milliseconds);
            var strYear = yesterday.getFullYear();
            var strDay = yesterday.getDate();
            var strMonth = yesterday.getMonth()+1;
            if(strMonth<10)
            {
                strMonth="0"+strMonth;
            }
            if(strDay<10)
            {
                strDay="0"+strDay;
            }

            /*var hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
            var minute = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
            var second = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
            var time = hour+":"+minute+":"+second;*/
            var datastr = strYear+"-"+strMonth+"-"+strDay;
            return datastr;
        },
        //当天从0点开始
        _currentDate:function(){
            var widget = this, el = $(widget.el);
            var nowDate = widget._getFormatDate(widget.global.currentDayCount);
            return nowDate;
        },
        //设置默认时间
        _setDefaultDateTime : function(){
            var widget = this, element = $(widget.el),
                beginDate = element.find("#beginDate"),
                endDate = element.find("#endDate");
            beginDate.val(widget.global.defaultBeginTime);
            endDate.val(widget.global.defaultEndTime);
        },
        //时间控件开发方法
        _dateTimeSwitch:function(flag){
            var widget = this, element = $(widget.el),
                beginDate = element.find("#beginDate"),
                endDate = element.find("#endDate");
            if(flag==="on"){
                widget._setDefaultDateTime();
                beginDate.prop("disabled", false);
                endDate.prop("disabled", false);
                element.find("#qryTimeQuantum").find('button').removeAttr('disabled');
            }else{
                beginDate.val("");
                endDate.val("");
                element.find("#qryTimeQuantum").find('button').attr('disabled','disabled');
                beginDate.prop("disabled", true);
                endDate.prop("disabled", true);
            }
        },
        _getConds: function () {
            var widget = this,element = $(widget.el);
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            params.busiCode = "digitalOrderInfoQuery";
            var paramCnt = 0;
            if (element.find("#c_region").is(":checked")) {
            var regionId = element.find("#regionId").attr("value");
                paramCnt++;
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return false;
                } else {
                    params.regionId = regionId;
                }
            }
            if (element.find("#c_statusCD").is(":checked")) {
                paramCnt++;
                var statusCD = $.trim(element.find("#statusCD").val());
                params.statusCd = statusCD;
            }

            if (element.find("#c_accNum").is(":checked")) {
                paramCnt++;
                var accNum = $.trim(element.find("#accNum").val());
                if(widget._isNullStr(accNum)) {
                    widget.popup("请输入接入号码！");
                    return false;
                } else {
                    params.accNum = accNum;
                }
            }
            if (element.find("#c_callNum").is(":checked")) {
                paramCnt++;
                var callNum = $.trim(element.find("#callNum").val());
                if(widget._isNullStr(callNum)) {
                    widget.popup("请输入联系电话！");
                    return false;
                } else {
                    params.callNum = callNum;
                }
            }
            if (element.find("#c_custName").is(":checked")) {
                paramCnt++;
                var custName = $.trim(element.find("#custName").val());
                if(widget._isNullStr(custName)) {
                    widget.popup("请输入客户姓名！");
                    return false;
                } else {
                    params.custName = custName;
                }
            }
            if (element.find("#c_custOrderId").is(":checked")) {
                paramCnt++;
                var custOrderId = $.trim(element.find("#custOrderId").val());
                if(widget._isNullStr(custOrderId)) {
                    widget.popup("请输入订单编号！");
                    return false;
                } else {
                    params.custOrderId = custOrderId;
                }
            }
            if (element.find("#c_certNum").is(":checked")) {
                paramCnt++;
                var certNum = $.trim(element.find("#certNum").val());
                if(widget._isNullStr(certNum)) {
                    widget.popup("请输入客户证件号！");
                    return false;
                } else {
                    params.certNum = certNum;
                }
            }
            if (element.find("#c_orderNo").is(":checked")) {
                paramCnt++;
                var orderNo = $.trim(element.find("#orderNo").val());
                if(widget._isNullStr(orderNo)) {
                    widget.popup("请输入数字人订单号！");
                    return false;
                } else {
                    params.orderNo = orderNo;
                }
            }
            var startTime = element.find("#beginDate").val();
            var endTime = element.find("#endDate").val();
            startTime = startTime ? startTime + " 00:00:00" : startTime;
            endTime = endTime ? endTime + " 23:59:59" : endTime;
            if (!widget._isNullStr(startTime) && !widget._isNullStr(endTime)) {
                if(startTime>=endTime){
                    widget.popup("结束时间应大于开始时间!");
                    return false;
                }
                var start  = new Date(startTime.replace(/-/g,"/")).getTime();
                var end = new Date(endTime.replace(/-/g,"/")).getTime();

                var flag = end - start  > 90*24*60*60*1000
                if (flag) {
                    widget.popup("开始、结束时间跨度不能超出90天!");
                    return false;
                }
            }
            if (element.find("#c_Date").is(":checked")) {
                paramCnt++
                if (!widget._isNullStr(startTime) || !widget._isNullStr(endTime)) {
                    if (!widget._isNullStr(startTime)) {
                        params.createStartDt = startTime;
                    }
                    if (!widget._isNullStr(endTime)) {
                        params.createEndDt = endTime;
                    }
                }
            }
            if(paramCnt < 1) {
                widget.popup("请至少选择一个查询条件！");
                return false;
            }
            return params;
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },


        _BKXX: function () {
            var widget = this,element = $(widget.el);
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条进行操作!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            var digitalOrderInfoId = $($tr.find("[link=replaceUIM]")).data("digitalOrderInfoId");
            var iccid = $($tr.find("[link=replaceUIM]")).data("iccid");
            var dialogId = "uimComp";
            var url = './uimComp';
            widget.dialog({
                id : dialogId,
                url : url,
                left : "-200px",
                params : {
                    "digitalOrderInfoId":digitalOrderInfoId,
                    "type":"BKXX",
                    "dataString":iccid
                },
                onClose : function() {
                    var params = widget._getConds();
                    if (params) {
                        widget.refreshPart("replaceUIMQry", JSON.stringify(params), "#infoList");
                    }
                }
            });
        },

        _printDelivery: function () {
            var widget = this,element = $(widget.el);
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条进行操作!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            var digitalOrderInfoId = $($tr.find("[link=replaceUIM]")).data("digitalOrderInfoId");
            var deliveryId = $($tr.find("[link=replaceUIM]")).data("deliveryId");
            var dialogId = "uimComp";
            var url = './uimComp';
            widget.dialog({
                id : dialogId,
                url : url,
                left : "-200px",
                params : {
                    "digitalOrderInfoId":digitalOrderInfoId,
                    "type":"printDelivery",
                    "dataString":deliveryId
                },
                onClose : function() {
                    var params = widget._getConds();
                    if (params) {
                        widget.refreshPart("replaceUIMQry", JSON.stringify(params), "#infoList");
                    }
                }
            });
        },
    });
    vita.widget.register("replaceUIMQuery", replaceUIMQuery, true);
})(window.vita);