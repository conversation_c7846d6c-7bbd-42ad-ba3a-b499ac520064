(function(vita) {
    var cloudOrderInfo = vita.Backbone.BizView.extend({
        events: {
            "click #btn-showInfo": "_showInfo",
        },
        global:{
            ShowParam: {}
        },
        _initialize: function () {
            var widget = this,
                element = $(widget.el);
            console.log(element);
            widget._echart();
        },
        _hasArray: (arr) => {
            return arr!=null && arr.length > 0 ? true : false;
        },

        nodeSpec: {
            "cloudOrderInfo": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/DDLK.png"},
            "orderItem": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/LKXX.png"},
            "runSheet": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/XFLK1.png"},
            "procStep": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/SKH.png"},
        },

        _isNullOrEmpty: function (condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty;
        },
        _getData: function(data) {

            let obj = { nodes: [], links: [] };

            const nodeOffsets = {
                cloudOrderInfo: { x: 100, y: 100 },
                orderItem:   { x: 220, y: 50 },
                runSheet:    { x: 350, y: -30 },
                procStep:    { x: 480, y: -70 } };

            if (data && data.masterOrderNo != null && data.masterOrderNo !== '') {
                //let spec = this.nodeSpec["cloudOrderInfo"],
                let spec = "",
                    inst = Object.assign({}, data, { key: data.masterOrderNo, name: data.masterOrderNo });
                if (spec) {
                    inst = Object.assign({}, spec, inst);
                }
                inst.id = data.masterOrderNo;
                inst.x = nodeOffsets.cloudOrderInfo.x;
                inst.y = nodeOffsets.cloudOrderInfo.y;
                obj.nodes.push(inst);
                obj.links.push({ "source": data.masterOrderNo, "target": data.masterOrderNo });
            }
            let itemIndex = 0;
            let stepIndex = 0;
            let runSheetIndex = 0;
            if (data && data.orderItems && data.orderItems.length > 0) {

                for (let item of data.orderItems) {
                    //let spec = this.nodeSpec["orderItem"],
                    let spec = "",
                        inst = Object.assign({}, item, { key: item.orderItemId, name: item.orderItemNbr });
                    if (spec) {
                        inst = Object.assign({}, spec, inst);
                    }
                    inst.id = item.orderItemId;
                    inst.x = nodeOffsets.orderItem.x ;
                    inst.y = nodeOffsets.orderItem.y + (stepIndex * 50);
                    obj.nodes.push(inst);
                    obj.links.push({ "source": data.masterOrderNo, "target": item.orderItemId });

                    if (item.runSheets && item.runSheets.length > 0) {
                        for (let runSheet of item.runSheets) {
                            //let spec = this.nodeSpec["runSheet"],
                            let spec = "",
                                inst = Object.assign({}, runSheet, { key: runSheet.osId, name: runSheet.osType });
                            if (spec) {
                                inst = Object.assign({}, spec, inst);
                            }
                            inst.id = runSheet.osId;
                            inst.x = nodeOffsets.runSheet.x ;
                            inst.y = nodeOffsets.runSheet.y + (runSheetIndex * 110);
                            obj.nodes.push(inst);
                            obj.links.push({ "source": item.orderItemId, "target": runSheet.osId });

                            if (runSheet.procSteps && runSheet.procSteps.length > 0) {

                                for (let step of runSheet.procSteps) {
                                    let spec = "",
                                        inst = Object.assign({}, step, { key: step.taskId, name: step.hdlState });
                                    if (spec) {
                                        inst = Object.assign({}, spec, inst);
                                    }
                                    inst.id = step.taskId;
                                    inst.x = nodeOffsets.procStep.x ;
                                    inst.y = nodeOffsets.procStep.y + (stepIndex * 70);
                                    obj.nodes.push(inst);
                                    obj.links.push({ "source": runSheet.osId, "target": step.taskId.toString() });
                                    stepIndex++;
                                }
                            }
                            runSheetIndex++;
                        }
                    }
                    itemIndex++;
                }
            }
            return obj;
        },


        _echart: function (e) {

            var data;
            var links;
            var widget = this, element = $(widget.el),
             gSession = widget.gSession;
            var data = element.data("data");
            var masterOrderNo = data.masterOrderNo;
            var commonRegionId = data.commonRegionId;
            var channelNbr = gSession.curChannelNbr;
            if (widget._isNullOrEmpty(channelNbr)) {
                var channels = gSession.channels;
                if (channels && channels.length > 0) {
                    var channel = channels[0];
                    channelNbr = channel.channelNbr
                }
            }
            // 时间控件类型初始化
            var text = element.find("#text").val();
            var sourceType = element.find("#sourceType").val();
            var params = {
                "salesCode" : gSession.staffCode,
                "channelNbr" : channelNbr,
                "landId": gSession.staffLanId,
                "masterOrderNo" : masterOrderNo,
                "commonRegionId" :commonRegionId
            };
            params.queryType = sourceType;
            params.queryValue = text;
            widget.callService("qryCloudOrderInfo", JSON.stringify(params), function (res) {
                if (res.cloudOrderInfos==null || res.cloudOrderInfos.length == 0 ){
                    widget.popup("查询不到该订单信息");
                    return
                }

                var obj=this._getData(res.cloudOrderInfos)

                data = obj.nodes
                links = obj.links
            }, {async: false});

            // 折线图
            // 基于准备好的dom，初始化echarts实例
            var line = echarts.init(document.getElementById('line'));
            // 指定图表的配置项和数据
            let option = {
                title: {
                },
                tooltip: {},
                symbolSize:[110,110],//图形大小
                animationDurationUpdate: 1500,
                animationEasingUpdate: 'quinticInOut',
                series: [
                    {
                        type: 'graph',
                        layout: 'none',
                        symbolSize: 85,
                        roam: true,
                        label: {
                            show: true
                        },
                        edgeSymbol: ['circle', 'arrow'],
                        edgeSymbolSize: [4, 10],
                        edgeLabel: {
                            fontSize: 20
                        },
                        data: data,
                        // links: [],
                        links: links,
                        lineStyle: {
                            opacity: 0.9,
                            width: 2,
                            curveness: 0
                        }
                    }
                ]
            };
            line.clear();
            line.off('click').on('click', function (param) {
                widget.global.ShowParam = param;
                document.getElementById("btn-showInfo").click()
            });
            line.setOption(option);
        },
    });
    vita.widget.register("cloudOrderInfo", cloudOrderInfo, true);
})(window.vita);
