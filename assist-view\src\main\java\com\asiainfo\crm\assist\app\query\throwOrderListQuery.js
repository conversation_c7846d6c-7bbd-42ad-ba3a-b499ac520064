(function (vita) {
    var throwOrderListQuery = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_throwOrderListQuery",
            "click #regionIdBtn": "_chooseArea",
            "click [link=customerOrder]": "_queryCustomerOrderDetailLink",
            "click [link=orderItem]": "_queryOrderItemDetailLink",

            "click input[type=checkbox]": "_nbrClick"
        },
        global: {
            pageIndex: 1,
            pageSize: 5,
            preset: "date",
            chooseArea: "../comm/chooseArea",
            defaultBeginTime:"",
            defaultEndTime:"",
            currentDayCount:0
        },

        _initialize: function () {
            var widget = this, element = $(widget.el);
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var nowDate = new Date();
            var beginDate = element.find("input[name='beginDate']");
            var endDate = element.find("input[name='endDate']");
            var startYear = nowDate.getFullYear(), endYear = nowDate.getFullYear();
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear:endYear
                });
            }
            ;
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear:endYear
                });
            }
            //日期控件设置默认时间
            widget.global.defaultBeginTime = widget._currentDate();
            widget.global.defaultEndTime = widget._currentDate();
            widget._setDefaultDateTime();

            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);

            //初始化输入框都不可输入，选择后才可输入
            element.find("#custOrderNbr").attr('disabled',true);
            element.find("#accNum").attr('disabled',true);
        },
        _getFormatDate: function(days) {
            var date = new Date();
            var yesterday_milliseconds = date.getTime()+days*1000*60*60*24;
            var yesterday = new Date();
            yesterday.setTime(yesterday_milliseconds);
            var strYear = yesterday.getFullYear();
            var strDay = yesterday.getDate();
            var strMonth = yesterday.getMonth()+1;
            if(strMonth<10)
            {
                strMonth="0"+strMonth;
            }
            if(strDay<10)
            {
                strDay="0"+strDay;
            }

            /*var hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
            var minute = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
            var second = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
            var time = hour+":"+minute+":"+second;*/
            var datastr = strYear+"-"+strMonth+"-"+strDay;
            return datastr;
        },

        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _nbrClick: function(e) {
            var widget = this, element = $(widget.el);
            var currentTarget = $(e.currentTarget);
            if($(currentTarget[0]).prop('checked')){
                $(currentTarget[0]).closest('.form-group').find('input[type=text]').attr('disabled',false);
            }else {
                $(currentTarget[0]).closest('.form-group').find('input[type=text]').attr('disabled',true);
            }
            var $checked = $(".form-group").find("input[type=checkbox]:checked").not(currentTarget);
            $.each($checked, function (i,chkInput) {
                $(chkInput).attr('checked', false);
                $(chkInput).closest('.form-group').find('input[type=text]').attr('disabled',true);
            });
        },
        _throwOrderListQuery: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("queryThrowCustomOrderList", JSON.stringify(params), "#orderListResult", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if(_pageIndex > 1 && !params.pageInfo.rowCount) {
                                    params.pageInfo.rowCount = totalNumber;
                                }
                                if (widget._getConds()) {
                                    widget.refreshPart("queryThrowCustomOrderList", JSON.stringify(params), "#orderList");
                                }
                                ;
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }

                }, {
                    async: false
                });
            }
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this,element = $(widget.el);
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            var regionId = element.find("#regionId").attr("value");
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.regionId = regionId;
            }
            params.isAcceptRegionId = "1";

            if (element.find("#c_custOrderNbr").is(":checked")) {
                var custOrderNbr = $.trim(element.find("#custOrderNbr").val());
                if(widget._isNullStr(custOrderNbr)) {
                    widget.popup("请输入流水号！");
                    return false;
                } else {
                    params.custOrderNbr = custOrderNbr;
                }
            }

            if (element.find("#c_accNum").is(":checked")) {
                var accNum = $.trim(element.find("#accNum").val());
                if(widget._isNullStr(accNum)) {
                    widget.popup("请输入号码！");
                    return false;
                } else {
                    params.accNum = accNum;
                }
            }

            var startTime = element.find("#beginDate").val();
            var endTime = element.find("#endDate").val();
            startTime = startTime ? startTime + " 00:00:00" : startTime;
            endTime = endTime ? endTime + " 23:59:59" : endTime;

        	if (!widget._isNullStr(startTime) && !widget._isNullStr(endTime)) {
                if(startTime>=endTime){
                    widget.popup("结束时间应大于开始时间!");
                    return false;
                }
                var start  = new Date(startTime.replace(/-/g,"/")).getTime();
                var end = new Date(endTime.replace(/-/g,"/")).getTime();

                var flag = end - start  > 2*24*60*60*1000
                if (flag) {
                    widget.popup("开始、结束时间跨度不能超出1天!");
                    return false;
                }
			}else {
                widget.popup("开始或结束时间不能为空!");
                return false;
            }
            params.acceptDateScope = {};
            params.acceptDateScope.beginDate = startTime;
            params.acceptDateScope.endDate = endTime;

            //是否查询历史
            var isQryHis = element.find("#isQryHis").val();
            params.isQryHis = isQryHis;

            return params;
        },
        //当天从0点开始
        _currentDate:function(){
            var widget = this, el = $(widget.el);
            var nowDate = widget._getFormatDate(widget.global.currentDayCount);
            return nowDate;
        },
        //设置默认时间
        _setDefaultDateTime : function(){
            var widget = this, element = $(widget.el),
                beginDate = element.find("#beginDate"),
                endDate = element.find("#endDate");
            beginDate.val(widget.global.defaultBeginTime);
            endDate.val(widget.global.defaultEndTime);
        },
    });
    vita.widget.register("throwOrderListQuery", throwOrderListQuery, true);
})(window.vita);