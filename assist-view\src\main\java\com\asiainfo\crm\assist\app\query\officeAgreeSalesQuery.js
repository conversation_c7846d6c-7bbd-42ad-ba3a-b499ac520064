(function (vita) {
    var officeAgreeSalesQuery = vita.Backbone.BizView.extend({
        events:{
            "click #regionIdBtn": "_chooseArea",
            "click #btn-query":"_officeOrderListQuery",
            "click #btn-clear": "_clearCond",
        },
        global:{
            chooseArea: "../comm/chooseArea",
        },
        _initialize:function () {
            var widget = this, element = $(widget.el),gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _officeOrderListQuery: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("qryOfferInstFeeInfoList", JSON.stringify(params), "#orderListResult", function (res) {
                    return;
                }, {
                    async: false
                });
            }
            ;
        },
        _clearCond:function(){
            var widget = this, element = $(widget.el);
            var $checked = element.find(".form-group").find("input[type=checkbox]:checked");
            $.each($checked, function (i,chkInput) {
                chkInput.click();
                var $input = $(chkInput).closest(".form-group").find("input.form-control");
                $input.val("");
                $input.attr("value", null);
            })
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds:function () {
            var widget = this,element = $(widget.el);
            var params = {};
            var paramCnt = 0;
            var regionId = element.find("#regionId").attr("value");
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.regionId = regionId;
            }

            if (element.find("#c_offerName").is(":checked")) {
                paramCnt++;
                var productName = element.find("#offerName").val();
                if(widget._isNullStr(productName)) {
                    widget.popup("请填写销售产品名！");
                    return false;
                } else {
                    params.offerName = productName;
                }
            }

            if (element.find("#c_custId").is(":checked")) {
                paramCnt++;
                var custId = element.find("#custId").val();
                if(widget._isNullStr(custId)) {
                    widget.popup("请填写产品客户标识！");
                    return false;
                } else {
                    params.custId = custId;
                }
            }

            if (element.find("#c_Remark").is(":checked")) {
                paramCnt++;
                var remark = element.find("#Remark").val();
                if(widget._isNullStr(remark)) {
                    widget.popup("请填写记录备注！");
                    return false;
                } else {
                    params.remark = remark;
                }
            }

            if (element.find("#c_statusCd").is(":checked")) {
                paramCnt++;
                var statusCd = element.find("#statusCd").val();
                if(widget._isNullStr(statusCd)) {
                    widget.popup("请选择记录状态！");
                    return false;
                } else {
                    params.statusCd = statusCd;
                }
            }

            if(paramCnt < 1) {
                widget.popup("请至少选择一个查询条件！");
                return false;
            }

            return params;
        }
    });
    vita.widget.register("officeAgreeSalesQuery", officeAgreeSalesQuery, true);
})(window.vita);