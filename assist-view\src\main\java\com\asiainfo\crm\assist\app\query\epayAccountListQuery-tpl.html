<div data-widget="epayAccountListQuery" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <!--<div class="sypage-nav">-->
        <!--<div class="titlefont">-->
            <!--<ul id="myTab" class="nav nav-tabs">-->
                <!--<li class="active">-->
                    <!--<a href="#1" data-toggle="tab">受理单</a>-->
                <!--</li>-->
                <!--<li>-->
                    <!--<a href="#2" data-toggle="tab">销售单</a>-->
                <!--</li>-->
                <!--</li>-->
            <!--</ul>-->
        <!--</div>-->
    <!--</div>-->
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4">
                                            <!--<label class="col-md-5 control-label lablep">-->
                                                <!--<label class="wu-radio full absolute" data-scope="">-->
                                                    <!--<input id="c_accNbr" type="checkbox" name="payment">-->
                                                <!--</label> 流水号-->
                                            <!--</label>-->
                                            <div class="col-md-7">
                                                <input id="accNbr" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-6">
                                            <div class="col-md-11 searchbutt_r" align="">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">搜索</button>

                                            </div>
                                        </div>


                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询结果</div>
                                </div>
                                <div class="wmin_content">

                                    <div class="col-lg-12 mart10" id="accountListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>账户号码</th>
                                                <th>账户名称</th>
                                                <th>账户类型</th>
                                                <th>账户状态</th>
                                                <th>账户余额(分)</th>
                                                <th>上日账户余额(分)</th>
                                                <th>上月账户余额(分)</th>
                                                <th>可用账户余额(分)</th>
                                                <th>不可用账户余额(分)</th>
                                                <th>可提现余额(分)</th>
                                                <th>卡号</th>
                                                <th>卡类型</th>
                                            </tr>
                                            </thead>
                                            <tbody id="">
                                            #if($options != "null" && $options.account && $options.account != "null" )
                                            #set($account = $options.account)
                                            <tr>
                                                <td>$!{account.ACCOUNTNO}</td>
                                                <td>$!{account.ACCOUNTNAME}</td>
                                                <td>$!{account.ACCOUNTTYPE}</td>
                                                <td>$!{account.ACCOUNTSTATUS}</td>
                                                <td>$!{account.ACCOUNTBALANCE}</td>
                                                <td>$!{account.PREDAYBALANCE}</td>
                                                <td>$!{account.PREMONTHBALANCE}</td>
                                                <td>$!{account.AVAILABLEBALANCE}</td>
                                                <td>$!{account.UNAVAILABLEBALANCE}</td>
                                                <td>$!{account.AVAILABLECASH}</td>
                                                <td>$!{account.CARDNUM}</td>
                                                <td>$!{account.CARDTYPE}</td>
                                            </tr>

                                            #elseif($options != "null" && $options.accountListResult && $options.accountListResult != "null" &&
                                            $options.accountListResult.size() == 0)
                                                <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
