package com.asiainfo.crm.assist.app.action;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.bcomm.utils.LogHelper;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.asiainfo.crm.service.intf.IResourceSMO;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import com.asiainfo.crm.service.intf.IStaffDataPrvQuerySMO;
import com.asiainfo.crm.sm.plugin.UniSessionUtil;
import com.asiainfo.crm.sm.vo.GlbSessionVo;
import com.google.common.collect.Maps;

/**
 * Created by wenhy on 2017/7/10.
 */
@Component("vita.releaseMaterial")
public class ReleaseMaterial extends AbstractComponent {

	private static final Logger logger = LoggerFactory.getLogger(ReleaseMaterial.class);

	@Autowired
	private IResourceSMO resourceSMO;

	@Autowired
	private IStaffDataPrvQuerySMO staffDataPrvQuerySMO;

	@Autowired
	private ISoQuerySMO iSoQuerySMO;

	@Autowired
	private IProdInstSMO prodInstSMO;

	@Autowired
	private IOrderQuerySMO orderQuerySMO;

	@Override
	public Map achieveData(Object... params) throws Exception {
		return null;
	}

	public Map releaseMaterial(String jsonStr) throws Exception {
		Map<String,String> retMap=new HashMap<>();
		Map<String, Object> paramMap = jsonConverter.toBean(jsonStr, Map.class);
		Long sysUserId = Long.valueOf(String.valueOf(paramMap.get("sysUserId")));
		String regionId = String.valueOf(paramMap.get("regionId"));
		String mktResInstNbr = String.valueOf(paramMap.get("mktResInstNbr"));// 获取终端串码
		String lanId = this.getLandId(regionId);// 根据输入的地区编码查询LAN_ID
		paramMap.put("lanId", lanId);
		Map<String, Object> options = Maps.newHashMap();

		//1、判断终端串号是否为员工预占，是员工预占则有权限处理
		//2、员工有权限释放这个终端号码
		Map<String,Object> accountParam = new HashMap<String, Object>();
		accountParam.put("mktResInstNbr", mktResInstNbr);
		accountParam.put("regionId", lanId);

		Map<String, Object> reqMap = new HashMap<String, Object>();
		reqMap.put("sysUserId", sysUserId);
		reqMap.put("privCode", AssistMDA.RELEASE_MATERIAL);// 释放终端串码权限	CRMXXSOM00197
		String privInfo = staffDataPrvQuerySMO.checkSysUserPriv(jsonConverter.toJson(reqMap));// 判断是否有权限
		Map<String, Object> privInfoMap = (Map<String, Object>) resolveResult(privInfo);
		String isHave = String.valueOf(privInfoMap.get("isHave"));

		if(isOperatorNumber(accountParam,sysUserId)
				||(!StringUtils.isEmpty(isHave) && "true".equals(isHave))){
			
			Map<String, Object> resultObjectMap = new HashMap<>();
			
			String deliveryDetail = resourceSMO.queryDeliveryDetail(jsonStr);
			Map<String, Object> deliveryDetailMap = (Map<String, Object>) resolveResult(deliveryDetail);
			List<Map> mktInsts =(List)deliveryDetailMap.get("mktInsts");
			String reqCode =(String)deliveryDetailMap.get("reqCode");
			Map<String, Object> routeParam = Maps.newHashMap();
			routeParam.put("regionId", regionId);
		
			if(reqCode!=null&&reqCode!=""){
				/*
				 * 原先逻辑，根据串码查询出库单，再根据出库单查询详情，将出库单下面的所有终端遍历去调用手动释放，成功之后在调用释放出库单号；
				 * 该方式存在问题：多终端的情况无法保证串码释放都统一，有可能会出现终端释放部分失败，部分成功的情况；
				 * 固采用新的方式，通过输入出库单号调用营销资源进行释放，资源那边统一去释放终端
				 */
				Map<String, Object> releaseMap = Maps.newHashMap();
				releaseMap.put("staffId", paramMap.get("staffId"));
				releaseMap.put("orgId", paramMap.get("orgId"));
				releaseMap.put("regionId", regionId);
				releaseMap.put("reqCode", reqCode);
				releaseMap.put("isReleaseCoupon", "Y");
				releaseMap.put("routeParam", routeParam);
				resultObjectMap = releaseDeliveryDetail(releaseMap);

			}else{
				//没有出库单号的直接释放串码
				String materialInfoStr = resourceSMO.releaseMaterialByManual(jsonStr);
				resultObjectMap = (Map<String, Object>) resolveResult(materialInfoStr);
			}
			
			retMap.put("retCode",String.valueOf(resultObjectMap.get("handleResultCode")));
			retMap.put("retDesc", String.valueOf(resultObjectMap.get("handleResultMsg")));
			options.put("releaseInfo",retMap);
			return options;
		}
		retMap.put("retCode", "-1");
		retMap.put("retDesc", "您没有释放号码的权限！");
		options.put("releaseInfo", retMap);
		return options;

	}

	/**
	 * 释放号码
	 * @param jsonStr
	 * @return
	 * @throws Exception
	 */
	public Map releaseAccNum(String jsonStr) throws Exception {
		Map<String, Object> paramMap = jsonConverter.toBean(jsonStr, Map.class);
		Long sysUserId = Long.valueOf(String.valueOf(paramMap.get("sysUserId")));
		String regionId = String.valueOf(paramMap.get("regionId"));
		String accNum = String.valueOf(paramMap.get("accNum"));
		String lanId = this.getLandId(regionId);// 根据输入的地区编码查询LAN_ID
		paramMap.put("lanId", lanId);
		Map<String, Object> options = Maps.newHashMap();

		//1、判断终端串号是否为员工预占，是员工预占则有权限处理
		//2、员工有权限释放这个终端号码
		Map<String,Object> accountParam = new HashMap<String, Object>();
		accountParam.put("accNum", accNum);
		accountParam.put("regionId", lanId);
		accountParam.put("anTypeCd", "103");

		Map<String, Object> reqMap = new HashMap<String, Object>();
		reqMap.put("sysUserId", sysUserId);
		reqMap.put("privCode", AssistMDA.RELEASE_RS);// 释放号码权限编码	CRMXXSOM00198
		String privInfo = staffDataPrvQuerySMO.checkSysUserPriv(jsonConverter.toJson(reqMap));
		Map<String, Object> privInfoMap = (Map<String, Object>) resolveResult(privInfo);
		String isHave = String.valueOf(privInfoMap.get("isHave"));
		if(isOperatorNumber(accountParam,sysUserId)
				||(!StringUtils.isEmpty(isHave) && "true".equals(isHave))){
			Map<String, Object> resultObjectMap = this.releaseNumberCore(paramMap);
			options.put("releaseInfo", resultObjectMap);
			return options;
		}

		Map<String, Object> resultObjectMap = new HashMap<String, Object>();
		resultObjectMap.put("retCode", "-1");
		resultObjectMap.put("retDesc", "您没有释放号码的权限！");
		options.put("releaseInfo", resultObjectMap);
		return options;
	}

	/**
	 * 释放uim卡
	 * @param jsonStr
	 * @return
	 * @throws Exception
	 */
	public Map releaseUimCode(String jsonStr) throws Exception {
		Map<String, Object> paramMap = jsonConverter.toBean(jsonStr, Map.class);
		Long sysUserId = Long.valueOf(String.valueOf(paramMap.get("sysUserId")));
		String uimCode = String.valueOf(paramMap.get("uimCode"));// 获取uim卡号
		String regionId = String.valueOf(paramMap.get("regionId"));
		String lanId = this.getLandId(regionId);// 根据输入的地区编码查询LAN_ID
		paramMap.put("lanId", lanId);

		//1、判断终UIM号是否为员工预占，是员工预占则有权限处理
		//2、员工有权限释放这个UIM号码
		Map<String,Object> accountParam = new HashMap<String, Object>();
		accountParam.put("mktResInstNbr", uimCode);
		accountParam.put("regionId", lanId);

		Map<String, Object> reqMap = new HashMap<String, Object>();
		reqMap.put("sysUserId", sysUserId);
		reqMap.put("privCode", AssistMDA.RELEASE_UIM); // 释放UIM卡权限编码		CRMXXSOM00199
		Map<String, Object> options = Maps.newHashMap();
		String privInfo = staffDataPrvQuerySMO.checkSysUserPriv(jsonConverter.toJson(reqMap));
		Map<String, Object> privInfoMap = (Map<String, Object>) resolveResult(privInfo);
		String isHave = String.valueOf(privInfoMap.get("isHave"));

		if(isOperatorNumber(accountParam,sysUserId)
				||(!StringUtils.isEmpty(isHave) && "true".equals(isHave))){
			Map<String, Object> resultObjectMap = this.occupyReleaseMktResCore(paramMap);// 释放uim卡
			return resultObjectMap;
		}
		Map<String,String> retMap = new HashMap<>();
		retMap.put("retCode", "-1");
		retMap.put("retDesc", "您没有释放UIM卡的权限！");
		options.put("releaseInfo", retMap);
		return options;
	}


	/**
	 * 释放号码，支持cdma
	 * @param paramMap
	 * @return
	 */
	public Map<String, Object> releaseNumberCore(Map paramMap) throws Exception {
		Map<String, Object> retMap = new HashMap<String, Object>();

		// cdma号码释放，不知道是否是4g，方案为先调用4g进行释放，如果不成功在调用省内进行释放
		//1、查询号码信息
		Map<String, Object> resourceParam = new HashMap<String, Object>();
		resourceParam.put("phoneNumber", paramMap.get("accNum"));
		this.addResourceParamLocal(resourceParam, paramMap);
		Map<String, Object> routeParam = new HashMap<String, Object>();
		routeParam.put("regionId", paramMap.get("lanId"));
		resourceParam.put("routeParam", routeParam);
		String retRsStr = resourceSMO.qryPhoneNumInfoByCond(jsonConverter.toJson(resourceParam));
		Map<String, Object> retRsMap = (Map<String, Object>) resolveResult(retRsStr);
		String retCode = String.valueOf(retRsMap.get("handleResultCode"));
		String retMsg = String.valueOf(retRsMap.get("handleResultMsg"));
		if (!"0".equals(retCode)) {
			retMap.put("retCode", "-1");
			retMap.put("retDesc", retMsg);
			return retMap;
		}
		List<Map<String, Object>> numberList = (List<Map<String, Object>>) MapUtils.getObject(retRsMap, "phoneNumInfoList");
		if (null != numberList && numberList.size() < 1) {
			retMap.put("retCode", "-1");
			retMap.put("retDesc", "未查询到该号码的信息！");
			return retMap;
		}

		if (null != numberList && numberList.size() > 1) {
			retMap.put("retCode", "-1");
			retMap.put("retDesc", "查询该号码信息记录数异常，请联系运维人员！");
			return retMap;
		}
		Map<String, Object> numberInfo = numberList.get(0);
		if(!"103".equals(String.valueOf(numberInfo.get("anTypeCd")))){
			retMap.put("retCode", "-1");
			retMap.put("retDesc", "此号码非CDMA号码，不能进行释放操作");
			return retMap;
		}
		String phoneNumberId = String.valueOf(numberInfo.get("phoneNumberId"));
		//直接调用集团4G接口释放，4G释放集团会有校验，释放不成功再调用省内3G释放
		Map<String, Object> routePreParam = new HashMap<String, Object>();
		routePreParam.put("regionId", paramMap.get("lanId"));
		Map<String, Object> releaseRsParam = new HashMap<String, Object>();
		releaseRsParam.put("anId", phoneNumberId);
		releaseRsParam.put("anTypeCd", numberInfo.get("anTypeCd"));
		releaseRsParam.put("preSo", "true");// 操作类型:true释放;false查状态
		releaseRsParam.put("staffCode", paramMap.get("staffCode"));
		releaseRsParam.put("channelNbr", paramMap.get("channelNbr"));
		GlbSessionVo glbSessionVo = UniSessionUtil.getGlbSessionVoById(String.valueOf(paramMap.get("glbSessionId")));
		releaseRsParam.put("channelId", glbSessionVo.getCurChannelId());
		releaseRsParam.put("regionId", paramMap.get("regionId"));
		releaseRsParam.put("lanId", paramMap.get("lanId"));
		releaseRsParam.put("accNum", paramMap.get("accNum"));
		releaseRsParam.put("busiType", "4G");// 4G标识
		this.addParamLocal(releaseRsParam);

		releaseRsParam.put("routeParam", routePreParam);
		String releaseRsRetStr = resourceSMO.releaseNumberCore(jsonConverter.toJson(releaseRsParam));
		Map<String, Object> releaseRsRetMap = (Map<String, Object>) resolveResult(releaseRsRetStr);
		String handleResultCode = String.valueOf(releaseRsRetMap.get("handleResultCode"));

		if ("0".equals(handleResultCode)) {
			//记录日志
			Map<String, Object> logData = new HashMap<String, Object>();
			logData.put("method", "releaseNumberCore");
			logData.put("paramJson", jsonConverter.toJson(releaseRsParam));
			logData.put("retStr", releaseRsRetStr);
			LogHelper.writeLog("releaseNumberCore", logData);

			retMap.put("retCode", "0");
			retMap.put("retDesc", "接入号码释放成功");
			return retMap;
		}

		//3G号码释放走原来核心逻辑
		String releaseAccNumResult = resourceSMO.releaseAccNum(jsonConverter.toJson(paramMap));
		Map<String, Object> result3GObjectMap = (Map<String, Object>) resolveResult(releaseAccNumResult);
		String handle3GResultCode=String.valueOf(result3GObjectMap.get("handleResultCode"));
		if("0".equals(handle3GResultCode)){
			retMap.put("retCode","0");
			retMap.put("retDesc", "资源释放成功");
			return retMap;
		}
		retMap.put("retCode",String.valueOf(result3GObjectMap.get("handleResultCode")));
		retMap.put("retDesc", String.valueOf(result3GObjectMap.get("handleResultMsg")));
		return retMap;

	}

	public void addResourceParamLocal(Map<String, Object> resourceParam, Map paramMap) {
		resourceParam.put("regionId", paramMap.get("lanId"));
	}

	public void addParamLocal(Map<String,Object> releaseRsParam) {
	}

	/**
	 * 释放uim卡，支持3g/4g
	 * @param paramMap
	 * @return
	 */
	public Map<String, Object> occupyReleaseMktResCore(Map<String, Object> paramMap) throws Exception {
		String s=String.valueOf(paramMap.get("glbSessionId"));
		GlbSessionVo glbSessionVo = UniSessionUtil.getGlbSessionVoById(String.valueOf(paramMap.get("glbSessionId")));
		Map<String, Object> retMap = new HashMap<String, Object>();
		Map<String, Object> options = Maps.newHashMap();
		String busiType = "4G"; // 默认是4g，调用接口进行更新
		Map<String,Object> resourceParam = new HashMap<String, Object>();
		resourceParam.put("uimCode", paramMap.get("uimCode"));
		resourceParam.put("regionId", paramMap.get("lanId"));
		Map<String,Object> routeParam = new HashMap<String, Object>();
		routeParam.put("regionId", paramMap.get("lanId"));
		resourceParam.put("routeParam", routeParam);
		String retRsStr = resourceSMO.qryUimCardInfoByCond(jsonConverter.toJson(resourceParam));
//			retRsStr = "{\"resultObject\":{\"handleResultCode\":0,\"handleResultMsg\":\"处理成功！\",\"anId\":293072,\"imsiNbr\":8986111410532501690,\"rscStatusCd\":1102},\"resultCode\":\"0\",\"resultType\":\"0\"}";
		Map<String, Object> retRsMap = (Map<String, Object>) resolveResult(retRsStr);
		String retCode = String.valueOf(retRsMap.get("handleResultCode"));
		String retMsg = String.valueOf(retRsMap.get("handleResultMsg"));
		if(!"0".equals(retCode)){
			retMap.put("retCode", "-1");
			retMap.put("retDesc", retMsg);
			options.put("releaseInfo", retMap);
			return options;
		}

		String anId = String.valueOf(retRsMap.get("anId"));
		Map<String,Object> mktResParam = new HashMap<String, Object>();
		mktResParam.put("anId", anId);
		mktResParam.put("busiType", busiType);

		mktResParam.put("channelId", glbSessionVo.getCurChannelId());
		mktResParam.put("channelNbr", paramMap.get("channelNbr"));
		mktResParam.put("isCheckChannel", "Y");
		mktResParam.put("lanId", paramMap.get("lanId"));
		mktResParam.put("mktResInstNbr", paramMap.get("uimCode"));
		mktResParam.put("regionId", paramMap.get("regionId"));
		mktResParam.put("staffCode", paramMap.get("staffCode"));
		mktResParam.put("routeParam", routeParam);
		Map<String,Object> pageInfo = new HashMap<String, Object>();
		pageInfo.put("pageIndex", "1");
		pageInfo.put("pageSize", "20");
		routeParam.put("regionId", paramMap.get("lanId"));
		mktResParam.put("pageInfo", pageInfo);
		String retMktResStr = resourceSMO.queryMktResListCore(jsonConverter.toJson(mktResParam));
		Map<String, Object> retMktResMap = (Map<String, Object>) resolveResult(retMktResStr);
		String retCodeFlag = String.valueOf(retMktResMap.get("handleResultCode"));
		//4G UIM释放
		if("0".equals(retCodeFlag)){
			//释放UIM卡
			Map<String,Object> releaseRsParam = new HashMap<String, Object>();
			String deviceNumId = String.valueOf(retMktResMap.get("deviceNumId"));
			String mktResInstId = String.valueOf(retMktResMap.get("mktResInstId"));
			releaseRsParam.put("actionType", "R");
			releaseRsParam.put("anId", anId);
			releaseRsParam.put("busiType", busiType);

			releaseRsParam.put("channelId", glbSessionVo.getCurChannelId());
			releaseRsParam.put("channelNbr", paramMap.get("channelNbr"));
			releaseRsParam.put("deviceNumId", deviceNumId);
			releaseRsParam.put("lanId", paramMap.get("lanId"));
			releaseRsParam.put("mktResInstId", mktResInstId);
			releaseRsParam.put("mktResInstNbr", paramMap.get("uimCode"));
			releaseRsParam.put("regionId", paramMap.get("lanId"));
			Map<String,Object> routePreParam = new HashMap<String, Object>();
			routePreParam.put("regionId", paramMap.get("lanId"));
			releaseRsParam.put("routeParam", routePreParam);
			releaseRsParam.put("staffCode", glbSessionVo.getStaffCode());
			String releaseRsRetStr = resourceSMO.occupyReleaseMktResCore(jsonConverter.toJson(releaseRsParam));
			Map<String, Object> releaseRsRetMap = (Map<String, Object>) resolveResult(releaseRsRetStr);

			//记录日志
			Map<String,Object> logData = new HashMap<String,Object>();
			logData.put("method","occupyReleaseMktResCore");
			logData.put("paramJson",jsonConverter.toJson(releaseRsParam));
			logData.put("retStr",releaseRsRetStr);
			LogHelper.writeLog("occupyReleaseMktResCore",logData);

			String handleResultCode = String.valueOf(releaseRsRetMap.get("handleResultCode"));
			if(!"0".equals(handleResultCode)){
				retMap.put("retCode", "-1");
				retMap.put("retDesc", "释放UIM卡失败！"+String.valueOf(releaseRsRetMap.get("handleResultMsg")));
			}else{
				retMap.put("retCode", handleResultCode);
				retMap.put("retDesc", String.valueOf(releaseRsRetMap.get("handleResultMsg")));
			}
			options.put("releaseInfo", retMap);
			return options;

		}

		//3G UIM释放走原来核心逻辑
		String materialInfoStr = resourceSMO.releaseUimCode(jsonConverter.toJson(paramMap));
		Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(materialInfoStr);
		options.put("releaseInfo", resultObjectMap);
		return options;

	}

	/**
	 *	根据输入的地区编码查询LAN_ID
	 * @param regionId
	 * @return
	 * @throws Exception
	 */
	protected String getLandId(String regionId) throws Exception {
		String lanId = null;
		//根据输入的地区编码查询LAN_ID
		Map<String, Object> regionIdMap = new HashMap();
		regionIdMap.put("regionId", regionId);
		String regionIdStr = iSoQuerySMO.qryCommonRegionByIds(regionIdMap);
		String regionIdStrs =  (String) resolveResult(regionIdStr);
		Map<String, Object> regionIdMaps = jsonConverter.toBean(regionIdStrs, Map.class);
		if (null !=  regionIdMaps.get("lanId")) {
			lanId = String.valueOf(regionIdMaps.get("lanId"));
		}
		return lanId;
	}

	//检查是否是员工自己预占的号码
	public boolean isOperatorNumber(Map<String,Object> accountParam,Long sysUserId) throws Exception{
		String retRsStr = iSoQuerySMO.qryOrdProdInstAccNum(jsonConverter.toJson(accountParam));
		Map<String, Object> retRsMap = (Map<String, Object>) resolveResult(retRsStr);
		String retCode = String.valueOf(retRsMap.get("handleResultCode"));
		if("0".equals(retCode)) {
			List<Map<String, Object>> accountList = (List<Map<String, Object>>) MapUtils.getObject(retRsMap, "accountInfoList");
			if(null != accountList && accountList.size() > 0) {
				Map<String, Object> accountInfo = accountList.get(0);
				String createStaff = String.valueOf(accountInfo.get("createStaff"));
				String updateStaff = String.valueOf(accountInfo.get("updateStaff"));
				return createStaff.equals(String.valueOf(sysUserId)) || updateStaff.equals(String.valueOf(sysUserId));
			}

		}
		return false;
	}
	
	private Map releaseDeliveryDetail(Map<String,Object> releaseMap) throws Exception {
		Map<String, Object> resultObjectMap = Maps.newHashMap();
		String resultObjectStr = resourceSMO.cancelDeliveryDetail(jsonConverter.toJson(releaseMap));
		resultObjectMap = (Map<String, Object>) resolveResult(resultObjectStr);
		return resultObjectMap;
	}

}
