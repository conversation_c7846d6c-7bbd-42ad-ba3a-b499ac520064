<div data-widget="showInfoOrderProcess" class="h-liBao-desc">
    <p class="vita-data">{"custOrderId":"$options.coId",
        "nodeCenter" :"$!options.nodeCenter",

        "msgId" : "$!options.msgId",
        "msgNbr" : "$!options.msgNbr",
        "msgTopic" : "$!options.msgTopic"

        }</p>
    <div class="calctitle">
        <div class="titlefont">
            $!options.nodeName
        </div>
        <div class="toolr">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
        </div>
    </div>

    <div class="calcw_rightcont ">
        <div class="desc-content">
            <h5 class="meal_htitle nobrder"><i class="bot"></i>环节状态</h5>
            <div class="row">
                #if($options.nodeStatus == "1" )
                    <div class="col-md-12">
                        <pre style="background-color: green" >         </pre>

                    </div>
                #end
                #if($options.nodeStatus == "-1" )
                <div class="col-md-11">
                        <pre style="background-color: red" >$options.nodeStatusDec</pre>

                </div>
                <div class="col-md-1">
                    <button id="btn-query"   type="button" class="btn btn-primary">修复</button>
                </div>
                #end


            </div>




            <h5 class="meal_htitle nobrder"><i class="bot"></i>详情描述</h5>
            <div class="row" style="text-align: center">
                <div class="col-md-3">
                        <pre>订单项信息</pre>
                </div>
                <div class="col-md-1">
                    <button  id="orderItemInfo"  type="button" class="btn btn-primary">详情</button>
                </div>
            </div>
            <div class="row" style="text-align: center">

                <div class="col-md-3">
                        <pre  >消息历史</pre>
                </div>
                <div class="col-md-1">
                    <button   id="asynMsgInfo"  type="button" class="btn btn-primary">详情</button>
                </div>
            </div>
            #if($options.nodeCode == "OrderSaved" )
            <div class="row" style="text-align: center">
                <div class="col-md-3">
                        <pre  >入库报文</pre>

                </div>
                <div class="col-md-1">
                    <button  id="warehousingInfo"  type="button" class="btn btn-primary">详情</button>
                </div>
            </div>
            #end
            #if($options.nodeCode == "CustomerCenterArchived" || $options.nodeCode == "AccountCenterArchived" ||$options.nodeCode == "InstCenterArchived" )
            <div class="row" style="text-align: center">
                <div class="col-md-3">
                        <pre  >归档数据</pre>
                </div>
                <div class="col-md-1">
                    <button  id="GDXJ"  type="button" class="btn btn-primary">详情</button>
                </div>
            </div>
            #end
        </div>
    </div>
</div>
