package com.asiainfo.crm.assist.app.action;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IAcctQuerySMO;
import com.asiainfo.crm.service.intf.IAllowanceSMO;
import com.asiainfo.crm.service.intf.IOfferSMO;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.asiainfo.crm.service.intf.ISpecSMO;

/**
 * Created by wenhy on 2017/7/17.
 */
@Component("vita.allowanceManage")
public class AllowanceManage extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(AllowanceManage.class);
    
    @Autowired
    private IProdInstSMO prodInstSMO;
    
    @Autowired
    private IAllowanceSMO allowanceSMO;
    
    @Autowired
    private IOfferSMO offerSMO;
    
    @Autowired
	protected IOrderQuerySMO orderQuerySMO;
    
    @Autowired 
    private IAcctQuerySMO acctQuerySmo;
    
    @Autowired
    private ISpecSMO specSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }
	/**
	 * 查询单位用户信息
	 * @param jsonStr
	 * @return
	 * @throws Exception
	 */
    public Map qryGroupUser(String jsonStr) throws Exception {

    	Map<String, Object> retMap = new HashMap<String, Object>();
        Map<String, Object> paramMap = jsonConverter.toBean(jsonStr, Map.class);
        paramMap.put("groupUserOffers", AssistMDA.ALLOWANCE_GROUP_OFFER_ID);
        //调用资产中心接口获取基本用户信息
        String retStr = prodInstSMO.qryCrossSubsidySalesInfo(jsonConverter.toJson(paramMap));
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(retStr);
        
        String qryType = String.valueOf(paramMap.get("qryType"));//1、单位产品 2、个人产品
        String accNumType = String.valueOf(paramMap.get("accNumType"));//1、接入号码 2、宽带账号 3、合同号
        String queryValue = String.valueOf(paramMap.get("queryValue"));
        String busiRegionId = String.valueOf(paramMap.get("busiRegionId"));
    	
    	//基本信息
        if(MapUtils.isEmpty(resultObjectMap)){
        	retMap.put("retCode", "-1");	
        	retMap.put("groupUserCount", "0");
    		if("3".equals(accNumType)){
			retMap.put("retDesc", "所选地区不存在此帐户！");
			}else{
				retMap.put("retDesc", "产品不存在或已拆机！");
			}
    		return retMap;
        }else{
        	retMap.put("groupUserCount", "1");
        }
        String custName = String.valueOf(resultObjectMap.get("custName"));
        String certNum = String.valueOf(resultObjectMap.get("certNum"));
        String address = String.valueOf(resultObjectMap.get("address"));
        //敏感信息脱敏
        if(StringUtils.isNotEmpty(custName)){
        	if(custName.length() < 2){
        		custName = "****";
        	}else{
        		custName = encrypt(custName, "cert", 1);
        	}
        }
        if(StringUtils.isNotEmpty(certNum)){
        	if(certNum.length() < 5){
        		certNum = "****";
        	}else{
        		certNum = encrypt(certNum, "cert", 6);
        	}
        }
        if(StringUtils.isNotEmpty(address)){
        	if(address.length() < 5){
        		address = "**";
        	}else{
        		address = encrypt(address, "cert", 6);
        	}
        }
        
    	retMap.put("custName", custName);
    	retMap.put("custId", resultObjectMap.get("custId"));
    	retMap.put("identityTypeName", resultObjectMap.get("certType"));
    	retMap.put("identityNum", certNum);
    	retMap.put("custAddress", address);
    	retMap.put("prodName", resultObjectMap.get("prodName"));
    	retMap.put("busiNum", queryValue);
    	retMap.put("qryType", qryType);
    	retMap.put("accNumType", accNumType);
    	retMap.put("busiRegionId", busiRegionId);
    	retMap.put("prodState", resultObjectMap.get("statusCd"));
    	retMap.put("statusCdName", resultObjectMap.get("statusCdName"));
    	retMap.put("prodInstId", resultObjectMap.get("prodInstId"));
    	retMap.put("prodId", resultObjectMap.get("prodId"));
    	retMap.put("offerInstIds", resultObjectMap.get("offerInstIds"));
    	retMap.put("acctId", resultObjectMap.get("acctId"));
    	
    	//按照单位产品查询,需要查询补贴总额
    	List offerInsts = (List) resultObjectMap.get("offerInsts");
    	
    	if("1".equals(qryType) && offerInsts != null){

    		Map attrInfo = getGroupAmont(offerInsts);
    		if(!"0".equals(attrInfo.get("retCode"))){
    			retMap.put("retCode", attrInfo.get("retCode"));	
    			retMap.put("retDesc", attrInfo.get("retDesc"));
    			return retMap;
    		}
    		retMap.put("subsidyMode", attrInfo.get("subsidyMode"));
    		retMap.put("subsidyModeName", attrInfo.get("subsidyModeName"));
    		retMap.put("subRateKeyValue", attrInfo.get("subRateKeyValue"));
    		String amont = String.valueOf(attrInfo.get("amont"));
    		String rate = String.valueOf(attrInfo.get("rate"));
    		retMap.put("amont", Long.valueOf(amont) * Long.valueOf(rate) / 100 );
    		retMap.put("rate", rate);
    		retMap.put("offerId", attrInfo.get("offerId"));
    		retMap.put("offerType", attrInfo.get("offerType"));
    		retMap.put("offerInstId", attrInfo.get("offerInstId"));
    	}
    	if("1".equals(qryType) && "3".equals(accNumType)){
    		retMap.put("acctCd", queryValue);	
    	}
    	retMap.put("retCode", "0");	
		retMap.put("retDesc", "查询成功！");
  	
    	
        return retMap;
    }
    
    protected <T> T encrypt(T value, String type,int len){
        T reValue = null;
        switch (type){
            case "cert" :
                String t = value.toString();
                if(t.length() > len){
                    t = t.substring(0, len) + "*********";
                    reValue = (T) t;
                }else{
                    reValue = value;
                }
                break;
            default:
        }
        return reValue;
    }
    /**
     * 查询单位用户补贴信息
     * @param offerInsts
     * @return
     * @throws Exception
     */
    private Map getGroupAmont(List offerInsts) throws Exception{
    	Map<String, Object> retMap = new HashMap<String, Object>();
    	retMap.put("retCode", "0");	
    	String amont = null;//补贴额度
    	String subsidyMode = null;//补贴模式
    	String rate = null;//补贴比例
    	String subRateKeyValue = null;//子比例属性
    	for (int i = 0; i < offerInsts.size(); i++) {
    		Map offerInst = (Map) offerInsts.get(i);
    		String offerId =  String.valueOf(offerInst.get("offerId"));
    		subsidyMode = AssistMDA.ALLOWANCE_GROUP_OFFER_MODE.get(Long.valueOf(offerId).longValue());
    		if(StringUtils.isEmpty(subsidyMode)){
    			continue;
    		}
    		retMap.put("subsidyMode", subsidyMode);
    		retMap.put("subsidyModeName", AssistMDA.ALLOWANCE_TYPE_NAME.get(subsidyMode));
    		retMap.put("offerId", offerInst.get("offerId"));
    		retMap.put("offerType", offerInst.get("offerType"));
    		retMap.put("offerInstId", offerInst.get("offerInstId"));
    		List offerInstAttrs = (List) offerInst.get("offerInstAttrs");
    		for (int j = 0; j < offerInstAttrs.size(); j++) {
    			Map offerInstAttr = (Map) offerInstAttrs.get(j);
    			Long attrId = Long.valueOf(String.valueOf(offerInstAttr.get("attrId")));
    			String attrValue = String.valueOf(offerInstAttr.get("attrValue"));
    			if(AssistMDA.ALLOWANCE_GROUP_AMONT_XF == attrId.longValue()){
    				amont = attrValue;
    				retMap.put("amont", Long.valueOf(amont)/100);
    			}
    			if(AssistMDA.ALLOWANCE_GROUP_AMONT_BD == attrId.longValue()){
    				amont = attrValue;
    				retMap.put("amont", Long.valueOf(amont)/100);
    			}
    			if(AssistMDA.ALLOWANCE_GROUP_AMONT_RATE == attrId.longValue()){
    				rate = attrValue;
    				retMap.put("rate", rate);
    			}
    			if(AssistMDA.ALLOWANCE_GROUP_AMONT_SUBRATE == attrId.longValue()){
    				subRateKeyValue = attrValue;
    				retMap.put("subRateKeyValue", subRateKeyValue);
    			}
    		}
    		
		}
    	
    	if(StringUtils.isEmpty(amont) || StringUtils.isEmpty(subsidyMode) || StringUtils.isEmpty(rate)){
    		retMap.put("retCode", "-1");	
    		retMap.put("retDesc", "查询单位用户补贴信息异常！");	
    		return retMap;
    	}
    	
    	
    	return retMap;
    	
    }
    /**
     * 查询成员信息列表
     * @param jsonStr
     * @return
     * @throws Exception
     */
    public Map qryMemUser(String jsonStr) throws Exception {
    	
    	Map<String, Object> retMap = new HashMap<String, Object>();
    	
        Map<String, Object> paramMap = jsonConverter.toBean(jsonStr, Map.class);
        Map<String, Object> groupInfo = (Map<String, Object>) paramMap.get("groupInfo");
        
        Map<String, Object> memMap = new HashMap<String, Object>();
        String busiRegionId = String.valueOf(groupInfo.get("busiRegionId"));
        String qryType = String.valueOf(groupInfo.get("qryType"));
        String prodInstId = String.valueOf(groupInfo.get("prodInstId"));
        String acctId = String.valueOf(groupInfo.get("acctId"));
        String offerInstIds = String.valueOf(groupInfo.get("offerInstIds"));
        //如果按照单位用户查询，但非单位用户
        if("1".equals(qryType) && (StringUtils.isEmpty(offerInstIds) || "null".equals(offerInstIds))){
        	return retMap;
        }
        
        if(StringUtils.isNotEmpty(busiRegionId) && !"null".equals(busiRegionId)){
        	memMap.put("busiRegionId", groupInfo.get("busiRegionId"));
        	memMap.put("regionId", groupInfo.get("busiRegionId"));
        }
        if(StringUtils.isNotEmpty(prodInstId) && !"null".equals(prodInstId)){
        	memMap.put("prodInstId", groupInfo.get("prodInstId"));
        }
        if(StringUtils.isNotEmpty(acctId) && !"null".equals(acctId)){
        	memMap.put("acctId", groupInfo.get("acctId"));
        }
        if(StringUtils.isNotEmpty(offerInstIds) && !"null".equals(offerInstIds)){
        	memMap.put("offerInstIds", groupInfo.get("offerInstIds"));
        }
        memMap.put("pageInfo", paramMap.get("pageInfo"));
        memMap.put("groupUserOffers", AssistMDA.ALLOWANCE_GROUP_OFFER_ID);
        memMap.put("menberUserOffers", AssistMDA.ALLOWANCE_MEMBER_OFFER_ID);
        
        //员工用户
    	List<Map<String, Object>> memList = new ArrayList<Map<String, Object>>();
    	
        String retStr = prodInstSMO.qryCrossSubsidySalesMenber(jsonConverter.toJson(memMap));
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(retStr);
        List memInfoList = (List) MapUtils.getObject(resultObjectMap, "compInfoVos");
        if(null != memInfoList && memInfoList.size() > 0){
        	for(int i=0;i<memInfoList.size();i++){
        		Map<String, Object> mem = new HashMap<String, Object>();
        		Map<String, Object> memDetail = (Map<String, Object>) memInfoList.get(i);
        		getMemInfo(mem,memDetail);
        		memList.add(mem);
        	}
        }
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        retMap.put("pageCount",pageCount);
        retMap.put("pageIndex",pageIndex);
        retMap.put("totalNumber", total);
    	retMap.put("memList", memList);
		return retMap;
    	
    }
    /**
     * 从资产查询结果中取成员展示信息
     * @param mem
     * @param memDetail
     * @throws Exception
     */
    private void getMemInfo(Map<String, Object> mem,Map<String, Object> memDetail) throws Exception{
    	
    	Map<String, Object> retMap = new HashMap<String, Object>();
    	List<Map<String, Object>> offerInstsList = new ArrayList<Map<String, Object>>();
    	List<Map<String, Object>> offerInstRelsList = new ArrayList<Map<String, Object>>();
    	
		List prodInsts = (List) MapUtils.getObject(memDetail, "prodInsts");
		List offerInsts = (List) MapUtils.getObject(memDetail, "offerInsts");
		List offerInstRels = (List) MapUtils.getObject(memDetail, "offerInstRels");
		List groupInfoList = (List) MapUtils.getObject(memDetail, "groupProductInfos");
		String subsidyMode = "";
		String groupOfferInstId = "";
		if(null != groupInfoList && groupInfoList.size() > 0){
			Map<String, Object> groupInfo = (Map<String, Object>) groupInfoList.get(0);
			mem.put("groupNum", groupInfo.get("accNum"));
			String offerId =  String.valueOf(groupInfo.get("offerId"));
			subsidyMode = AssistMDA.ALLOWANCE_GROUP_OFFER_MODE.get(Long.valueOf(offerId).longValue());
			groupOfferInstId =  String.valueOf(groupInfo.get("offerInstId"));
		}

		
		if(null != prodInsts && prodInsts.size() > 0){
			Map<String, Object> memInfo = (Map<String, Object>) prodInsts.get(0);
			mem.put("memNum", memInfo.get("accNum"));
		}
		//资产会把一个成员所有的成员销售品信息都返回在一个成员节点，这里需要过滤
		if(null != offerInsts && offerInsts.size() > 0){
			for (int z = 0; z < offerInsts.size(); z++) {
				Map<String, Object> memOffer = (Map<String, Object>) offerInsts.get(z);
				String effDateStr = String.valueOf(memOffer.get("effDate"));
				String expDateStr = String.valueOf(memOffer.get("expDate"));
				String memOfferId =  String.valueOf(memOffer.get("offerId"));
				//单位用户模式1，成员只订购100000000059
				if(AssistMDA.ALLOWANCE_TYPE_1.equals(subsidyMode) && 
						!AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_1.toString().equals(memOfferId)){
					continue;
				}
				//单位用户模式3，成员只订购100020004176
				if(AssistMDA.ALLOWANCE_TYPE_3.equals(subsidyMode) && 
						!AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_3.toString().equals(memOfferId)){
					continue;
				}
				//单位用户模式2，成员只订购100000000060 和  21001
				if(AssistMDA.ALLOWANCE_TYPE_2.equals(subsidyMode) && 
						!AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_21.toString().equals(memOfferId)
						&& !AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_22.toString().equals(memOfferId)){
					continue;
				}
				
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd "); 
				Date effDate = sdf.parse(effDateStr); 
				Date expDate = sdf.parse(expDateStr); 
				mem.put("startTime", sdf.format(effDate));
				mem.put("endTime", sdf.format(expDate));
				mem.put("stateName", memOffer.get("statusName"));
				List memAttrs = (List) MapUtils.getObject(memOffer, "offerInstAttrs");
				if(null != memAttrs && memAttrs.size() > 0){
					for (int i = 0; i < memAttrs.size(); i++) {
						Map<String, Object> memAttr = (Map<String, Object>) memAttrs.get(i);
						String attrId = String.valueOf(memAttr.get("attrId"));
						String attrValue = String.valueOf(memAttr.get("attrValue"));
						if(AssistMDA.ALLOWANCE_MEM_AMONT_MODE.toString().equals(attrId)){
							mem.put("subsidyModeName", AssistMDA.ALLOWANCE_TYPE_NAME.get(attrValue));
						}
						
						if(AssistMDA.ALLOWANCE_MEM_AMONT_MODE_3.toString().equals(attrId) ||
							AssistMDA.ALLOWANCE_MEM_AMONT_MODE_21.toString().equals(attrId) ||
							AssistMDA.ALLOWANCE_MEM_AMONT_MODE_1_AND_22.toString().equals(attrId)){
							mem.put("amont", Long.valueOf(attrValue)/100 );
				        }
					}
				}
				
				String offerInstId =  String.valueOf(memOffer.get("offerInstId"));
				if(null != offerInstRels && offerInstRels.size() > 0){
					for (int j = 0; j < offerInstRels.size(); j++) {
						Map<String, Object> offerInstRel = (Map<String, Object>) offerInstRels.get(j);
						String offerInstId4Rel =  String.valueOf(offerInstRel.get("offerInstId"));
						String roleId =  String.valueOf(offerInstRel.get("roleId"));
						//自身订购销售品关系
						if(StringUtils.isNotEmpty(offerInstId4Rel) && offerInstId4Rel.equals(offerInstId)){
							offerInstRelsList.add(offerInstRel);
						}
						//与单位用户的关系
						if("885".equals(roleId) && StringUtils.isNotEmpty(offerInstId4Rel) && offerInstId4Rel.equals(groupOfferInstId)){
							offerInstRelsList.add(offerInstRel);
						}
					}
				}
				offerInstsList.add(memOffer);
			}
		}
		
		retMap.put("prodInsts", prodInsts);
		retMap.put("offerInstRels", offerInstRelsList);
		retMap.put("offerInsts", offerInstsList);
		retMap.put("groupProductInfos", groupInfoList);
		
		mem.put("memDetail", retMap);
    }
    /**
     * 查询加成员信息
     * @param jsonStr
     * @return
     * @throws Exception
     */
    public Map qryAddMem(String jsonStr) throws Exception {
    	
    	Map<String, Object> retMap = new HashMap<String, Object>();
        Map<String, Object> paramMap = jsonConverter.toBean(jsonStr, Map.class);
        String groupModeId = String.valueOf(paramMap.get("groupModeId"));//1单位额外补贴员工模式 2电信融合交叉补贴模式 3定向抵扣补贴员工模式
        String subRateKeyValue = String.valueOf(paramMap.get("subRateKeyValue"));
        //如果是电信融合交叉补贴模式 则属性 660001(天翼融合及单产品补贴)一定有值
        if("2".equals(groupModeId)){
        	if(StringUtils.isEmpty(subRateKeyValue) || "null".equals(subRateKeyValue)){
        		retMap.put("retCode", "-1");	
        		retMap.put("retDesc", "电信融合交叉补贴模式的子比例属性值为空,查询失败！");
        		return retMap;
        	}
        }
        //查询配置获取成员的补贴方式
        List<Map<String, Object>> memModeInfoList = new ArrayList<Map<String, Object>>();//补贴模式
        List<Map<String, Object>> amountList = new ArrayList<Map<String, Object>>();//补贴金额
        retMap.put("groupModeId", groupModeId);
        //单位额外补贴员工模式 
        if(AssistMDA.ALLOWANCE_TYPE_1.equals(groupModeId)){
        	Map<String, Object> mode = new HashMap<String, Object>();
        	mode.put("subsidyMode", AssistMDA.ALLOWANCE_TYPE_1);
        	mode.put("subsidyModeName", AssistMDA.ALLOWANCE_TYPE_NAME.get(AssistMDA.ALLOWANCE_TYPE_1));
        	memModeInfoList.add(mode);
        	
        	retMap.put("memOfferId", AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_1);
            retMap.put("memOfferName", AssistMDA.ALLOWANCE_MEMBER_OFFER_NAME.get(AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_1));
            
            //补贴金额下拉取值
            List<String> allowanceAmount = AssistMDA.ALLOWANCE_TYPE_1_AMOUNT;
            for (String amountInfo : allowanceAmount) {
            	Map<String, Object> amount = new HashMap<String, Object>();
            	amount.put("amountText", amountInfo.split("\\|")[0]);
            	amount.put("amount", amountInfo.split("\\|")[1]);
                amountList.add(amount);
			}
        }
        //电信融合交叉补贴模式
        else if(AssistMDA.ALLOWANCE_TYPE_2.equals(groupModeId)){
        	List<String> memAllowanceType = AssistMDA.ALLOWANCE_TYPE_2_MEM_ALLOWANCE_TYPE;
        	for (String allowanceType : memAllowanceType) {
        		Map<String, Object> mode = new HashMap<String, Object>();
            	mode.put("subsidyMode", allowanceType);
            	mode.put("subsidyModeName", AssistMDA.ALLOWANCE_TYPE_NAME.get(allowanceType));
            	memModeInfoList.add(mode);
			}
        	retMap.put("memOfferId", AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_21);
            retMap.put("memOfferName", AssistMDA.ALLOWANCE_MEMBER_OFFER_NAME.get(AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_21));
            
            //补贴金额下拉取值,子比例有两个取值
            if("1".equals(subRateKeyValue) || "30".equals(subRateKeyValue)){
            	List<String> allowanceAmount = AssistMDA.ALLOWANCE_TYPE_21_1_AMOUNT;
            	for (String amountInfo : allowanceAmount) {
                	Map<String, Object> amount = new HashMap<String, Object>();
                	amount.put("amountText", amountInfo.split("\\|")[0]);
                	amount.put("amount", amountInfo.split("\\|")[1]);
                    amountList.add(amount);
    			}
            }else if("2".equals(subRateKeyValue) || "15".equals(subRateKeyValue)){
            	List<String> allowanceAmount = AssistMDA.ALLOWANCE_TYPE_21_2_AMOUNT;
            	for (String amountInfo : allowanceAmount) {
                	Map<String, Object> amount = new HashMap<String, Object>();
                	amount.put("amountText", amountInfo.split("\\|")[0]);
                	amount.put("amount", amountInfo.split("\\|")[1]);
                    amountList.add(amount);
    			}
            }
        }
        //定向抵扣补贴员工模式，补贴金额为前台输入
        else if(AssistMDA.ALLOWANCE_TYPE_3.equals(groupModeId)){
        	Map<String, Object> mode = new HashMap<String, Object>();
        	mode.put("subsidyMode", AssistMDA.ALLOWANCE_TYPE_3);
        	mode.put("subsidyModeName", AssistMDA.ALLOWANCE_TYPE_NAME.get(AssistMDA.ALLOWANCE_TYPE_3));
        	memModeInfoList.add(mode);
        	
        	retMap.put("memOfferId", AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_3);
            retMap.put("memOfferName", AssistMDA.ALLOWANCE_MEMBER_OFFER_NAME.get(AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_3));
        }
        else{
        	retMap.put("retCode", "-1");	
    		retMap.put("retDesc", "补贴模式值错误,modeId = "+groupModeId);
    		return retMap;
        }
        retMap.put("memModeInfoList", memModeInfoList);//成员补贴模式
        retMap.put("amountList", amountList);
        retMap.put("retCode", "0");	
		retMap.put("retDesc", "查询成功！");
    	return retMap;
    }
    /**
     * 退出补贴
     * @param jsonStr
     * @return
     * @throws Exception
     */
    public Map quitSubsidy(String jsonStr) throws Exception {
    	
    	Map<String, Object> retMap = new HashMap<String, Object>();
    	Map<String, Object> paramMap = jsonConverter.toBean(jsonStr, Map.class);

    	Map<String, Object> memDetail = (Map<String, Object>) paramMap.get("memDetail");
        String createOrgId = String.valueOf(paramMap.get("createOrgId"));//组织编码
        String createStaff = String.valueOf(paramMap.get("createStaff"));//操作工号
        
        String regionId = null;
        String lanId = null;
        String custId = null;
        String memOfferInstId = null;
        String offerProdInstRelId = null;
        String groupOfferInstId = null;
        Map<String, Object> prodInst = null;
        Map<String, Object> offerInst = null;
        Map<String, Object> offerInstRel_885 = null;
        
        List prodInsts = (List) MapUtils.getObject(memDetail, "prodInsts");
        List offerInsts = (List) MapUtils.getObject(memDetail, "offerInsts");
		List groupInfoList = (List) MapUtils.getObject(memDetail, "groupProductInfos");
		List offerInstRels = (List) MapUtils.getObject(memDetail, "offerInstRels");
		if(null != prodInsts && prodInsts.size() > 0){
			prodInst = (Map<String, Object>) prodInsts.get(0);
//			accNum = String.valueOf(prodInst.get("accNum"));
		}
		
		if(null != offerInsts && offerInsts.size() > 0){
			offerInst = (Map<String, Object>) offerInsts.get(0);
			regionId = String.valueOf(offerInst.get("regionId"));
			lanId = String.valueOf(offerInst.get("lanId"));
			custId = String.valueOf(offerInst.get("ownerCustId"));
			memOfferInstId = String.valueOf(offerInst.get("offerInstId"));
		}
		if(null != groupInfoList && groupInfoList.size() > 0){
			Map<String, Object> groupInfo = (Map<String, Object>) groupInfoList.get(0);
			groupOfferInstId = String.valueOf(groupInfo.get("offerInstId"));
		}
		if(null != offerInstRels && offerInstRels.size() > 0){
			for (int i = 0; i < offerInstRels.size(); i++) {
				Map<String, Object> offerProdRel = (Map<String, Object>) offerInstRels.get(i);
				String roleId = String.valueOf(offerProdRel.get("roleId"));
				String relId = String.valueOf(offerProdRel.get("offerProdInstRelId"));
				if(AssistMDA.ALLOWANCE_MEM_ROLE_ID.toString().endsWith(roleId)){
					offerProdInstRelId = relId;
					offerInstRel_885 = offerProdRel;
				}
			}
		}
		if(StringUtils.isEmpty(regionId)){
			retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "地市编码参数为空异常！");
    		return retMap;
		}
		if(StringUtils.isEmpty(lanId)){
			retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "本地网编码参数为空异常！");
    		return retMap;
		}
		if(StringUtils.isEmpty(custId)){
			retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "客户ID参数为空异常！");
    		return retMap;
		}
		if(StringUtils.isEmpty(memOfferInstId)){
			retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "成员订购销售品编码参数为空异常！");
    		return retMap;
		}
		if(StringUtils.isEmpty(offerProdInstRelId)){
			retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "成员关系编码参数为空异常！");
    		return retMap;
		}
		if(StringUtils.isEmpty(groupOfferInstId)){
			retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "单位订购销售品编码参数为空异常！");
    		return retMap;
		}

		//查询是否有在途单
        Map<String, Object> onlineMap = new HashMap<String, Object>();
        List<String> orderItemStatuses = new ArrayList<String>();
        orderItemStatuses.add("101400");
        orderItemStatuses.add("201300");
        orderItemStatuses.add("401200");
        orderItemStatuses.add("101200");
        orderItemStatuses.add("401302");
        orderItemStatuses.add("401400");
        orderItemStatuses.add("301100");
        List<Long> serviceOfferIds = new ArrayList<Long>();
        serviceOfferIds.add(3030100000L);
        onlineMap.put("orderItemStatuses", orderItemStatuses);
        onlineMap.put("serviceOfferIds", serviceOfferIds);
        Map<String, Object> data = new HashMap<String, Object>();
        data.put("prodInstId", prodInst.get("prodInstId"));
        data.put("offerId", offerInst.get("offerId"));
        onlineMap.put("data", data);
        
        String retOffInfoStr = orderQuerySMO.qryUnarchivedOrdOfferProdInstRels(jsonConverter.toJson(onlineMap));
        List retList = (List) resolveResult(retOffInfoStr);
        if(retList != null && retList.size() > 0){
        	Map orderItem = (Map) retList.get(0);
    		String orderItemId =  String.valueOf(orderItem.get("orderItemId"));
    		retMap.put("retCode", "-1");	
    		retMap.put("retDesc", "该用户存在在途单未完成！订单项: "+orderItemId);
    		return retMap;
        }

        //调用受理接口，退出补贴
        Map<String, Object> customerOrder = new HashMap<String, Object>();
        Map<String, Object> offerOrderItems = new HashMap<String, Object>();
        offerOrderItems.put("acceptRegionId", Long.valueOf(regionId));
        offerOrderItems.put("acceptLanId", Long.valueOf(lanId));
        offerOrderItems.put("createOrgId", Long.valueOf(createOrgId));
        offerOrderItems.put("custId", Long.valueOf(custId));
        offerOrderItems.put("orderSource", "1000");
        offerOrderItems.put("createStaff", createStaff);
//        offerOrderItems.put("custOrderType", "101");
        offerOrderItems.put("sysSource", "100005");
        
        List<Map<String, Object>> orderItemList = new ArrayList<Map<String, Object>>();
        
        //组装第一个订单项，退订销售品订单项
        Map<String, Object> orderItem1 = new HashMap<String, Object>();
        orderItem1.put("orderItemId", -1);
        orderItem1.put("serviceOfferId", 3030100000L);//退订
        orderItem1.put("createOrgId", Long.valueOf(createOrgId));
        
        //销售品信息
        Map<String, Object> ordOfferInsts1 = new HashMap<String, Object>();
        List<Map<String, Object>> instList1 = new ArrayList<Map<String, Object>>();
//        ordOfferInsts1.put("effDate", offerInst.get("effDate"));
//        ordOfferInsts1.put("expDate", getNowTime());
        ordOfferInsts1.put("offerInstId", Long.valueOf(memOfferInstId));
        ordOfferInsts1.put("operType", "1100");//1100 删除
        instList1.add(ordOfferInsts1);
        orderItem1.put("ordOfferInsts", instList1);
        
        orderItemList.add(orderItem1);
        
        //组装第二个订单项，删除员工与单位用户关系
        Map<String, Object> orderItem2 = new HashMap<String, Object>();//员工与单位用户建立关系订单项
        orderItem2.put("orderItemId", -2);
        orderItem2.put("serviceOfferId", 3020800000L);//变更成员
        orderItem2.put("createOrgId", Long.valueOf(createOrgId));
        
        Map<String, Object> ordOfferProdInstRels2 = new HashMap<String, Object>();
        List<Map<String, Object>> relsList2 = new ArrayList<Map<String, Object>>();
        ordOfferProdInstRels2.put("piRegionId", Long.valueOf(regionId));//员工地市
        ordOfferProdInstRels2.put("offerProdInstRelId", Long.valueOf(offerProdInstRelId));
        ordOfferProdInstRels2.put("accNum", prodInst.get("accNum"));
        ordOfferProdInstRels2.put("operType", "1100");//1100 删除
//        ordOfferProdInstRels2.put("effDate", offerInstRel_885.get("effDate"));
//        ordOfferProdInstRels2.put("expDate", getNowTime());
        ordOfferProdInstRels2.put("roleId", offerInstRel_885.get("roleId"));
        
        relsList2.add(ordOfferProdInstRels2);
        orderItem2.put("ordOfferProdInstRels", relsList2);

        //销售品信息
        Map<String, Object> ordOfferInsts2 = new HashMap<String, Object>();
        List<Map<String, Object>> instList2 = new ArrayList<Map<String, Object>>();
        ordOfferInsts2.put("offerInstId", Long.valueOf(groupOfferInstId));//单位销售品实例编码
        ordOfferInsts2.put("operType", "1300");//1300 保持
        instList2.add(ordOfferInsts2);
        orderItem2.put("ordOfferInsts", instList2);
        
        orderItemList.add(orderItem2);
        offerOrderItems.put("offerOrderItems", orderItemList);
        customerOrder.put("customerOrder", offerOrderItems);
        customerOrder.put("orderFlow", new String[]{"CHAIN","CONFIRM"});
        String customerOrderId = "";
        
		//查询是否还存在关系
        Map<String, Object> relMap = new HashMap<String, Object>();
        relMap.put("offerProdInstRelId", offerInstRel_885.get("offerProdInstRelId"));
        relMap.put("offerInstId", offerInstRel_885.get("offerInstId"));
        relMap.put("prodInstId", offerInstRel_885.get("prodInstId"));
        relMap.put("roleId", offerInstRel_885.get("roleId"));
        String retStr = prodInstSMO.getOfferProdInstRelInfo(jsonConverter.toJson(relMap));
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(retStr);
        List relList = (List) MapUtils.getObject(resultObjectMap, "offerProdInstRels");
        if(relList == null || relList.size() < 1){
        	retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "补贴已退订成功，请刷新查询！");
    		return retMap;
        }
        
        try{

        	logger.error("req_quitSubsidy_createCustomerOrderIntf"+jsonConverter.toJson(customerOrder));
        	String orderInfoStr =  allowanceSMO.createCustomerOrderIntf(jsonConverter.toJson(customerOrder));
            Map<String, Object> orderInfoMap = (Map<String, Object>) resolveResult(orderInfoStr);
            logger.error("resp_quitSubsidy_createCustomerOrderIntf"+orderInfoStr);
            String resultCode = String.valueOf(orderInfoMap.get("resultCode"));
            String resultMsg =  String.valueOf(orderInfoMap.get("resultMsg"));
            customerOrderId =  String.valueOf(orderInfoMap.get("customerOrderId"));
            if(StringUtils.isNotEmpty(resultCode) && !"0".equals(resultCode)){
            	retMap.put("retCode", "-1");	
            	retMap.put("retDesc", resultMsg);
            	return retMap;
            }
            
        }catch(Exception e){
        	retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "生成订单异常:"+e.getMessage());
    		return retMap;
        }
        retMap.put("retCode", "0");	
        retMap.put("retDesc", "提交成功，订单编号: "+customerOrderId);
        return retMap;
    }
    
    private String getNowTime() throws Exception{
    	Date currentTime = new Date();  
    	SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
    	String dateString = formatter.format(currentTime);
    	return dateString;
    }
    /**
     * 添加成员
     * @param jsonStr
     * @return
     * @throws Exception
     */
    public Map addMemCommit(String jsonStr) throws Exception {
    	
    	Map<String, Object> retMap = new HashMap<String, Object>();
    	Map<String, Object> paramMap = jsonConverter.toBean(jsonStr, Map.class);
        String prodInstId = String.valueOf(paramMap.get("prodInstId"));//单位用户实例ID
        String offerType = String.valueOf(paramMap.get("offerType"));//单位用户销售品类型
        String offerId = String.valueOf(paramMap.get("offerId"));//单位用户销售品编码
        String offerInstId = String.valueOf(paramMap.get("offerInstId"));//单位用户销售品实例编码
        String totalAmount = String.valueOf(paramMap.get("totalAmount"));//补贴总金额
        String rate = String.valueOf(paramMap.get("rate"));//补贴比例
        String busiNumber = String.valueOf(paramMap.get("busiNumber"));//员工号码
        String memAccNumType = String.valueOf(paramMap.get("memAccNumType"));//号码类型 1接入号码 2宽带账号
        String memMode = String.valueOf(paramMap.get("memMode"));//补贴模式
        String subRateKeyValue = String.valueOf(paramMap.get("subRateKeyValue"));//子补贴比例
        String memOfferId = String.valueOf(paramMap.get("orderOfferId"));//成员订购的销售品ID
        String amount = String.valueOf(paramMap.get("amount"));//补贴金额
        String busiRegionId = String.valueOf(paramMap.get("busiRegionId"));//地市
        String createOrgId = String.valueOf(paramMap.get("createOrgId"));//组织编码
        String createStaff = String.valueOf(paramMap.get("createStaff"));//操作工号
        String acctCd = String.valueOf(paramMap.get("acctCd"));//单位合同号
        
        if(AssistMDA.ALLOWANCE_TYPE_22.equals(memMode)){
        	memOfferId = AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_22.toString();
        }
        
        Map checkRet = checkAddMemInfo(prodInstId,acctCd,offerInstId,busiRegionId,busiNumber,memAccNumType,memMode,memOfferId,subRateKeyValue,amount,totalAmount,rate);
        if(!"0".equals(checkRet.get("retCode"))){
        	retMap.put("retCode", checkRet.get("retCode"));	
        	retMap.put("retDesc", checkRet.get("retDesc"));
    		return retMap;
        }
        
        Long memProdId = Long.valueOf(String.valueOf(checkRet.get("memProdId")));//成员产品规格
        Long memProdInstId = Long.valueOf(String.valueOf(checkRet.get("memProdInstId")));//成员实例编码
        Long memRegionId = Long.valueOf(String.valueOf(checkRet.get("memRegionId")));//成员地市编码
        Long memLanId = Long.valueOf(String.valueOf(checkRet.get("memLanId")));//成员本地网编码
        Long memCustId = Long.valueOf(String.valueOf(checkRet.get("memCustId")));//成员本地网编码
        
        if(AssistMDA.ALLOWANCE_TYPE_22.equals(memMode)){
        	amount = String.valueOf(checkRet.get("amount22"));
        }
        
        //调用受理接口，添加成员，加入补贴
        Map<String, Object> customerOrder = new HashMap<String, Object>();
        Map<String, Object> offerOrderItems = new HashMap<String, Object>();
        offerOrderItems.put("acceptRegionId", memRegionId);
        offerOrderItems.put("acceptLanId", memLanId);
        offerOrderItems.put("createOrgId", Long.valueOf(createOrgId));
        offerOrderItems.put("custId", memCustId);
        offerOrderItems.put("orderSource", "1000");
        offerOrderItems.put("createStaff", createStaff);
//        offerOrderItems.put("custOrderType", "101");
        offerOrderItems.put("sysSource", "100005");
        
        List<Map<String, Object>> orderItemList = new ArrayList<Map<String, Object>>();
        
        //组装第一个订单项，订购销售品订单项
        Map<String, Object> orderItem1 = new HashMap<String, Object>();
        orderItem1.put("orderItemId", -1);
        orderItem1.put("serviceOfferId", 3010100000L);
        orderItem1.put("createOrgId", Long.valueOf(createOrgId));
        
        //销售品与产品关系
        Map<String, Object> ordOfferProdInstRels1 = new HashMap<String, Object>();
        List<Map<String, Object>> relsList1 = new ArrayList<Map<String, Object>>();
        String memOfferType;
        //查询ppm
        Map<String, Object> memOfferRelMap = new HashMap<String, Object>();
        //交叉补贴虚拟附属销售品 为虚拟的销售品，销售品与产品关系配置的-9999
        if(AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_22.toString().equals(memOfferId)){
        	memOfferRelMap.put("offerId", Long.valueOf(memOfferId));
            memOfferRelMap.put("prodId", -9999);
            memOfferRelMap.put("roleId", 1);
            memOfferType = "12";
        }else{
        	memOfferRelMap.put("offerId", Long.valueOf(memOfferId));
            memOfferRelMap.put("prodId", memProdId);
            memOfferRelMap.put("roleId", 1);
            memOfferType = "13";
        }
        List memOfferRelList = null;
        try{
        	String retStr = offerSMO.qryOfferProdRels(jsonConverter.toJson(memOfferRelMap));
        	memOfferRelList = (List) resolveResult(retStr);
        }catch(Exception e){
        	retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "未查询到成员销售品与成员产品关系异常！");
    		return retMap;
        }
        if(null == memOfferRelList || memOfferRelList.size() < 1){
        	retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "查询ppm，未查询到成员销售品["+memOfferId+"]与成员产品["+memOfferRelMap.get("prodId")+"]关系！无法纳入成员！");
    		return retMap;
        }
        Map memOfferProdRel = (Map) memOfferRelList.get(0);
        
        ordOfferProdInstRels1.put("offerProdRelId", memOfferProdRel.get("offerProdRelId"));
        ordOfferProdInstRels1.put("relType", memOfferProdRel.get("relType"));
        ordOfferProdInstRels1.put("roleName", memOfferProdRel.get("roleName"));
        ordOfferProdInstRels1.put("piRegionId", memRegionId);//员工地市编码
        ordOfferProdInstRels1.put("offerProdInstRelId", -1);
        ordOfferProdInstRels1.put("prodFuncType", "101");
        ordOfferProdInstRels1.put("prodId", memProdId);
        ordOfferProdInstRels1.put("offerInstId", -1);
        ordOfferProdInstRels1.put("prodInstId", memProdInstId);
        ordOfferProdInstRels1.put("offerId", Long.valueOf(memOfferId));
        ordOfferProdInstRels1.put("accNum", busiNumber);
        ordOfferProdInstRels1.put("accProdInstId", memProdInstId);
        ordOfferProdInstRels1.put("prodUseType", "1000");
        ordOfferProdInstRels1.put("assistProdId", memProdId);
        ordOfferProdInstRels1.put("operType", "1000");
        ordOfferProdInstRels1.put("roleId", 1);
        ordOfferProdInstRels1.put("offerType", memOfferType);
        
        relsList1.add(ordOfferProdInstRels1);
        orderItem1.put("ordOfferProdInstRels", relsList1);

        //销售品信息
        Map<String, Object> ordOfferInsts1 = new HashMap<String, Object>();
        List<Map<String, Object>> instList1 = new ArrayList<Map<String, Object>>();
        ordOfferInsts1.put("offerType", memOfferType);
        ordOfferInsts1.put("offerId", Long.valueOf(memOfferId));
        ordOfferInsts1.put("ownerCustId", memCustId);
        ordOfferInsts1.put("lanId", memLanId);
//        ordOfferInsts1.put("isIndependent", "Y");
        ordOfferInsts1.put("offerName", AssistMDA.ALLOWANCE_MEMBER_OFFER_NAME.get(Long.valueOf(memOfferId).longValue()));
        ordOfferInsts1.put("createOrgId", Long.valueOf(createOrgId));
        ordOfferInsts1.put("offerInstId", -1);
        ordOfferInsts1.put("operType", "1000");
        instList1.add(ordOfferInsts1);
        orderItem1.put("ordOfferInsts", instList1);
        
        //销售品属性
        List<Map<String, Object>> attrList = new ArrayList<Map<String, Object>>();
        Map<String, Object> attr1 = new HashMap<String, Object>();
        if(AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_3.toString().equals(memOfferId)){
        	attr1.put("attrId", AssistMDA.ALLOWANCE_MEM_AMONT_MODE_3);
        }else if(AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_21.toString().equals(memOfferId)){
        	attr1.put("attrId", AssistMDA.ALLOWANCE_MEM_AMONT_MODE_21);
        }else{
        	attr1.put("attrId", AssistMDA.ALLOWANCE_MEM_AMONT_MODE_1_AND_22);
        }
        attr1.put("orderItemId", -1);
        attr1.put("offerInstAttrId", -1);
        attr1.put("offerInstId", -1);
        attr1.put("operType", "1000");
        attr1.put("attrValue", String.valueOf(amount));
        attrList.add(attr1);
        
        Map<String, Object> attr2 = new HashMap<String, Object>();
        attr2.put("attrId", AssistMDA.ALLOWANCE_MEM_AMONT_MODE);
        attr2.put("orderItemId", -1);
        attr2.put("offerInstAttrId", -2);
        attr2.put("offerInstId", -1);
        attr2.put("operType", "1000");
        attr2.put("attrValue", memMode);
        attrList.add(attr2);
        
        Map<String, Object> attr3 = new HashMap<String, Object>();
        attr3.put("attrId", AssistMDA.ALLOWANCE_MEM_REL_GROUP_OFF_INST_ID);
        attr3.put("orderItemId", -1);
        attr3.put("offerInstAttrId", -2);
        attr3.put("offerInstId", -1);
        attr3.put("operType", "1000");
        attr3.put("attrValue", offerInstId);
        attrList.add(attr3);
        
        orderItem1.put("ordOfferInstAttrs", attrList);
        
        orderItemList.add(orderItem1);
        
        //组装第二个订单项，员工与单位用户关系
        Map<String, Object> orderItem2 = new HashMap<String, Object>();//员工与单位用户建立关系订单项
        orderItem2.put("orderItemId", -2);
        orderItem2.put("serviceOfferId", 3020800000L);
        orderItem2.put("createOrgId", Long.valueOf(createOrgId));
        
        Map<String, Object> ordOfferProdInstRels2 = new HashMap<String, Object>();
        List<Map<String, Object>> relsList2 = new ArrayList<Map<String, Object>>();
        //查询ppm
        Map<String, Object> offerRelMap = new HashMap<String, Object>();
        offerRelMap.put("offerId", Long.valueOf(offerId));
        offerRelMap.put("prodId", -9999);
        offerRelMap.put("roleId", 885);
        List offerRelList = null;
        try{
        	String retStr = offerSMO.qryOfferProdRels(jsonConverter.toJson(offerRelMap));
            offerRelList = (List) resolveResult(retStr);
        }catch(Exception e){
        	retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "未查询到单位销售品与成员产品关系异常！");
    		return retMap;
        }
        
        if(null == offerRelList || offerRelList.size() < 1){
        	retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "未查询到单位销售品与成员关系！");
    		return retMap;
        }
        Map offerProdRel = (Map) offerRelList.get(0);
        
        ordOfferProdInstRels2.put("piRegionId", memRegionId);//员工地市
        ordOfferProdInstRels2.put("offerProdInstRelId", -2);
        ordOfferProdInstRels2.put("prodFuncType", "101");//定值
        ordOfferProdInstRels2.put("prodId", memProdId);//员工用户产品规格
        ordOfferProdInstRels2.put("roleName", offerProdRel.get("roleName"));
        ordOfferProdInstRels2.put("offerInstId", Long.valueOf(offerInstId));//单位用户的销售品实例编码
        ordOfferProdInstRels2.put("prodInstId", memProdInstId);//员工用户实例编码
        ordOfferProdInstRels2.put("offerType", offerType);//单位销售品类型
        ordOfferProdInstRels2.put("accNum", busiNumber);
        ordOfferProdInstRels2.put("accProdInstId", memProdInstId);
        ordOfferProdInstRels2.put("prodUseType", "1000");
        ordOfferProdInstRels2.put("assistProdId", memProdId);
        ordOfferProdInstRels2.put("operType", "1000");//1000 新增
        ordOfferProdInstRels2.put("offerId", Long.valueOf(offerId));//单位订购销售品编码
        ordOfferProdInstRels2.put("roleId", 885);
        ordOfferProdInstRels2.put("offerProdRelId", offerProdRel.get("offerProdRelId"));
        ordOfferProdInstRels2.put("relType", offerProdRel.get("relType"));
        relsList2.add(ordOfferProdInstRels2);
        orderItem2.put("ordOfferProdInstRels", relsList2);

        //销售品信息
        Map<String, Object> ordOfferInsts2 = new HashMap<String, Object>();
        List<Map<String, Object>> instList2 = new ArrayList<Map<String, Object>>();
        ordOfferInsts2.put("offerInstId", Long.valueOf(offerInstId));//单位销售品实例编码
        ordOfferInsts2.put("operType", "1300");//1300 保持
//        ordOfferInsts1.put("isIndependent", "Y");
        instList2.add(ordOfferInsts2);
        orderItem2.put("ordOfferInsts", instList2);
        
        orderItemList.add(orderItem2);
        offerOrderItems.put("offerOrderItems", orderItemList);
        customerOrder.put("customerOrder", offerOrderItems);
        customerOrder.put("orderFlow", new String[]{"CONFIRM"});
        String customerOrderId = "";
        try{
        	logger.error("req_addMemCommit_createCustomerOrderIntf:"+jsonConverter.toJson(customerOrder));
        	String orderInfoStr =  allowanceSMO.createCustomerOrderIntf(jsonConverter.toJson(customerOrder));
            Map<String, Object> orderInfoMap = (Map<String, Object>) resolveResult(orderInfoStr);
            logger.error("resp_addMemCommit_createCustomerOrderIntf:"+orderInfoStr);
            String resultCode = String.valueOf(orderInfoMap.get("resultCode"));
            String resultMsg =  String.valueOf(orderInfoMap.get("resultMsg"));
            customerOrderId =  String.valueOf(orderInfoMap.get("customerOrderId"));
            if(StringUtils.isNotEmpty(resultCode) && !"0".equals(resultCode)){
            	retMap.put("retCode", "-1");	
            	retMap.put("retDesc", resultMsg);
            	return retMap;
            }
        }catch(Exception e){
        	retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "生成订单异常:"+e.getMessage());
    		return retMap;
        }
        retMap.put("retCode", "0");	
        retMap.put("retDesc", "提交成功，订单编号: "+customerOrderId);
        return retMap;
    }
    /**
     * 
     * @param prodInstId  单位用户产品实例编码
     * @param acctCd  单位合同号
     * @param offerInstId  单位用户销售品实例编码
     * @param busiRegionId  查询地市
     * @param busiNumber 成员号码
     * @param memAccNumType 成员类型
     * @param memMode 成员补贴模式
     * @param memMode 成员订购销售品编码
     * @param subRateKeyValue 子补贴比例
     * @param amount 成员补贴金额
     * @param totalAmount 补贴金额总额
     * @param rate 补贴比例
     * @return
     * @throws Exception
     */
    private Map checkAddMemInfo(String prodInstId,String acctCd,String offerInstId,String busiRegionId,String busiNumber,String memAccNumType,
    		String memMode,String memOfferId,String subRateKeyValue,String amount,String totalAmount,String rate) throws Exception {
    	Map<String, Object> checkRet = new HashMap<String, Object>();
    	
    	//查询成员用户销售品信息
        Map<String, Object> userInfoMap = new HashMap<String, Object>();
        userInfoMap.put("regionId", busiRegionId);
        userInfoMap.put("busiRegionId", busiRegionId);
        userInfoMap.put("qryType", 1);
        userInfoMap.put("accNumType", memAccNumType);
        userInfoMap.put("queryValue", busiNumber);
        String retStr = prodInstSMO.qryCrossSubsidySalesInfo(jsonConverter.toJson(userInfoMap));
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(retStr);
        if(MapUtils.isEmpty(resultObjectMap)){
        	checkRet.put("retCode", "-1");	
        	checkRet.put("retDesc", "产品不存在或已拆机！");
    		return checkRet;
        }
        Long memProdId = Long.valueOf(String.valueOf(resultObjectMap.get("prodId")));//成员产品规格
        Long memProdInstId = Long.valueOf(String.valueOf(resultObjectMap.get("prodInstId")));//成员产品实例编码
        Long memRegionId = Long.valueOf(String.valueOf(resultObjectMap.get("regionId")));//成员地市编码
        Long memLanId = Long.valueOf(String.valueOf(resultObjectMap.get("lanId")));//成员本地网编码
        Long memCustId = Long.valueOf(String.valueOf(resultObjectMap.get("custId")));//成员本地网编码
        checkRet.put("memProdId", memProdId);
        checkRet.put("memProdInstId", memProdInstId);
        checkRet.put("memRegionId", memRegionId);
        checkRet.put("memLanId", memLanId);
        checkRet.put("memCustId", memCustId);
        
        //查询是否有在途单
        Map<String, Object> paramMap = new HashMap<String, Object>();
        List<String> orderItemStatuses = new ArrayList<String>();
        orderItemStatuses.add("101400");
        orderItemStatuses.add("201300");
        orderItemStatuses.add("401200");
        orderItemStatuses.add("101200");
        orderItemStatuses.add("401302");
        orderItemStatuses.add("401400");
        orderItemStatuses.add("301100");
        List<Long> serviceOfferIds = new ArrayList<Long>();
        serviceOfferIds.add(3010100000L);
        paramMap.put("orderItemStatuses", orderItemStatuses);
        paramMap.put("serviceOfferIds", serviceOfferIds);
        Map<String, Object> data = new HashMap<String, Object>();
        data.put("prodInstId", memProdInstId);
        data.put("offerId", Long.valueOf(memOfferId));
        paramMap.put("data", data);
        
        String retOffInfoStr = orderQuerySMO.qryUnarchivedOrdOfferProdInstRels(jsonConverter.toJson(paramMap));
        List retList = (List) resolveResult(retOffInfoStr);
        if(retList != null && retList.size() > 0){
        	Map orderItem = (Map) retList.get(0);
    		String orderItemId =  String.valueOf(orderItem.get("orderItemId"));
        	checkRet.put("retCode", "-1");	
        	checkRet.put("retDesc", "该用户存在在途单未完成！订单项: "+orderItemId);
    		return checkRet;
        }
        
        if(AssistMDA.ALLOWANCE_TYPE_21.equals(memMode) && AssistMDA.ALLOWANCE_MEM_PROD_ID_MODE_21 != memProdId.longValue()){
        	checkRet.put("retCode", "-1");	
        	checkRet.put("retDesc", "电信融合交叉补贴模式只允许【手机】加入，受理失败！");
    		return checkRet;
        }
        if(AssistMDA.ALLOWANCE_TYPE_3.equals(memMode) && !AssistMDA.ALLOWANCE_MEM_PROD_ID_MODE_3.contains(memProdId.longValue())){
        	checkRet.put("retCode", "-1");	
        	checkRet.put("retDesc", "只允许【手机】、【光纤宽带】、【专线LAN】、【专线光纤宽带】、【专线商务光纤】、【虚拟拨号商务光纤】、【H虚拟拨号光纤宽带】、【H专线光纤宽】 加入，受理失败！");
    		return checkRet;
        }
//        if(AssistMDA.ALLOWANCE_TYPE_1.equals(memMode) && !AssistMDA.ALLOWANCE_MEM_PROD_ID_MODE_1.contains(memProdId.longValue())){
//        	checkRet.put("retCode", "-1");	
//        	checkRet.put("retDesc", "只允许【手机】、【普通电话】、【宽带】、【LAN】 加入，受理失败！");
//    		return checkRet;
//        }
        
        boolean isHaveMode1 = false;//成员是否已经加入模式1
        boolean isHaveMode3 = false;//成员是否已经加入模式3
        boolean isHaveMode21 = false;//成员是否已经加入模式21
        boolean isHaveMode22 = false;//成员是否已经加入模式22
        boolean isHavePreMode22 = false;//加入E9/天翼领航信息板块补贴模式需要先订购E9/天翼领航信息板块 套餐
        List offerInsts = (List) resultObjectMap.get("offerInsts");
        if(null != offerInsts && offerInsts.size() > 0){
        	for (int i = 0; i < offerInsts.size(); i++) {
        		Map offerInst = (Map) offerInsts.get(i);
        		String offerId =  String.valueOf(offerInst.get("offerId"));
        		if(AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_1.toString().equals(offerId)){
        			isHaveMode1 = true;
        		}
        		if(AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_3.toString().equals(offerId)){
        			isHaveMode3 = true;
        		}
        		if(AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_21.toString().equals(offerId)){
        			isHaveMode21 = true;
        		}
        		if(AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_22.toString().equals(offerId)){
        			isHaveMode22 = true;
        		}
        		if(AssistMDA.ALLOWANCE_TYPE_22.equals(memMode) && ("1".equals(subRateKeyValue)|| "30".equals(subRateKeyValue))){
        			Long amount22 = AssistMDA.ALLOWANCE_TYPE_22_1_AMOUNT.get(Long.valueOf(offerId).longValue());
        			if(null != amount22){
        				isHavePreMode22 = true;
        				checkRet.put("amount22", amount22);
        			}
        		}
        		
        		if(AssistMDA.ALLOWANCE_TYPE_22.equals(memMode) && ("2".equals(subRateKeyValue)||"15".equals(subRateKeyValue))){
        			Long amount22 = AssistMDA.ALLOWANCE_TYPE_22_2_AMOUNT.get(Long.valueOf(offerId).longValue());
        			if(null != amount22){
        				isHavePreMode22 = true;
        				checkRet.put("amount22", amount22);
        			}
        		}
            }
        }
        
        if((isHaveMode1 && AssistMDA.ALLOWANCE_TYPE_1.equals(memMode)) ||
           (isHaveMode3 && AssistMDA.ALLOWANCE_TYPE_3.equals(memMode)) ||
           (isHaveMode21 && AssistMDA.ALLOWANCE_TYPE_21.equals(memMode)) ||
           (isHaveMode22 && AssistMDA.ALLOWANCE_TYPE_22.equals(memMode))){
        	checkRet.put("retCode", "-1");	
        	checkRet.put("retDesc", "您已经参加了相同补贴模式的交叉补贴，不能重复参加，受理失败！");
    		return checkRet;
        }
        
        if(isHaveMode22 && AssistMDA.ALLOWANCE_TYPE_21.equals(memMode)){
        	checkRet.put("retCode", "-1");	
        	checkRet.put("retDesc", "该成员已经加入E9/天翼领航信息板块补贴模式的套餐，不能参加天翼单产品模式，补贴模式请选择E9/天翼领航信息板块补。");
    		return checkRet;
        }
        
        if(!isHavePreMode22 && AssistMDA.ALLOWANCE_TYPE_22.equals(memMode)){
        	checkRet.put("retCode", "-1");	
        	checkRet.put("retDesc", "E9/天翼领航信息板块补贴模式失败：该成员并未加入套餐。");
    		return checkRet;
        }
        
        //查询单位用户下已经存在的成员，计算已经使用过的补贴金额总和+本次补贴金额是否大于补贴总额
        Map<String, Object> userAmountMap = new HashMap<String, Object>();
        userAmountMap.put("offerInstId", offerInstId);
        userAmountMap.put("offerId", memOfferId);
        if(AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_1.toString().equals(memOfferId) || 
        		AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_22.toString().equals(memOfferId)){
        	userAmountMap.put("attrId", AssistMDA.ALLOWANCE_MEM_AMONT_MODE_1_AND_22);
        }else if(AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_3.toString().equals(memOfferId)){
        	userAmountMap.put("attrId", AssistMDA.ALLOWANCE_MEM_AMONT_MODE_3);
        }else if(AssistMDA.ALLOWANCE_MEMBER_OFFER_ID_MODE_21.toString().equals(memOfferId)){
        	userAmountMap.put("attrId", AssistMDA.ALLOWANCE_MEM_AMONT_MODE_21);
        }else{
        	checkRet.put("retCode", "-1");	
        	checkRet.put("retDesc", "成员订购销售品错误！");
    		return checkRet;
        }
        
        String retAmountStr = prodInstSMO.qryCrossSubsidySalesMenberAmount(jsonConverter.toJson(userAmountMap));
        Map<String, Object> amountMap = (Map<String, Object>) resolveResult(retAmountStr);
        String usedAmount = String.valueOf(amountMap.get("amount"));
        if(StringUtils.isEmpty(usedAmount) || "null".equals(usedAmount)){
        	usedAmount = "0";
        }
        if((Long.valueOf(usedAmount) / 100 + Long.valueOf(amount) / 100) > Long.valueOf(totalAmount)){
        	checkRet.put("retCode", "-1");	
        	checkRet.put("retDesc", "已经超出单位产品的补贴额度，受理失败！");
    		return checkRet;
        }
        
        //单位账户与成员账户不能为同一合同号
        //查询单位用户的账户信息
        List<Map<String, Object>> memAcctInfo = getAcctInfo(memProdInstId);
        if(StringUtils.isNotEmpty(acctCd) && !"null".equals(acctCd)){
        	boolean isEqual = ifEqualAcct(acctCd,memAcctInfo);
        	if(isEqual){
				checkRet.put("retCode", "-1");	
	        	checkRet.put("retDesc", "单位账户与成员账户不能为同一合同号！合同号："+acctCd);
	    		return checkRet;
			}
        }else{
            List<Map<String, Object>> groupAcctInfo = getAcctInfo(Long.valueOf(prodInstId));
            if(null != groupAcctInfo && null != memAcctInfo){
            	for (int i=0;i<groupAcctInfo.size();i++) {
            		Map<String, Object> groupAcctInfoDetail = (Map<String, Object>) groupAcctInfo.get(i);
            		String acctCdGrp = (String) groupAcctInfoDetail.get("acctCd");
            		if(StringUtils.isNotEmpty(acctCdGrp)){
            			boolean isEqual = ifEqualAcct(acctCdGrp,memAcctInfo);
            			if(isEqual){
            				checkRet.put("retCode", "-1");	
            	        	checkRet.put("retDesc", "单位账户与成员账户不能为同一合同号！合同号："+acctCdGrp);
            	    		return checkRet;
            			}
            		}
            	}
            }
        }
        
        //该用户的账户下存在成员已经订购【单位付费大划小】销售品
        if(AssistMDA.ALLOWANCE_TYPE_1.equals(memMode)){
        	List<Long> offerIds = qryOfferExtAttrByAttrSpec(AssistMDA.ALLOWANCE_TYPE_1);
        	Map<String, Object> validReqMap = new HashMap<String, Object>();
        	validReqMap.put("prodInstId", memProdInstId);
        	validReqMap.put("offerIds", offerIds);
        	String ret = prodInstSMO.validOfferInstByProdInstIdAndOfferIds(jsonConverter.toJson(validReqMap));
            Map<String, Object> validResqMap = (Map<String, Object>) resolveResult(ret);
            if(MapUtils.isNotEmpty(validResqMap) && "true".equals(String.valueOf(validResqMap.get("success")))){
            	checkRet.put("retCode", "-1");	
	        	checkRet.put("retDesc", "该用户的帐户下订购的销售品与大划小互斥！不允许纳入！");
	    		return checkRet;
            }
        	
        }
        
        //该用户的账户下存在成员已经订购【电信赠费大划小】销售品
        if(AssistMDA.ALLOWANCE_TYPE_21.equals(memMode) || AssistMDA.ALLOWANCE_TYPE_22.equals(memMode) ||
        		AssistMDA.ALLOWANCE_TYPE_2.equals(memMode)){
        	List<Long> offerIds = qryOfferExtAttrByAttrSpec(AssistMDA.ALLOWANCE_TYPE_2);
        	Map<String, Object> validReqMap = new HashMap<String, Object>();
        	validReqMap.put("prodInstId", memProdInstId);
        	validReqMap.put("offerIds", offerIds);
        	String ret = prodInstSMO.validOfferInstByProdInstIdAndOfferIds(jsonConverter.toJson(validReqMap));
            Map<String, Object> validResqMap = (Map<String, Object>) resolveResult(ret);
            if(MapUtils.isNotEmpty(validResqMap) && "true".equals(String.valueOf(validResqMap.get("success")))){
            	checkRet.put("retCode", "-1");	
            	checkRet.put("retDesc", "该用户的帐户下订购的销售品与大划小互斥！不允许纳入！");
	    		return checkRet;
            }
        	
        }			

    	checkRet.put("retCode", "0");
    	checkRet.put("retDesc", "加入成功 ！");
    	return checkRet;
    }
    
    private List<Map<String, Object>> getAcctInfo(Long prodInstId) throws IOException {
    	 Map<String, Object> reqMap = new HashMap<String, Object>();
    	 reqMap.put("prodInstId", prodInstId);
    	 String acctReqStr = jsonConverter.toJson(reqMap);
    	 String acctInfo = acctQuerySmo.qryAccountListForMulti(acctReqStr);
    	 Map<String, Object> acctInfoMap = (Map<String, Object>) resolveResult(acctInfo);
    	 List accounts = (List) MapUtils.getObject(acctInfoMap, "accounts");
    	 if(null != accounts && accounts.size() > 0){
    		 return accounts;
    	 }
    	 return null;
    }
    
    private boolean ifEqualAcct(String acctCd,List<Map<String, Object>> memAcctInfo){
    	
    	for (int i=0;i<memAcctInfo.size();i++) {
    		Map<String, Object> memAcctInfoDetail = (Map<String, Object>) memAcctInfo.get(i);
    		String memAcctCd = (String) memAcctInfoDetail.get("acctCd");
    		if(StringUtils.isNotEmpty(memAcctCd) && acctCd.equals(memAcctCd)){
    			return true;
    		}
    	}
    	return false;
    }
    
    
    private List<Long> qryOfferExtAttrByAttrSpec(String memMode) throws IOException  {
    	List<Long> memOfferIds = new ArrayList<Long>();
    	Map pageInfoMap = new HashMap<>();
		pageInfoMap.put("pageIndex", 1);
		pageInfoMap.put("pageSize", 100);
		Map reqParam = new HashMap<>();
		reqParam.put("pageInfo", pageInfoMap);
		reqParam.put("attrId", "************");
		reqParam.put("defaultValue", memMode);
		String reqParamStr = jsonConverter.toJson(reqParam);
		String retStr = specSMO.qryOfferExtAttrByAttrSpec(reqParamStr);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(retStr);
        List<Map<String, Object>> offerInfos = (List<Map<String, Object>>) MapUtils.getObject(resultObjectMap, "offerExtAttrs");
        if(null != offerInfos && offerInfos.size() > 0){
        	for (int i=0;i<offerInfos.size();i++) {
        		Map<String, Object> memOffer  = offerInfos.get(i);
    			String offerId = String.valueOf(memOffer.get("offerId"));
    			if(StringUtils.isNotEmpty(offerId)){
    				memOfferIds.add(Long.valueOf(offerId));
    			}
        		
        	}
        }
        //存在多页返回
        Map<String, Object> pageInfo = (Map<String, Object>) MapUtils.getObject(resultObjectMap, "pageInfo");
        String pageCountStr = String.valueOf(pageInfo.get("pageCount")) ;
        if(StringUtils.isNotEmpty(pageCountStr) && Integer.valueOf(pageCountStr).intValue() > 1){
        	int pageCount = Integer.valueOf(pageCountStr).intValue();
        	for(int i=2;i<=pageCount;i++){
        		pageInfoMap.put("pageIndex", i);
        		reqParam.put("pageInfo", pageInfoMap);
        		String reqParamStrMore = jsonConverter.toJson(reqParam);
        		String retStrMore = specSMO.qryOfferExtAttrByAttrSpec(reqParamStrMore);
        		Map<String, Object> resultObjectMapMore = (Map<String, Object>) resolveResult(retStrMore);
        		 List<Map<String, Object>> offerInfosMore = (List<Map<String, Object>>) MapUtils.getObject(resultObjectMapMore, "offerExtAttrs");
        	        if(null != offerInfosMore && offerInfosMore.size() > 0){
        	        	for (int j=0;j<offerInfosMore.size();j++) {
        	        		Map<String, Object> memOffer  = offerInfosMore.get(j);
        	    			String offerId = String.valueOf(memOffer.get("offerId"));
        	    			if(StringUtils.isNotEmpty(offerId)){
        	    				memOfferIds.add(Long.valueOf(offerId));
        	    			}
        	        		
        	        	}
        	        }
        	}
        }
        
    	return memOfferIds;
    }
    
}
