(function(vita){
    var logisticsOptionalEditAttach = vita.Backbone.BizView.extend({
        events : {
            "click button[btn-type=confirm]":"_saveLogisticsInfo",
            "click button[btn-type=cancel]" : "_closePage"
        },
        _initialize:function(){
            var widget = this,
                element = $(widget.el);
            var gSession = widget.gSession;
            var timeEl = element.find("[data-date=time]");
        },
        global:{
            pageIndex:1,
            pageSize:5,
            preset : "date"
        },
        _getConds : function(){
            var widget = this,element = $(widget.el);
            var data = element.data("data");
            if(!data || !data.item || !data.item.logisticsId){
                return null;
            }
            var elPost = element.find("#statusCd").find("option:selected");
            var postCode = elPost.attr("value");
            var param = {
                logisticsId :data.item.logisticsId,
                optionalId : data.item.optionalId,
                receiver : element.find("#receiver").val().trim(),
                receiverAddr :element.find("#receiverAddr").val().trim(),
                receiverPhone :element.find("#receiverPhone").val().trim(),
                postCode : element.find("#postCode").val().trim(),
                logisticsNbr : element.find("#logisticsNbr").val().trim(),
                logisticsCompany : element.find("#logisticsCompany").val().trim(),
                statusCd : postCode,
                remark : element.find("#remark").val().trim()
            };
            return param;
        },
        _saveLogisticsInfo: function () {
            var widget = this,
                gSession = widget.gSession,
                element = $(widget.el);
            if(!widget._validate()){
                return false;
            }
            var param = widget._getConds();
            if(!param){
                widget.popup("数据错误！");
                return false;
            }
            widget.callService("saveLogisticsInfo", param, function(res) {
                    var ret = res;
                    if (ret.resultCode == 0) {
                        widget.popup(ret.resultObject.resultMessage, function (){
                            widget._closePage();
                        });
                    } else {
                        widget.popup(ret.resultObject.resultMessage);
                    }
                }, {
                async : true,
                mask : true
            });
        },
        //取消
        _closePage : function() {
            var widget = this,
                element = $(widget.el);
            element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
        },
        _validate : function(){
            var widget = this,element = $(widget.el);
            var receiver = element.find("#receiver").val().trim();
            var receiverPhone = element.find("#receiverPhone").val().trim();
            var receiverAddr = element.find("#receiverAddr").val().trim();
            var postCode = element.find("#postCode").val().trim();
            var logisticsNbr = element.find("#logisticsNbr").val().trim();
            var logisticsCompany = element.find("#logisticsCompany").val().trim();
            var remark = element.find("#remark").val().trim();

            var statusCdEl= element.find("#statusCd").find("option:selected");
            var statusCd = statusCdEl.attr("value");
            var flag = true;
            if(!receiver || receiver.length > 50){
                flag = false;
                widget.popup("收货人不能为空或收货人长度过长")
                return flag;
            }
            if(!receiverPhone){
                flag = false;
                widget.popup("收货人手机号码不能为空")
                return flag;
            }else{
                var contactPhone = /^([1][3,4,5,7,8][0-9]\d{8})$/;
                if(!contactPhone.test(receiverPhone)){
                    widget.popup("无效的收货人手机号码！");
                    flag = false;
                    return flag;
                }
            }
            if(!receiverAddr || receiverAddr.length > 200){
                flag = false;
                widget.popup("收货人地址不能为空或收货人地址长度过长")
                return flag;
            }
            if(!postCode){
                flag = false;
                widget.popup("邮政编码不能为空")
                return flag;
            }else{
                var postRule =  /^[1-9][0-9]{5}$/;
                if(!postRule.test(postCode)){
                    flag = false;
                    widget.popup("邮政编码格式不正确");
                    return flag;
                }
            }

            if(!logisticsNbr || logisticsNbr.length > 40){
                flag = false;
                widget.popup("物流单号不能为空或物流单号长度过长");
                return flag;
            }
            if(!logisticsCompany || logisticsCompany.length > 200){
                flag = false;
                widget.popup("物流公司不能为空或物流公司长度过长");
                return flag;
            }
            if(!statusCd){
                flag = false;
                widget.popup("物流状态不能为空");
                return flag;
            }
            return flag;
        }
    });
    vita.widget.register("logisticsOptionalEditAttach", logisticsOptionalEditAttach, true);
})(window.vita);
