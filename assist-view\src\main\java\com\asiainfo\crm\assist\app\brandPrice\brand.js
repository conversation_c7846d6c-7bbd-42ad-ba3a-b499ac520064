(function(vita){
    var brand = vita.Backbone.BizView.extend({
        events : {
            "click #qryCommonLogSearchBtn" : "_qryBrandList",
            "click #searchDel": "_searchParamsDel",
            "click [name='moduleRadio'] " : "_clickRadio",
            "click [link=brandRel]": "_queryBrandRel",
        },
        _initialize : function() {
            var widget = this,
                element = $(widget.el);
            data = element.data("data");
            widget.model.set(data);
            //初始化查询
            widget._qryBrandList();
        },
        global : {
            pageIndex: 1,
            pageSize: 5, //每页记录数
        },
        _clickRadio : function(e){
            var widget = this,
                element = $(widget.el);
            var radio = $(e.target).closest('input');
            var data = radio.data('data');
            widget.model.set("selectedBrand",data);
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        /**
         * 获取查询条件参数对象
         */
        _getConds : function(pageIndex) {
            var widget = this,
                element = $(widget.el);
            gSession = widget.gSession;
            var param = {
                "pageInfo" : {
                    "pageIndex" : pageIndex,
                    "pageSize" : widget.global.pageSize
                }
            };
            var brandCode = widget.model.get("brandCode");
            if (!widget._isNullOrEmpty(brandCode)) {
                param.brandCode = brandCode;
            }
            var brandName = widget.model.get("brandName");
            if (!widget._isNullOrEmpty(brandName)) {
                param.brandName = brandName;
            }
            return param;
        },
        _isNullOrEmpty: function (condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty;
        },
        /**
         * 清空查询条件
         */
        _searchParamsDel : function() {
            var widget = this,
                element = $(widget.el);
            widget.model.set("brandName","");
            element.find("#brandName").val("");
            widget.model.set("brandCode","");
            element.find("#brandCode").val("");

        },

        _queryBrandRel: function(e){
            var widget = this;
            var aEl = $(e.target).closest("a");
            var brandId = aEl.data("brandId");
            if(!brandId){
                widget.popup("品牌不存在,请重新查询后再试.");
                return;
            }
            var data = {
                aBrandId:brandId
            };
            window.open("../../crm_assist/query/brandRel?aBrandId=" + brandId);
        },


        _qryBrandList : function(e) {
            var widget = this, element = $(widget.el);
            var param = widget._getConds(widget.global.pageIndex);
            element.find('#showPageInfo').empty();
            widget.refreshPart("qryBrand", param, ".table-hover",
                "brands", function(res) {
                    var paging = this.require("paging");
                    var totalNumber = $(res).find("input").eq(1).data("totalNumber");
                    if (parseInt(totalNumber) > 0) {
                        var e = paging.new({
                            recordNumber : widget.global.pageSize,
                            total : totalNumber,
                            pageIndex : widget.global.pageIndex,
                            callback : function(pageIndex){
                                //判断当前页
                                if(pageIndex == null || pageIndex == undefined || isNaN(pageIndex)){
                                    pageIndex = 1;
                                }
                                widget.model.set("pageIndex",pageIndex);
                                widget.refreshPart("qryBrand", widget._getConds(pageIndex), ".table-hover");
                            }
                        });
                        element.find('#showPageInfo').append(e.getElement());
                    }
                }, {
                    mask : true
                });
        },
    });
    vita.widget.register("brand", brand, true);
})(window.vita);
