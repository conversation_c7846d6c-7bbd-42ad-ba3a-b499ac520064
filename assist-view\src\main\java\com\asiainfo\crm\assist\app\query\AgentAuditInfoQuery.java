package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IAgencyCapitalSMO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by wenhy on 2017/7/17.
 */
@Component("vita.agentAuditInfoQuery")
public class AgentAuditInfoQuery extends AbstractComponent {

    private final Logger logger = LoggerFactory.getLogger(AgentAuditInfoQuery.class);

    @Autowired
    private IAgencyCapitalSMO agencyCapitalSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map queryAgentAuditInfo(String json) throws Exception {
        String auditAdjustListInfo = agencyCapitalSMO.queryAuditAdjustListInfo(json);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(auditAdjustListInfo);
        Map<String, Object> options = Maps.newHashMap();
        List auditAdjustInfoList = (List) MapUtils.getObject(resultObjectMap, "auditAdjustInfoList");
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", total);
        options.put("exportNum", MDA.EXPORT_NUM);
        options.put("auditAdjustInfoList", auditAdjustInfoList);
        return options;
    }
}
