<div data-widget="orderCheckBusi">
    <vita-data>{"data":$options}</vita-data>
    <p class="vita-data">$options</p>
    <div class="sypage-nav">
        <div class="nav_alink"><strong class="text16">订单稽核业务管理</strong></div>
    </div>
    <div class="sypage-min">
        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">查询条件</strong></div>
        </div>
        <div class="sypagemin_content ">
            <form>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">稽核业务名称</label>
                    <div class="col-md-8">
                        <input type="text" class="form-control" placeholder="" id="serviceName">
                        <p class="vita-bind" model="serviceName"></p>
                    </div>
                </div>
                <div class="form-group col-md-6">
                    <label class="col-md-4 control-label lablep">状态</label>
                    <div class="col-md-8">
                        <select class="form-control" id="statusCd">
                            <option value="">-请选择-</option>
                            <option value="1000">有效</option>
                            <option value="1100">无效</option>
                        </select>
                        <p class="vita-bind" model="statusCd"></p>
                    </div>
                </div>
                <div class="form-group col-md-12">
                    <div class="col-md-12 searchbutt_r" align="right">
                        <button type="button" class="btn btn-default" id="searchDel">清除</button>
                        <button type="button" class="btn btn-primary" id="SearchBtn">搜索</button>
                    </div>
                </div>

            </form>
        </div>

        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">订单稽核业务配置表</strong></div>
        </div>
        <div class="sypagemin_content ">
            <div class="col-lg-12">
                <a class="btn btn-gray btn-outline" id="addOrderCheckBusi">
                    <i class="glyphicon fa-add text18" > </i> 新增订单稽核业务配置
                </a>
                <a class="btn btn-gray btn-outline"  id="updateOrderCheckBusi">
                    <i class="glyphicon fa-revise text18"> </i> 修改订单稽核业务配置
                </a>
                <a class="btn btn-gray btn-outline" id="deleteOrderCheckBusi">
                    <i class="glyphicon fa-revise text18" > </i> 删除
                </a>
            </div>
            <div class="col-lg-12 mart10">
                <table class="table table-hover" id="systemRoleList">
                    <thead>
                    <tr>
                        <th>选择</th>
                        <th>稽核业务名称</th>
                        <th>稽核点</th>
                        <th>稽核类型</th>
                        <th>规格类型</th>
                        <th>状态</th>
                    </tr>
                    </thead>
                    <tbody>
                    #if($options != "null" && $options.orderCheckBusis && $options.orderCheckBusis != "null" && $options.orderCheckBusis.size() > 0)
                    #foreach($orderCheckBusi in $options.orderCheckBusis)
                    <tr >
                        <td>
                            <label class="wu-radio full absolute" data-scope="">
                                <input type="radio" name="moduleRadio" />
                                <p class="vita-data">{"data":$orderCheckBusi,"totalNumber":$options.totalNumber}</p>
                            </label></td>
                        <td>$!orderCheckBusi.serviceName</td>
                        <td>$!orderCheckBusi.checkSite</td>
                        <td>$!orderCheckBusi.checkType</td>
                        <td>$!orderCheckBusi.specType</td>
                        <td>$!orderCheckBusi.statusCd</td>
                    </tr>
                    #end
                    #end
                    </tbody>
                </table>
                <!--翻页start -->
                <div class="page-box" id="showPageInfo">
                </div>
                <!--翻页end -->
            </div>
        </div>
    </div>
</div>