(function (vita) {
    var configCustOfferService = vita.Backbone.BizView.extend({
        events: {
            "click #btn-queryCust": "_queryCustOfferService",//查询
            "click #regionIdChooseBtn": "_chooseArea",//地区查询
            "click #btn-addCustService": "_addRelationship",//新增
            "click #deleteCustService": "_deleteCustService",//批量删除
            "click #subbutton": "_subExcelButton",//导入EXCEL
            "click #btn-expResults": "_btnExpResults",//导出操作结果
            "click #upLoadTemplet": "_upLoadTemplet"//导出EXCEL模板
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
        },
        global: {
            pageIndex: 1,
            pageSize: 3,
            preset: "date",
            chooseArea: "../comm/chooseArea",
            isChanged : false,
        },
        _subExcelButton:function () {
            var widget = this,element = $(widget.el);
            var gsession = widget.gSession;
            var m_fileVal = $('.m_file').val();
            if(m_fileVal=="") {
                alert('请选择文件！');
                return ;
            }
            if('-1' == m_fileVal.indexOf('.xls')) {
                alert('只能上传Excel文件！');
                return ;
            }
           /* var regionId = $("#regionId").attr("value");
            if (regionId == null || regionId.trim() == ""){
                widget.popup("地区不能为空");
                return;
            }*/
            var params = {
                statusStaff:gsession.staffId, //员工编号
                regionName:$("#regionId").val(),//地区id
                regionId:$("#regionId").attr("value"),//地区名
                typeName:"产品",
                prodId:$("#prodId").val(),//产品规格
            }
            $(".route_input").val(JSON.stringify(params));
            var url ="../action/uploadCustConfigExcels";
            $('#a_form').ajaxSubmit({
                type:"post",url:url, scriptCharset: 'utf-8',
                success:function (date) {
                    var jsonData = JSON.parse(date);
                    if(jsonData.resultObject.resultCode == 0){
                        var dataInfo = jsonData.resultObject.configDataInfoVo
                        localStorage.setItem('batchId',dataInfo.batchId);
                        alert('操作批次号：'+dataInfo.batchId+' 成功'+
                            dataInfo.successSum+'个; 失败'+dataInfo.errorSum+'个');
                    } else if(jsonData.resultObject.resultCode == -1){
                        alert('一次上传不能超过500条');
                    }else {
                        alert('上传失败，请检查内容是否合法');
                    }
                }
            })
        },
        _btnExpResults:function(){
            var widget = this;
            var gsession = widget.gSession;
            var url = "../action/forExcelPrintCustConfg";
            var marking = localStorage.getItem('batchId');
            var params = {
                batchId: marking//唯一标识
            }
            if(!marking){
                widget.popup("请先上传文件再导出结果")
                return;
            }
            debugger;
            location.href=url+"?jsonStr="+marking;
        },

        _chooseArea: function () {
            debugger;
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _queryCustOfferService:function () {
            debugger;
            var widget = this,element = $(widget.el);
            var gsession = widget.gSession;
            var custIdentity = $("#custIdentityNum").val();
            var regionId = $("#regionId").attr("value");
            if($("#custIdentityNum").val() == null || $("#custIdentityNum").val().trim() == "" ){
                widget.popup("客户证件号码不能为空");
                return;
            }
            /*if (regionId == null || regionId.trim() == ""){
                widget.popup("地区不能为空")
                return;
            }*/
            var partyTypeName = $("#partyTypeQry").find("option:selected").text();//证件类型名称
            var partyType=$("#partyTypeQry").val();//证件类型
            if (partyType == null || partyType.trim() == ""){
                widget.popup("客户证件类型不能为空")
                return;
            }
            $("#custIdentity").val(custIdentity);
            $("#partyType").val(partyType);
            //联动设置下面选择框
            $("#partyType option[value=partyType]").prop('selected',true);
            var params = {
                certType:partyType,
                regionId:regionId,
                certNum:custIdentity,
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            }
            widget.refreshPart("configCustProdlnstQuery", JSON.stringify(params), "#configCustOfferServiceResult", function (re) {
                var r = $(re), paging = this.require("paging");
                var e = paging.new({
                    recordNumber : widget.global.pageSize,
                    total : r.find("#showPageInfo").data("totalNumber"),
                    pageIndex : widget.global.pageIndex,
                    callback : function (pageIndex, pageSize) {
                        debugger;
                        var params = {
                            certType:partyType,
                            regionId:regionId,
                            certNum:custIdentity,
                            pageInfo: {
                                pageIndex: pageIndex,
                                pageSize: widget.global.pageSize
                            }
                        };
                        widget.refreshPart("configCustProdlnstQuery", JSON.stringify(params), "#custServLimitCfgs");

                    }
                });
                r.find("#showPageInfo").append(e.getElement());
            }, { async: false });
        },
        _addRelationship: function () {
            var widget = this,element = $(widget.el);
            var gsession = widget.gSession;
            if($("#custIdentity").val() == null || $("#custIdentity").val().trim() == "" ){
                widget.popup("客户证件号码");
                return;
            }
            if($("#remarks").val() == null || $("#remarks").val().trim() == "" ){
                widget.popup("备注不能为空");
                return;
            }
            if($("#partyType").val() == null || $("#remarks").val().trim() == "" ){
                widget.popup("业务动作不能为空");
                return;
            }
            var params = {
                certTypeName:$("#partyType").find("option:selected").text(),//证件类型名称
                certType:$("#partyType").val(),//证件类型
                statusStaff:gsession.staffId, //员工编号
                regionName:$("#regionId").val(),//地区id
                regionId:$("#regionId").attr("value"), //地区名
                remark:$("#remarks").val(), //备注
                certNum:$("#custIdentity").val(),//证件号码
                serviceOfferName:$("#addBoType").val(),//动作名称
                prodId:$("#prodId").val(),//产品规格
                serviceOfferId :$("#addBoType").find("option:selected").attr("prodInstId"),//$("#addBoType").attr("id"),        //动作id
                typeName:"产品",
                state:1,
                typeStatus:"ADD"
            };
            widget.callService("addCustProdlnst", JSON.stringify(params), function(res) {
                if(res.resultCode==0){
                    widget.popup(res.msg);
                    $("#btn-queryCust").click();
                    return;
                }else{
                    widget.popup(res.msg);
                }

            }, {
                async : false
            });
        },
        _deleteCustService:function () {
            var widget = this,element = $(widget.el);
            var gsession = widget.gSession;
            //得到选中的数据ID
            var idArray = "";
            element.find("#configCustOfferServiceResult :checkbox").each(function (i, oi) {
                var o = $(oi);
                if(o.is(":checked")){
                    var custId=o.data("data").custServLimitCfgId;
                    idArray=idArray+custId+",";
                }
            });
            idArray = idArray.substring(0,idArray.length-1);
            var params={
                "ids" : idArray,
                staffId :gsession.staffId
            };

            if (idArray.length == 0){
                widget.popup("请选择需要删除的数据");
                return;
            }
            widget.callService("deleteCustOfferService", params, function(re) {
                widget.popup("删除成功");
                $("#btn-queryCust").click();
            },{ async : false })
        },
        _upLoadTemplet: function () {
            var widget = this;
            var gsession = widget.gSession;
            var url = "../action/upLoadCustConfigTemplet";
            location.href=url;
        }
    });
    vita.widget.register("configCustOfferService", configCustOfferService, true);
 })(window.vita);