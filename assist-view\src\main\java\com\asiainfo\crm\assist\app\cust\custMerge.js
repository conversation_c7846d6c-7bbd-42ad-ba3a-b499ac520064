(function (vita) {
    var custMerge = vita.Backbone.BizView.extend({
        events: {
            "click #queryInfo": "_queryInfo",//点击查询
            "click .identitys": "_clickIdentity",//改变证据类行
            //"change #custType": "_setPartyType",//改变客户类型
            "click input[name='mudi']": "_operationVerify",//验证未竣工销售品订单、未竣工产品订单
            "click input[name='yuan']": "_operationVerifYuan",
            "click #perserve": "_commitOrder",
            // "click #submit":"_commitOrder",
            "click #resetBtn": "_resetOrder",
            "click #mergeInfo": "_mergeInfo",
            "click #printBtn": "_printBtn"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            element.find("#qryCustomerList").html("");
            var data = element.data("data");
            widget.global.groupYuanSwitch = data.groupYuanSwitch;
        },
        _resetOrder: function () {
            var widget = this, element = $(widget.el);
            element.find("#qryCustomerList").html("");
            element.find("#identityNum").val("");
        },
        global: {
            pageIndex: 1,
            pageSize: 1000, //每页记录数
            groupYuanSwitch: "N"
        },
        _getConds: function () {
            var widget = this, element = $(widget.el);
            var identiType = widget.model.get("identiType");
            var identityNum = $("#identityNum").val();
            var yuanAccNum = $("#yuanAccNum").val();
            var mudiAccNum = $("#mudiAccNum").val();
            var accNums = new Array();
            if (yuanAccNum != undefined && yuanAccNum != null && !yuanAccNum == "" && mudiAccNum != undefined && mudiAccNum != null && !mudiAccNum == "") {
                var accNums = [mudiAccNum, yuanAccNum];
            }
            if (identiType == "" || identiType == null || identiType == undefined) {
                widget.popup("请选择证件类型");
                return
            } else if (identityNum == "" || identityNum == null || identityNum == undefined) {
                widget.popup("输入证件号");
                return
            }

            var param = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                },
                certNum: identityNum,
                certType: identiType,
                accNums: accNums,
                // queryType: "identity",
                // value: identityNum,
                regionId: widget.gSession.staffLanId,
                dgRegionIdFlag: '1',
                custMergeFlag: true,
                glbSessionId: widget.gSession.glbSessionId
            };
            return param;
        },
        _queryInfo: function () {
            var widget = this;
            var param = widget._getConds();
            widget.model.set("isPrint", false);
            widget.model.set("skipReceipt", false);
            if (param == "" || param == null || param == undefined) {
                return;
            }
            widget.refreshPart("qryCustomerList", JSON.stringify(param), "#qryCustomerList", function (res) {
                var data = $(res).data();
                if (!data.success) {
                    widget.popup(data.msg ? data.msg : "查询客户信息异常！");
                    return false;
                }
                // var paging = widget.require("paging"),r = $(res);
                // var totalNumber = r.find("#showPageInfo").data("totalNumber") || 0;
                // var e = paging.new({
                //     recordNumber : widget.global.pageSize,
                //     total : totalNumber,
                //     pageIndex : widget.global.pageIndex,
                //     callback : function (_pageIndex, _recordNumber) {
                //         param.pageInfo.pageIndex = _pageIndex;
                //         param.pageInfo.pageSize = _recordNumber;
                //         widget.refreshPart("qryCustomerList", JSON.stringify(param),"#qryCustomertbody",{
                //             async: true,
                //             mask: true
                //         });
                //     }
                // });
                // r.find("#showPageInfo").append(e.getElement());
            }, {
                async: true,
                mask: true
            });
        },
        _clickIdentity: function (e) {
            var widget = this;
            var tarLi = $(e.target);
            var buttonId = $(e.target).closest("ul").prev("button").attr("id");
            var data = tarLi.closest("li").data("data");
            $("#" + buttonId).html(tarLi.html());
            widget.model.set("identiType", data);//放入选中的证件类型
        },
        _operationVerify: function (e) {
            var widget = this;
            var custId = e.target.value;
            var param = {
                "custId": custId,
            };
            $(e.target).closest('td').find("input[name='yuan']").prop("checked", false);
            // 目的客户检测是否合规
            var soConst = widget.require("soConst");
            var checkSwitch = soConst.getMda("COMP_CHECK_SWITCH");
            if(checkSwitch && checkSwitch == 'Y'){
                var params = {
                    "custId":custId
                }
                widget.callService("getCustDetialInfo", params, function (res) {
                    if(res && res.customerDetail){
                        if(res.customerDetail.partyType == '2' && (res.customerDetail.partyNbr == "" || res.customerDetail.partyNbr == undefined)){
                            //未合规
                            widget.popup("您所选择的目的客户不合规，请先到客户资料修改处完成组织客户合规性校验，再操作即可！");
                            $(e.target).closest('td').find("input[name='mudi']").prop("checked", false);
                            return false;
                        }
                    }
                }, {
                    mask: true
                });
            }
            /* 目的客户不检测未竣工
            widget.callService("operationVerify", param, function (res) {
                 var number = res.resultObject;
                 if(number!=0){
                     $(e.target).prop("checked",false);
                     widget.popup("你选择的客户存在未竣工业务不能客户合并");
                 }
             })*/
        },
        _operationVerifYuan: function (e) {
            var widget = this;
            var custId = e.target.value;
            var param = {
                "custId": custId,
            };
            $(e.target).closest('td').find("input[name='mudi']").prop("checked", false);
            widget.callService("operationVerify", param, function (res) {
                var number = res.resultObject;
                if (number != 0) {
                    $(e.target).prop("checked", false);
                    widget.popup("你选择的客户存在未竣工业务不能客户合并");
                }
            })

            // 失信人信息校验
//            widget.callService("isDishonestPerson", param, function (res) {
//                if (res.isDishonestPerson == "Y") {
//                    $(e.target).prop("checked", false);
//                    widget.popup("你选择的客户下存在失信的号码不能作为源客户");
//                }
//            });
            widget.global.custMomValue = "";

        },
        _getAccProdDatas: function () {//获取原客户的信息
            var accProdDatas = new Array();
            // $("input[name='yuan']:checked").each(function(){//把所有被选中的复选框的值存入数组
            //     var td = this.closest("td");
            //     var mudi = $(td).find("input[name='mudi']").prop('checked');//去除原客户中的目的客户
            //     if(mudi==false){
            //         var data = $(td).data("data");
            //         accProdDatas.push(data);
            //     }
            // });
            var td = $("input[name='yuan']:checked").closest("td");
            var data = $(td).data("data");
            accProdDatas.push(data);
            return accProdDatas;
        },
        _getChoosedInfo: function () {//拼接产品信息
            var widget = this;
            var soConst = this.require("soConst");
            var accProdShape = new Array();
            var accProdDatas = widget._getAccProdDatas();
            //console.log(accProdDatas);
            accProdDatas.forEach(function (e, i) {
                var prodInstIds = e.prodInstIds;
                var prodIds = e.prodIds;
                var custId = e.custId;
                if (prodInstIds != undefined) {
                    prodInstIds.forEach(function (e, i) {
                        accProdShape.push({
                            prodInstId: e,
                            prodId: prodIds[i],
                            custId: custId,
                            serviceOfferId: soConst.get("SERVICE_CHANGE_CUST")
                        })
                    })
                }

            })
            return accProdShape;
        },
        _getYuanCusTs: function () {
            var widget = this;
            var yuanCusTs = new Array();
            widget._getAccProdDatas().forEach(function (e, i) {
                yuanCusTs.push(e.custId);
            })
            return yuanCusTs;
        },
        _submitOrder: function () {

            var widget = this;
            var mudiCustId = $("input[name='mudi']:checked").val();
            if (mudiCustId == undefined || mudiCustId == null || mudiCustId == "") {
                widget.popup("必须选择目的客户");
                return;
            }
            cust = {
                custId: mudiCustId,
                glbSessionId: widget.gSession.glbSessionId
            };
            // console.log("++++++++++++");
            // console.log(cust);
            widget.callService("saveInitCustToken", cust, function (res) {
                // console.log(res);
                // console.log("++++++++++++");
                if (res.custTokenId != null) {
                    widget.model.set("custTokenId", res.custTokenId);
                    widget._submitOrderAfter();
                } else {
                    widget.popup("获取custTokenId失败");
                }
                return;
            });
        },

        _submitOrderAfter: function () {

            var widget = this;

            var handlerObj = $(widget.el).find("[data-widget=handler]");
            var handler = handlerObj.handler("getValue");

            var widget = this;

            var checkID = [];//定义一个空数组
            var gSession = widget.gSession;
            var custIds = widget._getYuanCusTs();
            var data = {
                "accProdDatas": widget._getChoosedInfo(),
                "custIds": custIds,
                "custId": $("input[name='mudi']:checked").val(),
                "custTokenId": widget.model.get("custTokenId"),
                "sceneCode": "custMerge",
                "acceptRegionId": gSession.staffRegionId,
                "acceptLanId": gSession.staffLanId,
                "createOrgId": gSession.curChannelId,
                "createOrgName": gSession.curChannelName,
                "glbSessionId": gSession.glbSessionId,
                "createStaff": gSession.staffName,
                "createStaffName": gSession.staffName,
                "orderSource": "1000",//CRM 默认入口
                "staffId": gSession.staffId,
                "staffPostId": gSession.sysPostId,
                "isCommitOrder": "0",
                "certType": widget._getConds().certType,
                "certNum": widget._getConds().certNum,
                "commonData": {
                    "handler": handler.handler,//经办人
                    "handlerCertType": handler.handlerCertType,//经办人证件类型
                    "handlerCertNum": handler.handlerCertNum,//经办人证件号码
                    "handlerPhone": handler.handlerPhone,//经办人联系电话
                    "devStaffId": gSession.staffId,//员工ID
                    "devStaffName": gSession.staffName,//员工姓名
                    "devStaffNumber": gSession.staffCode,//员工编码
                    "remark": handler.remark//备注
                },
                "orderAttrs": [{
                    "attrId": "5002",
                    "attrValueId": "5002",
                    "value": $("input[name='mudi']:checked").val()
                }, {
                    "attrId": "5003",
                    "attrValueId": "5003",
                    "value": custIds[0] + ""
                }],
            };
            // console.log("_submitOrderAfter+++++++++++++");
            // console.log(data);
            widget.callService("saveSceneData", data, function (re) {
                var checkResult = true;
                var reList = re.reList;
                var rulevo = re.ruleChainRespVo;
                var ruleMsg = "";
                if (rulevo && rulevo.rules && rulevo.rules.length > 0) {
                    $.each(rulevo.rules, function (i, rule) {
                        if (rule.ruleResult != "0000") {
                            ruleMsg = rule.ruleMsg;
                            checkResult = false;
                            return false;
                        }
                    });
                }
                if (!checkResult) {
                    widget.popup(ruleMsg);
                    return false;
                } else {
                    if (reList[0].customerOrderRspVo) {
                        widget.model.set({
                            "isPrint": true,
                            "custOrder": reList,
                            "custOrderNbr": reList[0].customerOrderRspVo.custOrderNbr,
                            "custOrderId": reList[0].customerOrderRspVo.custOrderId
                        });
                        //跳转免填单打印
                        // console.info(re.customerOrderRspVo)
                        var skipReceipt = widget.model.get("skipReceipt");
                        if (skipReceipt) {
                            widget._commitOrder();
                        } else {
                            var url = "/crm/so/receipt?custOrderNbr=" + reList[0].customerOrderRspVo.custOrderNbr;
                            window.open(url);
                        }

                    } else {
                        widget.popup("受理失败...");
                        return false;
                    }
                }

            }, {mask: true, async: true});
        },
        // 合并信息
        _mergeInfo: function () {
            var widget = this;
            var arr = [];
            var table = $("#table");
            //var checkBox = $("table input[type=radio]:checked");
            var checkBox = $("input[name='yuan']:checked");
            checkBox.each(function () {//遍历
                var row = $(this).closest("td").data("data");
                row.optioonType = "源客户"
                arr.push(row);
            });
            //var radio = $("table input[type=radio]:checked");
            var radio = $("input[name='mudi']:checked");
            radio.each(function () {
                var row = $(this).closest("td").data("data");
                row.optioonType = "目的客户"
                arr.push(row);
            });
            var param = {
                "custMergeInfo": JSON.stringify(arr)
            };
            // console.log(param);
            var option = {
                url: '../query/custMergeInfo',
                params: param,
                onClose: function (res) {

                }
            };
            widget.dialog(option);
        },

        _printBtn: function () {
            var widget = this, element = $(widget.el);
            var mudiCustId = $("input[name='mudi']:checked").val();
            if (mudiCustId == undefined || mudiCustId == null || mudiCustId == "") {
                widget.popup("没有选择目的客户");
                return;
            }
            var yuanCustId = $("input[name='yuan']:checked").val();
            if (yuanCustId == undefined || yuanCustId == null || yuanCustId == "") {
                widget.popup("没有选择源的客户");
                return;
            }
            var ext2CustFlag = false, ext2Id;
            var ext1CustFlag = false, ext1Id;
            var accProdDatas = widget._getAccProdDatas();
            $.each(accProdDatas, function (index, e) {
                var ext2CustId = e.ext2CustId;
                var ext1CustId = e.ext1CustId;
                if (ext2CustId != undefined && ext2CustId != null && !ext2CustId == "") {
                    ext2CustFlag = true;
                    ext2Id = e.custId;
                }
                if (ext1CustId != undefined && ext1CustId != null && !ext1CustId == "" && ext1CustId != -1) {
                    ext1CustFlag = true;
                    ext1Id = e.custId;
                }
            })
            if (widget.global.groupYuanSwitch != "Y") {
                if (ext2CustFlag == true) {
                    widget.popup("原客户中存在卫星客户," + "客户id:" + ext2Id);
                    return;
                }
                if (ext1CustFlag == true) {
                    widget.popup("客户id:" + ext1Id + "是集团政企CRM下发客户，只能作为目的客户");
                    return;
                }
            }
            widget._submitOrder();
        },

        _commitOrder: function () {

            var widget = this, element = $(widget.el), gSession = widget.gSession;
            var status = widget._getAuthSkipReceipt();
            //1 没打免填单
            if (!widget.model.get("isPrint")) {
                if (status) {//跳过打印
                    //在 场景处理器回调中 再次调用_commitOrder方法 提交
                    widget.model.set("skipReceipt", true);
                    widget._printBtn();
                    return;
                } else {
                    widget.popup("您没有打印受理单，是否打印受理单？<br>如果不打印受理单，不可以办理该业务");
                    return;
                }
            }
            if (status || widget._getReceiptStatus()) {

                var widget = this, element = $(widget.el), gSession = widget.gSession;
                var custOrder = widget.model.get("custOrder");
                for (var i = 0; i < custOrder.length; i++) {
                    var param = {
                        custOrderId: custOrder[i].customerOrderRspVo.custOrderId,
                        custOrderNbr: custOrder[i].customerOrderRspVo.custOrderNbr,
                        custTokenId: widget.model.get("custTokenId"),
                        glbSessionId: gSession.glbSessionId
                    };
                    // console.log("_commitOrder+++++++++++++");
                    // console.log(param);
                    widget.callService("commitOrder", JSON.stringify(param), function (re) {
                        if (re && re.resultCode == 0) {
                            widget.popup("受理成功,订单号:" + re.custOrderNbr);
                            widget._resetOrder();
                        } else {
                            widget.popup("受理失败");
                        }
                    }, {mask: true})
                }
            } else {
                widget.popup("打印回执状态异常,请重试");
            }
        },
        //获取跳过打印回执权限
        _getAuthSkipReceipt: function () {
            var widget = this, gSession = widget.gSession;
            var param = {
                "staffId": gSession.staffId
            }
            var status = false;
            widget.callService("getAuthSkipReceipt", param, function (res) {
                if (res.success) {
                    status = res.status == 1;
                } else {
                    widget.popup(res.msg);
                }

            }, {async: false});
            return status;
        },
        //获取回执状态
        _getReceiptStatus: function () {
            var widget = this, gSession = widget.gSession;
            var param = {
                custOrderNbr: widget.model.get("custOrderNbr"),
                glbSessionId: gSession.glbSessionId
            };
            var status = false;
            widget.callService("getReceiptStatus", param, function (res) {
                if (res.success) {
                    status = res.status == 1;
                } else {
                    widget.popup(res.msg);
                }

            }, {async: false});
            return status;
        },
        _isMoon: function () {//验证是否是卫星客户
            $("input[name='yuan']:checked").each(function () {//把所有被选中的复选框的值存入数组
                var td = this.closest("td");
                var mudi = $(td).find("input[name='mudi']").prop('checked');//去除原客户中的目的客户
                if (mudi == false) {
                    var data = $(td).data("data");
                    accProdDatas.push(data);
                }
            });
        }
    })
    vita.widget.register("custMerge", custMerge, true);
})(window.vita);