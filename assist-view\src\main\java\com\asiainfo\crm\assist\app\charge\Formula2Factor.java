package com.asiainfo.crm.assist.app.charge;


import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;

import com.asiainfo.crm.service.intf.ISpecChargeSmo;

import com.asiainfo.crm.util.ListUtil;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("vita.formula2Factor")
public class Formula2Factor extends AbstractComponent {

    @Autowired
    private ISpecChargeSmo chargeSmo;

    @Override
    public Map achieveData(Object... objects) throws Exception {
        return null;
    }

    public Map qryFormula2FactorInfo(String params) throws Exception {
        Map<String, Object> options = new HashMap();
        String queryInfo = chargeSmo.queryFormula2FactorPage(params);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(queryInfo);
        Map<String,Object> pageInfo = (Map<String, Object>) MapUtils.getObject(resultObjectMap, "pageInfo");
        List<Map> listData = (List) MapUtils.getObject(resultObjectMap, "listData");
        for (Map map : ListUtil.nvlList(listData)) {
            map.put("statusCd", AssistMDA.COMMON_STATUS_CD_MAP.get(MapUtils.getString(map,"statusCd")));
            if (null!=MapUtils.getString(map,"factorRole")){
                map.put("factorRoleName", MapUtils.getString(map,"factorRole").equals("1")?"算费因子":"条件因子");
            }
        }
        resultObjectMap.clear();
        resultObjectMap.put("reqList", listData);
        resultObjectMap.put("totalNumber", pageInfo.get("rowCount"));
        return resultObjectMap;
    }

    public Map createFormula2Factor(String params) throws Exception {
        String insertResult = chargeSmo.createFormula2Factor(params);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(insertResult);
        resultObjectMap.put("resultCode",MapUtils.getString(resultObjectMap, "resultCode"));
        resultObjectMap.put("resultMsg",MapUtils.getString(resultObjectMap, "resultMsg"));
        return resultObjectMap;
    }

    public Map updateFormula2Factor(String params) throws Exception {
        String updateResult = chargeSmo.updateFormula2Factor(params);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(updateResult);
        resultObjectMap.put("resultCode",MapUtils.getString(resultObjectMap, "resultCode"));
        resultObjectMap.put("resultMsg",MapUtils.getString(resultObjectMap, "resultMsg"));
        return resultObjectMap;
    }



}
