package com.asiainfo.crm.assist.app.query;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOpticalCellQuerySMO;

/**
 * Created by cheny on 2018/1/25
 */
@Component("vita.opticalCellQuery")
public class OpticalCellQuery extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(OpticalCellQuery.class);

    @Autowired
    private IOpticalCellQuerySMO opticalCellQuerySMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map opticalCellQuery(String str) throws IOException, DocumentException{
	  Map<String, Object> options = new HashMap<String, Object>();
	  StringBuffer xml = new StringBuffer();
	  StringBuffer requestXml = new StringBuffer();
	  Map map = jsonConverter.toBean(str, Map.class);
	  String regionIdStr = map.get("regionId").toString();
	  String villageName = map.get("villageName").toString();
	  String oldAreaId = AssistMDA.regionId.get(String.valueOf(regionIdStr));
	  xml.append("").append("<querydata>").append("<name>").append(villageName).append("</name>").append("<areaid>")
		.append(oldAreaId).append("</areaid>").append("</querydata>");
	  requestXml.append("<request><arg0>").append(oldAreaId).append("</arg0><arg1><![CDATA[ ").append(xml).append("]]></arg1></request>");
	  String outXml = opticalCellQuerySMO.getLanInfoByLanName(requestXml.toString());
	  Document document = DocumentHelper.parseText(outXml);
	  Element root = document.getRootElement();
	  StringBuffer contentView = new StringBuffer();
	  List list = new ArrayList<Object>();
	  this.getNodes(root, contentView,list);
	  options.put("list", list);
	  return options;
  }

    public void getNodes(Element node, StringBuffer contentView,List list) {
    	String nodeName = node.getName();
		String nodeText = node.getTextTrim();
    	if("resultflag".equals(nodeName)  ){
    		if (!"成功".equals(nodeText)) {
				return ;
			}
    	}else if("laninfos".equals(nodeName)){
    		// 当前节点的所有属性的list 
    		List<Element> listelement = node.elements();
    		for (Element attr : listelement) {
    			List<Element> listelements = attr.elements();
    			Map map = new HashMap();
    			for(Element attrs : listelements){
					// 属性名称
					String attrName = attrs.getName();
					// 属性的值
					String attrValue = attrs.getTextTrim();
					map.put(attrName, attrValue);
					//System.out.println(attrName+","+attrValue);
    			}
    			list.add(map);
			}
    	}
    	
    	// 递归遍历当前节点所有的子节点
		List<Element> listElement = node.elements();
		// 遍历所有一级子节点
		for (Element e : listElement) {
			// 递归
			this.getNodes(e, contentView,list);
		}
    }
}
