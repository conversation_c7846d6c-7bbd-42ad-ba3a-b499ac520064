package com.asiainfo.crm.assist.app.orgCust;

import com.al.common.utils.StringUtil;
import com.alibaba.fastjson.JSON;
import com.asiainfo.angel.json.JsonConverter;
import com.asiainfo.crm.assist.common.JsonUtils;
import com.asiainfo.crm.assist.proxy.IBatchUpdateDevStaffServiceProxy;
import com.asiainfo.crm.bcomm.exception.BError;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IImportBatchOrgMultiSmo;
import com.asiainfo.crm.service.intf.IStaffDataPrvQuerySMO;
import com.asiainfo.crm.service.intf.IStaffInfoQuerySMO;
import com.asiainfo.crm.service.intf.IStaffOrgAreaSMO;
import com.asiainfo.crm.util.Des33;
import com.google.common.collect.Maps;
import net.sf.json.JSONObject;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 关联合作渠道审核
 *
 * <AUTHOR>
 */
@Component("vita.orgCustApproval")
public class OrgCustApproval extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(OrgCustApproval.class);

    @Autowired
    private IImportBatchOrgMultiSmo importBatchOrgMultiSmo;
    @Autowired
    private IBatchUpdateDevStaffServiceProxy batchUpdateDevStaffServiceProxy;
    @Autowired
    private IStaffDataPrvQuerySMO staffDataPrvQuerySMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map<String, Object> queryOrgByObj(String jsonStr) {
        Map<String, Object> options = Maps.newHashMap();
        String resultJson = "";
        try {
            Map<String, Object> map = JsonUtils.toObject(jsonStr, Map.class);
            String staffId = MapUtils.getString(map,"staffId");
            String curStaffOrgId = this.qryStaffDetail(staffId);
            if (curStaffOrgId == null || "0".equals(curStaffOrgId)) {
                return null;
            }
            String secondOrgId = batchUpdateDevStaffServiceProxy.qryBjSecondDep(curStaffOrgId);
            map.put("importStaffSecondOrgId", secondOrgId);
            map.put("importSatfCode", map.get("staffCode"));
            map.put("beginDate", strToDate(String.valueOf(map.get("beginDate")), "yyyyMMdd"));
            map.put("endDate", strToDate(String.valueOf(map.get("endDate")), "yyyyMMdd"));
            map.put("auditStatus",map.get("auditStatus"));
            resultJson = importBatchOrgMultiSmo.queryOrgByObj(JsonUtils.toJson(map));
            if (resultJson.isEmpty()) {
                options.put("orgVoList", null);
                return options;
            }
            Map<String, Object> resultObject = (Map<String, Object>) resolveResult(resultJson);
            String resultCode = MapUtils.getString(resultObject,"resultCode","1");
            if("0".equals(resultCode)){
                List list = (List) MapUtils.getObject(resultObject,"orgVoList");
                options.put("orgVoList",list);
            }
            Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObject,"pageInfo");
            int rowCount = MapUtils.getIntValue(pageInfoMap, "rowCount");
            int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
            int pageSize = MapUtils.getIntValue(pageInfoMap, "pageSize");

            options.put("pageCount",pageIndex);
            options.put("pageIndex",pageSize);
            options.put("totalNumber", rowCount);
            return options;
        } catch (Exception e) {
            options.put("retCode", "-1");
            if (e instanceof BError) {
                BError bError = (BError) e;
                options.put("retDesc", bError.getMsg());
            } else {
                options.put("retDesc", e.getMessage());
            }
            return options;
        }

    }

    public Map<String, Object> updateApprovalOptions(String json) {
        Map<String, Object> resultObject = new HashMap<>();
        try {
            String result = importBatchOrgMultiSmo.updateApprovalOptions(json);
            resultObject = (Map<String, Object>) resolveResult(result);
            return resultObject;
        } catch (Exception e) {
            resultObject.put("retCode", "-1");
            if (e instanceof BError) {
                BError bError = (BError) e;
                resultObject.put("retDesc", bError.getMsg());
            } else {
                resultObject.put("retDesc", e.getMessage());
            }
        }
        return resultObject;
    }
    /**
     * 将时间格式串
     * @param strDate
     * @param formate
     * @return
     */
    public Date strToDate(String strDate, String formate){
        SimpleDateFormat formatter = new SimpleDateFormat(formate);
        ParsePosition pos = new ParsePosition(0);
        Date strtodate = formatter.parse(strDate, pos);
        return strtodate;
    }
    public String qryStaffDetail(String staffId) throws Exception {
        Map jsonStr = new HashMap();
        jsonStr.put("staffId", staffId);
        String retStr = staffDataPrvQuerySMO.qryStaffDetail(jsonConverter.toJson(jsonStr));
        Map<String, List> resultObject = (Map) resolveResult(retStr);
        Map staffDetail = (Map)resultObject.get("staffDetail");
        return MapUtils.getString(staffDetail,"orgId");
    }
}
