(function (vita) {
    var sesameCreditQuery = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_querySesameCredit"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
        },
        _querySesameCredit: function () {
            var widget = this;
            var gsession = widget.gSession;
            var certNum = $.trim($("#certNum").val());
            if (certNum == "") {
                widget.popup("客户身份证号码不能为空！");
                return false;
            }
            var params = {
                certNo: $("#certNum").val(),
                staffId: gsession.staffId,
                regionId : gsession.staffRegionId
            };
            widget.refreshPart("querySesameCredit", JSON.stringify(params), "#sesameCreditResult", function (re) {
                }, { async: false });
        }
    });
    vita.widget.register("sesameCreditQuery", sesameCreditQuery, true);
})(window.vita);