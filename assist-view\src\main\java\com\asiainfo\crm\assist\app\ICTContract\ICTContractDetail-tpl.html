<div data-widget="ICTContractDetail" class="calcw_rightbox noneleft">
    <p class="vita-data">{
        "data": #if($options) $options #else "" #end,
        "params":#if($options.params) $options.params #else "" #end

        }</p>
    <div class="calctitle">
        <div class="titlefont"><i class="bot"></i><strong class="text16">ICT合同详情</strong></div>
        <div class="toolr">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close" id='btnClose'><span aria-hidden="true">×</span></button>
        </div>
    </div>
    <div class="calcw_rightbox noneleft">
        <div class="ibox">
            <div class="pad_tabtitle">
                <ul id="myTab" class="nav nav-tabs">
                    <li class="active" id="tab1"><a href="javascript:void(0)" data-toggle="tab" aria-expanded="true">项目信息</a></li>
                    <li  id="tab2"><a href="javascript:void(0);" data-toggle="tab" aria-expanded="false">合同信息</a></li>
                    <li  id="tab3"><a href="javascript:void(0);" data-toggle="tab" aria-expanded="false">子项目信息</a></li>

                </ul>
            </div>
        </div>
    <div class="calcw_rightcont" style="padding-bottom: 0px">
        <div id="myTabContent" class="tab-content height100" >
            <div id="content1" class="tab-pane fade in active">
                <div class=" mincontent">
                    <div class="col-md-12">
                        <div class="show-grid">
                            <ul>
                                #if($options != "null" && $options.ICTProjectDeail && $options.ICTProjectDeail != "null" )
                                    #set($projectDetail = $options.ICTProjectDeail)
                                    #set($contractDetail = $options.ICTProjectDeail.ictContract)
                                    #if($projectDetail && $projectDetail.ictItemCode && $projectDetail.ictItemCode !="null")
                                        <li class="col-xs-6 col-sm-2 labellcont">项目编码</li>
                                        <li class="col-xs-6 col-sm-4">$!projectDetail.ictItemCode</li>
                                    #end
                                    #if($projectDetail && $projectDetail.ictItemName && $projectDetail.ictItemName !="null")
                                        <li class="col-xs-6 col-sm-2 labellcont">项目名称</li>
                                        <li class="col-xs-6 col-sm-4">$!projectDetail.ictItemName</li>
                                    #end
                                    #if($projectDetail && $projectDetail.contractCode && $projectDetail.contractCode !="null")
                                        <li class="col-xs-6 col-sm-2 labellcont">合同编号</li>
                                        <li class="col-xs-6 col-sm-4">$!projectDetail.contractCode</li>
                                    #end
                                    #if($contractDetail && $contractDetail.contractName && $contractDetail.contractName !="null")
                                        <li class="col-xs-6 col-sm-2 labellcont">合同名称</li>
                                        <li class="col-xs-6 col-sm-4">$!contractDetail.contractName</li>
                                    #end
                                    #if($contractDetail && $contractDetail.createDate && $contractDetail.createDate !="null")
                                        <li class="col-xs-6 col-sm-2 labellcont">发起时间</li>
                                        <li class="col-xs-6 col-sm-4">$!contractDetail.createDate</li>
                                    #end
                                    #if($contractDetail && $contractDetail.tradeSum && $contractDetail.tradeSum !="null")
                                        <li class="col-xs-6 col-sm-2 labellcont">合同金额</li>
                                        <li class="col-xs-6 col-sm-4">$!contractDetail.tradeSum</li>
                                    #end
                                    #if($projectDetail && $projectDetail.clientCode && $projectDetail.clientCode !="null")
                                        <li class="col-xs-6 col-sm-2 labellcont">客户编码</li>
                                        <li class="col-xs-6 col-sm-4">$!projectDetail.clientCode</li>
                                    #end
                                    #if($projectDetail && $projectDetail.clientName && $projectDetail.clientName !="null")
                                        <li class="col-xs-6 col-sm-2 labellcont">客户名称</li>
                                        <li class="col-xs-6 col-sm-4">$!projectDetail.clientName</li>
                                    #end
                                    #if($projectDetail && $projectDetail.clientManager && $projectDetail.clientManager !="null")
                                        <li class="col-xs-6 col-sm-2 labellcont">客户经理</li>
                                        <li class="col-xs-6 col-sm-4">$!projectDetail.clientManager</li>
                                    #end
                                    #if($projectDetail && $projectDetail.clientManagerDept && $projectDetail.clientManagerDept !="null")
                                        <li class="col-xs-6 col-sm-2 labellcont">发起部门</li>
                                        <li class="col-xs-6 col-sm-4">$!projectDetail.clientManagerDept</li>
                                    #end
                                #end
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div id="content2" class="tab-pane fade" style="display: none">
                <div class="mincontent ">
                    <div class="col-md-12">
                        <div class="show-grid">
                            <ul>
                                #if($options != "null" && $options.ICTContractDeail && $options.ICTContractDeail != "null" )
                                    #set($contractDetail = $options.ICTContractDeail)
                                    #if($contractDetail && $contractDetail.contractCode && $contractDetail.contractCode !="null")
                                        <li class="col-xs-6 col-sm-2 labellcont">合同编号</li>
                                        <li class="col-xs-6 col-sm-4">$!contractDetail.contractCode</li>
                                    #end
                                    #if($contractDetail && $contractDetail.contractName && $contractDetail.contractName !="null")
                                        <li class="col-xs-6 col-sm-2 labellcont">合同名称</li>
                                        <li class="col-xs-6 col-sm-4">$!contractDetail.contractName</li>
                                    #end
                                    #if($contractDetail && $contractDetail.contractType && $contractDetail.contractType !="null")
                                        <li class="col-xs-6 col-sm-2 labellcont">合同类型</li>
                                        <li class="col-xs-6 col-sm-4">$!contractDetail.contractType</li>
                                    #end
                                    #if($contractDetail && $contractDetail.contractStartCode && $contractDetail.contractStartCode !="null")
                                        <li class="col-xs-6 col-sm-2 labellcont">合同发起号</li>
                                        <li class="col-xs-6 col-sm-4">$!contractDetail.contractStartCode</li>
                                    #end
                                    #if($contractDetail && $contractDetail.tradeSum && $contractDetail.tradeSum !="null")
                                        <li class="col-xs-6 col-sm-2 labellcont">合同金额</li>
                                        <li class="col-xs-6 col-sm-4">$!contractDetail.tradeSum</li>
                                    #end
                                #end
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div id="content3" class="tab-pane fade" style="display: none">
                <div class="mincontent ">
                    <table class="table table-hover" >
                        <thead>
                        <tr>
                            <th>子项目编码</th>
                            <th>子项目名称</th>
                            <th>利润中心</th>
                            <th>地区</th>
                            <th>生单状态</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody >
                                #if($options != "null" && $options.ICTSubProjectDeail && $options.ICTSubProjectDeail != "null" && $options.ICTSubProjectDeail.size() > 0)
                                #foreach($contract in $options.ICTSubProjectDeail)
                                <tr >
                                <td>
                                    $!contract.subItemCode
                                    <p class="vita-data">{"totalNumber" : "$!options.totalNumber"}</p>
                                </td>
                                <td>
                                    $!contract.subItemName
                                </td>
                                <td>
                                    $!contract.profitcenter
                                </td>
                                <td>
                                    $!contract.regionName
                                </td>
                                <td>
                                    $!contract.statusName
                                </td>
                                <td>
                                    <span class="orderDetail"><a  href="javascript:void(0);">订单详情</a><p class="vita-data">{"data" : $!contract}</p></span>
                                    <label>&nbsp&nbsp&nbsp&nbsp</label>
                                    <span class="orderPlan"><a  href="javascript:void(0);">收入计划</a><p class="vita-data">{"data" : $!contract}</p></span>
                                </td>
                                </tr>
                            #end
                            #end
                        </tbody>
                    </table>
                    <div class="page-box" id="showPageInfo">
                    </div>
                </div>
            </div>

        </div>
    </div>
    </div>
</div>