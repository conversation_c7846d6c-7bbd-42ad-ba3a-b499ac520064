<div data-widget="creditFJ" style="height:100%">
    <div class="pace-done wrapper mini-rightmax no-minright">
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">号码</label>
                                            <div class="col-md-7">
                                                <input id="accNum" type="text" class="form-control" placeholder="">
                                                <input id="channelId" type="hidden" class="form-control" placeholder="" readonly="readonly">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <div class="col-md-12 searchbutt_r" align="center">
                                                <button id="btn-query" type="button" class="btn btn-primary">查询</button>
                                            </div>
                                        </div>
                                    </form>
                                    <p class="vita-data">{"data":$options}</p>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12">
                                        <a id="recoverConnect" class="btn btn-primary"> 复机  </a>
                                        #if($options.ifCredit && $options.ifCredit =="Y")
                                        <span class="textcolorred">通过权益进行紧急开机，信用度与权益合并后，单月只能使用<strong>一次</strong>授信紧急复机</span>
                                        #end
                                    </div>
                                    <div class="col-lg-12 mart10" id="userListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>选择</th>
                                                <th>地区</th>
                                                <th>客户名</th>
                                                <th>号码</th>
                                                <th>产品规格</th>
                                                #if($options.ifCredit && $options.ifCredit =="Y")
                                                <th>信用度</th>
                                                #end
                                                <th>星级</th>
                                                <th>用户状态</th>
                                            </tr>
                                            </thead>
                                            <tbody id="userList">
                                            #if($options && $options.size() >0 && $options.accNum)
                                            <tr>
                                                <td><label class="wu-radio full absolute" data-scope="">
                                                    <input type="radio" name="userInfo">
                                                        <p class="vita-data">{"data":$options}</p>
                                                    </label>
                                                </td>
                                                <td>$!{options.areaName}</td>
                                                <td>$!{options.custName}</td>
                                                <td>$!{options.accNum}</td>
                                                <td>$!{options.prodName}</td>
                                                #if($options.ifCredit && $options.ifCredit =="Y")
                                                <td>$!{options.credit}</td>
                                                #end
                                                <td>$!{options.starInfo}</td>
                                                <td>$!{options.stopReason}</td>
                                            </tr>
                                            #else
                                                <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
