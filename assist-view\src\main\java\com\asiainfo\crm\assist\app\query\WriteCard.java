package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.util.WriteCardUtil;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.ICertNumFiveQuerySMO;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.service.intf.IMenuCollectionSMO;
import com.google.common.collect.Maps;
import net.sf.json.JSONObject;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Created by fengjie on 2018/6/22.
 */
@Component("vita.writeCard")
public class WriteCard extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(WriteCard.class);

	@Autowired
	private IMenuCollectionSMO menuCollectionSMO;

    @Autowired
    private ICertNumFiveQuerySMO certNumFiveQrySmo;
	@Autowired
	private ICustMultiSMO custMultiSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map getAsciiToHex(String inJson) throws Exception {
        Map<String, Object> rMap = new HashMap<String, Object>();
        Map<String,Object> param = jsonConverter.toBean(inJson,Map.class);
        String asciiStr =  (String) (param.get("asciiFStr") == null?"":param.get("asciiFStr"));
        byte[] baKeyword = new byte[asciiStr.length() / 2];
        for (int i = 0; i < baKeyword.length; i++) {
            try {
                baKeyword[i] = (byte) (0xff & Integer.parseInt(asciiStr.substring(
                        i * 2, i * 2 + 2), 16));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        try {
            asciiStr = new String(baKeyword, "ASCII");
            rMap.put("asciiStr", asciiStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return rMap;
    }
    public Map cardDllInfo(String inJson) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        Map<String,Object> param = jsonConverter.toBean(inJson,Map.class);
        String factoryCode =  MapUtils.getString(param,"factoryCode");
        if(StringUtils.isNotBlank(factoryCode) && MDA.LMC_LOCAL_DLL_INFO.containsKey(factoryCode)){
                retMap.put("retCode", "0");
                retMap.put("retObj",MDA.LMC_LOCAL_DLL_INFO.get(factoryCode));
        }else{
            retMap.put("retCode", "-1");
            retMap.put("retMsg", "获取动态链接库信息失败");
        }
        return retMap;
    }
    public Map getAuthCode(String inJson) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        Map<String,Object> param = jsonConverter.toBean(inJson,Map.class);
        String randomNum =  MapUtils.getString(param,"randomNum");
        String dllPassword =  MapUtils.getString(param,"dllPassword");
        String factoryCode =  MapUtils.getString(param,"factoryCode");
        String authCodeType =  MapUtils.getString(param,"authCodeType");
        if(StringUtils.isBlank(randomNum)){
            retMap.put("retCode", "-1");
            retMap.put("retMsg", "randomNum不能为空");
        }
        if(StringUtils.isBlank(dllPassword)){
            retMap.put("retCode", "-1");
            retMap.put("retMsg", "dllPassword不能为空");
        }
        if(StringUtils.isBlank(factoryCode)){
            retMap.put("retCode", "-1");
            retMap.put("retMsg", "factoryCode不能为空");
        }
        if(StringUtils.isBlank(authCodeType)){
            retMap.put("retCode", "-1");
            retMap.put("retMsg", "authCodeType不能为空");
        }
        int iAuthCodeType = Integer.valueOf(authCodeType);

        if(StringUtils.isNotBlank(factoryCode) && MDA.LMC_WRITE_CARD_PWD_INFO.containsKey(factoryCode)){
                Map  writeCardPwdInfo = MDA.LMC_WRITE_CARD_PWD_INFO.get(factoryCode);
                String pwdKeyValue =   MapUtils.getString(writeCardPwdInfo,"pwdKeyValue");
            try{
                //组件鉴权密钥解密密钥DLL_KEY_DECRIPT_KEY
                //动态链接库写卡密钥需要传入暗文，之后用约定的密钥解密后生成
                //密钥暗文  解密获取密钥明文
                String passwordKey = WriteCardUtil.desECB(dllPassword, pwdKeyValue, 1);
                String authCode = WriteCardUtil.desECB(randomNum, passwordKey, 0);
                retMap.put("retCode", "0");
                retMap.put("retObj", authCode);
            }catch (Exception e){
                e.printStackTrace();
                retMap.put("retCode", "-3");
                retMap.put("retMsg", "生成鉴权码失败");
            }


        }else{
            retMap.put("retCode", "-2");
            retMap.put("retMsg", "获取组件写卡信息失败");
        }
        return retMap;
    }


    public Map certNumFive(String jsonStr) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        Map<String,Object> mapString = jsonConverter.toBean(jsonStr,Map.class);
        Map map = new HashMap();
        map.put("sysUserId",mapString.get("staffId"));
        map.put("privCode",AssistMDA.JURISDICTION);
        //根据员工id查权限
        String permissionReturn = menuCollectionSMO.checkSysUserPriv(jsonConverter.toJson(map));
        Map<String,Object> permissionReturnMap = (Map<String, Object>) resolveResult(permissionReturn);
        String stre = MapUtils.getString(permissionReturnMap,"isHave");
        if ("false".equals(stre.toString())){
            //options.put("resultValue","权限不足");
            //return options;
        }
       //查询
        String resultJson = certNumFiveQrySmo.certNumFiveQuery(jsonStr);
        Map<String, Object> resultObjectMap =(Map<String, Object>) resolveResult(resultJson);
        if (resultObjectMap == null) {
        	 options.put("certNumFiveList", null);
             return options;
		}
        List certNumFiveList = new ArrayList<>();
        if (resultObjectMap.containsKey("resultObject")) {
        	Map<String, Object> resultObjects = (Map<String, Object>)resultObjectMap.get("resultObject");
        	certNumFiveList = (List) MapUtils.getObject(resultObjects,"value");
		}else {
			certNumFiveList = (List) MapUtils.getObject(resultObjectMap,"value");
		}
        if(certNumFiveList == null || certNumFiveList.size() < 0){
            options.put("certNumFiveList", null);
            return options;
        }
       // Map<String, Object> AccNum = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"valueAccNum");
       Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int totalCount = MapUtils.getIntValue(pageInfoMap, "totalCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageSize = MapUtils.getIntValue(pageInfoMap, "pageSize");
        options.put("resultValue",null);
        options.put("pageCount",pageSize);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", totalCount);
        options.put("certNumFiveList", certNumFiveList);
//        options.put("AccNum",AccNum);
       // options.putAll(AccNum);
        return options;

    }
    

    /**
     * 获取白卡资源
     * */
    public Map gainBlankCardRsrc(String jsonString) throws Exception {
        Map options = new HashMap();
        Map inMap = jsonConverter.toBean(jsonString,Map.class);
        JSONObject inJson = new JSONObject();
        inJson.put("mdn",MapUtils.getString(inMap,"phoneNumber"));
        inJson.put("cardNo",MapUtils.getString(inMap,"cardNo"));
        inJson.put("mvNo","");
        inJson.put("fromAreaNum",MapUtils.getString(inMap,"fromAreaCode"));
        inJson.put("toAreaNum",MapUtils.getString(inMap,"toAreaCode"));
        inJson.put("toLanId",MapUtils.getString(inMap,"toAreaCode"));
        inJson.put("staffId",MapUtils.getString(inMap,"staffId"));
        inJson.put("staffName",MapUtils.getString(inMap,"staffName"));
        inJson.put("channelId", MapUtils.getString(inMap,"channelId"));
        inJson.put("channelName", MapUtils.getString(inMap,"channelName"));
        inJson.put("regionId", MapUtils.getString(inMap,"toRegionId"));
        options.put("retCode", "-1");
        String respStr = custMultiSMO.gainBlankCardRsrc(inJson.toString());
        Map resultObject = (Map) resolveResult(respStr);
        if (resultObject instanceof  Map && resultObject.containsKey("blnkCrdRsrcGainRes")) {
            Map blnkCrdRsrcGainRes = (Map)resultObject.get("blnkCrdRsrcGainRes");
            Map personalData = MapUtils.getMap(blnkCrdRsrcGainRes,"personalData");

            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append(MapUtils.getString(personalData,"iccId"));
            stringBuffer.append("," + MapUtils.getString(personalData,"imsi"));
            stringBuffer.append("," + MapUtils.getString(personalData,"uimId"));
            stringBuffer.append("," + MapUtils.getString(personalData,"sId"));
            stringBuffer.append("," + MapUtils.getString(personalData,"accolc"));
            stringBuffer.append("," + MapUtils.getString(personalData,"nId"));
            stringBuffer.append("," + MapUtils.getString(personalData,"aKey"));
            stringBuffer.append("," + MapUtils.getString(personalData,"pin1"));
            stringBuffer.append("," + MapUtils.getString(personalData,"pin2"));
            stringBuffer.append("," +  MapUtils.getString(personalData,"puk1"));
            stringBuffer.append("," +  MapUtils.getString(personalData,"puk2"));
            stringBuffer.append("," +  MapUtils.getString(personalData,"adm"));

            stringBuffer.append("," + MapUtils.getString(personalData,"hrpdupp"));
            stringBuffer.append("," + MapUtils.getString(personalData,"hrpdss"));

            //多值
            List<Map> sipuppList =  (List<Map>)MapUtils.getObject(personalData,"sipupp");
            if(sipuppList != null && sipuppList.size()>0){
                String sipuppData = "";
                for(Map sipupp : sipuppList){
                    sipuppData += ";" + MapUtils.getString(sipupp,"sipuppData");
                }
                stringBuffer.append("," +  sipuppData);
            }
            List<Map> sipssList =  (List<Map>)MapUtils.getObject(personalData,"sipss");
            if(sipssList != null && sipssList.size()>0){
                String sipssData = "";
                for(Map sipss : sipssList){
                    sipssData += ";" + MapUtils.getString(sipss,"sipssData");
                }
                stringBuffer.append("," +  sipssData);
            }
            List<Map> mipuppList =  (List<Map>)MapUtils.getObject(personalData,"mipupp");
            if(mipuppList != null && mipuppList.size()>0){
                String mipuppData = "";
                for(Map mipupp : mipuppList){
                    mipuppData += ";" + MapUtils.getString(mipupp,"mipuppData");
                }
                stringBuffer.append("," +  mipuppData);
            }
            List<Map> mnhassList =  (List<Map>)MapUtils.getObject(personalData,"mnhass");
            if(mnhassList != null && mnhassList.size()>0){
                String mnhassData = "";
                for(Map mnhass : mnhassList){
                    mnhassData += ";" + MapUtils.getString(mnhass,"mnhassData");
                }
                stringBuffer.append("," + mnhassData );
            }
            List<Map> mnaaassList =  (List<Map>)MapUtils.getObject(personalData,"mnaaass");
            if(mnaaassList != null && mnaaassList.size()>0){
                String mnaaassData = "";
                for(Map mnaaass : mnaaassList){
                    mnaaassData += ";" + MapUtils.getString(mnaaass,"mnaaassData");
                }
                stringBuffer.append("," +  mnaaassData);
            }

            stringBuffer.append("," + MapUtils.getString(personalData,"imsiG"));
            stringBuffer.append("," + MapUtils.getString(personalData,"acc"));
            stringBuffer.append("," +  MapUtils.getString(personalData,"ki"));
            stringBuffer.append("," +  MapUtils.getString(personalData,"smsp"));

            //4G新增字段
            stringBuffer.append("," + MapUtils.getString(personalData,"opcG"));
            stringBuffer.append("," + MapUtils.getString(personalData,"imsiLte"));
            stringBuffer.append("," + MapUtils.getString(personalData,"accLte"));
            stringBuffer.append("," + MapUtils.getString(personalData,"kiLte"));
            stringBuffer.append("," + MapUtils.getString(personalData,"opcLte"));
            String writeCardData = stringBuffer.toString();

            personalData.put("data",writeCardData);
            personalData.put("dataLength",writeCardData.length());
            options.put("retCode", "0");
            options.put("retObj", personalData);
        }else{
            options.put("retMsg", "申请白卡资源失败");
        }
        return options;
    }

    /**
     * 写卡结果上报
     * */
    public Map reportEmptyCardWrtRslt(String jsonString) throws Exception {
        Map options = new HashMap();
        String respStr = custMultiSMO.reportEmptyCardWrtRslt(jsonString.toString());
        Map resultObject = (Map) resolveResult(respStr);
        options = resultObject;
        return options;
    }
    /**
     * 白卡入库
     * */
    public Map prhldngRlsUimCard(String jsonString) throws Exception {
        Map options = new HashMap();
        String respStr = custMultiSMO.prhldngRlsUimCard(jsonString.toString());
        Map resultObject = (Map) resolveResult(respStr);
        options = resultObject;
        return options;
    }
}
