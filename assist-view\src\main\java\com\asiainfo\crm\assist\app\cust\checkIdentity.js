(function (vita) {
    var checkIdentity = vita.Backbone.BizView.extend({
        events: {
            "click #btn-check": "_checkIdentity",
            "click #btn-check-realtime" : "_checkIdentityRealTime",
            "click #btn-clear" : "_clear",
            "keydown #custName" : "_keydownSubmit"
        },
        /*验证身份证号*/
        _checkIdentity: function (realTime) {
            var gsession = this.gSession;
            var objIdentityNum =  $(this.el).find("#identityNum");
            var objCustName = $(this.el).find("#custName");
            if(!this._validateInput(objIdentityNum, objCustName)){
                return;
            }
            if(realTime != true){
                realTime = false;
            }
            var params = {
                identityNum: objIdentityNum.val(),
                custName: objCustName.val(),
                realTime : realTime,
                glbSessionId : gsession.glbSessionId,
                staffId: gsession.staffId,
                regionId: gsession.staffRegionId,
                routeParam : {
                    "regionId": gsession.staffRegionId
                }
            };
            var p = JSON.stringify(params);
            this.refreshPart("checkIdentityNum", p, "#checkIdentityResult", function (res) {
                var r = $(res);
                var resultInfo = r.data("data");
                if(resultInfo.isPass != "Y"){
                    this.popup(resultInfo.msg);
                    return;
                }
            }, { async: false, headers:{} });
        },
        /*实时验证身份证*/
        _checkIdentityRealTime : function(){
            this._checkIdentity(true);
        },
        _clear : function(){
            var objIdentityNum =  $(this.el).find("#identityNum");
            objIdentityNum.val("");
            $(this.el).find("#custName").val("");
            objIdentityNum.focus();

        },
        _keydownSubmit : function(e){
            if (e.keyCode == 13) {
                this._checkIdentity();
                return false;
            }
        },
        /*校验输入内容是否合法*/
        _validateInput : function(objIdentityNum, objCustName){
            var soUtil = this.require("soUtil");
            if(soUtil.isNullOrEmpty(objIdentityNum.val())){
                this.popup("请输入身份证号码",function(){
                    objIdentityNum.focus();
                });
                return false;
            }
            var checkFormatRes = soUtil.validatePID(objIdentityNum.val());
            if(checkFormatRes != ""){
                this.popup(checkFormatRes, function(){
                    objIdentityNum.focus();
                });
                return false;
            }
            if(soUtil.isNullOrEmpty($.trim(objCustName.val()))){
                this.popup("请输入客户姓名", function(){
                    objCustName.focus();
                });
                return false;
            }
            return true;
        },
    });
    vita.widget.register("checkIdentity", checkIdentity, true);
})(window.vita);