<div data-widget="ICTContract">
    <vita-data>{"data":$options}</vita-data>
    <p class="vita-data">$options</p>
    <div class="sypage-min">
        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">查询条件</strong></div>
        </div>
        <div class="sypagemin_content ">
            <form>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">项目编号</label>
                    <div class="col-md-8">
                        <input  type="text" class="form-control" placeholder="请输入项目编号">
                        <p class="vita-bind" model="ictItemCode"></p>
                    </div>
                </div>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">项目名称</label>
                    <div class="col-md-8">
                        <input  type="text" class="form-control" placeholder="请输入项目名称">
                        <p class="vita-bind" model="ictItemName"></p>
                    </div>
                </div>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">合同编号</label>
                    <div class="col-md-8">
                        <input  type="text" class="form-control" placeholder="请输入合同编号">
                        <p class="vita-bind" model="contractCode"></p>
                    </div>
                </div>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">合同名称</label>
                    <div class="col-md-8">
                        <input  type="text" class="form-control" placeholder="请输入合同名称">
                        <p class="vita-bind" model="contractName"></p>
                    </div>
                </div>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">客户名称</label>
                    <div class="col-md-8">
                        <input  type="text" class="form-control" placeholder="请输入客户名称">
                        <p class="vita-bind" model="clientName"></p>
                    </div>
                </div>
                <div class="form-group col-md-6">
                    <label class="col-md-4 control-label lablep"></label>
                    <div class="col-md-8 searchbutt_r">
                        <button type="button" class="btn btn-default" id="searchICTDel">清除</button>
                        <button type="button" class="btn btn-primary" id="searchICTBtn">搜索</button>
                    </div>
                </div>

            </form>
        </div>
        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">ICT合同列表</strong></div>
        </div>
        <div class="sypagemin_content " >

            <div class="col-lg-12 mart10">
                <table class="table table-hover" >
                    <thead>
                    <tr>
                        <th>项目编码</th>
                        <th>项目名称</th>
                        <th>合同编号</th>
                        <th>合同名称</th>
                        <th>CRM客户编码</th>
                        <th>CRM客户名称</th>
                        <th>合同金额(元)</th>
                        <th>项目经理</th>
                        <th>创建时间</th>
                    </tr>
                    </thead>
                    <tbody >
                    #if($options != "null" && $options.ICTContracts && $options.ICTContracts != "null" && $options.ICTContracts.size() > 0)
                    #foreach($contract in $options.ICTContracts)
                    <tr >
                        <td><a  href="javascript:void(0);">#if($contract.ictItemCode != 'null')
                            $!contract.ictItemCode
                            #end</a>
                            <p class="vita-data">{"data" : $!contract,"totalNumber" : "$!options.totalNumber"}</p>
                        </td>
                        <td>#if($contract.ictItemName != 'null')
                            $!contract.ictItemName
                            #end</td>
                        <td>#if($contract.contractCode != 'null')
                            $!contract.contractCode
                            #end</td>
                        <td>
                            #if($contract.contractName != 'null')
                            $!contract.contractName
                            #end
                        </td>
                        <td>#if($contract.clientCode !='null')
                            $!contract.clientCode
                            #end</td>
                        <td>#if($contract.clientName != 'null')
                            $!contract.clientName
                            #end</td>
                        <td>#if($contract.tradeSum != 'null')
                            $contract.tradeSum
                            #end</td>
                        <td>#if($contract.clientManager != 'null')
                            $!contract.clientManager
                            #end</td>
                        <td>#if($contract.createDate != 'null')
                            $!contract.createDate
                            #end</td>
                    </tr>
                    #end
                    #end
                    </tbody>
                </table>
                <div class="page-box" id="showPageInfo">
                </div>
            </div>
        </div>
    </div>
</div>