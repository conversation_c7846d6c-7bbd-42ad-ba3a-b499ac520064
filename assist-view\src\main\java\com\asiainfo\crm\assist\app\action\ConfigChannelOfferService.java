package com.asiainfo.crm.assist.app.action;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ISoConfigSMO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by pushuo on 2017/7/22.
 */
@Component("vita.configChannelOfferService")
public class ConfigChannelOfferService extends AbstractComponent {

    @Autowired
    private ISoConfigSMO soConfigSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    /**
     * 查配置的禁止渠道
     * @param jsonStr
     * @return
     * @throws Exception
     */
    public Map qryForbidChannelsById(String jsonStr) throws Exception{
        String forbidChannelListStr = soConfigSMO.qryForbidChannelsById(jsonStr);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(forbidChannelListStr);
        Map<String, Object> options = Maps.newHashMap();
        List forbidChannelList = (List) MapUtils.getObject(resultObjectMap, "forbidChannelList");
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", total);
        options.put("forbidChannelList", forbidChannelList);
        return options;
    }

}
