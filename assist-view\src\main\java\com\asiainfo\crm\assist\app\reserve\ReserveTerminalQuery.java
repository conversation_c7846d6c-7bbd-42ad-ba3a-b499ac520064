package com.asiainfo.crm.assist.app.reserve;

import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.ISaopServiceSmo;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import com.asiainfo.crm.service.intf.ISoSaveSMO;
import com.asiainfo.crm.service.intf.ISpecSMO;
import com.asiainfo.crm.util.ListUtil;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

@Component("vita.reserveTerminalQuery")
public class ReserveTerminalQuery extends AbstractSoComponent {

    @Autowired
    protected ISaopServiceSmo saopServiceSmo;
    @Autowired
    private ISoQuerySMO soQuerySmo;
    @Autowired
	private ISpecSMO specSMO;
    @Autowired
    private ISoSaveSMO soSaveSmo;
    @Override
    public Map achieveData(Object... param) throws Exception {
        Map options = new HashMap();
        //证件类型下拉项
        List<Map<String,String>>  certTypeList =new ArrayList<Map<String,String>>();
        Set<Entry<String, String>> certTypeEntries = MDA.CERT_TYPE_MAP.entrySet();
        for (Entry<String, String> entry : certTypeEntries) {
            Map<String, String> map = new HashMap<>();
            String isSelect = "0";
            //默认身份证为已选择
            if("1".equals(entry.getKey())){
                isSelect="1";
            }
            map.put("id", entry.getKey());
            map.put("isSelect", isSelect);
            map.put("name", entry.getValue());
            certTypeList.add(map);
        }
        options.put("certTypeList", certTypeList);
        options.put("url", MDA.CASHIER_SYS_URL.get("simpleCashier"));
        return options;
    }

    public Map<String, Object> queryIphoneReserve(String json) throws Exception {
//        Map maps = jsonConverter.toBean(json, Map.class);
//        Map<String,Object> paramsVo =  new HashMap<String, Object>();
//		paramsVo.put(MDA.GROUP_SRVC_NODE_MAP.get("reserveTerminalQuery"), maps);
//		String reqParams =jsonConverter.toJson(paramsVo);
//		String reqJson = SaopUtil.convertSaopParam(MDA.GROUP_SRVC_INST_MAP.get("reserveTerminalQuery"), Long.parseLong(MDA.PROVINCE_AREA), reqParams);
//		String reJsonString = saopServiceSmo.exchange(reqJson);
//
//        Map options = new HashMap();
//        Map objMap = jsonConverter.toBean(reJsonString, Map.class);
//        Map contractRot = (Map) objMap.get("contractRoot");
//        Map svcContMap = (Map) contractRot.get("svcCont");
//
//        String resultCode = MapUtils.getString(svcContMap, "resultCode");
//        String resultMsg = MapUtils.getString(svcContMap, "resultMsg");
//        if (!StringUtil.isEmpty(resultCode) && resultCode.equals(MDA.RESULT_SUCCESS.toString())) {
//            Map qryResInfoRes = (Map)((Map) svcContMap.get("resultObject")).get("qryResInfoRes");
//            List list=(List)qryResInfoRes.get("orderListInfos");
//            options.put("orderListInfos",list);
//            options.put("totalNumber",list.size());
//        } else {
//            options.put("handleResultCode", resultCode);
//            options.put("handleResultMsg", resultMsg);
//            options.put("orderListInfos",Collections.emptyList());
//        }
        //根据前台查询参数查询预约信息
        Map options = new HashMap();
        String resultJson = soQuerySmo.qryReserveTerminal(json);
        Map<String, Object> resultObjectMap =(Map<String, Object>) resolveResult(resultJson);
        List<Map<String, Object>> reserveInfos = (List<Map<String, Object>>)resultObjectMap.get("reserveInfos");
        //将后台查询预约终端信息装换为前台展示所需参数
        if (!ListUtil.isListEmpty(reserveInfos)) {
            List<Map<String, Object>> orderListInfos=new ArrayList<Map<String, Object>>();
            for(Map<String, Object> reserveInfo:reserveInfos) {
                Map<String, Object> orderListInfo=new HashMap<String, Object>();
                orderListInfo.put("reserveTerminalId",reserveInfo.get("reserveTerminalId"));
                orderListInfo.put("terminalPrice",reserveInfo.get("reservePrice"));
                orderListInfo.put("createDate",reserveInfo.get("createDate"));
                orderListInfo.put("reserveCode",reserveInfo.get("reserveCode"));
                orderListInfo.put("reserveStatus",reserveInfo.get("statusCd"));
                orderListInfo.put("custOrderId",reserveInfo.get("custOrderId"));
                orderListInfo.put("ifPayment",reserveInfo.get("ifPayment"));
                orderListInfo.put("paymentId",reserveInfo.get("paymentId"));
                orderListInfo.put("custName",reserveInfo.get("custName"));
                qryNameByCd(reserveInfo,orderListInfo);
                orderListInfos.add(orderListInfo);
            }
            Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
            int totalCount = MapUtils.getIntValue(pageInfoMap, "totalCount");
            int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
            int pageSize = MapUtils.getIntValue(pageInfoMap, "pageSize");
            options.put("pageCount",pageSize);
            options.put("pageIndex",pageIndex);
            options.put("totalNumber", totalCount);
            options.put("orderListInfos", orderListInfos);
        }

        return options;
    }

    public Map<String, Object> delIphoneReserve(String json) throws Exception {
        Map options = new HashMap();
        String resultJson = soSaveSmo.cancleReserveTerminal(json);
        Map<String, Object> resultObjectMap =(Map<String, Object>) resolveResult(resultJson);
        options.put("resultCode",resultObjectMap.get("resultCode"));
        options.put("resultMsg",resultObjectMap.get("resultMsg"));
        return options;
    }
    //获取终端预约名称，预约政策名
    private void qryNameByCd(Map<String, Object> params,Map orderListInfo) throws Exception {
        //获取的终端，地区，预约政策为空时不处理
        Object region=params.get("regionId");
        Object terminalType=params.get("terminalType");
        Object cfgRuleId=params.get("cfgRuleId");
        if(region==null||terminalType==null||cfgRuleId==null){
            return;
        }
        //获取终端名称
        Map qryparams = new HashMap();
        qryparams.put("regionId", region);
        String terminalResultStr = specSMO.qryReserListByRegionId(jsonConverter.toJson(qryparams));
        List<Map<String, Object>> terminalNameList = (List<Map<String, Object>>) resolveResult(terminalResultStr);
        for (Map<String, Object> terminal : ListUtil.nvlList(terminalNameList)) {
            Object mktResCd=terminal.get("mktResCd");
            if(mktResCd==null){
                continue;
            }
            if (terminalType.equals(mktResCd.toString())) {
                orderListInfo.put("terminalName", terminal.get("mktResName"));
                break;
            }
        }
        //预约政策名称
        Map mktResParams = new HashMap();
        mktResParams.put("mktResCd", params.get("terminalType"));
        String resultStr = specSMO.qryReserPolicyByMktResCd(jsonConverter.toJson(mktResParams));
        List<Map<String, Object>> resultObject = (List<Map<String, Object>>) resolveResult(resultStr);
        for (Map<String, Object> terminalInfoMap : ListUtil.nvlList(resultObject)) {
            Object policyId=terminalInfoMap.get("policyId");
            if(policyId==null){
                continue;
            }
            if (policyId.equals(cfgRuleId.toString())) {
                orderListInfo.put("cfgRuleName", terminalInfoMap.get("policyName"));
                break;
            }
        }
    }

}

