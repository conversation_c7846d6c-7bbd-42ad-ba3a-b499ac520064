package com.asiainfo.crm.assist.app.query;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.IStaffInfoQuerySMO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created on 2019/4/16.
 */
@Component("vita.qryCardResourceLogInfo")
public class QryCardResourceLogInfo extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(QryCardResourceLogInfo.class);

    @Autowired
    private IOrderQuerySMO orderQuerySMO;

    @Autowired
    private IStaffInfoQuerySMO staffInfoQuerySMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    /**
     * 查询用户卡资源信息日志
     *
     * @param json
     * @return
     * @throws Exception
     */
    public Map qryCardResourceLogInfo(String json) throws Exception {
        String cardResourceLogInfo = orderQuerySMO.qryCardResourceLogInfo(json);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(cardResourceLogInfo);
        Map<String, Object> options = new HashMap<>(4);
        List<Map> cardResourceQryLogList = Lists.newArrayList();
        List<Map> cardResourceQryLogs = (List) MapUtils.getObject(resultObjectMap, "cardResourceQryLogs");
        for (Map cardResourceLog : cardResourceQryLogs) {
            Map log = Maps.newHashMap();
            log.putAll(cardResourceLog);
            String createStaff = MapUtils.getString(cardResourceLog, "createStaff");
            if (!StringUtil.isEmpty(createStaff)) {
                log.put("staffName", qryStaffName(createStaff));
            }
            cardResourceQryLogList.add(log);
        }
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap, "pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount", pageCount);
        options.put("pageIndex", pageIndex);
        options.put("totalNumber", total);
        options.put("cardResourceQryLogList", cardResourceQryLogList);
        return options;
    }

    /**
     * 查询员工名称
     *
     * @param createStaff
     * @return
     * @throws Exception
     */
    private String qryStaffName(String createStaff) throws Exception {
        Map param = new HashMap(1);
        param.put("staffId", createStaff);
        String staffInfoRet = staffInfoQuerySMO.qryStaffInfo(jsonConverter.toJson(param));
        Map staffInfoMap = (Map) resolveResult(staffInfoRet);
        Map staffMap = MapUtils.getMap(staffInfoMap, "staffInfo");
        return MapUtils.getString(staffMap, "staffName", "");
    }
}
