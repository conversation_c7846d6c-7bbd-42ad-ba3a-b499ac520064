(function(vita) {
	var serverRecommend = vita.Backbone.BizView.extend({
		events: {
			"click #btn-query": "_echart",
		},

		global:{
			preset: 'datetime',
		},

		_initialize: function () {
			var widget = this, element = $(widget.el);
			// 时间控件类型初始化
			var datetime = widget.require("datetime");
			var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
			var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
			var now = widget._getNowFormatDate();
			var endMis=widget._getEndFormatDate();
			beginDate.val(now);
			endDate.val(endMis);
			if (beginDate.length) {
				datetime.register(beginDate, {
					preset: widget.global.preset,
					timeFormat: "HH:ii:ss ",
					timeWheels:"HHiiss"
				});
			}
			;
			if (endDate.length) {
				datetime.register(endDate, {
					preset: widget.global.preset,
					timeFormat: "HH:ii:ss ",
					timeWheels:"HHiiss"
				});
			}
		},



		_getNowFormatDate: function() {
			var date = new Date();
			var seperator1 = "-";
			var year = date.getFullYear();
			var month = date.getMonth() + 1;
			var strDate = date.getDate();
			var hour = date.getHours();
			var mins=date.getMinutes();
			var seconds = date.getSeconds();
			if (month >= 1 && month <= 9) {
				month = "0" + month;
			}
			if (strDate >= 0 && strDate <= 9) {
				strDate = "0" + strDate;
			}
			if (mins >= 0 && mins <= 9) {
				mins = "0" + mins;
			}
			if (seconds >= 0 && seconds <= 9) {
				seconds = "0" + seconds;
			}
			if (hour >= 0 && hour <= 9) {
				hour = "0" + hour;
			}
			var currentdate = year + seperator1 + month + seperator1 + strDate
				+" 00" + ":00" +":00";
			return currentdate;
		},
		_getEndFormatDate: function() {
			var date = new Date();
			var seperator1 = "-";
			var year = date.getFullYear();
			var month = date.getMonth() + 1;
			var strDate = date.getDate();
			var hour = date.getHours();
			var mins=date.getMinutes();
			var seconds = date.getSeconds();
			if (month >= 1 && month <= 9) {
				month = "0" + month;
			}
			if (strDate >= 0 && strDate <= 9) {
				strDate = "0" + strDate;
			}
			if (mins >= 0 && mins <= 9) {
				mins = "0" + mins;
			}
			if (seconds >= 0 && seconds <= 9) {
				seconds = "0" + seconds;
			}
			if (hour >= 0 && hour <= 9) {
				hour = "0" + hour;
			}
			var currentdate = year + seperator1 + month + seperator1 + strDate
				+" 23"+ ":59" +":59";
			return currentdate;
		},

		_echart: function (e) {
			var widget = this, element = $(widget.el);
			// 时间控件类型初始化
			var beginDate = element.find("#beginDate").val();
			var endDate = element.find("#endDate").val();
			var sourceType = element.find("#sourceType").val();
			var params = {
			};
			var times;
			var counts;
			var exceptionalCounts;
			params.beginDate = beginDate;
			params.endDate = endDate;
			params.sourceType = sourceType;
			widget.callService("queryCount", JSON.stringify(params), function (res) {
				times =res.times;
				counts =res.counts;
				exceptionalCounts =res.exceptionalCounts;
			}, {async: false});
			if (counts.length === 0 && exceptionalCounts.length === 0 ){
				widget.popup("该时间段内没有数据！");
			}
			// 折线图
			// 基于准备好的dom，初始化echarts实例
			var line = echarts.init(document.getElementById('line'));
			// 指定图表的配置项和数据
			var lineOption = {
				// 标题
				title: {
					text: '蓝：完成数量    红：异常数',
					// subtext:'',
					x: 'center',
					y: '7px',
					textStyle: {
						color: '#3A7BD5',
						fontSize: 16
					},
					textAlign: 'left'
				},
				legend: {
					data: ['success', 'fail']
				},
				// 图例
				tooltip: {
					trigger: 'axis'
				},
				xAxis: {
					type: 'category',
					data: times,
					name:"时间"
				},
				yAxis: {
					type: 'value'
					,
					name:"数量"
				},
				series: [{
					name: 'success',
					data: counts,
					type: 'line',
					itemStyle : { normal: {label : {show: true,color: "blue"}}},
					lineStyle: { // line的样式
						color: "blue"
					},
				},
					{
						name: 'fail',
						data: exceptionalCounts,
						type: 'line',
						itemStyle : { normal: {label : {show: true,color: "red"}}},
						lineStyle: { // line的样式
							color: "red"
						},
					}
				]
			};
			line.setOption(lineOption);
		}
	});
	vita.widget.register("serverRecommend", serverRecommend, true);
})(window.vita);