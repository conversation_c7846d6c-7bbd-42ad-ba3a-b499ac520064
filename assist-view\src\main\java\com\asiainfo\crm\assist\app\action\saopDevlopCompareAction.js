(function(vita) {
	var saopDevlopCompareAction = vita.Backbone.BizView.extend({
		events : {
			"click #btn-clear" : "_clearCond",
			"click #btn-revoke" : "_revokeCond",
		},
		_clearCond : function() {
			var $checked = $(".form-group").find("input[type=radio]:checked");
			$.each($checked, function(i, chkInput) {
				chkInput.click();
				var $input = $(chkInput).closest(".form-group").find("input.form-control");
				$input.val("");
				$input.attr("value", null);
			})
		},
		_isNullStr : function(str) {
			if (str == null || str == "") {
				return true;
			}
			return false;
		},
		_getParams : function() {
			var widget = this;
			var gSession = widget.gSession;
			var params = {
				staffName : gSession.staffName,
				staffCode : gSession.staffCode,
				srvcInstId : "SRVC_SAOP_CAP_DEVELOP",
				busyTypeCd : ""
			};
			var paramCnt = 0;
			
			if ($("#c_custOrderId").is(":checked")) {
				var custOrderId = $("#custOrderId").val();
				if (widget._isNullStr(custOrderId)) {
					widget.popup("请输入CRM3.0客户订单号！");
					return false;
				} else {
					paramCnt++;
					params.custOrderId = custOrderId;
				}
			}			
		
			if (!paramCnt) {
				widget.popup("请选择输入至少一个参数！");
				return;
			}
			return params;
		},
		_revokeCond : function() {
			var widget = this;
			var params = widget._getParams();
			if (params) {				
				widget.callService("doSaopAction", JSON.stringify(params), function(ret) {
					if (ret.resultCode == 0) {	
						$("#remark").text("");
						$("#groupXml").text("");
						$("#jsonCrm").text("");
						$("#outParam").text("");
						widget.popup("根据订单信息未能查到能力开放报错信息!");						
					}else{
						$("#groupXml").text(ret.groupXml);
						$("#remark").text(ret.remark);
						$("#jsonCrm").text(ret.jsonCrm);
						$("#outParam").text(ret.outParam);
					}					
				});					
			}
		}
	});
	vita.widget.register("saopDevlopCompareAction", saopDevlopCompareAction, true);
})(window.vita);
