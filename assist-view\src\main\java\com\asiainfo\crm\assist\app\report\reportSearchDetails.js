
(function (vita) {
    var reportSearchDetails = vita.Backbone.BizView.extend({
        events: {
        	"click #reportSearchDetailsBtn": "_closePage" ,
        	"click [name='custOrderIdA']" : "_queryCustomerOrderDetail"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
           
        },
        _closePage: function () {
            var widget = this,
                element = $(widget.el);
            element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
        },
        _queryCustomerOrderDetail: function(e){
        	var widget = this,element = $(widget.el);
        	var labelA = $(e.target).closest('a');
        	var custOrderId = labelA.data("custOrderId");
            window.open("../../crm/so/olDetailInfo?custOrderId="+custOrderId);
        }
    });
    vita.widget.register("reportSearchDetails", reportSearchDetails, true);
})(window.vita);