package com.asiainfo.crm.assist.app.checkauth;
 
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IStaffDataPrvQuerySMO;
import com.asiainfo.crm.service.intf.IStaffInfoQuerySMO;

/**
 * 鉴权校验
 */
@Component("vita.checkAuth")
public class CheckAuth extends AbstractComponent {
 
	@Autowired
	private IStaffDataPrvQuerySMO sysUserPrivQrySmo;
	@Autowired
	private IStaffInfoQuerySMO systemUserQrySmo;
	
	@Override
	public Map achieveData(Object... params) throws Exception {
		Map param = (Map) params[0];
		Map options = new HashMap();
		JSONObject  jasonObject = JSONObject.fromObject(param);
		
		// 跳过鉴权校验权限
		JSONObject privParams = new JSONObject();
		String glbSessionId = jasonObject.getString("glbSessionId");
		privParams.put("glbSessionId", glbSessionId);
		privParams.put("privCode", AssistMDA.CHECK_AUTH_PASS);
		String privsStr = sysUserPrivQrySmo.querySysUserPrivs(privParams.toString());
		JSONObject resultObj = JSONObject.fromObject(privsStr);
		if(resultObj != null){
			JSONObject privsObj = resultObj.getJSONObject("resultObject");
			String resultCode = resultObj.getString("resultCode");
			if(resultCode != null && MDA.RESULT_SUCCESS==Integer.parseInt(resultCode)){
				Object privs = privsObj.get("privs");
				if (privs != null) {
					List<Map> privList = (List) privs;
					options.put("isPass", !privList.isEmpty());
				}
			}
		}
		
		return options;
	}
	
	/**
	 * 鉴权校验
	 */
	public JSONObject checkAuth(String jsonString) throws Exception {
		JSONObject jsonObject = JSONObject.fromObject(jsonString);
		String privName = "此项功能";
		boolean isPass = false;
		String resultMsg = "";
		
		/*Map<String, Object> resultMap = systemUserQrySmo.checkUserPwd((Map)jsonObject);
 		String resultCode = MapUtils.getString(resultMap, "resultCode");
 		resultMsg = MapUtils.getString(resultMap, "resultMsg");
		if(resultCode != null && MDA.RESULT_SUCCESS==Integer.parseInt(resultCode)){
			Long sysUserId = systemUserQrySmo.qrySysUserId((Map)jsonObject);
			jsonObject.put("sysUserId", sysUserId);
			String privsStr = sysUserPrivQrySmo.querySysUserPrivs(jsonObject.toString());
			JSONObject resultObj = JSONObject.fromObject(privsStr);
			resultMsg = "权限验证失败！";
			if(resultObj != null){
				JSONObject privsObj = resultObj.getJSONObject("resultObject");
				resultCode = resultObj.getString("resultCode");
				if(resultCode != null && MDA.RESULT_SUCCESS==Integer.parseInt(resultCode)){
					Object privs = privsObj.get("privs");
					if (privs != null && !((List) privs).isEmpty()) {
						isPass = true;
						resultMsg = "验证通过！";
					}else{
						StringBuffer sb = new StringBuffer();
						sb.append("工号【").append(jsonObject.getString("sysUserCode")).append("】没有").append(privName).append("的审核权限！");
						resultMsg = sb.toString();
					}
				}
			}
		}*/
		
		jsonObject.put("flag", AssistMDA.CHECK_AUTH_4A);
		String privsStr = systemUserQrySmo.getTemporaryAndPriv((Map)jsonObject);
		JSONObject resultObj = JSONObject.fromObject(privsStr);
 		String resultCode = MapUtils.getString(resultObj, "resultCode");
 		if(resultCode != null && MDA.RESULT_SUCCESS == Integer.parseInt(resultCode)){
 			JSONObject privsObj = resultObj.getJSONObject("resultObject");
 			isPass = privsObj.getBoolean("type");
			resultMsg = privsObj.getString("msg");
 		}else{
 			resultMsg = "权限验证异常！" + MapUtils.getString(resultObj, "resultMsg");
 		}
 		
		jsonObject.put("resultMsg", resultMsg);
		jsonObject.put("resultObject", isPass);
		jsonObject.put("resultCode", MDA.RESULT_SUCCESS);

		return jsonObject;
	}
 
}
