(function (vita) {
    var newOrUpdateProtocol = vita.Backbone.BizView.extend({
        events: {
            "click #addParagraphs": "_addParagraphs",
            "click button[name=detectTexts]": function(e){//删除段落
            	this.detectText(e);
            },
            "click button[name=addTexts]": function(e){//添加文本
            	this.addText(e);
            },
			"click button[name=addParagraphsNew]": function(e){//
				this.addParagraphsNew(e);
			},
            "click button[name=detects]": function(e){//删除文本
            	this.detect(e);
            },
            "click input[name^=radio]" : function (e) {//设置输入框
				this._receipt(e);
			},
			"click input[name=dateSelection]" : function (e) {//日期
				this._radios(e);
			},
			"click input[name^=colour]" : function (e) {//字体颜色
				this._colour(e);
			},
			"click input[name^=bold]" : function (e) {//字体加粗
				this._bold(e);
			},
			"click input[name^=escape]" : function(e){
            	this._escapeContent(e)
			},
			"click #reviewBtn" : "_reviewBtn",//预览
			"click #saveBtn" : "_saveBtn",//保存
			"click #submitBtns" : "_submitBtns",//另存为
			"click #submitBtn" : "_submitBtn",//提交
			"change input[name^=input]" : function (e) {
                     this._check(e);

             },
            
        },
        _initialize : function () {
            var widget = this, 
            element = $(widget.el);
        	var data = element.data("data");
        	widget.model.set("crateStaff", data.crateStaff); 
        	widget.model.set("regionId", data.regionId); 
        	
        	if (null != data.str && "" != data.str) {
        		var protocolTempletId = data.str;
        		widget.model.set("protocolTempletId", protocolTempletId); 
        		widget._showProtocol(protocolTempletId);
			}
        	widget.model.set("agreementName", data.name); 
        },
        global: {
        	sectionSeq : 0,  //段落序列
        	textSeq : 0, //文本序列
        	inputSeq : 0,//输入框序列
        	root : {},
        	sections : [],
        	needYmd : "true",
        	text : {
        			txtVal : '',
        			needEdit : false,
        			editLen : 170   //默认170
        		}
        },
        //添加自然段
        _addParagraphs :　function (){
        	var widget = this;
        	var sectionSeqs = widget.global.sectionSeq+1;
        	 $("#addParagraphs").before("<table id='sectionTable"+sectionSeqs+"' class='table table-hover'><tr><td><button id='addText"+sectionSeqs+"' type='button' name='addTexts' class='btn btn-primary'>添加文本</button>" +
				 "<button style='margin-left: 15px' id='addParagraphsNew' name='addParagraphsNew' class='btn btn-primary'>添加自然段</button></td>" +
				 "<td><button id='detectText"+sectionSeqs+"' name='detectTexts' >删除该段落</button></td></tr></table>");
        	 widget.global.sectionSeq=sectionSeqs;
        },
		//直接向下添加自然段
		addParagraphsNew :　function (e){
			var widget = this;
			var button = $(e.target).closest("button");
			var tr = button[0].parentElement.parentElement.parentElement.parentElement;
			var sectionSeqs = widget.global.sectionSeq+1;
			$("#"+tr.id).after("<table id='sectionTable"+sectionSeqs+"' class='table table-hover'><tr><td><button id='addText"+sectionSeqs+"' type='button' name='addTexts' class='btn btn-primary'>添加文本</button>" +
				"<button style='margin-left: 15px' id='addParagraphsNew' name='addParagraphsNew' class='btn btn-primary'>添加自然段</button></td>" +
				"<td><button id='detectText"+sectionSeqs+"' name='detectTexts' >删除该段落</button></td></tr></table>");
			widget.global.sectionSeq=sectionSeqs;
		},
      //添加文本
        addText : function (e){
        	var widget = this;
        	var button = $(e.target).closest("button");
        	var tr = button[0].parentElement.parentElement.parentElement;
        	var textSeqs = widget.global.textSeq+1;
        	 $(tr).append("<tr id='text"+textSeqs+"'><td id='textTd"+textSeqs+"'><textarea id='textSeq"+textSeqs+"' rows='3' cols='80'></textarea></td><td name='edit"+textSeqs+"' boo='td_false'>是否需要编辑框： 是<input type='radio' value='1' name='radio"+textSeqs+"' >.否<input type='radio' checked='checked' name='radio"+textSeqs+"' ></td>" +
        	 		"<td name='colour"+textSeqs+"' colourBoo='td_false'>字体是否加红： 是<input type='radio' value='1' name='colourRadio"+textSeqs+"' >.否<input type='radio' checked='checked' name='colourRadio"+textSeqs+"' ></td>" +
        	 				"<td name='bold"+textSeqs+"' boldBoo='td_false'>字体是否加粗： 是<input type='radio' value='1' name='boldRadio"+textSeqs+"' >.否<input type='radio' checked='checked' name='boldRadio"+textSeqs+"' ></td>" +
        	 				"<td name='escape"+textSeqs+"' escapeBoo='td_false'>内容是否转义： 是<input type='radio' value='1' name='escapeRadio"+textSeqs+"' >.否<input type='radio' checked='checked' name='escapeRadio"+textSeqs+"' ></td>" +
				 			"<td><button ' name='detects'>删除</button></td></tr>");
//        	 var td = button[0].parentElement;
//        	 td.data("data",widget.global.text);
        	 widget.global.textSeq=textSeqs;
        },
      //删除段落
        detectText : function (e){
        	var widget = this;
        	var button = $(e.target).closest("button");
        	var tr = button[0].parentElement.parentElement;
        	$(tr).remove();
        },
      //删除文本
        detect : function (e){
        	var widget = this;
        	var button = $(e.target).closest("button");
        	var tr = button[0].parentElement.parentElement;
        	$(tr).remove();
        },
      //设置输入框.
        _receipt : function (e){
        	var widget = this;
        	var input = $(e.target).closest("input");
        	var value = input[0].value;
        	var td = input[0].parentElement;
        	var boo = $(td).attr("boo"); 
        	if (1 == value) {
        		if ("td_false" == boo) {
        			var inputSeqs = widget.global.inputSeq+1;
        			 $(td).append("<input name='input"+inputSeqs+"' type='text' style='width: 170px;' placeholder='请输入编辑框长度（默认170）'>");
        			 $(td).attr("boo","td_true");
        			 widget.global.inputSeq=inputSeqs;
				}
        	}else{
        		if ("td_false" != widget.global.boo) {
        			$(td).find("input[name^=input]").remove();
        			 $(td).attr("boo","td_false");
				}
        	}
        },
        _check : function(e){
        	var widget = this;
        	var input = $(e.target).closest("input").val();
        	if(input == ""){
        		return
        	}
        	var reg = new RegExp("^[0-9]*$");
        	if(!reg.test(input)){  
        		 widget.popup('请输入数字！');
        		$(input).val("");
                return;
            }  
        	/*var inputs = $(e.target).closest("input");
        	var tr = inputs[0].parentElement.parentElement;
        	tr.data("editLen",input);*/
        },
        //预览
        _reviewBtn : function(){
        	var widget = this;
        	var data = widget._getConds();
        	var str = widget.constructXmlStr(data);
        	
        	var id = "review";
        	var url = "../action/protocolDisplay";
			var param = {
					"str":str
			};
			var dialogId = id + 'Dialog';
			var option = {
				url : url,
				params : param,
				id : dialogId,
				onClose : function(res) {}
			};
			widget.dialog(option);	
            //window.open("../action/protocolDisplay?str="+str); 
    	},
        //取值
        _getConds : function (){
        	var widget = this;
        	var data = widget.global.root;
        	var sectionTables = $('table[id^=sectionTable]');
        	var sectionsArray = new Array();
        	$.each(sectionTables, function(i, secTable){  //各个段落
        		var textTrs = $(secTable).find('tr[id^=text]');
    			var sectionArray = new Array();
    			$.each(textTrs, function(i, textTr){
    				var textTd = $(textTr).find('td[id^=textTd]')[0];
    				var textarea = $(textTd).find('textarea[id^=textSeq]')[0];
    				var textVal = $(textarea).val(); //每一个文字行的数据
    				var textTds = $(textTr).find('td[name^=edit]')[0];//是否要编辑框
    				var boo = $(textTds).attr("boo");
    				var colourTextTd = $(textTr).find('td[name^=colour]')[0];//字体颜色是否加红
    				var colourboo = $(colourTextTd).attr("colourboo");
    				var boldTextTds = $(textTr).find('td[name^=bold]')[0];//字体是否加粗
    				var boldboo = $(boldTextTds).attr("boldboo");
                    var escapeboo = $(textTr).find('td[name^=escape]').attr("escapeBoo");
    				var textVals = "";
    				if ("td_true" == boo ) {
    					var textInput = $(textTds).find('input[name^=input]')[0];
    					textVals = $(textInput).val();
    					if ("" == textVals) {
    						textVals = "170"
						}
					}
    				var text = {
    						"textVal" : textVal,
    						"boo" : boo,
    						"textVals" : textVals,
    						"colourboo" : colourboo,
    						"boldboo" : boldboo,
							"escapeboo" : escapeboo
    				}
//    				var textData = $(textTd).data("data");
    				sectionArray.push(text);
//    				sectionArray.push(textData);
    			});
    			sectionsArray.push(sectionArray);
        	})
        	 widget.global.sections=sectionsArray;
        	return widget.global.sections;
        },
        //保存
        _saveBtn : function(){
        	var widget = this;
        	var data = {};
			//保存为下线状态
			data.statusCd = "1200";
			widget._createProtocol(data);
        },
        //另存为
        _submitBtns : function (){
        	var widget = this;
        	var data = {};
        	if (widget.model.get("agreementName") == $("#agreementName").val()) {
        		widget.popup("协议名称未改！请修改，");
				return false;
			}
			//保存为下线状态
			data.statusCd = "1200";
			data.boo = 1;
			widget._createProtocol(data);
        },
        //提交
        _submitBtn : function (){
        	var widget = this;
        	var data = {};
			//提交为上线状态
			data.statusCd = "1000";
			widget._createProtocol(data);
        },
        _createProtocol : function(data){
			var widget = this,
			element = $(widget.el); 
			
			var agreementName = $("#agreementName").val();
			if(agreementName == ""){
				widget.popup("协议名称不能为空！");
				return false;
			}
			data.name = agreementName;
			
			var content = widget._getConds();
			if(content == ""){
				widget.popup("协议内容不能为空！");
				return false;
			}
			data.content = widget.constructXmlStr(content);
			
			data.crateStaff = widget.model.get("crateStaff");
			data.regionId = widget.model.get("regionId");
			
			var protocolTempletId = widget.model.get("protocolTempletId");
			if(protocolTempletId != null){
				if (1 != data.boo) {
					data.protocolTempletId = protocolTempletId;	
				}
			}
			
	    	widget.callService("saveProtocol", JSON.stringify(data),function(res) {
				var ret = JSON.parse(res);
				if (ret.resultCode == 0) {
					widget.popup("操作成功！");
					widget._closeBtn();
					
				}else{
					widget.popup("操作失败！");
				}				
			}, {
				mask : true
			});
			 
		},
		_closeBtn : function () {
        	var widget = this,
			element = $(widget.el);
			var dialog = element.closest("[id=" + widget.widgetName + "Dialog]");
			if (dialog.length) {
				dialog.dialog("close");
			}
			//window.close();
		},
        //数据拼装
        constructXmlStr : function(data){
        	var widget = this;
    		var xml = '<?xml version="1.0" encoding="utf-8"?><root></root>';
    		var xmlDoc = $.parseXML( xml );
      		var $xml = $( xmlDoc );
      		var title = $("#agreementName").val();
    		var needYmd = widget.global.needYmd;
    		var root = $xml.find('root');
    		root.append('<title>' + title + '</title>');
    		root.append('<foot><ymd>' + needYmd + '</ymd></foot>');
    		root.append('<content/>');
    		var content = root.find('content');
    		var sections = data;
    		$.each(sections, function(i, section){  //每一个自然段 []
    			var txtXml = "";
    			$.each(section, function(i, text){ //每一段里的每一行,每一行是一个{}
    				//这里每一次需要加一个<text>
    				var txtVal = text.textVal;
    				var needEdit = text.boo;
    				var editLen = text.textVals;
    				var colourboo = text.colourboo;
					var boldboo = text.boldboo;
                    var escapeboo = text.escapeboo;
                    var txtStr = "";
                    //转为dom去增加属性，会自动转义，改为拼接方式
                    if(escapeboo ==="td_true"){
                        txtVal = "<![CDATA[" + txtVal + "]]>";
                        // txt.attr("escape", "true");
                        txtStr += " escape='true'";
					}
                    if(needEdit=="td_true") {
                        // txt.attr("editable", "true").attr("width",editLen);
                        txtStr += " editable='true' width='"+editLen+"'";
                    }
                    if(boldboo=="td_true" && colourboo == "td_true"){
                        // txt.attr("colour", "redBold");
                        txtStr += " colour='redBold'";
					}else if(boldboo=="td_true") {
                        // txt.attr("bold", "true");
                        txtStr += " bold='true'";
                    }else if(colourboo=="td_true") {
                        // txt.attr("colour", "red");
                        txtStr += " colour='red'";
                    }

    				txtXml += "<text "+txtStr+">"+txtVal + "</text>";
    			});
    			
    			//这里每一次需要加一个<section>
    			content.append('<section>' + txtXml + '</section>');
    		});
//    		console.info($xml.xml());
//    		return $xml.xml();
            var str = (new XMLSerializer()).serializeToString($xml[0]);
            console.info(str);
            return str;
        },
        _radios : function(e){
            var widget = this;
            var val = $("input[name=dateSelection]:checked").val();
            if ("1" == val) {
                widget.global.needYmd = "true";
            }else if("no" == val){
                widget.global.needYmd = "false";
            }else{
                widget.popup("编辑日期异常");
                return;
            }
        },
        _colour : function (e){
            var widget = this;
            var input = $(e.target).closest("input");
            var value = input[0].value;
            var td = input[0].parentElement;
            var boo = $(td).attr("colourBoo");
            if (1 == value) {
                $(td).attr("colourBoo","td_true");
            }else{
                $(td).attr("colourBoo","td_false");
            }
        },
        _bold : function (e){
            var widget = this;
            var input = $(e.target).closest("input");
            var value = input[0].value;
            var td = input[0].parentElement;
            var boo = $(td).attr("boldBoo");
            if (1 == value) {
                $(td).attr("boldBoo","td_true");
            }else{
                $(td).attr("boldBoo","td_false");
            }
        },

        _escapeContent : function(e){
            var widget = this;
            var input = $(e.target).closest("input");
            var value = input[0].value;
            var td = input[0].parentElement;
            var boo = $(td).attr("escapeBoo");
            if (1 == value) {
                $(td).attr("escapeBoo","td_true");
            }else{
                $(td).attr("escapeBoo","td_false");
            }
		},

        //编辑
        _showProtocol: function (str) {
            var widget = this, element = $(widget.el);
            var params = {
                "protocolTempletId": str
            }
            widget.callService("showProtocol", JSON.stringify(params), function (res) {
                widget._parseContent(res.result);
            });
        },
        //解析协议
        _parseContent : function(result){
            var widget = this, element = $(widget.el);
            if(!result) {
                widget.popup("查询协议内容异常，无法编辑!");
                return;
            }
            var $result = $($.parseXML(result));
            //解析日期
            var ymd = $result.find("ymd");
            if(!ymd.text()==="true") {
                element.find("#noInput").prop("checked", true);
            }

            //解析协议名称
            $("#agreementName").val($result.find("title").text());

            //解析内容
            var content = $result.find("content"),
                sections = content.find("section");
            if(sections.length===0) {
                return;
            }
            var textSun = 0;
            $.each(sections, function (j, section) {
                //添加段落
                $("#sectionDiv").before("<table id='sectionTable" + j + "' class='table table-hover'><tr><td><button id='addText" + j + "' type='button' name='addTexts' class='btn btn-primary'>添加文本</button></td>" +
                    "<td><button id='detectText" + j + "' name='detectTexts' >删除该段落</button></td></tr></table>");

                var texts = $(section).find("text");
                $.each(texts, function (i, text) {
                    textSun++;
                    var editable = $(text).attr("editable") === "true",
                        bold = $(text).attr("bold") === "true",
                        colourVal = $(text).attr("colour"),
                        colourRed = false,
                        escape = $(text).attr("escape") === "true",
                        escapeBoo = escape?"td_true":"td_false";
                    if(colourVal==="red"){
                        colourRed = true;
                    }else if(colourVal==="redBold"){
                        colourRed = true;
                        bold = true;
                    }
                    var textContent = text.innerHTML;
                    //如果有<![CDATA[才能去replace]]>
                    if(textContent.startsWith("<![CDATA[")){
                        textContent =  textContent.replace("<![CDATA[","").replace("]]>","");
					}
                    var trContent = $('<tr id="text' + textSun + '"\>' +
                            '<td id="textTd' + textSun + '"\><textarea id="textSeq"' + textSun + ' rows="3" cols="80">' + textContent + '</textarea></td>' +
                            '<td name="edit' + textSun + '"  boo="td_false">是否需要编辑框： 是<input value="1" type="radio" name="radio' + textSun + '">.否<input type="radio" checked="checked" name="radio' + textSun + '"></td>' +
                            '<td name="colour' + textSun + '" colourBoo="td_false">字体是否加红： 是<input value="1" type="radio"  name="colourRadio' + textSun + '">.否<input type="radio" checked="checked" name="colourRadio' + textSun + '"></td>' +
                            '<td name="bold' + textSun + '" boldBoo="td_false">字体是否加粗： 是<input value="1" type="radio"  name="boldRadio' + textSun + '">.否<input type="radio" checked="checked" name="boldRadio' + textSun + '"></td>' +
                            '<td name="escape' + textSun + '" escapeBoo='+ escapeBoo +'>内容是否转义： 是<input value="1" type="radio"  name="escapeRadio' + textSun + '">.否<input type="radio" checked="checked"  name="escapeRadio' + textSun + '"></td>' +
                            '<td><button name="detects">删除</button></td>' +
                        '</tr>');
                    $("#sectionTable" + j).append(trContent);
                    if(editable) {
                        var editTd = trContent.find("[name='edit" + textSun + "']"),
                            width = $(text).attr("width");
                        editTd.append("<input name='input" + textSun + "' type='text' style='width: 170px;' placeholder='请输入编辑框长度（默认170）' value='"+width+"'>");
                        editTd.find("input[value='1']").prop("checked", true);
                        editTd.attr("boo", "td_true");
                    }
                    if(colourRed) {
                        var colourTd = trContent.find("[name='colour" + textSun + "']");
                        colourTd.find("input[value='1']").prop("checked", true);
                        colourTd.attr("colourBoo", "td_true");
                    }
                    if(bold) {
                        var boldTd = trContent.find("[name='bold" + textSun + "']");
                        boldTd.find("input[value='1']").prop("checked", true);
                        boldTd.attr("boldBoo", "td_true");
                    }

                    if(escape) {
                        var escapeTd = trContent.find("[name^='escape']");
                        escapeTd.find("input[value='1']").prop("checked", true);
                        escapeTd.attr("escapeBoo", "td_true");
                    }
                });

            });
        }
    });
    vita.widget.register("newOrUpdateProtocol", newOrUpdateProtocol, true);
})(window.vita);
