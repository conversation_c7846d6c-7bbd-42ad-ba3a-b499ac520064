package com.asiainfo.crm.assist.app.query;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.bcomm.exception.BError;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.util.SmLogUtil;

/**
 * 一证五号强制修复接口
 * Created by niewq on 2018/5/5.
 */
@Component("vita.certNumFiveRepair")
public class CertNumFiveRepair extends AbstractComponent {

	private static final Logger logger = LoggerFactory
			.getLogger(CertNumFiveRepair.class);

	@Autowired
	private ICustMultiSMO custMultiSMO;

	@Override
	public Map<String, Object> achieveData(Object... params) throws Exception {
		return null;
	}

	/**
	 * 一证五号强制修复接口 
	 * 直接调集团接口强制释放
	 * @param jsonString 
	 * @return
	 */
	public Map<String, Object> exeForceRepairRelation(String jsonString) {
		Map<String, Object> retMap = new HashMap<String, Object>();
		String resultJson = "";
		try {
			logger.debug(jsonString);
			jsonString = SmLogUtil.supplyOneFivePatams(jsonString);
			resultJson = custMultiSMO.forceChangeOCFNRelation(jsonString);
			Map<String, Object> resultObject = (Map) resolveResult(resultJson);
			retMap.put("retCode", resultObject.get("resultCode"));
			retMap.put("retDesc", resultObject.get("resultMsg"));
			return retMap;
		} catch (Exception e) {
			retMap.put("retCode", "-1");
			if (e instanceof BError){
	            BError bError = (BError)e;
	            retMap.put("retDesc", bError.getMsg());
			}else{
				retMap.put("retDesc", e.getMessage());
			}
			return retMap;
		}
	}
	
	/**
	 * 一证五号强制修复接口 (暂时不用)
	 * 直接调集团接口强制释放
	 * @param jsonString 
	 * @return
	 */
	public Map<String, Object> addexeForceRepairRelation(String jsonString) {
		Map<String, Object> retMap = new HashMap<String, Object>();
		String resultJson = "";
		try {
			logger.debug(jsonString);
			jsonString = SmLogUtil.supplyOneFivePatams(jsonString);
			resultJson = custMultiSMO.changeOCFNRelation(jsonString);
			Map<String, Object> resultObject = (Map) resolveResult(resultJson);
			retMap.put("retCode", resultObject.get("resultCode"));
			retMap.put("retDesc", resultObject.get("resultMsg"));
			return retMap;
		} catch (Exception e) {
			retMap.put("retCode", "-1");
			if (e instanceof BError){
	            BError bError = (BError)e;
	            retMap.put("retDesc", bError.getMsg());
			}else{
				retMap.put("retDesc", e.getMessage());
			}
			return retMap;
		}
	}

}
