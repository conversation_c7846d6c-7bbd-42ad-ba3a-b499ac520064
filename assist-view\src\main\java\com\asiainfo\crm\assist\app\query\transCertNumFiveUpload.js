(function (vita) {
    var transCertNumFiveUpload = vita.Backbone.BizView.extend({
        events: {
        	"click #btn-submit":"_transDealSubmit",
        	
             "click #btn-return" : "_closeBtn",
            
             "click #btn-print" : "_print"
        },
        _initialize: function () {
            var widget = this,
                element = $(widget.el);

        },
        global: {
        	transOneCertNumFiveUploadUrl :"../action/transOneCertNumFiveUpload",
            pageIndex: 1,
            pageSize: 15,
            preset: "date",
            op:"",
            F:"",//回执
            E:"",//其他附件
            E1:"",//证件正面
            E2:"",//证件反面
            E3:""//拍照
        },
        _getConds: function () {
        	debugger;
            var widget = this,element = $(widget.el);;
            var gsession = widget.gSession;
            var data = element.data("data");
            var phoneNum = $("#phoneNum").val();
            var param = {
                collectionItemInfos: data.detail.collectionItemInfos,//需要提交处理集合
                custName: data.detail.custName,//客户姓名
                addressStr: data.detail.custAddress,//客户地址
                certNumber: data.detail.custIdentity,//客户证件号
                certType: data.detail.custType,//客户证件号类型
                orderNbr: data.detail.orderNbr,//订单号码
                areaId: gsession.staffRegionId,//受理地区id
                staffCode: gsession.staffId, //员工编码
                channelNbr: gsession.curChannelNbr,//受理渠道
                contactNbr: phoneNum,//联系方式
                clientIp:data.detail.clientIp,//客户ip
                F:widget.global.F, //回执信息
                E:widget.global.E, //其他附件信息
                E1:widget.global.E1, //正面照信息
                E2:widget.global.E2, //反面照信息
                E3:widget.global.E3, //拍照信息
                remarks: "采集单基本信息", //备注
                
            };
            return param;
        },
        _isNullStr: function (str) {
            if (str == null || str == "") {
                return true;
            }
            return false;
        },
        _clear: function () {
            $("#value").val("");
        },
       
        _fButton:function () {
            var widget = this,element = $(widget.el);
            var filePath = $("#F").val();
            if(filePath=="") {
            	  widget.popup("请选择文件！");
                return ;
            }
            var url = widget.global.transOneCertNumFiveUploadUrl;
            $('#F_form').ajaxSubmit({
                type:"post",url:url, scriptCharset: 'utf-8',
                success:function (date) {
                    var jsonData = JSON.parse(date);
                    if(jsonData.success==0){
                    	widget.global.F=jsonData.data;
                    	widget.popup('上传成功');
                    }else{
                        widget.popup('上传失败，请检查内容是否合法');
                    }
                }
            })
        },
        _eButton:function () {
        	var widget = this,element = $(widget.el);
            var filePath = $("#E").val();
            if(filePath=="") {
            	  widget.popup("请选择文件！");
                return ;
            }
            var url = widget.global.transOneCertNumFiveUploadUrl;
            $('#E_form').ajaxSubmit({
                type:"post",url:url, scriptCharset: 'utf-8',
                success:function (date) {
                    var jsonData = JSON.parse(date);
                    if(jsonData.success==0){
                    	widget.global.E=jsonData.data;
                    	widget.popup('上传成功');
                    }else{
                        widget.popup('上传失败，请检查内容是否合法');
                    }
                }
            })
            
        },
        _e1Button:function () {
        	var widget = this,element = $(widget.el);
            var filePath = $("#E1").val();
            if(filePath=="") {
            	  widget.popup("请选择文件！");
                return ;
            }
            var url = widget.global.transOneCertNumFiveUploadUrl;
            $('#E1_form').ajaxSubmit({
                type:"post",url:url, scriptCharset: 'utf-8',
                success:function (date) {
                    var jsonData = JSON.parse(date);
                    if(jsonData.success==0){
                    	widget.global.E1=jsonData.data;
                    	widget.popup('上传成功');
                    }else{
                        widget.popup('上传失败，请检查内容是否合法');
                    }
                }
            })
        },
        _e2Button:function () {
        	var widget = this,element = $(widget.el);
            var filePath = $("#E2").val();
            if(filePath=="") {
            	  widget.popup("请选择文件！");
                return ;
            }
            var url = widget.global.transOneCertNumFiveUploadUrl;
            $('#E2_form').ajaxSubmit({
                type:"post",url:url, scriptCharset: 'utf-8',
                success:function (date) {
                    var jsonData = JSON.parse(date);
                    if(jsonData.success==0){
                    	widget.global.E2=jsonData.data;
                    	widget.popup('上传成功');
                    }else{
                        widget.popup('上传失败，请检查内容是否合法');
                    }
                }
            })
        },
        _e3Button:function () {
        	var widget = this,element = $(widget.el);
            var filePath = $("#E3").val();
            if(filePath=="") {
            	  widget.popup("请选择文件！");
                return ;
            }
            var url = widget.global.transOneCertNumFiveUploadUrl;
            $('#E3_form').ajaxSubmit({
                type:"post",url:url, scriptCharset: 'utf-8',
                success:function (date) {
                    var jsonData = JSON.parse(date);
                    if(jsonData.success==0){
                    	widget.global.E3=jsonData.data;
                    	widget.popup('上传成功');
                    }else{
                        widget.popup('上传失败，请检查内容是否合法');
                    }
                }
            })
        },
        _transDealSubmit : function(cust) {
        	 var widget = this, element = $(widget.el);
             var gsession = widget.gSession;
             var params = widget._getConds();
             var res = widget._checkParams(params);
             if (res == false) return;
             widget.callService("transCertNumFiveDeal", params, function(res) {
             	widget.popup(res.retDesc);
					return;
             }, {
             	async: false 
             });
        },
    	_closeBtn : function () {
    		 var widget = this,
             element = $(widget.el);
    		 var dialog = element.closest("[data-widgetfullname=vita-dialog]");
    		 if (dialog.length) {
    			 dialog.dialog("close");
    		 }
		},
		_print : function () {
   		 //打印回执
            var widget = this,element = $(widget.el);;
            var gsession = widget.gSession;
            var data = element.data("data");
			var olnbr =data.detail.orderNbr;//订单号码
			var esBusiTypeCd = '1';//电子签名类型（默认传1）
			var areaId = gsession.staffRegionId;//受理地区id
			var partyId = '123456';//虚拟客户id
			var partyName = data.detail.custName;//客户姓名
			var channelId = data.detail.channelNbr;//受理渠道
			var staffId = gsession.staffId; //员工id
			var telephone = $("#phoneNum").val();;//联系方式
			if(telephone==null||telephone==""){
				widget.popup("联系方式不能为空！");
	            return false;
			}
			var certType = data.detail.custType;//客户证件号类型
			var certNum = data.detail.custIdentity;//客户证件号
			var waitPhone =  data.detail.phoneNameStr;//待核实号码
			var isCertFive = "1";//跨省一证五号订单标识 （1表示订单为跨省一证五号订单 0表示其它）
			var es_url = data.detail.serviceUrl;//url
		    var dataAll = [
		                   {'key':'olNbr','value':olnbr},
		                   {'key':'esBusiTypeCd ','value':esBusiTypeCd},
		                   {'key':'areaId','value':areaId},
		                   {'key':'partyId','value':partyId},
		                   {'key':'partyName','value':partyName},
		                   {'key':'channelId','value':channelId},
		                   {'key':'staffId','value':staffId},
		                   {'key':'telephone','value':telephone},
		                   {'key':'certType','value':certType},
		                   {'key':'certNum','value':certNum},
		                   {'key':'waitPhone','value':waitPhone},
		                   {'key':'isCertFive','value':isCertFive}
		                   ];
		    
		    var dataStr= "[{'key':'olNbr','value':'"+olnbr+"'},"+
		    "{'key':'esBusiTypeCd ','value':'"+esBusiTypeCd+"'},"+
            "{'key':'areaId','value':'"+areaId+"'},"+
            "{'key':'partyId','value':'"+partyId+"'},"+
            "{'key':'partyName','value':'"+partyName+"'},"+
            "{'key':'channelId','value':'"+channelId+"'},"+
            "{'key':'staffId','value':'"+staffId+"'},"+
            "{'key':'telephone','value':'"+telephone+"'},"+
            "{'key':'certType','value':'"+certType+"'},"+
            "{'key':'certNum','value':'"+certNum+"'},"+
            "{'key':'waitPhone','value':'"+waitPhone+"'},"+
            "{'key':'isCertFive','value':'"+isCertFive+"'}];";
		    //widget.popup(dataStr);		
		    widget._openWindowWithPost(es_url,'_blank',dataAll);
		                
		},
		_openWindowWithPost:function(url,name,params){
        	var widget = this,el = $(widget.el);
            var oForm = document.createElement("form");
            oForm.id="testid";
            oForm.method="post";
            oForm.action=url;
            oForm.target=name;
            if (params)
            {
                for (var i=0; i < params.length; i++)
                {
                    var oInput = document.createElement("input");
                    oInput.type="hidden";
                    oInput.name=params[i].key;
                    oInput.value=params[i].value;
                    oForm.appendChild(oInput);
                }
            }
            oForm.onSubmit=function(){
                window.open('about:blank',name,'width=700,height=400,menubar=no,scrollbars=no');
			};
            el.append(oForm);
            oForm.submit();
		},
		_checkParams : function (params) {
			if (params.contactNbr.trim().length == 0) {
                this.popup("联系方式不能为空！");
                return false;
            }
//			if (params.F.trim().length == 0) {
//                this.popup("请先上传回执文件！");
//                return false;
//            }
//			if (params.E1.trim().length == 0) {
//                this.popup("请先上传正面照！");
//                return false;
//            }
//			if (params.E2.trim().length == 0) {
//                this.popup("请先上传反面照！");
//                return false;
//            }
//			if (params.E3.trim().length == 0) {
//                this.popup("请先经办人拍照！");
//                return false;
//            }
           
            return true;
		},
        _toCustAuth : function(cust) {}
    });
    vita.widget.register("transCertNumFiveUpload", transCertNumFiveUpload, true);
})(window.vita);