(function (vita) {
    var sensitiveInfoListQuery = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_sensitiveInfoQuery",
            "click #btn-clear": "_clearCond",
            "click #custIdBtn": "_chooseCust",
            "click #channelIdBtn": "_chooseChannel"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
            var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd"
                });
            }
            ;
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd"
                });
            }
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
            widget._initDistConfig();
        },
        global: {
            pageIndex: 1,
            pageSize: 10,
            preset: "date",
            chooseArea: "../comm/chooseArea",
            chooseChannel: "../comm/chooseChannel",
            chooseCust: "../comm/chooseCust",
            auditChannel: "../query/auditChannel"
        },
        _initDistConfig:function(){
            var widget = this,el = $(widget.el);
            var soConst = this.require("soConst");

            var qryPointsData = soConst.getMda("SENSITIVE_INFO_QRY_POINT");
            if(qryPointsData){
                var qryPointsSelect = el.find("[name='qryPointsSelect']");
                qryPointsSelect.append($("<option/>").text("--请选择--"));
                for(var key in qryPointsData){
                    var option = $("<option/>").val(key).text(qryPointsData[key]).data(key, qryPointsData[key]);
                    qryPointsSelect.append(option);
                }
            }

            var qryObjTypeData = soConst.getMda("SENSITIVE_INFO_QRY_OBJ_TYPE");
            if(qryObjTypeData){
                var qryObjTypeSelect = el.find("[name='qryObjTypeSelect']");
                qryObjTypeSelect.append($("<option/>").text("--请选择--"));
                for(var key in qryObjTypeData){
                    var option = $("<option/>").val(key).text(qryObjTypeData[key]).data(key, qryObjTypeData[key]);
                    qryObjTypeSelect.append(option);
                }
            }

        },
        _clearCond: function () {
            var $checked = $(".form-group").find("input[type=checkbox]:checked");
            $.each($checked, function (i,chkInput) {
                chkInput.click();
                var $input = $(chkInput).closest(".form-group").find("input.form-control");
                $input.val("");
                $input.attr("value", null);
                if(chkInput.id == "c_date") {
                    var endDate = $(chkInput).closest(".form-group").next().find("input.form-control");
                    endDate.val("");
                    endDate.attr("value", null);
                }
            })
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _chooseChannel: function () {
            var widget = this,element = $(widget.el);
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseChannel,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);
                }
            });
        },

        _chooseCust: function () {
            var widget = this,element = $(widget.el);
            var compCode = "chooseCust";
            var dialogId = "chooseCustDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseCust,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data || !data.validate) {
                        return false;
                    }
                    element.find("#custId").val(data.custName).attr("value", data.custId);
                }
            });
        },
        _sensitiveInfoQuery: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("querySensitiveInfoList", JSON.stringify(params), "#sensitiveInfoListResult", function (res) {
                    var paging = this.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                var params = widget._getConds();
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if (params) {
                                    widget.refreshPart("querySensitiveInfoList", JSON.stringify(params), "#sensitiveInfoTable");
                                }
                                ;
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }

                }, { async: false});
            }
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this;
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            var paramCnt = 0;

            if ($("#c_channelId").is(":checked")) {
                paramCnt++;
                var channelId = $("#channelId").attr("value");
                if(widget._isNullStr(channelId)) {
                    widget.popup("请选择渠道！");
                    return false;
                } else {
                    params.orgId = channelId;
                }
            }


            if ($("#c_staffCode").is(":checked")) {
                paramCnt++;
                var staffCode = $("#staffCode").val();
                if(widget._isNullStr(staffCode)) {
                    widget.popup("请输入员工编码！");
                    return false;
                } else {
                    params.staffCode = staffCode;
                }
            }

            if ($("#c_custId").is(":checked")) {
                paramCnt++;
                var custId = $("#custId").attr("value");
                if(widget._isNullStr(custId)) {
                    widget.popup("请选择客户！");
                    return false;
                } else {
                    params.custId = custId;
                }
            }

            if ($("#c_qryPoints").is(":checked")) {
                paramCnt++;
                var qryPointsSelect = $("#qryPointsSelect").val();
                if(qryPointsSelect=="--请选择--") {
                    widget.popup("请选择入口类型！");
                    return false;
                } else {
                    params.qryPoint = qryPointsSelect;
                }
            }
            if ($("#c_qryObjType").is(":checked")) {
                paramCnt++;
                var qryObjTypeSelect = $("#qryObjTypeSelect").val();
                if(qryObjTypeSelect=="--请选择--") {
                    widget.popup("请选择对象类型！");
                    return false;
                } else {
                    params.qryObjType = qryObjTypeSelect;
                }
            }
            if ($("#c_qryObj").is(":checked")) {
                paramCnt++;
                var qryObj = $("#qryObj").val();
                if(widget._isNullStr(qryObj)) {
                    widget.popup("请输入实例ID！");
                    return false;
                } else {
                    params.qryObj = qryObj;
                }
            }

            if($("#c_date").is(":checked")) {
                paramCnt++;
                var beginDate = $("#beginDate").val();
                var endDate = $("#endDate").val();
                if (widget._isNullStr(beginDate) || widget._isNullStr(endDate)) {
                    widget.popup("请选择日期范围！");
                    return false;
                } else {
                    params.dateStart = beginDate;
                    params.dateEnd = endDate;
                }
            }
            
            if($("#c_verifyFlag").is(":checked")) {
                paramCnt++;
                var qryVerifyFlag = $("#qryVerifyFlag").val();
                if (widget._isNullStr(qryVerifyFlag)) {
                    widget.popup("请选择是否临时鉴权！");
                    return false;
                } else {
                    params.verifyFlag = qryVerifyFlag;
                }
            }
            return params;
        }
    });
    vita.widget.register("sensitiveInfoListQuery", sensitiveInfoListQuery, true);
})(window.vita);