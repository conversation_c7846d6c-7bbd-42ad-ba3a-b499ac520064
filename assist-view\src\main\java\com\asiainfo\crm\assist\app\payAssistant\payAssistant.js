(function (vita) {
    var payAssistant = vita.Backbone.BizView.extend({
        events: {
            "click #downloadfile": "_download",
        },
        global:{
            url : "http://132.224.23.9:7003/businessBind.do",
            salesStaffCode:null,
        },
        _initialize: function () {
            var widget = this,
                el = $(widget.el);
            var gSession = widget.gSession;
            var salesStaffCode = gSession.salesStaffCode;
            widget.global.salesStaffCode = salesStaffCode;
            widget._chooseChannel();
        },
        _chooseChannel: function() {
            var widget = this,element = $(widget.el);
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            widget.dialog({
                id : dialogId,
                url : "../comm/chooseChannel",
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    var loadUrl = widget.global.url + "?method=load" +
                        "&productno=&inputuid=" + widget.global.salesStaffCode + "&inputorg=" + data.channelNbr;
                    var businessQueryUrl = widget.global.url + "?method=businessQuery&" +
                        "productno=&inputuid=" + widget.global.salesStaffCode + "&inputorg=" + data.channelNbr;
                    var payAssistantLoad = $('<iframe frameborder="0" scrolling="yes" style="width:100%;height:100%" src="'+ loadUrl +'" />');
                    var payAssistantQuery = $('<iframe frameborder="0" scrolling="yes" style="width:100%;height:100%" src="'+ businessQueryUrl +'" />');
                    element.find("#payAssistantLoad").append(payAssistantLoad);
                    element.find("#payAssistantQuery").append(payAssistantQuery);
                }
            });
        },
    });
    vita.widget.register("payAssistant", payAssistant, true);
})(window.vita);