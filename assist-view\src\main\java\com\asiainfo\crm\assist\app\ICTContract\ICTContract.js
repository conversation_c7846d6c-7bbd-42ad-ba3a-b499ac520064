(function (vita) {
    var ICTContract = vita.Backbone.BizView.extend({
        events: {
            "click #searchICTBtn" : "_queryICTContractList",
            "click #searchICTDel": "_searchParamsDel",
            "click .table-hover a": "_ictContractDetail",
        },
        global : {
            ICTContractDetail : "../query/qryICTContractDetail",
            pageSize : 8 //每页记录数
        },
        _initialize : function() {

        },
        _queryICTContractList : function(e) {
            var widget = this,el = $(widget.el);
            var values = widget._getValues();
            var params = widget._getConds(1, values);
            var showPageInfo = el.find("#showPageInfo");
            var recordNumber=widget.global.pageSize;
            showPageInfo.empty();
            widget.refreshPart("queryICTContract", params, ".table-hover",
                "result", function(res) {
                    var totalNumber = $(res).find("a").eq(0).data("totalNumber");
                    if (parseInt(totalNumber) > 0) {
                        var paging = widget.require("paging");
                        var e = paging.new({
                            recordNumber : recordNumber,
                            total : totalNumber,
                            pageIndex : 1,
                            callback : function(pageIndex,recordNumber) {
                                if(pageIndex == null || pageIndex == undefined || isNaN(pageIndex)){
                                    pageIndex = 1;
                                }
                                var params = widget._getConds(pageIndex, values);
                                params.pageInfo.rowCount = totalNumber;
                                widget.refreshPart("queryICTContract", params, ".table-hover", $.noop, {
                                    headers : {
                                        "regionId" : widget.gSession.installArea
                                    }
                                });
                            }
                        });
                        showPageInfo.append(e.getElement());
                    }
                }, {
                    mask : true,
                    headers : {
                        "regionId" : widget.gSession.installArea
                    }
                });
        },
        _ictContractDetail :function(e){
            var widget = this,el = $(widget.el),params={};
            var data = $(e.target).data("data");
            params.ictItemCode=data.ictItemCode;
            params.contractCode=data.contractCode;
            widget.dialog({
                id : "ICTContractDetail",
                url : widget.global.ICTContractDetail,
                params : params,
                onClose : function(res) {
                    // var ICTContractDetail = $(res).closest("[data-widget=ICTContractDetail]");

                }
            });
        },
        //获取查询条件
        _getValues:function(){
            var widget = this,el = $(widget.el),params={};
            params.ictItemCode=widget.model.get("ictItemCode");
            params.ictItemName=widget.model.get("ictItemName");
            params.contractCode=widget.model.get("contractCode");
            params.contractName=widget.model.get("contractName");
            params.clientName=widget.model.get("clientName");
            return params;
        },
        /**
         * 清空查询条件
         */
        _searchParamsDel : function() {
            var widget = this,
                el = $(widget.el);
            //			gSession = widget.gSession;
            widget.model.set("ictItemCode","");
            widget.model.set("ictItemName","");
            widget.model.set("contractCode","");
            widget.model.set("contractName","")
            widget.model.set("clientName","");
            el.find("#ictItemCode").val("");
            el.find("#ictItemName").val("");
            el.find("#contractCode").val("");
            el.find("#contractName").val("");
            el.find("#clientName").val("");
        },
        _getConds : function(pageIndex,values) {
            var widget = this,el = $(widget.el);
            var param = {
                "pageInfo" : {
                    "pageIndex" : pageIndex,
                    "pageSize" : widget.global.pageSize
                }
            };
            return $.extend({},param,values);
        },
    });
    vita.widget.register("ICTContract", ICTContract, true);
})(window.vita);