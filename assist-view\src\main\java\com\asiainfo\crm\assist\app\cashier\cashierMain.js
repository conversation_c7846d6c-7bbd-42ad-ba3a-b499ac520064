(function(vita) {
	var cashierMain = vita.Backbone.BizView.extend({
		events : {
			"click .dropdown-menu" : "_setPayMethod", //批量设置所有的付费方式
			"click button.dim" : "_addChargeItem", //费用科目增加
			"blur input[name='feeAmount']" : "_updateChargeItem", //费用科目修改
			"change [name='busiObjSel']" : "_changeBusiObj", //业务对象
			"change [name='chargeItemSel']" : "_changeChargeItem", //费用科目,
			"click #popbg,button.close" : "_closeAddChargeItemPage",
			"click button.okbutt" : "_submitAddChargeItem",
			"click #addChargeItem" : "_openAddChargeItemPage"
		},
		_initialize : function() {
			var widget = this,
				element = $(widget.el);
			var options = element.data("data");
			widget.model.set(options);
			widget.model.set("chargeInfo", options.chargeData.resultObject.chargeInfo);
		},
		_submitAddChargeItem : function() {
			var widget = this,
				element = $(widget.el);
			var addChargeItem = element.find("[data-widget=addChargeItem]");
			if (addChargeItem.length) {
				var data = addChargeItem.addChargeItem("submit");
				if (!data) {
					return false;
				}
				element.find('#pop,#popbg').removeClass('active');
				data.olId = widget.model.get("chargeInfo").olId;
				widget._saveChargeItem(data);
			}
		},
		//费用增加
		_openAddChargeItemPage : function(e) {
			var widget = this,
				element = $(widget.el);
			var data = {
				pageName : "addChargeItem"
			};
			widget.refreshPart("refreshContent", data, '#contentDiv', "result", function() {
				element.find('#pop,#popbg').addClass('active');
			}, {
				mask : true
			});
		},
		_closeAddChargeItemPage : function() {
			var widget = this,
				element = $(widget.el);
			element.find('#pop,#popbg').removeClass('active');
		},
		//费用科目修改
		_updateChargeItem : function(e) {
			var widget = this,
				element = $(widget.el),
				tar = $(e.target);
			var tr = tar.closest("tr");
			var input = tar.closest("input");
			var chargeItem = input.data("chargeItem");
			var condValue = $.trim(input.val());
			if (!widget._checkValue(condValue, chargeItem)) {
				return false;
			}
			widget._submitChargeItem(condValue, chargeItem);
		},
		//费用科目修改
		_submitChargeItem : function(condValue, chargeItem) {
			var widget = this;
			var chargeInfo = widget.model.get("chargeInfo");
			var charge = parseFloat(condValue) * 100;
			var params = {
//				"infoId" : chargeItem.infoId,
				"charge" : charge + "",
				"olId" : chargeInfo.olId
			};
			widget.callService("updateChargeItem", params, function(data) {
				var ret = JSON.parse(data);
				if (ret.resultCode == "0") {
					widget._showFeeInfo();
				} else {
					widget.popup("修改费用失败" + ret.resultMsg);
				}
			}, {
				async : false
			});
		},
		_addChargeItemObj : function(data) {
			var widget = this,
				element = $(widget.el);
			var div = element.find(".page_main");
			if (!div.length) {
				return false;
			}

			var colDiv = $("<div/>").addClass("col-lg-12");

			var htmlArr = [];

			htmlArr.push('<div class="box-item">');
			htmlArr.push('	<div class="wmin_title">');
			htmlArr.push('		<h5><i class="fa-titile glyphicon text16 mar15"></i>');
			htmlArr.push(data.busiObj.busiObjName + ' ' + data.busiObj.accNbr + ' ' + data.busiObj.serviceOfferName);
			htmlArr.push('		</h5>');
			htmlArr.push('		<div class="wmin-tools">');
			htmlArr.push('			<button class="btn btn-primary dim" type="button"><i class="fa-addproject glyphicon"></i></button>');
			htmlArr.push('		</div>');
			htmlArr.push('	</div>');
			htmlArr.push('	<div class="wmin_content ">');
			htmlArr.push('		<div class="table-responsive collect_tab">');
			htmlArr.push('			<table class="table table-invoice">');
			htmlArr.push('				<tbody>');
			htmlArr.push('					<tr>');
			htmlArr.push('						<td>');
			htmlArr.push(data.chargeItemName);
			htmlArr.push('						</td>');
			htmlArr.push('						<td class="money">');
			htmlArr.push('							<div class="numberinput">');
			htmlArr.push('								<input type="text" value="' + data.oriCharge + '" class="form-control textcolorred"value="8" placeholder="">');
			htmlArr.push('							</div><span class="numberfont">元</span>');
			htmlArr.push('						</td>');
			htmlArr.push('						<td class="money">');
			htmlArr.push('							<select class="form-control" name="payMethodSel">');
			htmlArr.push('							</select>');
			htmlArr.push('						</td>');
			htmlArr.push('					</tr>');
			htmlArr.push('				</tbody>');
			htmlArr.push('			</table>');
			htmlArr.push('		</div>');
			htmlArr.push('	</div>');
			htmlArr.push('</div>');

			colDiv.append(htmlArr.join(''));
			div.append(colDiv);

			var payMethodSel = colDiv.find('select[name="payMethodSel"]');
			var payMethods = data.payMethods;
			$.each(payMethods, function(k, item) {
				if (item.payMethodCd == data.payMethodCd) {
					$("<option selected/>").text(item.name).val(item.payMethodCd).data("data", item).appendTo(payMethodSel);
				} else {
					$("<option/>").text(item.name).val(item.payMethodCd).data("data", item).appendTo(payMethodSel);
				}
			});
		},
		//费用科目新增
		_saveChargeItem : function(data) {
			var widget = this;
			var params = {
				"charge" : data.charge,
				"olId" : data.olId,
				"busiObjId" : data.busiObjId,
				"serviceOfferId" : data.serviceOfferId,
				"chargeItemCd" : data.chargeItemCd,
				"payMethodCd" : data.payMethodCd
			};
			widget.callService("saveChargeItem", params, function(res) {
				var ret = JSON.parse(res);
				if (ret.resultCode == "0") {
					widget._addChargeItemObj(data);
					widget._showFeeInfo();
				} else {
					widget.popup("新增费用失败" + ret.resultMsg);
				}
			}, {
				async : false
			});
		},
		//展示费用统计信息（TODO）
		_showFeeInfo : function() {
			var widget = this,
				element = $(widget.el),
				paidFeeAmount = 0,
				hbFeeAmount = 0,
				realIncomeAmount = 0,
				cartFeeAmount = 0,
				totalTax = 0;

			var pageFood = element.find(".page_food");
			var strong = pageFood.find("strong");

			//已收
			strong.eq(0).html("&yen;" + paidFeeAmount);
			//转账务系统计
			strong.eq(1).html("&yen;" + hbFeeAmount);
			//实收
			strong.eq(2).html("&yen;" + realIncomeAmount);
			//总计 应收
			strong.eq(3).html("&yen;" + cartFeeAmount);
			//税金
			pageFood.find(".textcolor9").html(" (含税金: ¥" + totalTax + " ) ");
		},
		_checkValue : function(condValue, chargeItem) {
			var widget = this;
			var chargeInfo = widget.model.get("chargeInfo");
			if (chargeInfo.hasStaffAdjustPermition == "N") {
				widget.popup("您没有费用修改的权限");
				return false;
			}
//			if (chargeItem.hasStaffAdjustPermition == "N") {
//				widget.popup(chargeItem.acctItemTypeName + "不允许修改费用");
//				return false;
//			}
			var soUtil = widget.require("soUtil");
			var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
			if (isNullOrEmpty) {
				widget.popup("请输入价格");
				return false;
			}
			//判断是否为数字以及小数点后是否为2位
			if (condValue.indexOf(".") > 0) {
				var nums = condValue.split(".");
				if (nums[1].length > 2) {
					widget.popup("小数点后最多为2位");
					return false;
				}
			}
//			if (parseFloat(condValue) == parseFloat(chargeItem.feeAmount)) {
//				widget.popup("您修改的费用与修改前一致，不能提交");
//				return false;
//			}
			return true;
		},
		//批量设置所有的付费方式
		_setPayMethod : function(e) {
			var widget = this,
				element = $(widget.el);
			var tarLi = $(e.target).closest("li");
			var curPayMethod = tarLi.data("payMethod");
			var curPayMethodCd = curPayMethod.payMethodCd;
			var select = element.find('select[name="payMethodSel"]');
			if (select.length) {
				select.each(function() {
					var item = $(this);
					var payMethods = item.data("payMethods");
					var isIn = false; // 费用科目对应可以设置的付费方式
					if (payMethods.length) {
						$.each(payMethods, function(key, obj) {
							if (obj.payMethodCd == curPayMethodCd) {
								isIn = true;
								return false;
							}
						});
						var selected = item.val();
						if (isIn && selected != curPayMethodCd) {
							item.val(curPayMethodCd);
						}
					}
				});
			}
		},
		//费用科目增加
		_addChargeItem : function(e) {
			var widget = this,
				element = $(widget.el);
			var button = $(e.target).closest("button");
			var div = button.closest(".col-lg-12");
			var charge = div.data("charge");

			if (!div.length || !charge) {
				return false;
			}
			var result = widget._queryConfigs(charge.busiSpecId);
			if (!result) {
				return false;
			}
			var chargeItems = result.chargeItems;
			var payMethods = result.payMethods;

			var htmlArr = [];
			htmlArr.push('<tr>');
			htmlArr.push('	<td class="money">');
			htmlArr.push('		<select class="form-control" name="chargeItemSel">');
			htmlArr.push('		</select>');
			htmlArr.push('	</td>');
			htmlArr.push('	<td class="money">');
			htmlArr.push('		<div class="numberinput">');
			htmlArr.push('			<input name="feeAmount" type="text" class="form-control textcolorred" value="" placeholder="">');
			htmlArr.push('		</div><span class="numberfont">元</span>');
			htmlArr.push('	</td>');
			htmlArr.push('	<td class="money">');
			htmlArr.push('		<select class="form-control" name="payMethodSel">');
			htmlArr.push('		</select>');
			htmlArr.push('	</td>');
			htmlArr.push('</tr>');

			div.find('tbody').append(htmlArr.join(''));

			var chargeItemSel = div.find('select[name="chargeItemSel"]:last');
			$.each(chargeItems, function(k, item) {
				$("<option/>").text(item.name).val(item.chargeItemCd).data("data", item).appendTo(chargeItemSel);
			});
			var payMethodSel = div.find('select[name="payMethodSel"]:last');
			$.each(payMethods, function(k, item) {
				$("<option/>").text(item.name).val(item.payMethodCd).data("data", item).appendTo(payMethodSel);
			});
		},
		_changeBusiObj : function(e) {
			var widget = this,
				element = $(widget.el);
			var select = $(e.target).closest("select");
			var selected = select.find("option:selected");
			var selectedData = selected.data("data");

			if (!selectedData) {
				return false;
			}
			var result = widget._queryConfigs(selectedData.busiSpecId);
			if (!result) {
				return false;
			}
			var chargeItems = result.chargeItems;
			var payMethods = result.payMethods;

			//费用科目初始化
			var div = select.closest("col-lg-12");
			var selectChargeItem = div.find('select[name="chargeItemSel"]');
			selectChargeItem.empty();
			var itemArr = [];
			$.each(chargeItems, function(k, item) {
				itemArr.push('<option data=' + item + ' value="' + item.chargeItemCd + '">' + item.name + '</option>');
			});
			selectChargeItem.append(itemArr.join(''));

			//付费方式初始化
			var payMethodSel = div.find('select[name="payMethodSel"]');
			payMethodSel.empty();
			var payArr = [];
			$.each(payMethods, function(k, item) {
				payArr.push('<option data=' + item + ' value="' + item.payMethodCd + '">' + item.name + '</option>');
			});
			payMethodSel.append(payArr.join(''));
		},
		_changeChargeItem : function(e) {
			var widget = this,
				element = $(widget.el);
			var select = $(e.target).closest("select");
			var selected = select.find("option:selected");
			var selectedData = selected.data("data");

			if (!selectedData) {
				return false;
			}
			var payMethods = widget._queryPayMethods(selectedData.chargeItemCd);
			if (!payMethods || !payMethods.length) {
				widget.popup("联系维护人员配置付费方式后再操作");
				return false;
			}
			var tr = select.closest("tr");
			var payMethodSel = tr.find('select[name="payMethodSel"]');
			payMethodSel.empty();
			var payArr = [];
			$.each(payMethods, function(k, item) {
				payArr.push('<option data=' + item + ' value="' + item.payMethodCd + '">' + item.name + '</option>');
			});
			payMethodSel.append(payArr.join(''));
		},
		_queryChargeItems : function(objId) {
			var widget = this,
				chargeItems = [];
			var params = {
				//产品规格
				"objId" : objId
			};
			widget.callService("queryChargeItems", params, function(data) {
				var ret = JSON.parse(data);
				if (ret.resultCode == "0") {
					var resultObject = ret.resultObject;
					if (resultObject && resultObject.chargeItems) {
						chargeItems = resultObject.chargeItems;
					}
				} else {
					widget.popup("查询费用项目异常:" + ret.resultMsg);
				}
			}, {
				async : false,
                headers : {
                    "regionId" : widget.gSession.installArea
                }
			});
			return chargeItems;
		},
		_queryPayMethods : function(chargeItemCd) {
			var widget = this,
				payMethods = [];
			var params = {
				//科目规格
				"chargeItemCd" : chargeItemCd
			};
			widget.callService("queryPayMethods", params, function(data) {
				var ret = JSON.parse(data);
				if (ret.resultCode == "0") {
					var resultObject = ret.resultObject;
					if (resultObject && resultObject.payMethods) {
						payMethods = resultObject.payMethods;
					}
				} else {
					widget.popup("查询付费方式异常:" + ret.resultMsg);
				}
			}, {
				async : false,
                headers : {
                    "regionId" : widget.gSession.installArea
                }
			});
			return payMethods;
		},
		_queryBusiObjs : function(olId) {
			var widget = this,
				busiObjs = [];
			var params = {
				"olId" : olId
			};
			widget.callService("queryBusiObjs", params, function(data) {
				var ret = JSON.parse(data);
				if (ret.resultCode == "0") {
					var resultObject = ret.resultObject;
					if (resultObject && resultObject.busiObjs) {
						busiObjs = resultObject.busiObjs;
					}
				} else {
					widget.popup("查询业务对象异常:" + ret.resultMsg);
				}
			}, {
				async : false,
				headers : {
					"regionId" : widget.gSession.installArea
				}
			});
			return busiObjs;
		},
		//查询费用、付费方式配置
		_queryConfigs : function(busiSpecId) {
			var widget = this;
			var busiSpecId = parseInt(busiSpecId) || 0;
			if (!busiSpecId) {
				return false;
			}
			var chargeItems = widget._queryChargeItems(busiSpecId);
			if (!chargeItems || !chargeItems.length) {
				widget.popup("联系维护人员配置费用名称后再操作");
				return false;
			}
			var payMethods = widget._queryPayMethods(chargeItems[0].chargeItemCd);
			if (!payMethods || !payMethods.length) {
				widget.popup("联系维护人员配置付费方式后再操作");
				return false;
			}
			var result = {
				chargeItems : [],
				payMethods : []
			};
			result.chargeItems = chargeItems;
			result.payMethods = payMethods;
			return result;
		}
	});
	vita.widget.register("cashierMain", cashierMain, true);
})(window.vita);