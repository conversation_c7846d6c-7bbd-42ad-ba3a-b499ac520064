package com.asiainfo.crm.assist.app.action;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by liyong on 2018/03/10.
 */
@Component("vita.configCustOfferService")
public class ConfigCustOfferService extends AbstractComponent {
    @Autowired
    private ISoQuerySMO soQuerySMO;

    private static final Logger logger = LoggerFactory.getLogger(ConfigCustOfferService.class);

    @Override
    public Map achieveData(Object... objects) throws Exception {
        Map options = new HashMap();
        List valueList = soQuerySMO.queryBoActionTypeCdByNum();
        options.put("serviceOfferIdsQuery",valueList);
        return options;
    }

    /**
     * 查询指定客户的限制数据
     * @param json
     * @return
     * @throws Exception
     */
    public Map configCustProdlnstQuery(String json) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        String resultJson = soQuerySMO.configCustProdlnstQuery(json);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(resultJson);
        List resultList = (List) MapUtils.getObject(resultObjectMap, "custServLimitCfgs");
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap, "pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount", pageCount);
        options.put("pageIndex", pageIndex);
        options.put("totalNumber", total);
        options.put("custServLimitCfgs", resultList);
        return options;
    }

    /**
     * 新增限制业务动作
     * @param json
     * @return
     * @throws Exception
     */
    public Map addCustProdlnst(String json)throws Exception{
        String projectDetails = soQuerySMO.addCustProdlnst(json);
        Map<String, Object> resultObjectMap =new HashedMap();
        Map<String, Object> paramMaps = jsonConverter.toBean(projectDetails,Map.class);
        Map<String,Object> resMap =(Map) paramMaps.get("resultObject");
        if(resMap.get("resultCode").equals("0")){
            resultObjectMap.put("resultCode",0);
            resultObjectMap.put("msg",resMap.get("msg"));
        }else{
            resultObjectMap.put("resultCode",-1);
            resultObjectMap.put("msg",resMap.get("msg"));
        }
        return resultObjectMap;
    }

    /**
     * 批量删除限制业务动作
     * @param json
     * @return
     */
    public Map deleteCustOfferService(String json){
        String projectDetails = soQuerySMO.updateCustOfferService(json);
        Map rets = new HashMap();
        rets.put("delectCustServLimitCfgs",projectDetails);
        return rets;
    }

}
