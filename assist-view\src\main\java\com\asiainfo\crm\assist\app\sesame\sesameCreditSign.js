(function (vita) {
    var sesameCreditSign = vita.Backbone.BizView.extend({
        events: {
            "click .nextbutt" : "_nextClick",
            "click #btn_sign_verify" : "_signClick"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            widget._genQRCode();
            var hasSigned = false;
            widget.model.set("hasSigned", hasSigned);
        },
        _signClick: function() {
            debugger;
            var widget = this, el = $(widget.el);
            el.find("#btn_sign_verify").attr("disabled", true);
            var gsession = widget.gSession;
            var transactionId = el.find("#transactionId").data("transactionId");
            if(transactionId == null || transactionId == "") {
                widget.popup("芝麻征信数字工单ID为空，请按步骤受理！");
                return false;
            }
            var params = {
                transactionId : transactionId,
                staffId : gsession.staffId,
                regionId : gsession.staffRegionId,
                channelId : gsession.curChannelId,
                step : "4"
            };
            widget.callService("sesameCreditAction", JSON.stringify(params), function(res) {
                if (res.handleResultCode != 0) {
                    widget.popup(res.handleResultMsg);
                    widget.model.set("hasSigned", false);
                    el.find("#btn_sign_verify").removeAttr("disabled");
                }else{
                    widget.popup(res.handleResultMsg);
                    widget.model.set("hasSigned", true);
                }
            },{ async : false });
        },
        _nextClick: function () {
            var widget = this, el = $(widget.el);
            var hasSigned = widget.model.get("hasSigned");
            if(!hasSigned) {
                widget.popup("请先完成代扣签约结果校验！");
                return false;
            }
            var transactionId = el.find("#transactionId").data("transactionId");
            this.end("sesameCreditLabelBind", {"transactionId":transactionId});
        },
        _genQRCode : function () {
            var widget = this, element = $(widget.el);
            debugger;
            var sesameSignUrl = element.data("vita-data");
            //这里取出来的地址不对，，
            //https://ur.alipay.com/6Or
            //需要转义  TODO 转义出来也不对，，等剑剑回复

            sesameSignUrl = "https://ur.alipay.com/6Or";
            var options = {
                render: 'canvas',
                minVersion: 6,
                maxVersion: 40,
                ecLevel: 'L',
                left: 0,
                top: 0,
                //width       : 280,     //设置宽度
                //height      : 260,     //设置高度
                size: 320,
                fill: '#000',
                background: '#fff',
                text: widget._toUtf8(sesameSignUrl),
                radius: 0,
                quiet: 0,
                mode: 0,
                mSize: 0.1,
                mPosX: 0.5,
                mPosY: 0.5,
                label: '',
                fontname: 'sans',
                fontcolor: '#000',
                image: null
            };
            $("#signDivImg").empty().qrcode(options);
        },
        _toUtf8 : function(str){
            var out, i, len, c;
            out = "";
            len = str.length;
            for(i = 0; i < len; i++) {
                c = str.charCodeAt(i);
                if ((c >= 0x0001) && (c <= 0x007F)) {
                    out += str.charAt(i);
                } else if (c > 0x07FF) {
                    out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F));
                    out += String.fromCharCode(0x80 | ((c >>  6) & 0x3F));
                    out += String.fromCharCode(0x80 | ((c >>  0) & 0x3F));
                } else {
                    out += String.fromCharCode(0xC0 | ((c >>  6) & 0x1F));
                    out += String.fromCharCode(0x80 | ((c >>  0) & 0x3F));
                }
            }
            return out;
        }

    });
    vita.widget.register("sesameCreditSign", sesameCreditSign, true);
})(window.vita);