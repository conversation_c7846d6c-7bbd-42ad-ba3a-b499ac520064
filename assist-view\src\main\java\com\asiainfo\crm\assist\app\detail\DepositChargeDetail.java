package com.asiainfo.crm.assist.app.detail;

import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IBillingSmo;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 违约金预查
 * create by liyc7
 */
@Component("vita.depositChargeDetail")
public class DepositChargeDetail extends AbstractSoComponent {

    @Autowired
    private IBillingSmo billingSmo;

    @Override
    public Map achieveData(Object... objects) throws Exception {
        return null;
    }

    /**
     * 违约金预查
     * @param params
     * @return
     * @throws Exception
     */
    public Map qryDepositDetail(String params) throws Exception {
        Map<String,Object> options = new HashMap<>();
//        String respStr = billingSmo.queryDepositChargeDetail(params);
        StringBuffer url = new StringBuffer(MDA.CASHIER_SYS_URL.get("depositCharge"));
        Map<String,Object> paramMap = jsonConverter.toMap(params,String.class,Object.class);
        url.append("?accNum=").append(MapUtils.getString(paramMap,"accNum"));
      //  url.append("&regionId=").append(MapUtils.getString(paramMap,"regionId"));

        options.put("url", url.toString());
        return options;
    }
}
