(function(vita) {
    var reserveTerminalQuery = vita.Backbone.BizView.extend({
        events : {
            "click #channelIdBtn": "_chooseChannelClick",
            "click #regionIdBtn": "_chooseArea",
            "click #btn-query": "_iphoneReserveQuery",
            "click #delIphoneReserve":"_delIphoneReserve"
        },
        _initialize : function() {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
        },
        global: {
            pageIndex: 1,
            pageSize: 100,
            chooseArea: "../comm/chooseArea",
            chooseChannel: "../comm/chooseChannel"
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _chooseChannelClick: function() {
            var widget = this,element = $(widget.el);
            widget._chooseChannel(false);
        },
        _chooseChannel: function(isNeedRefreshSession) {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseChannel,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);

                    if(isNeedRefreshSession) {
                        gSession.channelId   = data.orgId;
                        gSession.curChannelId= data.orgId;
                        gSession.curChannelNbr  = data.channelNbr;
                        gSession.channelName = data.channelName;
                        gSession.channelRegionId = data.channelRegionId;
                        gSession.channelRegionName = data.channelRegionName;
                        gSession.channelClass = data.channelClass;
                        gSession.orgId       = data.orgId;
                        gSession.orgName     = data.orgName;

                        gSession.orderArea   	 = data.channelRegionId; // 订单地区
                        gSession.installArea 	 = data.channelRegionId; // 安装地区
                        gSession.installAreaName = data.channelRegionName;
                        var param = {
                            curChannelId  : data.orgId,
                            curChannelNbr : data.channelNbr,
                            orderArea     : gSession.orderArea,
                            installArea   : gSession.installArea
                        };
                        widget.refreshSession(param);
                    }
                }
            });
        },
        _getConds: function () {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var params = {
                staffId: gSession.staffId,
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize,
                }

            };
            var regionId = element.find("#regionId").attr("value");
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.regionId = regionId;
            }

            var channelId = element.find("#channelId").attr("value");
            if(widget._isNullStr(channelId)) {
                widget.popup("请选择渠道！");
                return false;
            } else {
                params.channelId = channelId;
            }

            var certNum = $.trim(element.find("#certNum").val());
            var reserveCode = $.trim(element.find("#reserveCode").val());
            if(widget._isNullStr(certNum) && widget._isNullStr(reserveCode)) {
                widget.popup("证件号码和预约终端号不能全部为空！");
                return false;
            }
            if(!widget._isNullStr(certNum)) {
                var certTypeSelect = element.find("#certType");
                params.certType = certTypeSelect.find("option:selected").val();
                params.certNum = certNum;
            }
            if(!widget._isNullStr(reserveCode)) {
                params.reserveCode = reserveCode;
            }
            return params;
        },
        _iphoneReserveQuery:function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("queryIphoneReserve", JSON.stringify(params), "#orderListResult", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if (widget._getConds()) {
                                    widget.refreshPart("queryIphoneReserve", JSON.stringify(params), "#orderList");
                                }
                                ;
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }

                }, {
                    async: false
                });
            } ;
        },
        _delIphoneReserve:function (e) {
            var widget = this;
            var orderListInfo = $(e.target).data("orderListInfo");
            var reserveStatus=orderListInfo.reserveStatus;
            var paymentId=orderListInfo.paymentId;
            var ifPayment=orderListInfo.ifPayment;
            //判断终端状态是否为可取消状态
            if(widget._isNullStr(reserveStatus)||reserveStatus!="1000"){
                widget.popup("取消预约失败：终端不为已预约状态不可取消");
                return;
            }
            var param = {
                reserveTerminalId:orderListInfo.reserveTerminalId
            };
            var params={
                target:$(e.target),
                param:param
            };
            // 判断是否已收费，已收费，调用退费页面
            // 未收费，直接更新终端预约信息
            // 调用失败，给出提示信息
            if(ifPayment!=0){
                params.paymentId=paymentId;
                params.custName = orderListInfo.custName;
                widget._simpleCashierDialog(params)
            }else{
                widget._updateReserve(params);
            }
        },
        _simpleCashierDialog:function(e){
            var widget = this,element = $(widget.el);
            var dialog = "simpleCashierDialog";
            var params={
                busiName:encodeURI("终端预约退费","utf-8"),
                custName:encodeURI(e.custName,"utf-8"),
                action:"del",
                requestId:e.paymentId
            };
            params={
                jsonStr:JSON.stringify(params)
            };
            widget.dialog( {
                id:dialog,
                url: element.data("data").url,
                params:params,
                onClose: function (res) {
                    //判断是否退费成功，退费成功，更新预约信息，错误给出提示
                    var bool=true;
                    var simpleCashier= $(res).closest("[data-widget=simpleCashier]");
                    if (simpleCashier.length) {
                        var data = simpleCashier.simpleCashier("getValue");
                        if (!data || data.resultCode != 0) {
                            widget.popup("退费失败:"+data.resultMsg);
                            bool=false;
                        }
                        if(bool){
                            widget._updateReserve(e);
                        }
                    }
                }
            });
        },
        _updateReserve:function(e){
            var widget = this;
            widget.callService("delIphoneReserve", JSON.stringify(e.param),function (res) {
                if(res.resultCode == 0){
                    // $(e.target).closest("tr").hide();
                    e.target.closest("tr").hide();
                }else {
                    widget.popup("取消预约失败："+res.resultMsg);
                }
            }, {
                async: false
            });
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        }
    });
    vita.widget.register("reserveTerminalQuery", reserveTerminalQuery, true);
})(window.vita);