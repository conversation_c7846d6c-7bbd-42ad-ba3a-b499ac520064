<div data-widget="orderCheck" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>订单报文校验</div>
                                </div>

                                <div class="wmin_content row" style="text-align: center">
                                    <form class=" form-bordered">

                                        <div class="form-group col-md-6 "    style="margin-right: 28%;margin-left: 28%" >
                                            <div class="col-md-2" >
                                                <select id="isQryHis" class="form-control">
                                                    <option value="0">订单id</option>
                                                    <option value="1">订单流水</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6" >
                                                <input id="accNum" type="text" class="form-control" placeholder="">
                                            </div>
                                            <div class="col-md-4 searchbutt_r" align="left">
                                                <button id="btn-query"   type="button" class="btn btn-primary">信息查询</button>
<!--                                                    先去除流程查询功能-->
<!--                                                <button id="btn-buildOrderNode"   type="button" class="btn btn-primary">流程查询</button>-->
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12 mart10" id="orderListResult">

                                        <input style="display: none" id="func"  value="$options.func">
                                        <input style="display: none" id="str"  value="$options.str">

                                        #if($options != "null"  && $options.isData)
                                        <table class="table table-hover">
                                            <h4 style="text-align: center">未查询出数据</h4>
                                        </table>
                                        #end


                                        #if($options != "null" && $options.errItems && $options.errItems != "null" &&
                                        $options.errItems.size() > 0)
                                        <table class="table table-hover">
                                            <h4 style="text-align: center">修复引导</h4>
                                            <thead>
                                            <tr>
                                                <th class="col-md-4">检测项</th>
                                                <th class="col-md-4">说明</th>
                                                <th class="col-md-2">值</th>
                                                <th class="col-md-2" style="text-align: center">操作</th>
                                            </tr>
                                            </thead>
                                            <tbody id="orderList">

                                            #foreach($errItem in $options.errItems)
                                            <tr>
                                                <td>
                                                    #if($errItem.errCode != "null" &&  $errItem.errCode == "ER110003")
                                                        #foreach($orderItem in $errItem.orderItems)
                                                                $!{orderItem.orderItemId}
                                                                &nbsp     $!{orderItem.serviceOfferName}
                                                                &nbsp     $!{orderItem.applyObjSpecName}<br>
                                                        #end
                                                    #elseif($errItem.errCode != "null" &&  $errItem.errCode == "ER110002")
                                                        #foreach($orderItemId in $errItem.orderItemIds)
                                                            $!{orderItemId}
                                                        #end
                                                    #else
                                                        $!{errItem.objIdType}
                                                        &nbsp     $!{errItem.errObjId}
                                                        &nbsp     $!{errItem.serviceOfferName}
                                                        &nbsp     $!{errItem.applyObjSpecName}
                                                    #end
                                                </td>
                                                <td>
                                                       $!{errItem.errMsg}
                                                </td>
                                                <td>
                                                      $!{errItem.errCode}
                                                </td>
                                                <td style="text-align: center">

                                                    <button value="$errItem.errCode" name="repairName" type="button" class="btn btn-primary">修复</button>
                                                    <input style="display: none"   value="$!{errItem.errObjId}">
                                                    <input style="display: none"    value="#foreach($Key in $!{errItem.premaryKey})$!{Key},#end">
                                                    <input style="display: none"    value="#foreach($Key in $!{errItem.orderItemIds})$!{Key},#end">
                                                    <input style="display: none"    value="$errItem.errType">
                                                    <input style="display: none"    value="$!{errItem.objIdType}">
                                                </td>

                                            </tr>
                                            #end
                                            </tbody>
                                        </table>
                                        #end



                                        #if($options != "null" &&  $options.isMessageAnalysis)
                                        <table class="table table-hover">
                                            <h4 style="text-align: center">报文解析</h4>
                                            <thead>
                                            <tr>
                                                <th class="col-md-4">检测项</th>
                                                <th class="col-md-4">说明</th>
                                                <th class="col-md-2">值</th>

                                            </tr>
                                            </thead>
                                            <tbody >
                                            #if($options.statusCd!="null")
                                            <tr>
                                                <td>
                                                    statusCd
                                                </td>
                                                <td>
                                                    订单状态
                                                </td>
                                                <td>
                                                      $!{options.statusCd}
                                                </td>

                                            </tr>
                                            #end
                                            #if($options.statusDate != "null" )
                                            <tr>
                                                <td>
                                                    statusDate
                                                </td>
                                                <td>
                                                    时间日期
                                                </td>
                                                <td>
                                                    $!{options.statusDate}
                                                </td>


                                            </tr>
                                            #end
                                            #if($options.custOrderId != "null" )
                                            <tr>

                                                <td>
                                                    custOrderId
                                                </td>
                                                <td>
                                                    订单号
                                                </td>
                                                <td>
                                                    $options.custOrderId
                                                </td>

                                            </tr>
                                            #end
                                            #if($options.custId != "null" )
                                            <tr>

                                                <td>
                                                    custId
                                                </td>
                                                <td>
                                                    客户ID
                                                </td>
                                                <td>
                                                    $options.custId
                                                </td>

                                            </tr>
                                            #end
                                            #if($options.custOrderNbr != "null" )
                                            <tr>
                                                <td>
                                                    custOrderNbr
                                                </td>
                                                <td>
                                                    订单流水
                                                </td>
                                                <td>
                                                    $options.custOrderNbr
                                                </td>

                                            </tr>
                                            #end
                                            #if($options.offerObjInstId != "null" )
                                            <tr>
                                                <td>
                                                    offerObjInstId
                                                </td>
                                                <td>
                                                    实例ID
                                                </td>
                                                <td>
                                                    $options.offerObjInstId
                                                </td>

                                            </tr>
                                            #end
                                            #if($options.isSync != "null" )
                                            <tr>
                                                <td>
                                                    isSync
                                                </td>
                                                <td>
                                                    是否同步
                                                </td>
                                                <td>
                                                    $options.isSync
                                                </td>

                                            </tr>
                                            #end
                                            #if($options.isPay != "null" )
                                            <tr>
                                                <td>
                                                    isPay
                                                </td>
                                                <td>
                                                    是否支付
                                                </td>
                                                <td>
                                                    $options.isPay
                                                </td>

                                            </tr>
                                            #end
                                            </tbody>
                                        </table>
                                        #end



                                        #if($options != "null" && $options.prodOrderItems && $options.prodOrderItems != "null" &&
                                        $options.prodOrderItems.size() > 0)
                                        <table class="table table-hover">
                                            <h4 style="text-align: center">产品订单项(prodOrderItems)</h4>
                                            <thead>
                                            <tr>
                                                <th class="col-md-4">检测项</th>
                                                <th class="col-md-4">说明</th>
                                                <th class="col-md-2" >值</th>

                                            </tr>
                                            </thead>
                                            <tbody >

                                            #foreach($prodOrderItem in $options.prodOrderItems)
                                            <tr>
                                                <td>
                                                    产品状态statusCd
                                                </td>
                                                <td>
                                                    $prodOrderItem.applyObjSpecName --- orderItemId:  $prodOrderItem.orderItemId
                                                </td>
                                                <td >
                                                    $prodOrderItem.statusCd
                                                </td>

                                            </tr>
                                            #end
                                            </tbody>
                                        </table>
                                        #end


                                        #if($options != "null" && $options.offerOrderItems && $options.offerOrderItems != "null" &&
                                        $options.offerOrderItems.size() > 0)
                                        <table class="table table-hover">
                                            <h4 style="text-align: center">销售品项(offerOrderItems)</h4>
                                            <thead>
                                            <tr>
                                                <th class="col-md-4">检测项</th>
                                                <th class="col-md-4">说明</th>
                                                <th class="col-md-2">值</th>

                                            </tr>
                                            </thead>
                                            <tbody >

                                            #foreach($offerOrderItem in $options.offerOrderItems)
                                            <tr>
                                                <td>
                                                    销售品状态statusCd
                                                </td>
                                                <td>
                                                    $offerOrderItem.applyObjSpecName --- orderItemId:  $offerOrderItem.orderItemId
                                                </td>
                                                <td >
                                                    $offerOrderItem.statusCd
                                                </td>


                                            </tr>
                                            #end
                                            </tbody>
                                        </table>
                                        #end

                                        #if($options != "null" && $options.analysisList && $options.analysisList != "null" &&
                                        $options.analysisList.size() > 0 )
                                        <table class="table table-hover">
                                            <h4 style="text-align: center">异常信息检测</h4>
                                            <thead>
                                            <tr>
                                                <th class="col-md-2">信息名称</th>
                                                <th class="col-md-2">消息名称</th>
                                                <th class="col-md-6" style="text-align: center" >说明</th>


                                            </tr>
                                            </thead>
                                            <tbody >

                                            #foreach($analysis in $options.analysisList)
                                            <tr>
                                                <td>
                                                    $analysis.msgName
                                                </td>
                                                <td>
                                                    $analysis.msgTopic
                                                </td>
                                                <td style="word-break:break-all;">
                                                    #foreach($exception in $analysis.exceptions)
                                                       $exception.exception
                                                    #end
                                                </td>
                                            </tr>
                                            #end
                                            </tbody>
                                        </table>
                                        #end
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
