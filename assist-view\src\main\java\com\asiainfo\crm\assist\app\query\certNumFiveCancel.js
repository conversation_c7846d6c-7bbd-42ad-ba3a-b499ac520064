(function(vita) {
	var certNumFiveCancel = vita.Backbone.BizView.extend({
		events : {
			"click #btn-certNumCancel" : "_certNumCancel"
		},
		_initialize : function() {
			var widget = this, element = $(widget.el);
		},

		_getConds : function() {
		},

		_certNumCancel : function() {
			var widget = this;
            var gsession = widget.gSession;
            var phone = $("#phone").val();
            var certNum = $("#certNum").val();
            var certAddress = $.trim($("#certAddress").val());
            var companyOrder = $("#companyOrder").val();
            var remark = $("#remark").val();
            var res = widget._checkParams(phone,certNum,certAddress,companyOrder);
            if (res == false) return;
            var params = {
            	phoneNum: phone,
            	certNum: certNum,
            	certAddress: certAddress,
            	companyOrder: companyOrder,
            	remark: remark,
            	serviceType:1800,
            	actionType:12,
            	businessType:2,
            	lanId: gsession.staffLanId,
            	channelNbr: gsession.curChannelNbr,
            	staffCode : gsession.staffCode,
            	glbSessionId: gsession.glbSessionId
            };
			widget.callService("exeForceCancelRelation", params, function(res) {
					widget.popup(res.retDesc)
					return;
			}, {
				mask:true,
				async: true
			});
		},
		
		_checkParams: function(phone,certNum,certAddress,companyOrder) {
			 if (phone.trim().length == 0) {
	                this.popup("请输入手机号码！");
	                return false;
	            }
	            if (certNum.trim().length == 0) {
	                this.popup("请输入身份证号！");
	                return false;
	            }
	            if (certNum.trim().length == 0) {
	                this.popup("请输入身份证号！");
	                return false;
	            }
	            if (!certAddress) {
	            	this.popup("请输入证件地址！");
	            	return false;
	            }
	            if (companyOrder.trim().length == 0) {
	                this.popup("请输入集团流水号！");
	                return false;
	            }
	            if (20>companyOrder.trim().length) {
	                this.popup("集团流水号长度不正确！");
	                return false;
	            }
	            return true;
		}
	});
	vita.widget.register("certNumFiveCancel", certNumFiveCancel, true);
})(window.vita);