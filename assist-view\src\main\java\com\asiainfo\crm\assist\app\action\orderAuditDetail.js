(function (vita) {
    var orderAuditDetail = vita.Backbone.BizView.extend({
        events: {
            "click [name=orderAuditBtn]": "_orderAuditBtnClick", //稽核点击事件
            "click #closeBtn": "_closePage",//关闭页面
            "change select[name='auditResult']": "_DealAuditResult",//审核结果控制原因显示
            "change select[name='auditResultReason']": "_DealOtherReason",//选择其他原因显示输入原因
            "click #audit-btn": "_DealAudit"//审核结果提交
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            widget._DealAuditResult();
        },
        _closePage: function () {
            var widget = this,
                element = $(widget.el);
            element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
        },
        _DealAuditResult: function () {
            var widget = this,
                element = $(widget.el);
            var select = element.find("select[name='auditResult']");
            var option = select.find("option:selected");
            var result = option.val();
            var reasonSelectDiv = element.find("div[name='auditResultReasonDiv']");
            var reasonSelect = element.find("select[name='auditResultReason']");
            if (result == "3") {
                element.find("#auditFailPrompt").html("*对于审核不通过的，可以手工撤单");
                reasonSelectDiv.show();
            } else {
                element.find("#auditFailPrompt").html("");
                reasonSelectDiv.hide();
                reasonSelect.find("option:selected").removeAttr("selected");
                reasonSelect.find("option:first-child").attr("selected", "selected");
                element.find("div[name='otherReasonDiv']").hide();
                element.find("input[name='otherReason']").val("");
            }
        },
        _DealOtherReason: function () {
            var widget = this,
                element = $(widget.el);
            var reasonSelect = element.find("select[name='auditResultReason']");
            var option = reasonSelect.find("option:selected");
            var otherDiv = element.find("div[name='otherReasonDiv']");
            var otherInput = otherDiv.find("input[name='otherReason']");
            if (option.text() == "其他") {
                otherDiv.show();
            } else {
                otherDiv.hide();
                otherInput.val("");
            }
        },
        _DealAudit: function (e) {
            var widget = this,
                element = $(widget.el);
            var params = widget._getConds(e);
            if (params) {
                params.glbSessionId = widget.gSession.glbSessionId;
                widget.callService("audit", JSON.stringify(params), function (ret) {
                    if (ret.code == "0") {
                        widget.popup(ret.desc);
                        widget._closePage();
                    } else {
                        widget.popup(ret.desc);
                    }
                });
            }
        },
        _getConds: function (e) {
            var widget = this;
            var element = $(widget.el);
            var btn = $(e.currentTarget);
            var custOrderId = btn.data("custOrderId");
            var custOrderNbr = btn.data("custOrderNbr");
            var auditResultSelect = element.find("select[name='auditResult']");
            var auditResultOption = auditResultSelect.find("option:selected");
            var auditResult = auditResultOption.val();
            var auditResultReason = "";
            if (auditResult > "2") {
                var auditResultReasonSelect = element.find("select[name='auditResultReason']");
                var auditResultReasonOption = auditResultReasonSelect.find("option:selected");
                auditResultReason = auditResultReasonOption.val();
                if (auditResultReason == "其他") {
                    auditResultReason = $.trim(element.find("input[name='otherReason']").val());
                    if (widget._isNullStr(auditResultReason)) {
                        widget.popup("审核不通过原因为其他时，必须手动输入原因");
                        return false;
                    }
                }
            }
            var params = {};
            if (!widget._isNullStr(custOrderNbr)) {
                params.custOrderNbr = custOrderNbr;
            }
            if (!widget._isNullStr(custOrderId)) {
                params.custOrderId = custOrderId;
            }
            if (!widget._isNullStr(auditResult)) {
                params.auditResult = auditResult;
            }
            if (!widget._isNullStr($.trim(auditResultReason))) {
                params.remark = auditResultReason;
            }
            return params;
        },
        _isNullStr: function (str) {
            if (str == null || str == "") {
                return true;
            }
            return false;
        }
    });
    vita.widget.register("orderAuditDetail", orderAuditDetail, true);
})(window.vita);