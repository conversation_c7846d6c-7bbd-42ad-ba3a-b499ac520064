<div data-widget="userTerminalInfoQuery" style="height: 100%">

    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="wmin_content row">
                                <div class="form_title">
                                    <div><i class="bot"></i>用户终端查询</div>
                                </div>
                                <form class=" form-bordered">
                                    <div class="form-group col-md-5 ">
                                        <label class="col-md-4 control-label lablep">
                                            终端串号：</label>
                                        <div class="col-md-8">
                                            <input type="text" id="mktResNbr" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <div class="col-md-2 text-left">
                                            <button type="button" id="btn-query" class="btn btn-primary">查询</button>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <div class="container-fluid row" id="materialCodeResult">
                                <div class="wmin_content">
                                    <div class="col-lg-12 mart10">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                            	<th>用户号码</th>
                                                <th>用户当前手机串码(IMEI)</th>
                                                <th>生产企业</th>
                                                <th>设备型号</th>
                                                <th>发证日期</th>
                                                <th>停产日期</th>
                                                <th>生产状态</th>
                                                <th>手机品牌</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            #if($options != "null" && $options.materialInfo && $options.materialInfo != "null"
                                            && $options.materialInfo.resId != "-1" && $options.materialInfo.handleResultCode == "0")
                                            <tr>
                                            	<td>$!{options.materialInfo.accNum}</td>
                                                <td>$!{options.materialInfo.mktResInstNbr}</td>
                                                <td>$!{options.materialInfo.agentName}</td>
                                                <td>$!{options.materialInfo.mktResName}</td>
                                                <td></td>
                                                <td></td>
                                                <td>$!{options.materialInfo.stateName}</td>
                                                <td>$!{options.materialInfo.brand}</td>
                                            </tr>
                                            #elseif($options != "null" && $options.materialInfo && $options.materialInfo != "null"
                                            && ($options.materialInfo.resId == "-1" || $options.materialInfo.resId == "null")
                                            && $options.materialInfo.handleResultCode == "0")
                                            <tr><td align='center' colspan='12'>资源返回：$!{options.materialInfo.resMsg}<td></tr>
                                            #elseif($options != "null" && $options.materialInfo && $options.materialInfo != "null"
                                            && $options.materialInfo.handleResultCode == "-1")
                                            <tr><td align='center' colspan='12'>$options.materialInfo.handleResultMsg<td></tr>
                                            #elseif($options != "null" && $options.materialInfo && $options.materialInfo != "null")
                                            <tr><td align='center' colspan='12'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->

                </div>
            </div>
        </div>
    </div>
</div>
