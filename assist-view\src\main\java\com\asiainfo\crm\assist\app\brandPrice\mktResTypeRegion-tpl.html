<div data-widget="mktResTypeRegion">
    <vita-data>{"data":$options}</vita-data>
    <p class="vita-data">$options</p>
    <div class="sypage-nav">
        <div class="nav_alink"><strong class="text16">营销资源类别适用区域</strong></div>
    </div>
    <div class="sypage-min">
        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">查询条件</strong></div>
        </div>
        <div class="sypagemin_content ">
            <form>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">营销资源CD标识</label>
                    <div class="col-md-6">
                        <input type="text" class="form-control" placeholder="" id="mktResTypeId">
                        <p class="vita-bind" model="mktResTypeId"></p>
                    </div>
                </div>

                <div class="form-group col-md-12">
                    <div class="col-md-12 searchbutt_r" align="right">
                        <button type="button" class="btn btn-default" id="searchDel">清除</button>
                        <button type="button" class="btn btn-primary" id="qryCommonLogSearchBtn">搜索</button>
                    </div>
                </div>

            </form>
        </div>

        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">营销资源类别适用区域列表</strong></div>
        </div>
        <div class="sypagemin_content ">
            <div class="col-lg-12 mart10">
                <table class="table table-hover" id="systemRoleList">
                    <thead>
                    <tr>
                        <th>选择</th>
                        <th>营销资源适用地区标识</th>
                        <th>营销资源CD标识</th>
                        <th>适用地区标识</th>
                     <!--   <th>状态</th>-->
                        <th>创建时间</th>
                    </tr>
                    </thead>
                    <tbody>
                    #if($options != "null" && $options.mktResTypeRegions && $options.mktResTypeRegions != "null" && $options.mktResTypeRegions.size() > 0)
                    #foreach($mktResTypeRegion in $options.mktResTypeRegions)
                    <tr >
                        <td>
                            <label class="wu-radio full absolute" data-scope="">
                                <input type="radio" name="moduleRadio" />
                                <p class="vita-data">{"data":$mktResTypeRegion,"totalNumber":$options.totalNumber}</p>
                            </label></td>
                        <td>$!mktResTypeRegion.mktResTypeRegionId</td>
                        <td>
                            <a class="textcolorgreen" link="mktResTypeAttr" data-toggle="tooltip" data-placement="top">$!mktResTypeRegion.mktResTypeId</a>
                            <p class="vita-data">{"mktResTypeId" : $!mktResTypeRegion.mktResTypeId}</p>
                        </td>
                       <!-- <td>$!mktResTypeRegion.mktResTypeId</td>-->
                        <td>$!mktResTypeRegion.applyRegionId</td>
                     <!--   <td>#if($!mktResTypeRegion.statusCd == 1000)有效
                            #elseif($!mktResTypeRegion.statusCd == 1100)无效
                            #end
                        </td>-->
                        <td>$!mktResTypeRegion.createDate</td>
                    </tr>
                    #end
                    #end
                    </tbody>
                </table>
                <!--翻页start -->
                <div class="page-box" id="showPageInfo">
                </div>
                <!--翻页end -->
            </div>
        </div>
    </div>
</div>