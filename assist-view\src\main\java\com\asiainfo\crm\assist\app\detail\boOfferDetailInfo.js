(function (vita) {
    var boOfferDetailInfo = vita.Backbone.BizView.extend({
        events: {
            "click #closeBtn": "_closePage",
            "click #olNbr": "_qryCustomerDetail",
            "click #mainOfferLi,#attachOfferLi a": "_qryOfferDetail",
            "click a[name=acctDetail]": "_qryAcctDetail",
            "click a[name=custDetail]": "_custDetailClk"
        },
        global:{
          compUrl:{
              olDetailInfo : "../query/olDetailInfo",      // 订单详情
              boProdDetailInfo : "../query/boProdDetailInfo",      // 订单项(产品)详情
              boOfferDetailInfo : "../query/boOfferDetailInfo",    // 订单项(销售品)详情
              custDetailInfo : "../query/custDetailInfo",  // 客户详情
              acctDetailInfo : "../query/acctDetailInfo"   // 账户详情
          }
        },
        _initialize: function () {
            var widget = this,
                el = $(widget.el);
            var data = el.data("data");
            if (data) {
                widget.model.set(data);
            }
        },
        _closePage : function() {
            var widget = this,
                element = $(widget.el);
            element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
        },
        _custDetailClk: function (e) {
            var widget = this;
            var custId = $(e.currentTarget).data("custId");
            if (!custId) {
                widget.popup("客户ID未返回,请重新查询...");
                return;
            }
            var param = {
                "custId": custId
            };
            widget._openDialog("custDetailInfo",param);
        },
        /**
         * 查看购物车详情
         * @private
         */
        _qryCustomerDetail: function (e) {
            var widget = this;
            var aEl = $(e.target).closest("a");
            var param = {
                custOrderId: aEl.data("olNbr")
            };
            widget._openDialog("olDetailInfo",param);
        },
        /**
         * 查询销售品详情
         * @private
         */
        _qryOfferDetail: function () {
            var widget = this;
            var param = {
                custOrderId: widget.model.get("custOrderId"),
                orderItemId: widget.model.get("orderItemId")
            };
            widget._openDialog("boOfferDetailInfo",param);
        },
        /**
         * 查询账户详情
         * @private
         */
        _qryAcctDetail: function (e) {
            var widget = this;
            var acctCd = $(e.currentTarget).data("acctCd");
            if (!acctCd) {
                widget.popup("账户合同不存在,请重新查询...");
                return;
            }
            var param = {
                acctCd: acctCd
            };
            widget._openDialog("acctDetailInfo",param);
        },
        _openDialog:function(compName,data){
            var widget = this;
            var option = {
                url: widget.global.compUrl[compName],
                params: data,
                headers : {
                    "regionId" : widget.gSession.installArea
                }
            };
            widget.dialog(option);
        }
    });
    vita.widget.register("boOfferDetailInfo", boOfferDetailInfo, true);
})(window.vita);