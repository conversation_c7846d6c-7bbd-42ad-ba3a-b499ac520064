(function(vita) {
	var chooseCust = vita.Backbone.BizView.extend({
		events : {
			"click .pagination li" : "_pageClick",
			"click #custSearchBtn" : "_queryCustomers",
			"click .dropdown-menu" : "_setQueryType",
			"click input[name='cust']" : "_selectCust",
			"click #closeCustBtn" : "_closePage",
			"click #submitCustBtn" : "_submit",
			"click .scanbox":"_readCert",
            "click div[name=dropdownDiv]":"_toggleOpen"
		},
		_initialize : function() {
		},
        _toggleOpen:function(e){
            $(e.currentTarget).toggleClass("open");
        },
		global : {
			pageIndex : 1,
			pageSize : 10, //每页记录数
			scanCertUrl: "../comm/scanCert"
		},
		_closePage : function() {
			var widget = this,
				element = $(widget.el);
			var dialog = element.closest("[id=" + widget.widgetName + "Dialog]");
			if (dialog.length) {
				dialog.dialog("close");
			}
		},
		_submit : function() {
			var widget = this,
				validate = false;
			var cust = widget.model.toJSON();
			var custId = parseInt(cust.custId) || 0;
			if (custId <= 0) {
				widget.popup("请先选择客户");
				validate = false;
				return false;
			}
			validate = true;
			widget.model.set({
				validate : validate
			});
			widget._closePage();
			return false;
		},
		getValue : function() {
			var widget = this;
			var cust = widget.model.toJSON();
			return cust;
		},
		_queryCustomers : function(cust) {
			var widget = this,
				element = $(widget.el),
				gSession = widget.gSession,
				pageIndex = widget.global.pageIndex,
				recordNumber = widget.global.pageSize;
			var data = element.data("data");
			var soUtil = widget.require("soUtil");
			var queryValue = widget.model.get("queryValue");
			var isNullOrEmpty = soUtil.isNullOrEmpty(queryValue);
            if (isNullOrEmpty) {
                widget.popup("请输入查询内容...");
                return false;
            }
            
            var queryType = widget.model.get("queryType");
            var widgetSource = data.widgetSource;
            queryType = soUtil.isNullOrEmpty(queryType) ? "" : queryType;
			var param = {
				"pageInfo" : {
					"pageIndex" : pageIndex,
					"pageSize" : recordNumber
				},
				"value" : queryValue,
				"queryType" : queryType,
				"sysUserId": gSession.systemUserId,
				"glbSessionId" : gSession.glbSessionId,
				"widgetSource":widgetSource
			};
			widget._appendCertInfo(param, cust);
			widget.refreshPart("qryCustomerList", param, "#searchList", function (res) {
                var res = $(res), 
                	paging = this.require("paging");
                var totalNumber = $(res).find(".bordersolid").eq(0).data("totalNumber") || 0;
                var e = paging.new({
                    recordNumber : recordNumber,
                    total : totalNumber,
                    pageIndex : pageIndex,
                    callback : function (pageIndex, recordNumber) {
                    	var param = {
        					"pageInfo" : {
        						"pageIndex" : pageIndex,
        						"pageSize" : recordNumber
        					},
        					"value" : queryValue,
        					"queryType" : queryType,
                        	"sysUserId": gSession.systemUserId,
        					"glbSessionId" : gSession.glbSessionId,
        					"widgetSource":widgetSource
        				};
            			widget._appendCertInfo(param, cust);
                        widget.refreshPart("qryCustomerList", param, "#resultList");
                    }
                });
                res.find("#showPageInfo").append(e.getElement());
                $(widget.el).find("#searchList").show();
            });
		},
		_selectCust : function(e) {
			var widget = this,
				element = $(widget.el);
			var input = $(e.target).closest("input");
			var div = input.closest(".bordersolid");
			var customer = div.data("customer");
			widget.model.set(customer);
		},
		_setQueryType : function(e) {
			var widget = this,
				element = $(widget.el);
			var tarLi = $(e.target).closest("li");
			var dataRange = tarLi.data("dataRange");
			var dimensionCode = dataRange.dataDimensionCode;
			var dimensionValue = dataRange.dimensionValue;
			element.find(".filter-option").html(dataRange.dataDimensionName);
			widget.model.set("queryType", dimensionCode);
		},
		_pageClick : function(e) {
			var widget = this;
			var pageIndex = widget.pageInfo.getPageIndex(e);
			widget._queryCustomers();
			return false;
		},
		_readCert:function(){
			var widget = this, element = $(widget.el), params = {};
			var data = element.data("data");
			if (data.certOperType) {
				params.certOperType = data.certOperType;
			}
			var option = {
				url : widget.global.scanCertUrl,
				params: params,
				onClose : function(res) {
					var scanCert = $(res).closest("[data-widget=scanCert]");
					if (scanCert.length) {
						var cust = scanCert.scanCert("getValue");
						if (!cust) {
							return false;
						}
						if(cust.certNumber){
							widget.model.set("queryValue",cust.certNumber);
						}
						// 默认身份证
						widget.model.set("queryType", "identity");
						widget._queryCustomers(cust);
					}
				}
			};
			widget.dialog(option);
		},
		_appendCertInfo: function (params, cust) {
			if (cust) {
				if (cust.certType) {
					params.certType = cust.certType;
				}
				if (cust.oldCertNumber && cust.newCertNumber) {
					$.extend(params, _.pick(cust, "oldCertNumber", "newCertNumber", "partyName"));
					params.certType = "50";
				}
			}
		}
	});
	vita.widget.register("chooseCust", chooseCust, true);
})(window.vita);