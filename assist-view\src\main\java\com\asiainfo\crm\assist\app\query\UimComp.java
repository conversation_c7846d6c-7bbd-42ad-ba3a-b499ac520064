package com.asiainfo.crm.assist.app.query;

import com.al.common.utils.StringUtil;
import com.alibaba.fastjson.JSON;
import com.asiainfo.angel.json.JsonConverter;
import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.common.Constant;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IOrderSMO;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.asiainfo.crm.service.intf.IResourceSMO;
import com.asiainfo.crm.service.intf.ISaopServiceSmo;
import com.asiainfo.crm.service.intf.ISatelliteSystemServiceSmo;
import com.asiainfo.crm.util.EopUtil;
import com.asiainfo.crm.util.SaopUtil;
import com.asiainfo.crm.util.XmlConverUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Created by chenzui on 2017/3/1.
 */
@Component("vita.uimComp")
public class UimComp extends AbstractSoComponent {

    @Autowired
    private IResourceSMO resourceSMO;
    
    @Autowired
    private ISaopServiceSmo saopServiceSmo;
    @Autowired
    private IOrderSMO orderSMO;
    @Autowired
    private IProdInstSMO prodInstSMO;
    @Autowired
    private ISatelliteSystemServiceSmo satelliteSystemServiceSmo;

    @Override
    public Map achieveData(Object... objects) throws Exception {
        return null;
    }
    public Map callCrmCommonBusi(String jsonStr) throws IOException {
        Map retStr = orderSMO.callCrmCommonBusi(jsonStr);
        return retStr;
    }

    public Map getCardInfoOne(String jsonString) throws IOException {
        JsonConverter jsonConverter = JsonConverter.buildNormalConverter();
        Map inMap=jsonConverter.toBean(jsonString, Map.class);
        String regionId = MapUtils.getString(inMap,"regionId");
        String mktResInstNbr = MapUtils.getString(inMap,"mktResInstNbr");
        Map<String,Object> resourceParam = new HashMap<String, Object>();
        resourceParam.put("mktResInstNbr", mktResInstNbr);
        resourceParam.put("regionId", regionId);
        Map<String,Object> routeParam = new HashMap<String, Object>();
        routeParam.put("regionId", regionId);
        resourceParam.put("routeParam", routeParam);
        String retRsStr = resourceSMO.queryMaterialInfo(jsonConverter.toJson(resourceParam));
        return  (Map) resolveResult(retRsStr);
    }

    public Map getTerminalDevice(String jsonStr) throws IOException {
        String retStr = resourceSMO.queryMktResListCore(jsonStr);
        return (Map)resolveResult(retStr);
    }

    public Map occupy(String queryJson) throws IOException {
        String retStr = resourceSMO.occupyReleaseMktResCore(queryJson);
        return (Map)resolveResult(retStr);
    }

    public Map qryProdInstAccNumByProdInstId(String inStr) throws IOException{
        return (Map)resolveResult(prodInstSMO.qryProdInstAccNumByProdInstId(inStr));
    }

    /**
     * 根据mktResId获取集团mktResId
     * @param mktResId
     * @return
     * @throws Exception
     */
	protected String getMaterialCode(String mktResId) throws Exception{
	    //核心默认返回02201，省份资源具备根据mktResId查询mktResCd能力时
        //调资源接口查询
        return MDA.MKT_RES_TYPE_CD_ESIM;
    }

    /**
     * 查询卡信息
     * @param inMap
     * @param mktResInstNbr
     * @return
     * @throws IOException
     */
	protected Map getCardInfo(Map inMap,String mktResInstNbr) throws IOException {
        String regionId = MapUtils.getString(inMap,"regionId");
        Map<String,Object> resourceParam = new HashMap<String, Object>();
        resourceParam.put("mktResInstNbr", mktResInstNbr);
        resourceParam.put("regionId", regionId);
        Map<String,Object> routeParam = new HashMap<String, Object>();
        routeParam.put("regionId", regionId);
        resourceParam.put("routeParam", routeParam);
        String retRsStr = resourceSMO.queryMaterialInfo(jsonConverter.toJson(resourceParam));
        return  (Map) resolveResult(retRsStr);
    }





    /**
     * eSIM获取绑定.
     * @param params
     * @return
     * @throws Exception
     */
    public Map getESIM(String params) throws Exception {
    	Map result = new HashMap();
    	Map paramsMap = jsonConverter.toBean(params, Map.class);
        String mktResCd = getMaterialCode(MapUtils.getString(paramsMap,"mktResId"));
        if(StringUtil.isEmpty(mktResCd)){
            result.put("resultCode", "1");
            result.put("resultMsg", "根据省内mktResId查询集团mktResCd失败");
            return result;
        }
        paramsMap.put("mktResId", "0"+mktResCd);
        paramsMap.put("mktResCd", "0"+mktResCd);
        paramsMap.put("srcSysId", MDA.GROUP_SRC_SYS_ID);
    	Map reqMap = new HashMap();
    	reqMap.put(MDA.GROUP_SRVC_NODE_MAP.get("getESIM"), paramsMap);
		String req = SaopUtil.convertSaopParam(MDA.GROUP_SRVC_INST_MAP.get("getESIM"), Long.parseLong(MDA.PROVINCE_AREA), jsonConverter.toJson(reqMap));
        String re = saopServiceSmo.exchange(req);
        Map res = jsonConverter.toBean(re, Map.class);
        Map contractRoot = MapUtils.getMap(res, "contractRoot");
        Map svcCont = MapUtils.getMap(contractRoot, "svcCont");
        String resultCode = MapUtils.getString(svcCont, "resultCode");
        String resultMsg = MapUtils.getString(svcCont, "resultMsg");
        if (MDA.RESULT_SUCCESS.toString().equals(resultCode)) {
        	Map resultObject = MapUtils.getMap(svcCont, "resultObject");
        	Map eSIMRes = MapUtils.getMap(resultObject, "getESIMRes");
        	if (!eSIMRes.isEmpty()) {
        		result.putAll(eSIMRes);
                result.put("mktResCd", "0"+mktResCd);
                result.put("eId", MapUtils.getString(paramsMap, "eid"));
        	}
        } else {
        	result.put("resultMsg", resultMsg);
        }
        result.put("resultCode", resultCode);
        return result;
    }

    /**
     * eSIM绑定新方法
     * @param params
     * @return
     */
     public Map getESIMNew(String params) throws Exception {
        Map paramsMap = jsonConverter.toMap(params, String.class, Object.class);
        Map resource = (Map) paramsMap.remove("resource");
        Map result = getESIM(jsonConverter.toJson(paramsMap));
        if (!"0".equals(MapUtils.getString(result, "resultCode"))) {
            return result;
        }
        resource.put("mktResInstNbr",MapUtils.getString(result, "iccid"));
        Map esimCardAttrVo = MapUtils.getMap(resource, "esimCardAttrVo");
        if (esimCardAttrVo == null) {
            esimCardAttrVo = new HashMap();
            resource.put("esimCardAttrVo", esimCardAttrVo);
        }
        esimCardAttrVo.put("matchingId", MapUtils.getString(result, "matchingId"));
        esimCardAttrVo.put("profile", MapUtils.getString(result, "smdpAddress"));
        String req = jsonConverter.toJson(resource);
        String retStr = resourceSMO.queryUIMAllocAndRelease(req);
        Map resourceResult = (Map) resolveResult(retStr);
        result.put("resultCode", MapUtils.getString(resourceResult,"handleResultCode"));
        result.put("resultMsg", MapUtils.getString(resourceResult,"handleResultMsg"));
        result.putAll(resourceResult);
        // 加开关 资源为2.x时打开
         if (MDA.LOGIC_Y.equals(MDA.IS_OLD_SR_CENTER_SYS_VERSION)) {
             Map retRsMap = getCardInfo(paramsMap,MapUtils.getString(result, "iccid"));
             result.put("mktResId", MapUtils.getString(retRsMap,"mktResId"));
			 //ESIM营销资源返回字段terminalDevId开关

         }
        return result;
    }
    
    /**
     * eSIM解除绑定.
     * @param params
     * @return
     * @throws Exception
     */
     public Map releaseESIM(String params) throws Exception {
    	Map result = new HashMap();
    	Map paramsMap = jsonConverter.toBean(params, Map.class);
        String mktResCd = getMaterialCode(MapUtils.getString(paramsMap,"mktResId"));
        if(StringUtil.isEmpty(mktResCd)){
            result.put("resultCode", "1");
            result.put("resultMsg", "根据省内mktResId查询集团mktResCd失败");
            return result;
        }
        paramsMap.put("mktResId", "0"+mktResCd);
        paramsMap.put("mktResCd", "0"+mktResCd);
        paramsMap.put("srcSysId", MDA.GROUP_SRC_SYS_ID);
    	Map reqMap = new HashMap();
    	reqMap.put(MDA.GROUP_SRVC_NODE_MAP.get("releaseESIM"), paramsMap);
		String req = SaopUtil.convertSaopParam(MDA.GROUP_SRVC_INST_MAP.get("releaseESIM"), Long.parseLong(MDA.PROVINCE_AREA), jsonConverter.toJson(reqMap));
        String re = saopServiceSmo.exchange(req);
        Map res = jsonConverter.toBean(re, Map.class);
        Map contractRoot = MapUtils.getMap(res, "contractRoot");
        Map svcCont = MapUtils.getMap(contractRoot, "svcCont");
        if (!svcCont.isEmpty()) {
        	result.putAll(svcCont);
            result.put("mktResId", MapUtils.getString(paramsMap,"mktResId"));
            result.put("mktResCd", "0"+mktResCd);
        }
        return result;
    }


    /**
     * eSim卡新释放方法
     * @param params
     * @return
     * @throws Exception
     */
    public Map releaseESIMNew(String params) throws Exception {
        Map paramMap = jsonConverter.toMap(params, String.class, Object.class);
        Map releaseParam = (Map) paramMap.remove("releaseParam");
        Map result = releaseESIM(params);
        if (!"0".equals(MapUtils.getString(result, "resultCode"))) {
            return result;
        }
        String req = jsonConverter.toJson(releaseParam);
        String retStr = resourceSMO.queryUIMAllocAndRelease(req);
        Map resourceResult = (Map) resolveResult(retStr);
        result.put("resultCode", MapUtils.getString(resourceResult,"handleResultCode"));
        result.put("resultMsg", MapUtils.getString(resourceResult,"handleResultMsg"));
        result.putAll(resourceResult);
        return result;
    }
    
    /**
     * 是否为省内制卡
     *
     * @param jsonStr
     * @return
     * @throws IOException
     */
    public Map isProvinceUim(String jsonStr) throws IOException {
        Map<String,Object> options = new HashMap<>();
        Map<String, Object> paramMap = jsonConverter.toMap(jsonStr, String.class, Object.class);
        //查询uim卡信息
        String result = resourceSMO.getTerminalDevice(jsonConverter.toJson(paramMap));
        Map<String, Object> resultMap = (Map<String, Object>) resolveResult(result);
        String mktResInstId = MapUtils.getString(resultMap, "mktResInstId");
        String handleResultCode = MapUtils.getString(resultMap, "handleResultCode");
        String handleResultMsg = MapUtils.getString(resultMap, "handleResultMsg");

        if (MapUtils.isNotEmpty(resultMap) && !Constant.LOGIC_STR_0.equals(handleResultCode)) {
            options.put("resultCode", "2");
            options.put("desc", handleResultMsg);
            return options;
        }
        if (StringUtils.isEmpty(mktResInstId)) {
            options.put("resultCode", "1");
            options.put("desc", "查询UIM卡信息失败");
            return options;
        }

        //查询时候为省内制卡
        paramMap.remove("mktResInstNbr");
        paramMap.put("terminalDevId", mktResInstId);
        paramMap.put("itemSpecId", MDA.UIM_ATTR_VALUE_PAIR.get("isProvinceUimSpecId"));
        result = resourceSMO.getTerminalDevItem(jsonConverter.toJson(paramMap));
        resultMap = (Map<String, Object>) resolveResult(result);
        handleResultCode = MapUtils.getString(resultMap, "handleResultCode");
        options.put("resultCode", handleResultCode);
        if (!"0".equals(handleResultCode)) {
            options.put("desc", "查询UIM卡是否为省内制卡失败...");
            return options;
        }
        options.put("isProvinceUim", false);
        List<Map> terminalDevItemInfoList = (List<Map>) MapUtils.getObject(resultMap, "terminalDevItemInfoList");
        if (terminalDevItemInfoList != null && terminalDevItemInfoList.size() > 0) {
            for (Map map : terminalDevItemInfoList) {
                String itemSpecId = MapUtils.getString(map, "itemSpecId");
                if (MDA.UIM_ATTR_VALUE_PAIR.get("isProvinceUimSpecId").equals(itemSpecId)
                        && MDA.UIM_ATTR_VALUE_PAIR.get("isProvinceUimSpecValue").equals(MapUtils.getString(map, "value"))) {
                    options.put("isProvinceUim", true);
                }
            }
        }

        return options;
    }


    /**
     * 查询卡信息
     * @param jsonStr
     * @return
     */
    public Map queryMaterialInfo(String jsonStr) throws IOException {
        return (Map) resolveResult(resourceSMO.queryMaterialInfo(jsonStr));
    }


    /**
     * 查询ESIM卡信息
     * @param jsonStr
     * @return
     */
    public Map qryEsimInfo(String jsonStr) throws IOException {
        return (Map) resolveResult(resourceSMO.qryEsimInfo(jsonStr));
    }
}
