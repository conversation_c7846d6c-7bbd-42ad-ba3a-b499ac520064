(function(vita) {
	var addChargeItem = vita.Backbone.BizView.extend({
		events : {
			"change [name='busiObjSel']" : "_changeBusiObj", //业务对象
			"change [name='chargeItemSel']" : "_changeChargeItem" //费用科目
		},
		_initialize : function() {
			var widget = this,
				element = $(widget.el);
			var options = element.data("data");
			var busiObjSel = element.find('select[name="busiObjSel"]');
			$.each(options.busiObjs, function(k, item) {
				$("<option/>").text(item.busiObjName + ' ' + item.accNbr + ' ' + ' ' + item.serviceOfferName)
					.val(item.boId).data("data", item).appendTo(busiObjSel);
			});
			var chargeItemSel = element.find('select[name="chargeItemSel"]');
			$.each(options.chargeItems, function(k, item) {
				$("<option/>").text(item.name).val(item.chargeItemCd).data("data", item).appendTo(chargeItemSel);
			});
			var payMethodSel = element.find('select[name="payMethodSel"]');
			$.each(options.payMethods, function(k, item) {
				$("<option/>").text(item.name).val(item.payMethodCd).data("data", item).appendTo(payMethodSel);
			});
		},
		submit : function() {
			var widget = this,
				element = $(widget.el)
			div = element.find(".calcw_rightcont");
			var input = element.find("input");
			var inputVal = $.trim(input.val());
			var charge = parseFloat(inputVal) * 100;
			var soUtil = widget.require("soUtil");
			var isNullOrEmpty = soUtil.isNullOrEmpty(inputVal);
			if (isNullOrEmpty) {
				widget.popup("请输入价格");
				input.focus();
				return false;
			}
			//判断是否为数字以及小数点后是否为2位
			if (inputVal.indexOf(".") > 0) {
				var nums = inputVal.split(".");
				if (nums[1].length > 2) {
					widget.popup("费用校验失败：小数点后最多为2位");
					input.focus();
					return false;
				} else {
					if (!widget._checkIfNum(nums[0]) || !widget._checkIfNum(nums[1])) {
						widget.popup("费用校验失败：您输入的费用不为数字或是为负数");
						input.focus();
						return false;
					}
				}
			} else {
				if (!widget._checkIfNum(inputVal)) {
					widget.popup("费用校验失败：您输入的费用不为数字或是为负数");
					input.focus();
					return false;
				}
			}
			var feeAmount = soUtil.unformatMoney(inputVal);
			if (feeAmount <= 0) {
				widget.popup("新增费用项的费用的价格必须大于零");
				input.focus();
				return false;
			}
			if (feeAmount > 1000000) {
				var isSure = confirm("你的费用输入超过10000,是否确定提交?");
				if (!isSure) {
					input.focus();
					return false;
				}
			}
			var busiObjSel = div.find('select[name="busiObjSel"]');
			var busiObjSelected = busiObjSel.find("option:selected");
			var busiObjSelVal = busiObjSel.val();
			if (busiObjSelVal == null || busiObjSel.length == 0) {
				widget.popup("未找到业务对象");
				return false;
			}
			var serviceOfferSel = div.find('select[name="serviceOfferSel"]');
			var serviceOfferSelVal = serviceOfferSel.val();
			if (serviceOfferSelVal == null || serviceOfferSel.length == 0) {
				widget.popup("未找到费用所属业务");
				return false;
			}
			var chargeItemSel = div.find('select[name="chargeItemSel"]');
			var chargeItemSelVal = chargeItemSel.val();
			var chargeItemSelText = $.trim(chargeItemSel.find("option:selected").text());
			if (chargeItemSelVal == null || chargeItemSel.length == 0) {
				widget.popup("联系维护人员配置费用名称后再操作");
				return false;
			}
			var payMethodSel = div.find('select[name="payMethodSel"]');
			var payMethodSelVal = payMethodSel.val();
			var payMethodSelText = $.trim(payMethodSel.find("option:selected").text());
			if (payMethodSelVal == null || payMethodSel.length == 0) {
				widget.popup("联系维护人员配置付费方式后再操作");
				return false;
			}
			var options = element.data("data");
			var data = {
				busiObjId : busiObjSelVal,
				serviceOfferId : serviceOfferSelVal,
				charge : feeAmount,
				oriCharge : inputVal,
				chargeItemCd : chargeItemSelVal,
				chargeItemName : chargeItemSelText,
				payMethodCd : payMethodSelVal,
				payMethodName : payMethodSelText,
				payMethods : options.payMethods,
				busiObj : busiObjSelected.data("data")
			};
			return data;
		},
		_changeBusiObj : function(e) {
			var widget = this,
				element = $(widget.el);
			var select = $(e.target).closest("select");
			var selected = select.find("option:selected");
			var selectedData = selected.data("data");
			if (!selectedData) {
				return false;
			}
			var result = widget._queryConfigs(selectedData.busiSpecId);
			if (!result) {
				return false;
			}
			var chargeItems = result.chargeItems;
			var payMethods = result.payMethods;

			var div = select.closest("calcw_rightcont");
			//费用科目初始化
			var chargeItemSel = div.find('select[name="chargeItemSel"]');
			chargeItemSel.empty();
			$.each(chargeItems, function(k, item) {
				$("<option/>").text(item.name).val(item.chargeItemCd).data("data", item).appendTo(chargeItemSel);
			});

			//付费方式初始化
			var payMethodSel = div.find('select[name="payMethodSel"]');
			payMethodSel.empty();
			$.each(payMethods, function(k, item) {
				$("<option/>").text(item.name).val(item.payMethodCd).data("data", item).appendTo(payMethodSel);
			});
		},
		_changeChargeItem : function(e) {
			var widget = this,
				element = $(widget.el);
			var select = $(e.target).closest("select");
			var selected = select.find("option:selected");
			var selectedData = selected.data("data");

			if (!selectedData) {
				return false;
			}
			var payMethods = widget._queryPayMethods(selectedData.chargeItemCd);
			if (!payMethods || !payMethods.length) {
				widget.popup("联系维护人员配置付费方式后再操作");
				return false;
			}
			var div = select.closest(".calcw_rightcont");
			var payMethodSel = div.find('select[name="payMethodSel"]');
			payMethodSel.empty();
			$.each(payMethods, function(k, item) {
				$("<option/>").text(item.name).val(item.payMethodCd).data("data", item).appendTo(payMethodSel);
			});
		},
		//查询费用、付费方式配置
		_queryConfigs : function(busiSpecId) {
			var widget = this;
			var busiSpecId = parseInt(busiSpecId) || 0;
			if (!busiSpecId) {
				return false;
			}
			var chargeItems = widget._queryChargeItems(busiSpecId);
			if (!chargeItems || !chargeItems.length) {
				widget.popup("联系维护人员配置费用名称后再操作");
				return false;
			}
			var payMethods = widget._queryPayMethods(chargeItems[0].chargeItemCd);
			if (!payMethods || !payMethods.length) {
				widget.popup("联系维护人员配置付费方式后再操作");
				return false;
			}
			var result = {
				chargeItems : [],
				payMethods : []
			};
			result.chargeItems = chargeItems;
			result.payMethods = payMethods;
			return result;
		},
		_queryPayMethods : function(chargeItemCd) {
			var widget = this,
				payMethods = [];
			var params = {
				//科目规格
				"chargeItemCd" : chargeItemCd
			};
			widget.callService("queryPayMethods", params, function(data) {
				var ret = JSON.parse(data);
				if (ret.resultCode == "0") {
					var resultObject = ret.resultObject;
					if (resultObject && resultObject.payMethods) {
						payMethods = resultObject.payMethods;
					}
				} else {
					widget.popup("查询付费方式异常:" + ret.resultMsg);
				}
			}, {
				async : false,
                headers : {
                    "regionId" : widget.gSession.installArea
                }
			});
			return payMethods;
		},
		_queryChargeItems : function(objId) {
			var widget = this,
				chargeItems = [];
			var params = {
				//产品规格
				"objId" : objId
			};
			widget.callService("queryChargeItems", params, function(data) {
				var ret = JSON.parse(data);
				if (ret.resultCode == "0") {
					var resultObject = ret.resultObject;
					if (resultObject && resultObject.chargeItems) {
						chargeItems = resultObject.chargeItems;
					}
				} else {
					widget.popup("查询费用项目异常:" + ret.resultMsg);
				}
			}, {
				async : false,
                headers : {
                    "regionId" : widget.gSession.installArea
                }
			});
			return chargeItems;
		},
		_checkIfNum : function(num) {
			var reg = /^\d+$/;
			if (!num.match(reg)) {
				return false;
			} else {
				return true;
			}
		}
	});
	vita.widget.register("addChargeItem", addChargeItem, true);
})(window.vita);