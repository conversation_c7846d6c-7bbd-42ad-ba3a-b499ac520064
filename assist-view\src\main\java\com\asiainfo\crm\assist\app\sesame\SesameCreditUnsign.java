package com.asiainfo.crm.assist.app.sesame;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ISesameCreditSmo;

/**
 * Created by wenhy on 2017/7/7.
 * 芝麻信用购机解约
 */
@Component("vita.sesameCreditUnsign")
public class SesameCreditUnsign extends AbstractComponent {

    private final Logger logger = LoggerFactory.getLogger(SesameCreditUnsign.class);

    @Autowired
    private ISesameCreditSmo sesameCreditSmo;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map qrySesameCreditInfoByCert(String jsonStr) throws Exception {
        String sesameInfoStr = sesameCreditSmo.qrySesameCreditInfoByCert(jsonStr);
        List<Map<String, Object>> resultObject = (List<Map<String, Object>>) resolveResult(sesameInfoStr);
        Map resultMap = new HashMap();
        resultMap.put("sesameUserIdList", resultObject);
        return resultMap;
    }

    public Map refreshInfoListTips(String jsonString) throws Exception {
        Map options = jsonConverter.toBean(jsonString, Map.class);
        return options;
    }

    public Map qrySesameCreditUnsignInfo(String jsonStr) throws Exception {
        String sesameInfoStr = sesameCreditSmo.qrySesameCreditUnsignInfo(jsonStr);
        String resultObjectStr = (String) resolveResult(sesameInfoStr);
        String showData = getShowData(resultObjectStr);
        return jsonConverter.toBean(showData, Map.class);
    }

    public Map sesameCreditUnsignAction(String jsonStr) throws Exception {
        String sesameUnsignStr = sesameCreditSmo.sesameCreditUnsignAction(jsonStr);
        String resultObjectStr = (String) resolveResult(sesameUnsignStr);
        return jsonConverter.toBean(resultObjectStr, Map.class);
    }
    
    private String getShowData(String sesameInfoStr) throws Exception{
    	String retStr = "";
    	Map<String, Object> resultInfo =  jsonConverter.toBean(sesameInfoStr, Map.class);
    	List<Map<String, Object>> resultObject = (List<Map<String, Object>>) resultInfo.get("sesameCreditInfos");
    	Map<String, Object> resultMap = new HashMap<String, Object>();
    	List<Map<String, Object>> sesameCreditInfos = new ArrayList<Map<String,Object>>();
    	if(null != resultObject){
    		for (int i = 0; i < resultObject.size(); i++) {
    			Map<String, Object> data = resultObject.get(i);
    			String isAlipaySign = String.valueOf(data.get("isAlipaySign"));
    			if(!"Y".equals(isAlipaySign)){
    				continue;
    			}
    			Map<String, Object> dataMap = new HashMap<String, Object>();
    			dataMap.putAll(data);
    			sesameCreditInfos.add(dataMap);
			}
    	}
//    	if(sesameCreditInfos != null && sesameCreditInfos.size() > 0){
    		resultMap.put("sesameCreditInfos", sesameCreditInfos);
//    	}
        retStr = jsonConverter.toJson(resultMap);
    	
    	return retStr;
    }

}
