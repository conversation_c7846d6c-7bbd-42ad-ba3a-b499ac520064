(function(vita) {
	var chooseArea = vita.Backbone.BizView.extend({
		events : {
			"click #citys li" : "_queryCountys",
			"click #countys li" : "_clickCounty",
			"click #closeAreaBtn" : "_closePage",
			"click #submitAreaBtn" : "_submit",
		},
		_initialize : function() {},
		_closePage : function() {
			var widget = this,
				element = $(widget.el);
			var dialog = element.closest("[id=" + widget.widgetName + "Dialog]");
			if (dialog.length) {
				dialog.dialog("close");
			}
		},
		_submit : function() {
			var widget = this,
				validate = false;
			var soUtil = widget.require("soUtil");
			var area = widget.model.toJSON();
			var isNullOrEmpty = soUtil.isNullOrEmpty(area.commonRegionId);
			if (isNullOrEmpty) {
				widget.popup("请选择地区");
				validate = false;
				return false;
			}
			validate = true;
			widget.model.set({
				validate : validate
			});
			widget._closePage();
			return false;
		},
		getValue : function() {
			var widget = this;
			var area = widget.model.toJSON();
			return area;
		},
		_queryCountys : function(e) {
			var widget = this,
				element = $(widget.el),
				gSession = widget.gSession,
				li = $(e.target).closest("li");
			var area = li.data("data");
			li.addClass("active").siblings().removeClass("active");
			widget.model.set(area);
			var param = {
				"areaLevel" : parseInt(area.areaLevel) + 10,
				"upRegionId" : area.commonRegionId,
				"glbSessionId" : gSession.glbSessionId,
			};
			widget.refreshPart("qryDataPrivList", param, "#countys", "countys", function(res) {
				var lis = $(res).find("li");
				if (lis.length) {
					element.find("#countyDiv").show();
				}

			}, {
				mask : true,
				headers : {
					"regionId" : widget.gSession.installArea
				}
			});
		},
		_clickCounty : function(e) {
			var widget = this,
				li = $(e.target).closest("li");
			var area = li.data("data");
			widget.model.set(area);
			li.addClass("active").siblings().removeClass("active");
		}
	});
	vita.widget.register("chooseArea", chooseArea, true);
})(window.vita);