(function(vita){
    var cloudOrder = vita.Backbone.BizView.extend({
        events : {
            "click #qryCloudOrderSearchBtn" : "_queryCloudOrder",
            "click #searchDel": "_searchParamsDel",
            "click #regionIdBtn": "_chooseArea",
            "click [link=cloudOrderInfo]": "_queryCloudOrderInfoLink",
            "click [name='moduleRadio'] " : "_clickRadio",
            "click #qryTimeQuantum button" : "_qryTimeQuantum",

        },
        _initialize : function() {
            var widget = this,
                element = $(widget.el);
            var data = element.data("data");
            widget.model.set(data);
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var nowDate = new Date();
            var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
            var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
            var startYear = nowDate.getFullYear() - 5, endYear = nowDate.getFullYear();
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear:endYear
                });
            }
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear:endYear
                });
            }
            //日期控件设置默认时间
            widget.global.defaultBeginTime = widget._getFormatDate(widget.global.sevenDayCount); //默认是七天
            widget.global.defaultEndTime = widget._currentDate();
            widget._setDefaultDateTime();

            //初始化查询
            /*widget._queryCloudOrder();*/
        },
        global : {
            pageSize : 10, //每页记录数
            chooseArea : "../comm/chooseArea",
            preset: "date",
            defaultBeginTime:"",
            defaultEndTime:"",
            currentDayCount:0,
            sevenDayCount:-6,
            lastMonthDayCount:-29,
            ninthDayCount:-89
        },
        _clickRadio : function(e){
            var widget = this,
                element = $(widget.el);
            var radio = $(e.target).closest('input');
            var data = radio.data('data');
            widget.model.set("selectedCloudOrder",data);
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _isNullOrEmpty: function (condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty;
        },
        _queryCloudOrderInfoLink: function(e){
            var widget = this;
            var aEl = $(e.target).closest("a"); // 获取触发事件的a元素
            var masterOrderNo = aEl.data("masterOrderNo"); // 从a元素的data属性中获取masterOrderNo
            if(!masterOrderNo){
                widget.popup("订单不存在,请重新查询后再试.");
                return;
            }
            window.open("../../crm_assist/query/cloudOrderInfo?masterOrderNo=" + masterOrderNo + "&regionId=" + widget.global.regionId);
        },
        _getFormatDate: function(days) {
            var date = new Date();
            var yesterday_milliseconds = date.getTime()+days*1000*60*60*24;
            var yesterday = new Date();
            yesterday.setTime(yesterday_milliseconds);
            var strYear = yesterday.getFullYear();
            var strDay = yesterday.getDate();
            var strMonth = yesterday.getMonth()+1;
            if(strMonth<10)
            {
                strMonth="0"+strMonth;
            }
            if(strDay<10)
            {
                strDay="0"+strDay;
            }

            /*var hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
            var minute = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
            var second = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
            var time = hour+":"+minute+":"+second;*/
            var datastr = strYear+"-"+strMonth+"-"+strDay;
            return datastr;
        },
        _qryTimeQuantum:function(e){
            var widget = this,element = $(widget.el),
                timeQuantum = $(e.currentTarget).attr("name"),
                dayCount;
            timeQuantum = timeQuantum.substring(timeQuantum.indexOf("_") + 1);
            switch(timeQuantum){
                case "1":
                    dayCount = widget.global.currentDayCount;
                    break;
                case "7":
                    dayCount = widget.global.sevenDayCount;
                    break;
                case "30":
                    dayCount = widget.global.lastMonthDayCount;
                    break
                case "90":
                    dayCount = widget.global.ninthDayCount;
                    break
                default:
                    dayCount = widget.global.currentDayCount;
            }
            widget._changeTimeQuanTum(dayCount);
        },
        _changeTimeQuanTum:function(dayCount){
            var widget = this,element = $(widget.el),beginDate = element.find('#beginDate'),endDate = element.find('#endDate');
            if(dayCount==widget.global.currentDayCount){
                beginDate.val(widget._currentDate());
            }else if(dayCount==widget.global.lastMonthDayCount){
                beginDate.val(widget._lastMonthDate());
            }else{
                beginDate.val(widget._getFormatDate(dayCount));
            }
            endDate.val(widget._getFormatDate(widget.global.currentDayCount));
        },
        //当天从0点开始
        _currentDate:function(){
            var widget = this, el = $(widget.el);
            var nowDate = widget._getFormatDate(widget.global.currentDayCount);
            return nowDate;
        },
        //设置默认时间
        _setDefaultDateTime : function(){
            var widget = this, element = $(widget.el),
                beginDate = element.find("#beginDate"),
                endDate = element.find("#endDate");
            beginDate.val(widget.global.defaultBeginTime);
            endDate.val(widget.global.defaultEndTime);
        },
        //获取上个月时间
        _lastMonthDate:function (){
            var Nowdate = new Date();
            var vYear = Nowdate.getFullYear();
            var vMon = Nowdate.getMonth() + 1;
            var vDay = Nowdate.getDate();
            //每个月的最后一天日期（为了使用月份便于查找，数组第一位设为0）
            var daysInMonth = new Array(0,31,28,31,30,31,30,31,31,30,31,30,31);
            if(vMon==1){
                vYear = Nowdate.getFullYear()-1;
                vMon = 12;
            }else{
                vMon = vMon -1;
            }
            //若是闰年，二月最后一天是29号
            if(vYear%4 == 0 && vYear%100 != 0  || vYear%400 == 0 ){
                daysInMonth[2]= 29;
            }
            if(daysInMonth[vMon] < vDay){
                vDay = daysInMonth[vMon];
            }
            if(vDay<10){
                vDay="0"+vDay;
            }
            if(vMon<10){
                vMon="0"+vMon;
            }
            var date =vYear+"-"+ vMon +"-"+vDay;
            return date;
        },
        _chooseArea: function () {
            var widget = this, element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id: dialogId,
                url: widget.global.chooseArea,
                params: {},
                onClose: function (res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        /**
         * 获取查询条件参数对象
         */
        _getConds : function() {
            var widget = this,
                element = $(widget.el);
            var gSession = widget.gSession;
            var channelNbr = gSession.curChannelNbr;
            if (widget._isNullOrEmpty(channelNbr)) {
                var channels = gSession.channels;
                if (channels && channels.length > 0) {
                    var channel = channels[0];
                    channelNbr = channel.channelNbr
                }
            }
            var startTime = element.find("#beginDate").val();
            var endTime = element.find("#endDate").val();
            startTime = startTime ? startTime + " 00:00:00" : startTime;
            endTime = endTime ? endTime + " 23:59:59" : endTime;
            if (!widget._isNullStr(startTime) && !widget._isNullStr(endTime)) {
                if(startTime>endTime){
                    widget.popup("结束时间应大于开始时间!");
                    return false;
                }
                var start  = new Date(startTime.replace(/-/g,"/")).getTime();
                var end = new Date(endTime.replace(/-/g,"/")).getTime();

                var flag = end - start  > 90*24*60*60*1000
                if (flag) {
                    widget.popup("开始、结束时间跨度不能超出90天!");
                    return false;
                }
            }
            var param = {
                /*"pageInfo" : {
                    "pageIndex" : pageIndex,
                    "pageSize" : widget.global.pageSize,
                },*/
                "salesCode" : gSession.staffCode,
                "channelNbr" : channelNbr,
                "landId": gSession.staffLanId,
            };
            var bizId = widget.model.get("bizId");
            if (widget._isNullOrEmpty(bizId)) {
                widget.popup("请输入订单编码");
                return false;
            }else {
                param.bizId = bizId;
            }
            var regionId = element.find("#regionId").attr("value");
            if (widget._isNullOrEmpty(regionId)) {
                widget.popup("请选择地区！");
                return false;
            }else {
                param.commonRegionId = regionId;
                widget.global.regionId = regionId;
            }
            if(!widget._isNullStr(startTime)||!widget._isNullStr(endTime)) {

                if (!widget._isNullStr(startTime)) {
                    param.startTime = startTime;
                }
                if (!widget._isNullStr(endTime)) {
                    param.endTime = endTime;
                }
            }
            return param;
        },
        /**
         * 清空查询条件
         */
        _searchParamsDel : function() {
            var widget = this,
                element = $(widget.el);
            widget.model.set("bizId","");
            widget.model.set("regionId","");
            element.find("#regionId").val("");
            //时间设置为7天
            widget._changeTimeQuanTum(widget.global.sevenDayCount);
        },
        _queryCloudOrder : function(e) {
            var widget = this,
                element = $(widget.el);
            var param = widget._getConds();
            element.find('#showPageInfo').empty();
            widget.refreshPart("qryCloudOrder", param, ".table-hover",
                "cloudOrders", function(res) {
                    var paging = this.require("paging");
                    var totalNumber = $(res).find("input").eq(1).data("totalNumber");
                    if (parseInt(totalNumber) > 0) {
                        var e = paging.new({
                            recordNumber : widget.global.pageSize,
                            total : totalNumber,
                                pageIndex : widget.global.pageIndex,
                            callback : function(pageIndex){
                                //判断当前页
                                if(pageIndex == null || pageIndex == undefined || isNaN(pageIndex)){
                                    pageIndex = 1;
                                }
                                widget.model.set("pageIndex",pageIndex);
                                widget.refreshPart("qryCloudOrder", widget._getConds(pageIndex), ".table-hover");
                            }
                        });
                        element.find('#showPageInfo').append(e.getElement());
                    }
                }, {
                    mask : true
                });
        },
    });
    vita.widget.register("cloudOrder", cloudOrder, true);
})(window.vita);
