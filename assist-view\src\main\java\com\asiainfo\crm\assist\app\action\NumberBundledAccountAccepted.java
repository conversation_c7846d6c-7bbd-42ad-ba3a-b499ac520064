package com.asiainfo.crm.assist.app.action;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IAcctQuerySMO;
import com.asiainfo.crm.service.intf.IBindAcctQuerySMO;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.google.common.collect.Maps;

/**
 * Created by Administrator on 2018-01-27.
 */
@Component("vita.numberBundledAccountAccepted")
public class NumberBundledAccountAccepted  extends AbstractComponent{
	
	private static final Logger logger = LoggerFactory.getLogger(NumberBundledAccountAccepted.class);

    @Autowired
    private IProdInstSMO prodInstSmo;
    
    @Autowired 
    private IAcctQuerySMO acctQuerySmo;
    
    @Autowired 
    private IBindAcctQuerySMO acctBindQuerySmo;
    
    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map numberBundledAccountAccepted(String jsonStr) throws Exception {
        Map<String, Object> resultObjectMap = jsonConverter.toBean(jsonStr, Map.class);
        Map<String, Object> options = Maps.newHashMap();
        String queryCondition = (String) MapUtils.getObject(resultObjectMap, "queryCondition");
        String numberType = (String) MapUtils.getObject(resultObjectMap, "numberType");
        String regionId = (String) MapUtils.getObject(resultObjectMap, "regionId");
        String acctCd = "";
        if(resultObjectMap.containsKey("acctCd")){
        	acctCd = (String) MapUtils.getObject(resultObjectMap, "acctCd");
        }
        if("1".equals(queryCondition)){
        	String prodInstInfo =  prodInstSmo.qryAccProdInstListLocal(jsonStr);
        	Map<String, Object> prodInstInfoMap = jsonConverter.toBean(prodInstInfo, Map.class);
        	if(MapUtils.isEmpty(prodInstInfoMap)){
        		options.put("retCode", "-1");
        		options.put("retDesc", "所选地区不存在此接入号！");
        	}else{
        		if("0".equals(prodInstInfoMap.get("resultCode"))){
        			Map<String, Object> prodInstInfoRsMap = (Map<String, Object>) resolveResult(prodInstInfo);
        			if(!MapUtils.isEmpty(prodInstInfoRsMap)){
        				List prodInstDetailList = (List) prodInstInfoRsMap.get("accProdInsts");
        				if(prodInstDetailList!=null){
        					Map<String, Object> prodInstDetail = (Map<String, Object>) prodInstDetailList.get(0);
        					if(!MapUtils.isEmpty(prodInstDetail)){
        						options.put("managerNum", prodInstDetail.get("accNum"));
        						options.put("offerName", prodInstDetail.get("prodName"));
        						options.put("currentSate",prodInstDetail.get("statusName"));
        						options.put("custName", prodInstDetail.get("custName"));
        						options.put("acctNum", prodInstDetail.get("acctCd"));
        					}
        				}else{
        					  options.put("retCode", "-1");	
        				      options.put("retDesc", "无账户绑定信息！");
        				      return options;
        				}
        			}
        		}
        	}
        }else{
        	if(acctCd!=""){
        		Map<String, Object> reqMap = new HashMap<String, Object>();
        		reqMap.put("regionId", regionId);
        		reqMap.put("acctCd", acctCd);
        		String acctReqStr = jsonConverter.toJson(reqMap);
        		String acctInfo = acctQuerySmo.qryAccountListForMulti(acctReqStr);
        		Map<String, Object> acctInfoMap = (Map<String, Object>) resolveResult(acctInfo);
        		List accounts = (List) MapUtils.getObject(acctInfoMap, "accounts");
        		if(!MapUtils.isEmpty(acctInfoMap)){
        			for (int i=0;i<accounts.size();i++) {
        				Map<String, Object> accountsDetail = (Map<String, Object>) accounts.get(i);
        				String inAcctCd = (String) accountsDetail.get("acctCd");
        				if(acctCd!=""&&acctCd.trim().equals(inAcctCd.trim())){
        					options.put("managerNum", resultObjectMap.get("accessNumber"));
        					options.put("offerName", "");
        					Integer statusCd =  (Integer) accountsDetail.get("statusCd");
        					if(1==statusCd){
        						options.put("currentSate","有效");
        					}
        					if(2==statusCd){
        						options.put("currentSate","失效");
        					}
        					if(3==statusCd){
        						options.put("currentSate","未生效");
        					}
        					if(4==statusCd){
        						options.put("currentSate","已归档");
        					}
        					options.put("custName", accountsDetail.get("paymentMan"));
        					options.put("acctNum", accountsDetail.get("acctCd"));
        				}
        			}
        			
        		}
        	}
        }
        options.put("retCode", "0");	
        options.put("retDesc", "查询成功！");
        return options;
    }
    
    //查询号码绑定账户信息
    public Map qryBindAcctInfo(String jsonStr) throws Exception {
    	  Map<String, Object> options = Maps.newHashMap();
    	  if(jsonStr!=""&&jsonStr!=null){
    		  //模拟号码绑定账户信息
    		  //String bindAcctStr = "{\"resultObject\":{\"pageInfo\":{\"offset\":0,\"limit\":10,\"selectCount\":true,\"rowCount\":1,\"pageCount\":1,\"pageIndex\":1,\"pageSize\":10},\"bindAcctScLocalList\":[{\"areaCode\":\"999\",\"bindNumber\":\"1234232\",\"statusCd\":\"1000\",\"statusDate\":\"2018-01-31 00:00:00\",\"createStaff\":************,\"createDate\":\"2018-01-31 00:00:00\",\"updateDate\":\"2018-01-31 00:00:00\",\"remark\":\"测试绑定\",\"dataVerNum\":0,\"bindAcctId\":1,\"acctId\":*********,\"acctNumber\":\"************\",\"acctCd\":\"************\",\"anType\":\"9\",\"bindAnType\":\"2\",\"bindDate\":\"2018-01-31 00:00:00\",\"bindRegionId\":8511101,\"operateType\":1,\"bindOperateType\":1,\"regionId\":8511101}]},\"resultCode\":\"0\",\"resultType\":\"0\"}";
    		  String bindAcctStr = acctBindQuerySmo.qryBindAcctScLocal(jsonStr);
    		  Map<String, Object> bindAcctMap = jsonConverter.toBean(bindAcctStr, Map.class);
          	if(MapUtils.isEmpty(bindAcctMap)){
          		options.put("retCode", "-1");
          		options.put("retDesc", "查询号码绑定账户信息异常");
          	}else{
          		if("0".equals(bindAcctMap.get("resultCode"))){
          			 Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(bindAcctStr);
          			 List bindAcctScLocalList = (List) MapUtils.getObject(resultObjectMap, "bindAcctScLocalList");
          			if(bindAcctScLocalList!=null&&bindAcctScLocalList.size()>0){
          				options.put("bindAccountList", bindAcctScLocalList);
          			}
          			Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
          	        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
          	        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
          	        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
          	        options.put("pageCount",pageCount);
          	        options.put("pageIndex",pageIndex);
          	        options.put("totalNumber", total);
          	        options.put("retCode", "0");	
          	        options.put("retDesc", "查询成功！");
          		}	
    	    }
        }
      return options;
    } 
    
    
    //修改绑定账户 
    public Map updateNumberMsg(String jsonStr) throws Exception {
    	Map<String, Object> options = Maps.newHashMap();
    	Map<String, Object> reqMap = jsonConverter.toBean(jsonStr, Map.class);
    	String statusCd =  (String) MapUtils.getObject(reqMap,"statusCd");
    	if(!"1000".equals(statusCd)){
    		options.put("resultCode", "-1");	
		    options.put("resultDesc", "账户绑定状态无效,无法取消绑定");
		    return options;
    	}
    	reqMap.remove("statusCd");
    	reqMap.put("statusCd", "1300");
    	jsonStr = jsonConverter.toJson(reqMap);
		String acctInfo = acctBindQuerySmo.updateBindAcct(jsonStr);
        //测试报文
        //String acctInfo =  "{\"resultObject\":710000000001,\"resultCode\":\"0\",\"resultType\":\"0\"}";
        Map<String, Object> acctInfoMap = jsonConverter.toBean(acctInfo, Map.class);
		if(!MapUtils.isEmpty(acctInfoMap)){
			String resultCode =  (String) MapUtils.getObject(acctInfoMap,"resultCode");
			if("0".equals(resultCode)){
				options.put("resultCode", "0");	
				options.put("resultDesc", "取消账户绑定成功");
			}else{
				options.put("resultCode", "-1");	
			    options.put("resultDesc", "取消账户绑定失败");
			}
		 }else{
			options.put("resultCode", "-1");	
		    options.put("resultDesc", "账户绑定失败");
		 }
        return options;
    }
}
