package com.asiainfo.crm.assist.app.area;

import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IStaffDataPrvQuerySMO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 选择地区类
 */
@Component("vita.chooseArea")
public class ChooseArea extends AbstractSoComponent {

	@Autowired
	private IStaffDataPrvQuerySMO staffDataPrvQuerySMO;

	@Override
	public Map achieveData(Object... param) throws Exception {
		String jsonString = param[0].toString();
		Map options = new HashMap();
		List areas = this.qryDataPrivList(jsonString);
		options.put("areas", areas);
		return options;
	}

	public List qryDataPrivList(String jsonString) throws Exception {
		Map paramMap = jsonConverter.toBean(jsonString, Map.class);
		paramMap.put("busiObjNbr", MDA.DIMENSION_CODE_AREA);
		paramMap.put("privCode", MDA.PRIV_AREA_CODE);
		String paramStr = jsonConverter.toJson(paramMap);
		List areas = new ArrayList();
		String areaString = staffDataPrvQuerySMO.querySysUserRegionPriv(paramStr);
		Map<String, List> resultObject = (Map) resolveResult(areaString);
		areas = resultObject.get("areas");
		return areas;
	}

}