(function(vita) {
    var warehousingInfo = vita.Backbone.BizView.extend({
        events: {
            "click .close" : "_closePage",

        },
        _closePage : function() {
            var widget = this,
                element = $(widget.el);
            var dialog = element.closest("[data-widgetfullname=vita-dialog]");
            if (dialog.length) {
                dialog.dialog("close");
            }
        },

    });



    vita.widget.register("warehousingInfo", warehousingInfo, true);
})(window.vita);
