<div data-widget="cashierMain">
	<p class="vita-data">{"data":$options}</p>
	
	#if($options && $options != "null")
		#set($chargeData = $options.chargeData)
		#if($chargeData && $chargeData != "null")
       		#if($chargeData.resultCode == "0")
	        	#set($chargeInfo = $chargeData.resultObject.chargeInfo)
	        	#set($charges = $chargeData.resultObject.charges)
				
				<div class="page_main notopnav">
				
				<div class="col-lg-12">
					<div class="box-item">
						<div class="form_title">
							<div>
								<i class="bot"></i><strong class="text16">订单结算</strong><span class="text14 textcolor9 marl15">编号：</span><span class="text12 textcolor9">$chargeInfo.olNbr</span>
							</div>
							<div class="wmin-tools">
								<div class="btn-group mar15" id="addChargeItem">
									<button type="button" class="btn btn-w-m btn-white btn-sm">新增项目</button>
								</div>
								<div class="btn-group ">
									<button type="button" class="btn btn-w-m btn-white btn-sm dropdown-toggle" data-toggle="dropdown">
				                    #if($options.payMethodData)
		                            	$options.payMethodData[0].name
		                            #end
									<span class="caret"></span>
									</button>
									<ul class="dropdown-menu">
										#if($options.payMethodData && $options.payMethodData != "null" && $options.payMethodData.size() > 0)
											#foreach($payMethod in $options.payMethodData)
				                              <li>
				                                  <a href="javascript:void(0);">
				                                      $payMethod.name
				                                  </a>
				                              </li>
				                              <p class="vita-data">{"payMethod":$payMethod}</p>
				                            #end
										#end
									</ul>
								</div>
							</div>
						</div>
						
						#foreach($charge in $charges)
							#set($actionTypes = $charge.actionTypes)
							#foreach($actionType in $actionTypes)
								#set($items = $actionType.items)
								#if($items && $items.size() > 0)
									<div class="wmin_content ">
										<div class=" collect_tab">
											<table class="table table-invoice">
											<tbody>
											#foreach($item in $items)
												<tr>
													<td>
														$item.acctItemTypeName
													</td>
													<td class="money">
														<div class="numberinput">
															<input name="feeAmount" value="$item.feeAmount" type="text" class="form-control textcolorred"value="8" placeholder="">
														</div><span class="numberfont">元</span>
													</td>
													<td class="money">
														<select class="form-control">
															#if($item.payMethods && $item.payMethods.size() > 0)
																#foreach($itemPayMethod in $item.payMethods)
																	#if($itemPayMethod.payMethodCd == $item.payMethod)
                                                                         <option selected value="$itemPayMethod.payMethodCd">$itemPayMethod.name</option>
																	#else
                                                                         <option value="$itemPayMethod.payMethodCd">$itemPayMethod.name</option>
																	#end
                                                                    <p class="vita-data">{"itemPayMethod":$itemPayMethod}</p>
									                            #end
															#end
														</select>
														<p class="vita-data">{"payMethods":$item.payMethods}</p>
													</td>
												</tr>
											#end
											</tbody>
											</table>
										</div>
									</div>
									<p class="vita-data">{"charge":$charge}</p>
								#end	
							#end
						#end
					</div>
				</div>
				
				</div>
				<div class="page_food">
					<div class="floatl collect_leftbox">
						<div class="number_box">
							<span>已收: </span>
							<strong class="textcolorred ">&yen;$chargeInfo.paidFeeAmount</strong>
						</div>
						<div class="number_box">
							<span>转账务系统计: </span>
							<strong class="textcolorred ">&yen;$chargeInfo.hbFeeAmount</strong>
						</div>
						<div class="number_box">
							<span>实收: </span>
							<strong class="textcolorred ">&yen;$chargeInfo.realIncomeAmount</strong>
						</div>
					</div>
					<div class="floatr">
						<div class="floatl collect_leftbox">
							<div class="number_box">
								<span class="text16">总计: </span>
								<strong class="textcolorred text24">&yen;$chargeInfo.cartFeeAmount</strong>
								<span class="text12 textcolor9"> (含税金: ￥$chargeInfo.totalTax ) </span>
							</div>
						</div>
						<a class="nextbutt" id="returnBtn">上一步</a>
						<a class="nextbutt" id="nextBtn">订单确认</a>
					</div>
				</div>			
			#end
		#end
	#end
	
	<!--弹窗 start-->
	<div class="popup-mask" id="popbg">
	</div>
	<div class="agile-popup right" id="pop">
		<div class="popup-container" id="contentDiv">
			#if($options && $options.pageName)
				#widgetCreate($options.pageName, $options)
			#end
		</div>
	</div>
	<!--弹窗 end-->
</div>
