<div class="wrapper mini-rightmax"  data-widget="certNumFive" style="overflow: auto; height: 100%;">

	<div class="box-maincont">
		<div class="homenofood">
			<div class="page-nav">
				<div class="row">
					<div class="pagenav_box">
						<div class="page_title">一证五号查询</div>
					</div>
				</div>
			</div>
			<div class="page_main">
				<!--填单start-->
				<div class="col-lg-12">
					<div class="box-item">
						<div class="container-fluid row">
							<div class="form_title">
								<div>
									<i class="bot"></i>查询条件
								</div>
							</div>
							<div class="wmin_content row ">
								<form class=" form-bordered">

									<div class="form-group col-md-4">
										<label class="col-md-5 control-label lablep"> 证件类型</label>
										<div class="col-md-7">
											<select id="qryType" class="form-control">
												#foreach($identity in $options.identitys.entrySet())
													<option value="$!identity.key" certTypeName="$identity.value">$identity.value</option>
												#end
											</select>
										</div>
									</div>
									<div class="form-group col-md-4">
										<label class="col-md-5 control-label lablep"> <label
											class="wu-radio full absolute" data-scope=""> </label> 客户姓名
										</label>
										<div class="col-md-7">
											<input id="custName" type="text" class="form-control"
												placeholder="">
										</div>
									</div>
									#if($options.sensitiveOn == true)
										<div class="form-group col-md-4">
											<label class="col-md-5 control-label lablep"> 证件号码</label>
											<div class="col-md-7" >
												<div class="input-group">
													<input id="certNum" type="text" class="form-control"  placeholder="">
													<div id="readCertBtn" class="input-group-btn">
														<button name="readCert" class="btn btn-green" type="button">读取</button>
													</div>
												</div>
											</div>
										</div>
									#else
										<div class="form-group col-md-4">
											<label class="col-md-5 control-label lablep"> <label
												class="wu-radio full absolute" data-scope=""> </label> 证件号码
											</label>
											<div class="col-md-7">
												<input id="certNum" type="text" class="form-control"
													placeholder="">
											</div>
										</div>
									#end
									<div class="form-group col-md-12">
										<label class="col-md-2 control-label lablep"></label>
										<div class="col-md-10 text-right">
											<button id="btn-qryCertNumFive" type="button"
												class="btn btn-primary">查询</button>
												
										</div>
									</div>
									
								</form>
							</div>
						</div>
						
						<div class="col-lg-12" id="ToClaim">
							<div class="container-fluid row">
								<div class="box-item">
									<a id="claim" class="btn btn-gray btn-outline"><i
										class="glyphicon fa-details text18"></i> 认领 </a>
								</div>
							</div>
						</div>
						
						<div class="container-fluid row">
							<div class="form_title">
								<div>
									<i class="bot"></i>查询结果
								</div>
							</div>
							<!--<div class="wmin_content">-->
							<!--<div class="col-lg-12">
                                    <a class="btn btn-gray btn-outline">
                                        <i class="glyphicon fa-bearfruit text18"> </i> 导出查询结果
                                    </a>
                                    <a class="btn btn-gray btn-outline">
                                        <i class="glyphicon fa-equipment text18"> </i> 导出设备清单
                                    </a>
                                </div>-->
						<!-- 	<table class="table table-hover" id="certNumTable">
								<thead>
									<tr>
										<th></th>
										<th>地区</th>
										<th>类别</th>
										<th>号码</th>
										<th>状态</th>
										<th>政企产权客户名</th>
									</tr>
								</thead>
							</table> -->
							<div class="container-fluid row" id="certNumTbody"
								>
								<div class="wmin_content" id="certNumFiveListR">
									<div class="col-lg-12 mart10" id="certNumFiveList">
										<p class="vita-data">{"data": $options}</p>
										<table class="table table-hover">
											<thead>
											<tr id="certNumTable" style="display: none;">
												<th></th>
												<th>地区</th>
												<th>类别</th>
												<th>号码</th>
												<th>状态</th>
												#if($options.statusSwitch)
												<th>证号关系状态</th>
												#end
												<th>关系类型</th>
												<th>政企产权客户名</th>
											</tr>
												<tr>
													<th></th>
													<th>地区</th>
													<th>类别</th>
													<th>号码</th>
													<th>状态</th>
													#if($options.statusSwitch)
													<th>证号关系状态</th>
													#end
													<th>关系类型</th>
													<th>政企产权客户名</th>
												</tr>
											</thead>

											<tbody>
												#if($options != "null" && $options.certNumFiveList && $options.certNumFiveList != "null" && $options.certNumFiveList.size() > 0) 
												#foreach($certNumFive in $options.certNumFiveList)
												<tr>
													<td><input type="radio" name="checkCertFive">
														<a class="textcolorgreen" link="certNumFive"></a> -
														<p class="vita-data">{"certNumFive" : $!{certNumFive}
															}</p></td>
													<td>#if($certNumFive.regionName =="") #else
														$!{certNumFive.regionName} #end</td>
													<td>#if($certNumFive.partyTypeName =="") #else
														$!{certNumFive.partyTypeName} #end</td>
													<td>$!certNumFive.valueAccNum.accNum</td>
													<td>#if($certNumFive.statusCdName =="") #else
														$!{certNumFive.statusCdName} #end</td>
													#if($options.statusSwitch)
														#set($status = $!certNumFive.valueAccNum.phoneNumStatus)
														#if($status=="10")
														<td>预占</td>
														#elseif($status=="11")
														<td>实占</td>
														#else
														<td></td>
														#end
													#end
													<td>$!certNumFive.valueAccNum.systemFlagName</td>
													<td>#if($certNumFive.custType == "1000")
														$!{certNumFive.custName} #else #end</td>
												</tr>
												#end
												#elseif( $options.certNumFiveList == "null")
												<tr>
													<td align='center' colspan='7'>未查询到数据！
													<td>
												</tr>
												#end
											</tbody>
										</table>
									</div>
								</div>
									<!--翻页start-->
									<div id="showPageInfo" class="page-box"></div>
									#if($options.totalNumber && $options.totalNumber != "null")
									<p class="vita-data">{"totalNumber":$options.totalNumber}</p>
									#end
									<!--翻页end -->
							</div>
						</div>
					</div>
				</div>
			</div>
			<!--填单end-->
		</div>
	</div>
</div>

