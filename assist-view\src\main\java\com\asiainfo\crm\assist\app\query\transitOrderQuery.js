(function(vita){
    var transitOrderQuery = vita.Backbone.BizView.extend({
        events : {
            "click #btn-qryTransit":"_qryTransit"
        },
        _initialize:function(){
        	var widget = this,
			element = $(widget.el);
			var datetime = widget.require("datetime");
			var installTime = element.find("#beginDate,#endDate");
			if (installTime.length) {
				datetime.register(installTime, {
					preset : widget.global.preset
				});
			}
            
        },
        _clearUserInfo:function () {
        	
        },
        global:{
        	pageIndex:1,
        	pageSize:5,
        	preset : "date"
        },
        _getConds : function(pageIndex){
        	var param = {
    			"pageInfo": {
                    "pageIndex": pageIndex,
                    "pageSize": 10
                },
                "timeScope" : {
                	"beginDate" : $("#beginDate").val(),
                	"endDate" : $("#endDate").val()
                },
            	areaId : $("#areaId").val(),
            	channelId : $("#channelId").val(),
            	staffId : $("#staffId").val(),
            	accNum : $("#accNum").val().trim(),
            	custOrderNbr : $("#custOrderNbr").val().trim(),
            	custId : 10001, //$("#custId").val(),
            	statusCds : $("#statusCds").val(),
            	"custTokenId" : "10000001"
            };
        	return param;
        },
        _qryTransit : function () {
            this._clearUserInfo();
            var widget = this;
            var param = widget._getConds(1);
            widget.refreshPart("queryTransitList", JSON.stringify(param), "#transitList", "numbers", function(res){
            	var totalNumber = $(res).data("totalNumber");
            	alert(totalNumber);
                if (parseInt(totalNumber) > 0) {
                    var r = $(res), paging = widget.require("paging");
                    var e = paging.new({
                        recordNumber : widget.global.pageSize,
                        total : totalNumber,
                        pageIndex : widget.global.pageIndex,
                        callback : function (pageIndex, recordNumber) {
                            var inParam = widget._getConds(pageIndex);
                            widget.refreshPart("queryTransitList", JSON.stringify(inParam), "#transitList");
                        }
                    });
                    r.find(".pagination").append(e.getElement());
                }
            },{
            	mask: true
            });

        }
    });
    vita.widget.register("transitOrderQuery", transitOrderQuery, true);
})(window.vita);