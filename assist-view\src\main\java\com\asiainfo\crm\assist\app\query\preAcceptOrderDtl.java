package com.asiainfo.crm.assist.app.query;


import com.al.common.utils.ObjectUtil;
import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import com.asiainfo.crm.service.intf.IStaffOrgAreaSMO;
import com.asiainfo.crm.util.ListUtil;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.asiainfo.crm.common.MDA;

import java.io.IOException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("vita.preAcceptOrderDtl")
public class preAcceptOrderDtl extends AbstractSoComponent {

    @Autowired
    private IOrderQuerySMO orderQuerySMO;
    @Autowired
    private IStaffOrgAreaSMO staffOrgAreaSMO;
    @Autowired
    private ISoQuerySMO soQuerySMO;

    @Override
    public Map achieveData(Object... param) throws Exception {
        String p = (String) param[0];
        Map options = new HashMap();
        Map orders = this.updateAcceptOrderDtl(p);
        options.putAll(orders);
        options.putAll(jsonConverter.toBean(p, Map.class));
        return options;
    }

    private Map updateAcceptOrderDtl(String jsonString) throws Exception {
        String certTypeName = "";
        String channelName = "";
        Map options = new HashMap();

        String res = orderQuerySMO.queryPrepareOrderDetailInfo(jsonString);
        Map resultMap = (Map) resolveResult(res);
        Map detailInfoVo = (Map) resultMap.get("detailInfoVo");

        dealPrepareOrderData(detailInfoVo);
        options = detailInfoVo;
        return options;
    }


    protected void dealPrepareOrderData(Map options) throws Exception {
        // 解析状态
        HashMap<String, String> statusCdMap = new HashMap<String, String>() {{
            put("M", "待补录");
            put("I", "补录中");
            put("C", "补录完成");
            put("D", "作废");
        }};
        Map prepareOrder = (Map) options.get("prepareOrder");
        String statusCd = (String) prepareOrder.get("statusCd");
        String statusCdStr = statusCdMap.get(statusCd);
        prepareOrder.put("statusCd", statusCdStr);

        String certTypeName = "";
        String certType = (String) prepareOrder.get("certType");
        if (!StringUtil.isEmpty(certType)) {
            certTypeName = MDA.ORDER_HANDLER_IDENTITYS.get(certType);
        }
        prepareOrder.put("certTypeName", certTypeName);

        String regionId = MapUtils.getString(prepareOrder, "regionId");
        if (!StringUtil.isEmpty(regionId)) {
            prepareOrder.put("regionName", qryRegionName(regionId));
        }
        String createOrgId = MapUtils.getString(prepareOrder, "createOrgId");
        if (!StringUtil.isEmpty(createOrgId)) {
            prepareOrder.put("createOrgName", qryChannelName(createOrgId));
        }

        formatPrice(prepareOrder);

        List prepareOrderDataList = (List) options.get("prepareOrderDataList");
        if (!ListUtil.isListEmpty(prepareOrderDataList)) {
            //获取填单时的json格式
            for (int i = 0; i < prepareOrderDataList.size(); i++) {
                Map prepareOrderData = (Map) prepareOrderDataList.get(i);
                if (prepareOrderData.get("busiData") == null || prepareOrderData.get("busiData") == "") {
                    continue;
                }
                Map busiMap = jsonConverter.toBean(prepareOrderData.get("busiData").toString(), Map.class);
                Map commonData = (Map) busiMap.get("commonData");
                // 解析经办人证件类型
                String handlerCertTypeName = "";
                String handlerCertType = (String) commonData.get("handlerCertType");
                if (!StringUtil.isEmpty(handlerCertType)) {
                    handlerCertTypeName = MDA.ORDER_HANDLER_IDENTITYS.get(handlerCertType);
                }
                commonData.put("handlerCertTypeName", handlerCertTypeName);
                prepareOrderData.put("busiData", busiMap);

            }
        }
    }

    protected void formatPrice(Map prepareOrder ) throws Exception {
        DecimalFormat df = new DecimalFormat("0.00");
        //String newPrice = df.format((float)weiprice / 100);
        if (prepareOrder.containsKey("charge")) {
            int chargeNum = (int) prepareOrder.get("charge");
            String charge = df.format((float) chargeNum/100);
            prepareOrder.put("charge", charge);
        }
        if (prepareOrder.containsKey("advanceCharge")) {
            int advanceChargeNum = (int) prepareOrder.get("advanceCharge");
            String advanceCharge = df.format((float) advanceChargeNum/100);
            prepareOrder.put("advanceCharge", advanceCharge);
        }
        if (prepareOrder.containsKey("backCharge")) {
            int backChargeNum = (int) prepareOrder.get("backCharge");
            String backCharge = df.format((float) backChargeNum/100);
            prepareOrder.put("backCharge", backCharge);
        }

    }

    /**
     * 查询渠道名称
     *
     * @param createOrgId
     * @return
     * @throws Exception
     */
    private String qryChannelName(String createOrgId) throws Exception {
        String channelName = "";
        List<String> orgIds = new ArrayList<>(1);
        orgIds.add(createOrgId);
        Map<String, Object> channelParam = new HashMap<>(1);
        channelParam.put("orgIds", orgIds);
        String result = staffOrgAreaSMO.queryChannelListByIds(jsonConverter.toJson(channelParam));
        List resultList = (List) resolveResult(result);
        if (!ListUtil.isListEmpty(resultList)) {
            Map<String, Object> orgRetMap = (Map) resultList.get(0);
            channelName = MapUtils.getString(orgRetMap, "channelName", "");
        }
        return channelName;
    }

    /**
     * 查询地区名称
     *
     * @param regionId
     * @return
     * @throws Exception
     */
    private String qryRegionName(String regionId) throws Exception {
        Map<String, Object> regionIdMap = new HashMap(1);
        regionIdMap.put("regionId", regionId);
        String ret = soQuerySMO.qryCommonRegionByIds(regionIdMap);
        String regionStr = (String) resolveResult(ret);
        Map<String, Object> regionMap = jsonConverter.toBean(regionStr, Map.class);
        String regionName = MapUtils.getString(regionMap, "regionName", "");
        return regionName;
    }
}
