<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="common-view" />
        <module name="so-view" />
        <module name="configs-view" />
        <module name="local-view" />
        <module name="offerpackage-view" />
        <module name="so-web" />
      </profile>
    </annotationProcessing>
  </component>
</project>