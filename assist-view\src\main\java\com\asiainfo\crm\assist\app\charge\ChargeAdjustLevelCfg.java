package com.asiainfo.crm.assist.app.charge;


import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import com.asiainfo.crm.service.intf.ISpecChargeSmo;
import com.asiainfo.crm.util.ListUtil;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.util.List;
import java.util.Map;

@Component("vita.chargeAdjustLevelCfg")
public class ChargeAdjustLevelCfg extends AbstractComponent {

    @Autowired
    private ISpecChargeSmo chargeSmo;

    @Autowired
    private ISoQuerySMO soQuerySMO;

    @Override
    public Map achieveData(Object... objects) throws Exception {
        return null;
    }

    public Map qryChargeAdjustLevelCfgPageInfo(String params) throws Exception {
        String queryInfo = chargeSmo.queryChargeAdjustLevelCfgPage(params);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(queryInfo);
        Map<String,Object> pageInfo = (Map<String, Object>) MapUtils.getObject(resultObjectMap, "pageInfo");
        List<Map> listData = (List) MapUtils.getObject(resultObjectMap, "listData");
        for (Map map : ListUtil.nvlList(listData)) {
            map.put("statusCd", AssistMDA.COMMON_STATUS_CD_MAP.get(MapUtils.getString(map,"statusCd")));
            if (null!=AssistMDA.SERVICEOFFERID_MAP.get(MapUtils.getString(map,"serviceOfferId"))){
                map.put("serviceOfferName",AssistMDA.SERVICEOFFERID_MAP.get(MapUtils.getString(map,"serviceOfferId")));
            }
        }
        resultObjectMap.clear();
        resultObjectMap.put("reqList", listData);
        resultObjectMap.put("totalNumber", pageInfo.get("rowCount"));
        return resultObjectMap;
    }

    public Map createChargeAdjustLevelCfg(String params) throws Exception {
        String insertResult = chargeSmo.createChargeAdjustLevelCfg(params);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(insertResult);
        resultObjectMap.put("resultCode",MapUtils.getString(resultObjectMap, "resultCode"));
        resultObjectMap.put("resultMsg",MapUtils.getString(resultObjectMap, "resultMsg"));
        return resultObjectMap;
    }

    public Map updateChargeAdjustLevelCfg(String params) throws Exception {
        String updateResult = chargeSmo.updateChargeAdjustLevelCfg(params);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(updateResult);
        resultObjectMap.put("resultCode",MapUtils.getString(resultObjectMap, "resultCode"));
        resultObjectMap.put("resultMsg",MapUtils.getString(resultObjectMap, "resultMsg"));
        return resultObjectMap;
    }



}
