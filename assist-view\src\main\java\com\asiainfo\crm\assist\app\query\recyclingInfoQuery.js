(function (vita) {
    var recyclingInfoQuery = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_queryRecyclingInfo",
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _queryRecyclingInfo: function () {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var params = {
                routeParam:{
                    "regionId":gSession.staffRegionId
                }
            };
            if ($("#c_accNum").is(":checked")) {
                var accNum = $("#accNum").val();
                if (widget._isNullStr(accNum)) {
                    widget.popup("请输入接入号！");
                    return false;
                } else {
                    params.accNum = accNum;
                }
            }
            if ($("#c_mktResInstNbr").is(":checked")) {
                var mktResInstNbr = $("#mktResInstNbr").val();
                if (widget._isNullStr(mktResInstNbr)) {
                    widget.popup("请输入串码！");
                    return false;
                } else {
                    params.mktResInstNbr = mktResInstNbr;
                }
            }
            widget.refreshPart("queryRecyclingInfo", JSON.stringify(params), "#custListResult", function (re) {
            }, { async: false });
        }
    });
    vita.widget.register("recyclingInfoQuery", recyclingInfoQuery, true);
})(window.vita);