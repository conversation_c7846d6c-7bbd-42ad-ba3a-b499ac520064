package com.asiainfo.crm.assist.app.cust;

import com.asiainfo.crm.common.AbstractComponent;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Classname AccountMergeInfo
 * @Description 账户合并信息
 * @Date 2019/7/16 10:40
 * @Created by liusq5
 */
@Component("vita.custMergeInfo")
public class CustMergeInfo extends AbstractComponent {

    @Override
    public Map achieveData(Object... objects) throws Exception {
        String strJson = objects[0].toString();
        List<Map<String, Object>> list = jsonConverter.toBean(strJson, List.class);
        Map<String, Object> options = new HashMap<>();
        options.put("custMergeInfo", list);
        return options;
    }
}
