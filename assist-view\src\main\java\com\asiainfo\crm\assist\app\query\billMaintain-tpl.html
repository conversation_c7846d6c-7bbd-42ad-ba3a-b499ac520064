<div data-widget="billMaintain" style="height: 100%">
<div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
<div class="box-maincont">
<div class="homenofood">
    <div class="page_main notopnav">
         	<div class="col-lg-12">
            <div class="box-item">
               <div class="container-fluid row">
                   <div class="form_title">
                       <div><i class="bot"></i>计费接口维护工具</div>
                   </div>
               <div class="form-group col-md-12 ">
					<label class="col-md-2 control-label lablep"><span class="textcolorred">*</span>查询条件</label>
					<div class="col-md-6">
						<div class="input-group">
							<div class="input-group-btn" id="condChooseDiv">
								<select class="btn btn-default dropdown-toggle"  id="condChoose">
								        <option value="archGrpId">归档组Id</option>
                            			<option value="custOrderId">订单Id</option>
                       			</select>
							</div>
							<input id="condDesc" type="text" class="form-control" aria-label="Text input with dropdown button">
							<div id="billQueryDiv" class="input-group-btn">
								<button id="billQuery" class="btn btn-green" type="button" style="margin-left:1px">查询</button>
								<button id="billUpdate" class="btn btn-green" type="button" style="margin-left:1px">更新</button>
							</div>
						</div>
					</div>
				</div>
              </div>

              <div class="container-fluid row">
                    <div class="form_title">
                        <div><i class="bot"></i>查询结果</div>
                    </div>
                    <div class="wmin_content" id="billMaintainList">
                        <div class="col-lg-12 mart10" id="billMaintainListR">
                             <p class="vita-data">
                                                {"data": $options}
                                            </p>
                            <table class="table table-hover">
                                <thead>
                                <tr>
                                    <th width="50">归档组ID</th>
                                    <th width="50">订单ID</th>
                                    <th width="80">创建时间</th>
                                    <th width="80">竣工时间</th>
                                    <th width="100">计费处理结果</th>
                                    <th width="50">处理次数</th>
                                    <th width="80">处理时间</th>
	                                <th width="100">处理结果描述</th>
                                </tr>
                                </thead>
                                <tbody id="billMaintainListTbody">
                                #if($options.billData && $options.billData != "null" && $options.billData.size() > 0)
                                #foreach($billData in $options.billData)
                                <tr name="showBill">
                                	    <td style="display: none;" name="billInfo"> $!billData.ordBill.archGrpId</td>
                                        <td>$!billData.ordBill.archGrpId</td>
                                        <td>$!billData.ordBill.custOrderId</td>
                                        <td>$!billData.ordBill.createDate</td>
                                        <td>
                                        <input id="finishDate" name="finishDate" type="text" class="form-control" value="$!billData.ordBill.finishDate" />
                                   	    </td>
                                        <td>$!billData.ordBill.procFlag</td>
                                   	    <td>
                                   	    <input id="procCntinput" name="procCntinput" type="text" class="form-control"  value="$!billData.ordBill.procCnt"></input>
                                   	    </td>
                                        <td>$!billData.ordBill.procDate</td>
                                        <td>$!billData.ordBill.notes</td>
                                 </tr>
                                  #end
                                  #else
                                  <tr><td align='center' colspan='7'>未查询到数据！<td></tr>
                                    #end
                                  </tbody>
                                </table>
                        </div>
                    </div>
                </div>
                
              
                <div class="container-fluid row" id="billObj" style="display:none">
                    <div class="form_title">
                        <div><i class="bot"></i>总控对象表</div>
                    </div>
                    <div class="wmin_content">
                        <div class="col-lg-12 mart10" id="billMaintainListM">
                            <table class="table table-hover">
                                <thead>
                                <tr>
                                    <th width="50">归档组ID</th>
                                    <th width="80">订单项ID</th>
                                    <th width="100">订单项类型</th>
                                    <th width="100">服务提供名称</th>
                                    <th width="50">对象ID</th>
                                    <th width="80">对象规格ID</th>
                                    <th width="100">对象规格名称</th>
                                    <th width="100">状态</th>
                                    <th width="100">操作</th>
                                </tr>
                                </thead>
                                <tbody id="billMaintainListMTbody">
                                #if($options.billData && $options.billData != "null" && $options.billData.ordBillObjVos.size() > 0)
                                #foreach($ordBillObjVos in $options.billData.ordBillObjVos)
                                <tr name="billObjVos">
                                        <td style="display: none;" name="billInfoObj">$!ordBillObjVos.orderItemId</td>
                                        <td>$!ordBillObjVos.archGrpId</td>
                                        <td>$!ordBillObjVos.orderItemId</td>
                                        <td>$!ordBillObjVos.serviceOfferName</td>
                                        <td>$!ordBillObjVos.serviceOfferName</td>
                                   	    <td>$!ordBillObjVos.objId</td>
                                        <td>$!ordBillObjVos.applyObjSpec</td>
                                        <td>$!ordBillObjVos.applyObjSpecName</td>
                                        <td name="billShowStatus">$!ordBillObjVos.statusCd</td>
                                        <td><button id="changeStatus" name="statuCd" class="btn btn-green" type="button" style="margin-left:1px">屏蔽</button></td>
                                </tr>
                                <tbody  name="billtbody" style="display:none">
                                <td style="display: none;" name="billInfoStatus">$!ordBillObjVos.statusCd</td>
                                <td style="display: none;" name="billtbodyyz">$!ordBillObjVos.orderItemId</td>
								#if($ordBillObjVos.ordProdInsts.size()  > 0)
                                 <tr><td><i class="bot"></i><span style="color:red">ordProdInst表数据</span></td></tr>
                               	 <tr>
                               	 	<th width="80">账号（account）</th>
                               		<th width="80">归属人名称（ownerCustName）</th>
                                    <th width="80">归属人ID（ownerCustId）</th>
                                    <th width="80">使用人名称（useCustName）</th>
                                    <th width="80">使用人ID（useCustId）</th>
                               	 </tr>
                              	 #foreach($ordProdInsts in $ordBillObjVos.ordProdInsts)
                               	  <tr name="ordProdInsts">
                               	        <td style="display: none;" name="ordProdInstsrowid">$!ordProdInsts.rowId</td>
                               	  		<td>$!ordProdInsts.account</td>
                               	  		<td>$!ordProdInsts.ownerCustName</td>
                                       	<td>
                                   	    <input id ="ownerCustId" type="text" class="form-control" value="$!ordProdInsts.ownerCustId"></input>
                                   	  	</td>
                                   	  	<td>"$!ordProdInsts.useCustName</td>
                                   	  	<td>
                                   	    <input id ="useCustId" type="text" class="form-control" value="$!ordProdInsts.useCustId"></input>
                                   	  	</td>
                                  </tr>		
                                	   #end
                                    #end
                                
                                #if($ordBillObjVos.ordProdInstAttrs.size()  > 0)
                          	 	 <tr><td><i class="bot"></i><span style="color:red">ordProdInstAttr表数据</span></td></tr>
                               	 <tr>
                               	 	<th width="50">产品实例ID（prodInstId）</th>
                               	 	<th width="50">属性ID（attrId）</th>
                                    <th width="50">属性值（attrValue）</th>
                               	 </tr>
                              	  #foreach($ordProdInstAttrs in $ordBillObjVos.ordProdInstAttrs)
                               	  <tr name="ordProdInstAttrs">
                               	 		<td style="display: none;" name="ordProdInstAttrsrowid">$!ordProdInstAttrs.rowId</td>
                               	  		<td>$!ordProdInstAttrs.prodInstId</td>
                               	  		<td>$!ordProdInstAttrs.attrId</td>
                                       	<td>
                                   	    <input id="ordProdInstAttrsAttrValue" type="text" class="form-control"  value="$!ordProdInstAttrs.attrValue"></input>
                                   	  	</td>
                                   </tr>		
                                	   #end
                                     #end    
                                        
                                    
                             	#if($ordBillObjVos.ordProdInstAcctRels.size()  > 0)
                          	 	 <tr><td><i class="bot"></i><span style="color:red">ordProdInstAcctRel表数据</span></td></tr>
                               	 <tr>
                                    <th width="50">账户ID（acctId）</th>
                               	 </tr>
                              	  #foreach($ordProdInstAcctRels in $ordBillObjVos.ordProdInstAcctRels)
                               	  <tr name="ordProdInstAcctRels">
                               	        <td style="display: none;" name="ordProdInstAcctRelsrowid">$!ordProdInstAcctRels.rowId</td>
                                       	<td>
                                   	    <input id="acctId" type="text" class="form-control" value="$!ordProdInstAcctRels.acctId"></input>
                                   	  	</td>
                                   </tr>		
                                	   #end
                                     #end
                                 
                                #if($ordBillObjVos.ordOfferInsts.size()  > 0)
                          	 	 <tr><td><i class="bot"></i><span style="color:red">ordOfferInst表数据</span></td></tr>
                               	 <tr>
                                    <th width="50">失效时间（eff_date）</th>
                                    <th width="80">失效时间（exp_date）</th>
                               	 </tr>
                              	  #foreach($ordOfferInsts in $ordBillObjVos.ordOfferInsts)
                               	  <tr name="ordOfferInsts">
                               	 	    <td style="display: none;" name="ordOfferInstsrowid">$!ordOfferInsts.rowId</td>
                                       	<td>
                                   	    <input name="effDate" id="effDate" type="text" class="form-control" value="$!ordOfferInsts.effDate"></input>
                                   	  	</td>
                                   	  	<td>
                                   	    <input name="expDate" id="expDate" type="text" class="form-control" value="$!ordOfferInsts.expDate"></input>
                                   	  	</td>
                                   </tr>		
                                	   #end
                                     #end
                                     
                                 #if($ordBillObjVos.ordOfferInstAttrs.size()  > 0)
                          	 	 <tr><td><i class="bot"></i><span style="color:red">ordOfferInstAttr表数据</span></td></tr>
                               	 <tr>
                               	    <th width="50">销售品实例ID（offerInstId）</th>
                               	    <th width="50">属性ID（attrId）</th>
                                    <th width="50">属性值（attrValue）</th>
                               	 </tr>
                              	  #foreach($ordOfferInstAttrs in $ordBillObjVos.ordOfferInstAttrs)
                               	  <tr name="ordOfferInstAttrs">
                               	        <td style="display: none;" name="ordOfferInstAttrsrowid">$!ordOfferInstAttrs.rowId</td>
                               	  		<td>$!ordOfferInstAttrs.offerInstId</td>
                               	  		<td>$!ordOfferInstAttrs.attrId</td>
                                       	<td>
                                   	    <input id="ordOfferInstAttrsAttrValue" type="text" class="form-control" value="$!ordOfferInstAttrs.attrValue"></input>
                                   	  	</td>
                                   </tr>		
                                	   #end
                                     #end    
                                  
                                  </tbody>
                  					#end
                  				 #end  
                                    </tbody>
                                </table>
                        </div>
                    </div>
                </div>
                
                
            </div>
            </div>

    </div>
</div>
</div>
</div>
</div>
