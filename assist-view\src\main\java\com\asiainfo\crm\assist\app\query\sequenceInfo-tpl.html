<div data-widget="sequenceInfo">
    <div class="box-maincont">
        <div class="page_main notopnav">
            <div class="col-lg-12">
                <div class="box-item">
                    <div class="container-fluid row">
                        <div class="form_title">
                            <div><i class="bot"></i>查询条件</div>
                        </div>
                        <div class="wmin_content row">
                            <form class=" form-bordered">
                                <div class="form-group col-md-4">
                                <label class="col-md-4 control-label lablep">序列名称</label>
                                <div class="col-md-7">
                                    <input type="text" class="form-control" value=""
                                           id="querySequenceName">
                                </div>
                                </div>
                                <div class="form-group col-md-6">
                                    <label class="col-md-4 control-label lablep">地区</label>
                                    <div class="col-md-8">
                                        <div class="input-group">
                                            <input id="queryLanId" type="text" class="form-control" placeholder="" readonly="readonly">
                                            <div class="input-group-btn">
                                                <button id="queryRegionIdBtn" class="btn btn-green" type="button">选择</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group col-md-4 ">
                                    <label class="col-md-2 control-label lablep"></label>
                                    <div class="col-md-9 text-right">
                                        <button type="button" class="btn btn-white" id="resetBtn">清除</button>
                                        <button type="button" class="btn btn-primary" id="queryBtn">查询</button>
                                        <button type="button" class="btn btn-primary" id="addBtn">添加</button>
                                        <button type="button" class="btn btn-primary" id="batchImport">批量导入</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="container-fluid row">
                        <div class="form_title">
                            <div><i class="bot"></i>查询结果</div>
                        </div>
                        <div class="wmin_content" id="loadData">
                            <div class="col-lg-12">
                                <a class="btn btn-gray btn-outline" id="editBtn">
                                    <i class="glyphicon icon-progress text18"> </i> 编辑
                                </a>
                                <a class="btn btn-gray btn-outline" id="deleteBtn">
                                    <i class="glyphicon icon-batch text18"> </i> 删除
                                </a>
                            </div>
                            <div class="col-lg-12 mart10" name="tableDiv">
                                <table class="table table-hover">
                                    <thead>
                                    <tr>
                                        <th>选择</th>
                                        <th>序列ID</th>
                                        <th>序列名称</th>
                                        <th>表名称</th>
                                        <th>字段名称</th>
                                        <th>序列最大值</th>
                                        <th>字段大小</th>
                                        <th>序列大小</th>
                                        <th>地区</th>
                                        <th>告警阈值</th>
                                        <th>序列所属中心</th>
                                        <th>创建员工</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>备注</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    #if($options.reqList!="null" && $options.reqList.size()>0)
                                    #foreach($fc in $options.reqList)
                                    <tr>
                                        <td>
                                            <label class="wu-radio full absolute" data-scope="">
                                                <input type="radio" name="payment">
                                                <p class="vita-data">{"fcSequenceInfoId":"$!fc.sequenceInfoId"}</p>
                                            </label>
                                        </td>
                                        <td>$!fc.sequenceInfoId</td>
                                        <td>$!fc.seqName</td>
                                        <td>$!fc.tName</td>
                                        <td>$!fc.columnName</td>
                                        <td>$!fc.maxNum</td>
                                        <td>$!fc.columnSize</td>
                                        <td>$!fc.seqSize</td>
                                        <td>$!fc.regionName</td>
                                        <td>$!fc.threshold</td>
                                        <td>$!fc.center</td>
                                        <td>$!fc.staffName</td>
                                        <td>$!fc.statusCd</td>
                                        <td>$!fc.createDate</td>
                                        <td>$!fc.remark</td>
                                        <td><button type="button" class="btn btn-primary" value= $!fc.seqName id="checkZkValue">查看</button></td>
                                    </tr>
                                    #end
                                    #end
                                    </tbody>
                                </table>
                                #if($options.totalNumber)
                                <p class="vita-data" id = "_totalNumber">{"totalNumber":$options.totalNumber}</p>
                                #end
                            </div>
                            <div id="pageInfo">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--弹窗 start-->
    <div class="popup" style="display:none;">
        <div class="popup-mask active"></div>
        <div class="agile-popup right active" style="width: 75%">
            <div class="popup-container">
                <!--内容-->
                <div class="calctitle">
                    <div class="titlefont">添加</div>
                    <div class="toolr">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                    </div>
                </div>

                <div class="wmin_content row" style="margin-top: 10px">
                    <form class=" form-bordered" name="_popupForm">
                        <div class="hideAndShow form-group col-md-6" id="sequenceInfoIdColumn">
                            <label class="col-md-4 control-label lablep">序列id号</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_sequenceInfoIdVal" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">序列名称</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_sequenceNameVal"  placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">表名称</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_tableNameVal" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">字段名称</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_columnNameVal" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">序列最大值</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_maxNumVal" placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">字段大小</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_columnSizeVal" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">序列大小</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_sequenceSizeVal" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label class="col-md-4 control-label lablep">地区</label>
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input id="_lanId" type="text" class="form-control" placeholder="" readonly="readonly">
                                    <div class="input-group-btn">
                                        <button id="_regionIdBtn" class="btn btn-green" type="button">选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label class="col-md-4 control-label lablep">告警阀值</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_thresholdVal" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 " >
                            <label class="col-md-4 control-label lablep">序列所属中心</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_centerVal" required>
                            </div>
                        </div>
                        <div class="hideAndShow form-group col-md-6">
                            <label class="col-md-4 control-label lablep">状态</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_statusCodeVal" required>
                            </div>
                        </div>

                        <div class="form-group col-md-6">
                            <label class="col-md-4 control-label lablep">备注</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_remarkVal" required>
                            </div>
                        </div>
                        <div class="form-group col-md-12">
                            <div class="col-md-12 text-center">
                                <button type="button" class="btn btn-primary" id="addSubmitBtn">添加</button>
                                <button type="button" class="btn btn-primary" id="editSubmitBtn">编辑</button>
                                <button type="button" class="btn btn-primary" id="closeBtn">取消</button>
                                <input id="_fcRuleId" type="text" class="form-control" style="display: none" value="" readonly>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!--end-->

    <!--导入exel弹窗-->
    <div class="popup1" style="display:none;" >
        <div class="popup-mask active"></div>
            <div class="agile-popup right active" style="width: 75%">
            <div class="popup-container">
                <!--内容-->
                <div class="calctitle">
                    <div class="titlefont">导入Excel模板</div>
                    <div class="toolr">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                    </div>
                </div>

                <div class="wmin_content row" style="margin-top: 10px">
                    <form method="post" id="importForm" class="form-bordered" enctype="multipart/form-data">
                        <div class="form-group col-md-12 "></div>

                        <div class="form-group col-md-12">
                            <label class="col-md-4 control-label lablep">EXCEL文件：</label>
                            <div class="col-md-4">
                                <input id="upFile" name="upFile" value="" type="file">
                            </div>
                            <button type="button" class="btn btn-primary" id="submitImportBtn">导入</button>
                            <button type="button" class="btn btn-primary" id="tempDownloadBtn">模板下载</button>
                        </div>
                        <div class="form-group col-md-12">
                            <label class="col-md-4 control-label lablep">注意事项：</label>
                            <div class="col-md-9">
                                (1)仅支持上传xls、xlsx，单元格格式请设置为文本。
                                <br>
                                (2)严格按照模板导入信息。
                            </div>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>
    <!--导入exel结束-->
    <!--查看序列在zk序号弹窗-->
    <div class="zkPopup" style="display:none;" >
        <div class="popup-mask active"></div>
        <div class="agile-popup active" style="width: 45%; height: 20%; margin-left: 30%; margin-top: 10%;">
            <div class="popup-container">
                <!--内容-->
                <div class="calctitle">
                    <div class="titlefont">查看</div>
                    <div class="toolr">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                    </div>
                </div>
                <div style="margin-top: 2%; margin-left: 10%">zk序列号:<p id="zkSequenceInfo" style="margin-left: 10%"></div>

                </div>
            </div>
        </div>
    </div>
    <!--zk弹窗结束-->
</div>
</div>
</div>
<style>
    .modal{z-index: 9910;}
    .agile-popup{z-index: 9000;}
</style>
