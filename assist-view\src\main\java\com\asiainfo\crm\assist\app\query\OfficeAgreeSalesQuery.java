package com.asiainfo.crm.assist.app.query;
import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IAssetCenterSMO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("vita.officeAgreeSalesQuery")
public class OfficeAgreeSalesQuery extends AbstractComponent{
    @Autowired
   private IAssetCenterSMO assetCenterSMO;

    @Override
    public Map achieveData(Object... params) throws Exception{
        Map retMap = new HashMap();
        retMap.put("statusCdList", AssistMDA.OFFICE_RECORD_STATUS);
        return retMap;
    }
    /*
    * 查询政企销售品列表
    * */
    public Map qryOfferInstFeeInfoList(String json) throws Exception{
      //  String resultString = "{\"resultObject\": [{\"statusCd\": \"1000\",\"statusDate\": \"2019-04-23 00:00:00\",\"createStaff\": 11,\"createDate\": \"2019-04-23 00:00:00\",\"updateStaff\": 11,\"updateDate\": \"2019-04-23 00:00:00\",\"remark\": \"11\",\"lastOrderItemId\": -1,\"dataVerNum\": 0,\"offerInstFeeInfoId\": 1111111,\"offerInstId\": 11111,\"offerId\": 30050002,\"prodInstId\": 11111,\"acctItemTypeId\": 11111,\"currency\": \"111\",\"ratio\": 22,\"amount\": 33,\"ratioMethod\": \"44\",\"isFixedPrice\": \"33\",\"realAmount\": 33,\"chargeOrg\": 33,\"commChargeOrg\": 33,\"regionId\": 823000,\"ownerCustId\": 11111}],\"resultCode\": \"0\",\"resultType\": \"0\"}";
        String resultString = assetCenterSMO.qryOfferInstFeeInfoList(json);
        List orderList = (List)resolveResult(resultString);
        Map<String, Object> options = Maps.newHashMap();
        options.put("orderList",orderList);
        return options;
    }

}


