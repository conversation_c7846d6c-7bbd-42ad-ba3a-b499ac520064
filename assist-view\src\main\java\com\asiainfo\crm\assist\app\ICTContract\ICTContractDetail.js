(function(vita) {
    var ICTContractDetail = vita.Backbone.BizView.extend({
        events : {
            "click #btnClose": "_closeBtnClick",
            "click #myTab li" : "_clickTab",
            "click .table-hover .orderDetail a": "_showOrderDetail",
            "click .table-hover .orderPlan a": "_showOrderPlan",
        },
        global : {
            ICTInputItem : "../query/qryICTInputItem",
            pageSize : 8 //每页记录数
        },
        _initialize : function () {
            var widget = this,
                el = $(widget.el);
            widget._queryICTSubList();
        },
        _closeBtnClick: function (e) {
            var widget = this,
                el = $(widget.el);
            el.closest("[data-widgetfullname=vita-dialog]").dialog("close");
        },
        _clickTab : function(e) {
            var widget = this,
                element = $(widget.el),
                global = widget.global,
                $tar = $(e.target),
                li = $tar.closest("li");
            li.addClass("active").siblings("li").removeClass("active");
            var tabIndex = li.attr("id").replace(/\D/g, '');
            var content = element.find("#content" + tabIndex);
            if (content.length) {
                content.fadeIn(300).addClass("in active")
                    .siblings("div").hide().removeClass("in active");
            }
        },
        _queryICTSubList : function() {
            var widget = this,el = $(widget.el);
            var values = el.data("params");
            var params = widget._getConds(1, values);
            var showPageInfo = el.find("#showPageInfo");
            var recordNumber=widget.global.pageSize;
            showPageInfo.empty();
            widget.refreshPart("qrySubIctContractByItem", params, ".table-hover",
                "result", function(res) {
                    var totalNumber = $(res).find("td").eq(0).data("totalNumber");
                    if (parseInt(totalNumber) > 0) {
                        var paging = widget.require("paging");
                        var e = paging.new({
                            recordNumber : recordNumber,
                            total : totalNumber,
                            pageIndex : 1,
                            callback : function(pageIndex,recordNumber) {
                                //判断当前页
                                if(pageIndex == null || pageIndex == undefined || isNaN(pageIndex)){
                                    pageIndex = 1;
                                }
                                var params = widget._getConds(pageIndex, values);
                                params.pageInfo.rowCount = totalNumber;
                                widget.refreshPart("qrySubIctContractByItem", params, ".table-hover", $.noop, {
                                    headers : {
                                        "regionId" : widget.gSession.installArea
                                    }
                                });
                            }
                        });
                        showPageInfo.append(e.getElement());
                    }
                }, {
                    mask : true,
                    headers : {
                        "regionId" : widget.gSession.installArea
                    }
                });
        },
        _showOrderDetail :function(e){
            var widget = this,el = $(widget.el);
            var a=$(e.target).closest("a");
            var data=a.data("data") || {};
            if(!data || data==null || data.custOrderId==null ){
                widget.popup("订单不存在！");
                return;
            }
            window.open("../../crm/so/olDetailInfo?custOrderId="+data.custOrderId);
        },
        _showOrderPlan :function(e){
            var widget = this,el = $(widget.el),params={};
            var a=$(e.target).closest("a");
            var data=a.data("data") || {};
            params.subItemCode=data.subItemCode;
            widget.dialog({
                id : "ICTInputItem",
                url : widget.global.ICTInputItem,
                params : params,
                onClose : function(res) {
                    // var ICTContractDetail = $(res).closest("[data-widget=ICTInputItem]");

                }
            });
        },
        _getConds : function(pageIndex,values) {
            var widget = this,el = $(widget.el);
            var param = {
                "contractCode" :values.contractCode,
                "pageInfo" : {
                    "pageIndex" : pageIndex,
                    "pageSize" : widget.global.pageSize
                }
            };
            return param;
        },
    });
    vita.widget.register("ICTContractDetail",ICTContractDetail, true);
})(window.vita);