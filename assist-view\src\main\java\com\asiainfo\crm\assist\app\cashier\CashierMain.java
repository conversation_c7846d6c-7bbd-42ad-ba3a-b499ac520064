package com.asiainfo.crm.assist.app.cashier;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IOrderSMO;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 收银主体类
 */
@Component("vita.cashierMain")
public class CashierMain extends AbstractComponent {

	@Autowired
	private IOrderSMO orderSMO;

	@Override
	public Map achieveData(Object... params) throws Exception {
		String jsonString = params[0].toString();
		Map options = new HashMap();
		
		//查询算费信息
//		String chargeString = orderSMO.queryCharges(jsonString);
		String chargeString = this.getMockData();
		Map chargeMap = jsonConverter.toBean(chargeString, Map.class);
		options.put("chargeData", chargeMap);
		
		//查询支付方式
//		String payMethodString = orderSMO.queryPayMethods(jsonString);
		String payMethodString = "{\n" +
                " \"resultCode\":\"0\",\n" +
                " \"resultMsg\":\"处理成功!\",\n" +
                " \"resultObject\":{\n" +
                "  \"payMethods\":[\n" +
                "   {\n" +
                "    \"description\":\"现金支付\",\n" +
                "    \"name\":\"现金支付\",\n" +
                "    \"payMethodCd\":1\n" +
                "   },\n" +
                "   {\n" +
                "    \"description\":\"支票支付\",\n" +
                "    \"name\":\"支票支付\",\n" +
                "    \"payMethodCd\":2\n" +
                "   },\n" +
                "   {\n" +
                "    \"description\":\"汇票支付\",\n" +
                "    \"name\":\"汇票支付\",\n" +
                "    \"payMethodCd\":3\n" +
                "   },\n" +
                "   {\n" +
                "    \"description\":\"转帐务系统\",\n" +
                "    \"name\":\"转帐务系统\",\n" +
                "    \"payMethodCd\":4\n" +
                "   }\n" +
                "  ]\n" +
                " }\n" +
                "}";
		Map payMethodMap = jsonConverter.toBean(payMethodString, Map.class);
		String payMethodCode = MapUtils.getString(payMethodMap, "resultCode");
		if (!StringUtil.isEmpty(payMethodCode) && payMethodCode.equals(MDA.RESULT_SUCCESS.toString())) {
			Map resultObject = (Map) payMethodMap.get("resultObject");
			List payMethodData = (List) resultObject.get("payMethods");
			options.put("payMethodData", payMethodData);
		}
		
		return options;
	}

	public String queryChargeItems(String jsonString) throws Exception {
		// String resString = orderSMO.queryChargeItems(jsonString);
		String resString = "{\n" + " \"resultCode\":\"0\",\n"
				+ " \"resultMsg\":\"处理成功!\",\n" + " \"resultObject\":{\n"
				+ "  \"chargeItems\":[\n" + "   {\n"
				+ "    \"chargeItemCd\":\"43\",\n" + "    \"name\":\"工料费\",\n"
				+ "    \"taxItemId\":10030101,\n" + "    \"taxRate\":17\n"
				+ "   },\n" + "   {\n" + "    \"chargeItemCd\":\"63\",\n"
				+ "    \"name\":\"预存款\",\n" + "    \"taxItemId\":10500101,\n"
				+ "    \"taxRate\":0\n" + "   },\n" + "   {\n"
				+ "    \"chargeItemCd\":\"1315\",\n"
				+ "    \"name\":\"ADSL违约金\",\n"
				+ "    \"taxItemId\":10010201,\n" + "    \"taxRate\":6\n"
				+ "   }\n" + "  ]\n" + " }\n" + "}";
		return resString;
	}

	public String queryPayMethods(String jsonString) throws Exception {
		// String resString = orderSMO.queryPayMethods(jsonString);
		String resString = "{\n" + " \"resultCode\":\"0\",\n"
				+ " \"resultMsg\":\"处理成功!\",\n" + " \"resultObject\":{\n"
				+ "  \"payMethods\":[\n" + "   {\n"
				+ "    \"description\":\"现金支付\",\n" + "    \"name\":\"现金支付\",\n"
				+ "    \"payMethodCd\":1\n" + "   },\n" + "   {\n"
				+ "    \"description\":\"支票支付\",\n" + "    \"name\":\"支票支付\",\n"
				+ "    \"payMethodCd\":2\n" + "   },\n" + "   {\n"
				+ "    \"description\":\"汇票支付\",\n" + "    \"name\":\"汇票支付\",\n"
				+ "    \"payMethodCd\":3\n" + "   },\n" + "   {\n"
				+ "    \"description\":\"转帐务系统\",\n"
				+ "    \"name\":\"转帐务系统\",\n" + "    \"payMethodCd\":4\n"
				+ "   }\n" + "  ]\n" + " }\n" + "}";
		return resString;
	}

	public String queryBusiObjs(String jsonString) throws Exception {
		// String resString = orderSMO.queryBusiObjs(jsonString);
		String resString = "{\n" + " \"resultCode\":\"0\",\n"
				+ " \"resultMsg\":\"处理成功!\",\n" + " \"resultObject\":{\n"
				+ "  \"busiObjs\":[\n" + "   {\n" + "    \"accNbr\":\"111\",\n"
				+ "    \"actionTypeName\":\"a\",\n" + "    \"boId\":\"111\",\n"
				+ "    \"busiObjName\":\"手机\",\n"
				+ "    \"busiSpecId\":\"378\",\n"
				+ "    \"serviceOfferName\":\"新装1\"\n" + "   },\n" + "   {\n"
				+ "    \"accNbr\":\"222\",\n"
				+ "    \"actionTypeName\":\"aa\",\n" + "    \"boId\":\"222\",\n"
				+ "    \"busiObjName\":\"固话\",\n"
				+ "    \"busiSpecId\":\"2\",\n"
				+ "    \"serviceOfferName\":\"新装2\"\n" + "   },\n" + "   {\n"
				+ "    \"accNbr\":\"333\",\n"
				+ "    \"actionTypeName\":\"aaa\",\n"
				+ "    \"boId\":\"333\",\n" + "    \"busiObjName\":\"宽带\",\n"
				+ "    \"busiSpecId\":\"9\",\n"
				+ "    \"serviceOfferName\":\"新装3\"\n" + "   }\n" + "  ]\n"
				+ " }\n" + "}";
		return resString;
	}

	public String saveChargeItem(String jsonString) throws Exception {
		// String resString = orderSMO.saveChargeItem(jsonString);
		String resString = "{\"resultCode\":\"0\",\"resultMsg\":\"处理成功!\"}";
		return resString;
	}

	public String updateChargeItem(String jsonString) throws Exception {
		// String resString = orderSMO.updateChargeItem(jsonString);
		String resString = "{\"resultCode\":\"0\",\"resultMsg\":\"处理成功!\"}";
		return resString;
	}

	public String commitCashier(String jsonString) throws Exception {
		// String resString = orderSMO.commitCashier(jsonString);
		String resString = "{\"resultCode\":\"0\",\"resultMsg\":\"处理成功!\"}";
		return resString;
	}

	public Map refreshContent(String jsonString) throws Exception {
		Map ret = jsonConverter.toBean(jsonString, Map.class);
		String pageName = ret.get("pageName").toString();
		Map datas = achieveData(pageName, jsonString);
		if (datas != null) {
			ret.putAll(datas);
		}
		return ret;
	}

	public String getMockData() {
		return "{\n" + " \"resultCode\":\"0\",\n"
				+ " \"resultMsg\":\"处理成功!\",\n" + " \"resultObject\":{\n"
				+ "  \"chargeInfo\":{\n" + "   \"cartFeeAmount\":\"1\",\n"
				+ "   \"hasStaffAdjustPermition\":\"Y\",\n"
				+ "   \"hbFeeAmount\":\"0\",\n" + "   \"olId\":100000476286,\n"
				+ "   \"olNbr\":\"20170303914164\",\n"
				+ "   \"paidFeeAmount\":\"2\",\n" + "   \"partyId\":\"1212\",\n"
				+ "   \"partyName\":\"1212\",\n"
				+ "   \"realIncomeAmount\":\"3\",\n" + "   \"totalTax\":\"4\"\n"
				+ "  },\n" + "  \"charges\":[{\n"
				+ "   \"accNbr\":\"1359999999\",\n"
				+ "   \"actionClassCd\":\"4\",\n" + "   \"actionTypes\":[{\n"
				+ "    \"accNbr\":\"1359999999\",\n"
				+ "    \"actionTypeFeeAmount\":\"800\",\n"
				+ "    \"boId\":\"100001323352\",\n" + "    \"items\":[\n"
				+ "     {\n" + "      \"accNbr\":\"\",\n"
				+ "      \"acctItemId\":\"100000096730\",\n"
				+ "      \"acctItemTypeId\":\"63\",\n"
				+ "      \"acctItemTypeName\":\"预存款\",\n"
				+ "      \"boId\":\"100001323352\",\n"
				+ "      \"feeAmount\":\"0.00\",\n"
				+ "      \"hasItemAdjustPermition\":\"Y\",\n"
				+ "      \"infoId\":\"100000096729\",\n"
				+ "      \"itemSourceId\":\"11\",\n"
				+ "      \"payMethod\":\"1\",\n"
				+ "      \"payMethodName\":\"现金支付\",\n"
				+ "      \"payMethods\":[\n" + "       {\n"
				+ "        \"description\":\"现金支付\",\n"
				+ "        \"name\":\"现金支付\",\n" + "        \"payMethodCd\":1\n"
				+ "       },\n" + "       {\n"
				+ "        \"description\":\"支票支付\",\n"
				+ "        \"name\":\"支票支付\",\n" + "        \"payMethodCd\":2\n"
				+ "       },\n" + "       {\n"
				+ "        \"description\":\"汇票支付\",\n"
				+ "        \"name\":\"汇票支付\",\n" + "        \"payMethodCd\":3\n"
				+ "       },\n" + "       {\n"
				+ "        \"description\":\"转帐务系统\",\n"
				+ "        \"name\":\"转帐务系统\",\n"
				+ "        \"payMethodCd\":4\n" + "       }\n" + "      ],\n"
				+ "      \"productId\":\"2\",\n"
				+ "      \"serviceOfferId\":\"1\",\n"
				+ "      \"serviceOfferName\":\"新装\",\n"
				+ "      \"soChargeStatusCd\":\"1\",\n"
				+ "      \"tax\":\"566\",\n"
				+ "      \"taxItemId\":\"10010203\",\n"
				+ "      \"taxRate\":\"6\"\n" + "     },\n" + "     {\n"
				+ "      \"accNbr\":\"\",\n"
				+ "      \"acctItemId\":\"100000096732\",\n"
				+ "      \"acctItemTypeId\":\"83060\",\n"
				+ "      \"acctItemTypeName\":\"终端设备费\",\n"
				+ "      \"boId\":\"100001323352\",\n"
				+ "      \"feeAmount\":\"8.00\",\n"
				+ "      \"hasItemAdjustPermition\":\"Y\",\n"
				+ "      \"infoId\":\"100000096731\",\n"
				+ "      \"itemSourceId\":\"11\",\n"
				+ "      \"payMethod\":\"2\",\n"
				+ "      \"payMethodName\":\"支票支付\",\n"
				+ "      \"payMethods\":[\n" + "       {\n"
				+ "        \"description\":\"现金支付\",\n"
				+ "        \"name\":\"现金支付\",\n" + "        \"payMethodCd\":1\n"
				+ "       },\n" + "       {\n"
				+ "        \"description\":\"支票支付\",\n"
				+ "        \"name\":\"支票支付\",\n" + "        \"payMethodCd\":2\n"
				+ "       },\n" + "       {\n"
				+ "        \"description\":\"汇票支付\",\n"
				+ "        \"name\":\"汇票支付\",\n" + "        \"payMethodCd\":3\n"
				+ "       }\n" + "      ],\n" + "      \"productId\":\"2\",\n"
				+ "      \"serviceOfferId\":\"1\",\n"
				+ "      \"serviceOfferName\":\"新装\",\n"
				+ "      \"soChargeStatusCd\":\"1\",\n"
				+ "      \"tax\":\"1585\",\n"
				+ "      \"taxItemId\":\"10010201\",\n"
				+ "      \"taxRate\":\"6\"\n" + "     }\n" + "    ],\n"
				+ "    \"serviceOfferId\":\"1\",\n"
				+ "    \"serviceOfferName\":\"新装\"\n" + "   }],\n"
				+ "   \"boId\":\"100001323352\",\n"
				+ "   \"busiObjFeeAmount\":\"800\",\n"
				+ "   \"busiObjId\":\"100000008658\",\n"
				+ "   \"busiObjName\":\"手机\",\n" + "   \"busiSpecId\":\"2\",\n"
				+ "   \"calFeeFlag\":1\n" + "  }]\n" + " }\n" + "}";
	}

}
