package com.asiainfo.crm.assist.app.satellite;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICustSMO;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.asiainfo.crm.service.intf.ISatelliteSystemServiceSmo;
import com.asiainfo.crm.util.EopUtil;
import com.asiainfo.crm.util.XmlConverUtil;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Component("vita.releaseSatelliteNum")
public class ReleaseSatelliteNum extends AbstractComponent {

	private static final Logger logger = LoggerFactory.getLogger(ReleaseSatelliteNum.class);

	@Autowired
	private ISatelliteSystemServiceSmo satelliteSystemServiceSmo;

	@Autowired
	private IProdInstSMO prodInstSMO;

	@Autowired
	private ICustSMO custSMO;

	@Override
	public Map achieveData(Object... params) throws Exception {
		return null;
	}
	public Map satelliteRelease(String inStr) throws Exception {
		Map result = new HashMap();
		Map<String, Object> paramMap = jsonConverter.toBean(inStr, Map.class);
		Map<String, String> phoneNumReqMap = (Map) paramMap.get("phoneNumReq");
		String custNumber = phoneNumReqMap.get("custNumber");
		if(custNumber == null || phoneNumReqMap.get("commonRegionId") == null) {
			// 根据接入号查询客户Id
			Map instParamMap = new HashMap();
			instParamMap.put("accNum", phoneNumReqMap.get("phoneNum"));
			String prodInstStr = prodInstSMO.qryAccProdInstListLocal(jsonConverter.toJson(instParamMap));
			Map instResultObject = (Map) resolveResult(prodInstStr);
			List<Map> prodInsts = (List<Map>) instResultObject.get("accProdInsts");
			if(null != prodInsts && prodInsts.size() == 1) {
				Map prodInst = prodInsts.get(0);
				// 查询客户ID对应的客户编码
				Map custParamMap = new HashMap();
				custParamMap.put("custId", prodInst.get("ownerCustId"));
				String customerDetailStr = custSMO.qryCustomerDetail(jsonConverter.toJson(custParamMap));
				Map custResultObject = (Map) resolveResult(customerDetailStr);
				Map customerDetail = (Map) custResultObject.get("customerDetail");
				if (null != customerDetail && null != customerDetail.get("ext2CustId")) {
					custNumber = String.valueOf(customerDetail.get("ext2CustId"));
					String regionId = String.valueOf(customerDetail.get("regionId"));
					phoneNumReqMap.put("custNumber", custNumber);
					phoneNumReqMap.put("commonRegionId", regionId);
					inStr = JSONObject.fromObject(paramMap).toString();
				}
			}
		}
		if(custNumber != null) {
			// 组装eop报文
			Map<String,Object> contMap=new LinkedHashMap<>();
			contMap.put("SvcCont",phoneNumReqMap);
			Map<String,Object> tcoContMap= EopUtil.buildTcpContMap(inStr,"orders.changePhoneNumIot");
			contMap.put("TcpCont",tcoContMap);

			String xmlStr = XmlConverUtil.mapToXml(contMap,"ContractRoot");
			String resultXmlStr = satelliteSystemServiceSmo.HttpAPIService(xmlStr);
			result = (Map)EopUtil.resolveResult(resultXmlStr);
			return result;
		} else {
			result.put("resultCode", "-1");
		}
		return result;
	}

}