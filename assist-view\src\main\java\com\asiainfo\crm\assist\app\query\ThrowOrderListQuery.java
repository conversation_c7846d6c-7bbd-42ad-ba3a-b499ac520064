package com.asiainfo.crm.assist.app.query;

import com.asiainfo.angel.json.JsonConverter;
import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IAgencyCapitalSMO;
import com.asiainfo.crm.service.intf.IBillingSmo;
import com.asiainfo.crm.service.intf.ICommQuerySMO;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.IResourceSMO;
import com.asiainfo.crm.service.intf.ISoConfigSMO;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import com.asiainfo.crm.service.intf.IStaffDataPrvQuerySMO;
import com.asiainfo.crm.util.ListUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 甩单信息查询
 * liyc7
 * Created on 2019/3/6
 */
@Component("vita.throwOrderListQuery")
public class ThrowOrderListQuery extends AbstractComponent {

    @Autowired
    private IOrderQuerySMO orderQuerySMO;

    @Autowired
    private ICommQuerySMO commQuerySMO;

    @Autowired
    private ISoConfigSMO iSoConfigSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    /**
     * 查询甩单列表
     */
    public Map queryThrowCustomOrderList(String json) throws Exception {
        JsonConverter jsonConverter = JsonConverter.buildNormalConverter();
        Map reqMap = jsonConverter.toBean(json, Map.class);
        String regionId = String.valueOf(reqMap.get("regionId"));
        String throwOrderItemList = orderQuerySMO.queryThrowOrderListInfo(json);

        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(throwOrderItemList);
        Map<String, Object> options = Maps.newHashMap();
        List orderList = (List) MapUtils.getObject(resultObjectMap, "customerOrders");

        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", total);
        List throwOrders = dealOrderList(orderList);
        options.put("throwOrders", throwOrders);

        return options;
    }

    private List dealOrderList(List orderList) throws Exception{
        List<Map<String,Object>> throwOrders = new ArrayList<>();
        String retStr = commQuerySMO.qryAttrValuesById(AssistMDA.ORDER_ITEM_STATUS_NBR);
        List<Map<String, Object>> resultObjectMap = (List<Map<String, Object>>) resolveResult(retStr);
        if(!ListUtil.isListEmpty(orderList)){
            for(int i = 0;i < orderList.size();i++){
                Map<String,Object> throwOrder = (Map<String, Object>) orderList.get(i);
                String attrValue = MapUtils.getString(throwOrder,"statusCd");
                throwOrder.put("statusName","其他");
                if(!ListUtil.isListEmpty(resultObjectMap)){
                    for(Map<String,Object> objectMap : resultObjectMap){
                        String objectMapAttrValue = MapUtils.getString(objectMap,"attrValue");
                        if(attrValue.equals(objectMapAttrValue)){
                            throwOrder.put("statusName",MapUtils.getString(objectMap,"attrValueName"));
                        }
                    }
                }
                throwOrders.add(throwOrder);
            }
        }
        return throwOrders;
    }

}
