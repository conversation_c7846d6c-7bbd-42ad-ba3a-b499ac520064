(function (vita) {
    var uimComp = SoComp.extend({
        //事件绑定
        events: {
            "click #btn-query": "_replaceUIM",
            "click #checkUimBtn": "_validateAn",
            "click #printDeliveryBtn": "_callCrmCommonBusiB",
            "click .close" : "_closePage",
        },
        global: {
            isFuseOffer:false,   // 是否融合主套餐
            is4GFlag:null,       // 是否4G套餐标识,调用集团接口进行UIM卡的校验释放
            validateFlag: false, // 用来标识,uim是否通过校验
            hasChooseOffer:false,// 是否有可选基础套餐
            baseOfferId:"",      // 基础销售品ID
            defaultUimCode:"",    // 准备出来默认物品
            uimInfo:{}, //展示uim卡详情
            oldFlag : false,
            subData :{},
            uimDataCopy:null, //widget.Model.get('data')[0]复制
            isProvinceUim : false, //uim默认为非省内卡
            isWriteCard : false
        },
        accNumData:{ // 关联号码
            accNum:null,
            anId:null
        },
        //组件初始化逻辑
        _initialize: function () {
            debugger
            var widget = this, element = widget.$el,gSession = widget.gSession;
            var data = element.data("data");
            if (data.type=="BKXX"){
               var dataString =  data.dataString;
               if (dataString==-1){
                   btnEl = element.find("#checkUimBtn"), btnEl.data("clkType", "check");
               }else {
                   var param = {
                       "mktResInstNbr": data.dataString,
                       "channelNbr":gSession.curChannelNbr,
                       "channelId" : gSession.curChannelId,
                       "regionId":gSession.installArea,
                       "lanId":widget.convertArea(gSession.installArea),
                       "staffCode":gSession.staffCode,
                       "routeParam":{
                           "regionId":gSession.installArea
                       }
                       // "channelNbr":"5111001151577",
                       // "channelId" : "330037327",
                       // "regionId":"8530100",
                       // "lanId":widget.convertArea("8530100"),
                       // "staffCode":"zhangmg",
                       // "routeParam":{
                       //     "regionId":"8530100"
                       // }
                   };
                   this._getCardInfo(param);
               }
            }else if (data.type=="printDelivery"){
                var dataString = data.dataString;
                if (dataString!=-1){
                    element.find('#printDeliveryText').val(dataString);
                }
            }
        },
        //当前业务的卡类型
        _getCurChooseCardType: function () {
            var widget = this, element = $(widget.el);
            var chooseCardTypeBtn = element.find(".btn-default");
            if (chooseCardTypeBtn.length  && chooseCardTypeBtn.data()) {
                return chooseCardTypeBtn.data("value");
            }
            return widget.getMDA("MKT_RES_TYPE_UIM");
        },

        //当前是否为esim业务
        //根据当前产品类型下拉框
        _isESimCardBusiness: function () {
            return this._getCurChooseCardType() == this.getMDA("MKT_RES_TYPE_ESIM");
        },

        _isOrderUpdate:function(){ // 是否改单
            var uimData = this.model.get("data")[0];
            if(uimData){
                var prodInstId = uimData.prodInstId ? uimData.prodInstId : uimData.instId;
                if(prodInstId > 0 && uimData.serviceOfferId == this.getConstant("SERVICE_OFFER_ID_NEW")){
                    return true;
                }
            }
            return false;
        },
        _isHideBtn:function(){
            var widget = this,
                el = $(widget.el);
            if(widget.global.is4GFlag == widget.getConstant("OFFER_BUSITYPE_4G")) {
                el.find("button[name=virtual]").hide();
            }else{
                el.find("button[name=virtual]").show();
            }
            widget.global.isWriteCard ? el.find("#writeCard").show():null;
        },
        _addListenerEvent:function(seq){
            var widget = this,
                el = $(widget.el);
            var objectUtil = widget.require("objectUtil");
            var flowInstId = widget.model.get("flowInstId"),
                prodId = el.data("prodId"), offerProdRelId = el.data("data").offerProdRelId;
            widget.addListener("chooseNum_" + flowInstId + "_" + seq, function(data){
                if(data && data.anId){
                    widget.accNumData.anId = data.anId;
                }
                if(data && data.accNum){
                    widget.accNumData.accNum = data.accNum;
                }
            });

            // 产品组件删除事件监听
            widget.addListener("delProdCompEvent" + flowInstId, function(data) {
                // 对已预占资源释放处理
                if(data && data.seq && data.seq == seq){
                    var uimData = widget.model.get("data")[0];
                    if (widget._isESimCardBusiness()) {
                        if (uimData.othMktResInstNbr) {
                            widget._releaseESIM();
                        }
                    } else {
                        if(uimData.mktResInstId){
                            widget._releaseUim(uimData);
                        }
                    }
                }
            });

            // 基础套餐选择事件派发
            widget.addListener("chooseBaseOffer_" + flowInstId + "_" + seq, function(data) {
                // 对已预占资源释放处理
                if(!objectUtil.isEmpty(data)){
                    if(data.is4GFlag){
                        widget._setGlobalIs4GFlag(data.is4GFlag);
                    }else{
                        var is4G = el.data("is4GFlag");
                        widget._setGlobalIs4GFlag(is4G);
                    }
                    widget.global.hasChooseOffer = data.hasChooseOffer;
                    widget.global.baseOfferId = data.offerSpecId;
                }
            });
            //动作链带出3G改4G
            widget.addListener("fourGProds_" + flowInstId, function(fourGProd) {
                var uimData = widget.model.get("data")[0];
                var prodInstId = uimData.prodInstId ? uimData.prodInstId : uimData.instId;
                if (fourGProd.prodInstId == prodInstId && fourGProd.busiType) {
                    widget._setGlobalIs4GFlag(fourGProd.busiType);
                }
            });

            //超简甩单更新数据
            var prepareOrderEventNames = [];
            prepareOrderEventNames.push("prepareOrderUpdateComp_uimComp_" + prodId + "_" + flowInstId);
            if(offerProdRelId) {
                prepareOrderEventNames.push("prepareOrderUpdateComp_uimComp_" + prodId + "_"+offerProdRelId+"_" + flowInstId);
            }
            $.each(prepareOrderEventNames, function (i, eventName) {
                widget.addListener(eventName, function (re) {
                    //只回填数据到输入框，手动发起校验
                    if (!el.find('#uimText').val() && re.event) {
                        el.find('#uimText').val(re.event.code);
                        delete re.event
                    }
                });
            });

            //自动校验uim卡
            widget.addListener("checkUimCard_" + flowInstId + "_" + seq, function () {
                if(!el.find('#uimText').attr("readOnly") && el.find('#uimText').val()) {
                    widget._validateAn();
                }
            });

        },
        // UIM卡校验
        _validateAn: function (e) {
            debugger
            var widget = this,
                element = widget.$el,
                btnEl = element.find("#checkUimBtn"),
                virtualCardFlag = btnEl.attr('name')?1:0,
                btnName = virtualCardFlag?'虚拟卡校验':'校验';
            if (btnEl.data("clkType") == "check") {
                var ret;
                ret = widget._occupy();
                if (ret) {
                    btnEl.html('取消');
                    btnEl.removeData();
                    btnEl.siblings("button").hide();
                    element.find('#uimText').attr("readonly", true);
                }
            } else {
                var ret = widget._release();
                if (ret) {
                    if (!widget._isESimCardBusiness()) {
                        widget.global.uimInfo="";
                        element.find('#uimText').attr("data-original-title",widget.global.uimInfo);
                        //btnEl.siblings("button").show();
                        widget._isHideBtn();
                    }
                    btnEl.html(btnName);
                    btnEl.data("clkType", "check");
                    element.find('#uimText').attr("readonly", false);
                }
            }
            this._callCrmCommonBusiA()
        },
        _callCrmCommonBusiA: function () {
            var widget = this,
                element = widget.$el;
            var data = element.data("data");
            var param ={};
            param.iccid = element.find('#uimText').val();
            param.digitalOrderInfoId = data.digitalOrderInfoId
            param.busiCode = "updateDigitalOrderInfo"
            widget.callService("callCrmCommonBusi", JSON.stringify(param), function (res) {
                if (res.rspCode=="10000") {
                    widget.popup(res.rspDesc);
                }else {
                    widget.popup("提交有误！"+res.rspDesc);
                }
            }, {async: false});
        },
        _callCrmCommonBusiB: function () {
            var widget = this,
                element = widget.$el;
            var data = element.data("data");
            var param ={};
            param.deliveryId = element.find('#printDeliveryText').val();
            param.digitalOrderInfoId = data.digitalOrderInfoId
            param.statusCd = "1100"
            param.busiCode = "updateDigitalOrderInfo"
            widget.callService("callCrmCommonBusi", JSON.stringify(param), function (res) {
                if (res.rspCode=="10000") {
                    widget.popup(res.rspDesc);

                }else {
                    widget.popup("提交有误！"+res.rspDesc);
                }
            }, {async: false});
        },

        _getCardInfo: function (param) {
            var widget = this,
                element = widget.$el;
            widget.callService("getCardInfoOne", param, function (res) {
                if (res) {
                    if(res.handleResultCode == "0"){
                        element.find("#correctMark").show();
                        var subData = widget.global.uimInfo;
                        if(res.smdpAddress){
                            subData.smdpAddress = res.smdpAddress;
                        }
                        if(res.matchingId){
                            subData.matchingId = res.matchingId;
                        }
                        if(res.eId){
                            subData.eId = res.eId;
                        }
                        debugger
                        subData.anTypeCd = res.anTypeCd;
                        subData.mktResId = res.mktResId;
                        subData.mktResType = res.mktResType;
                        subData.mktResInstId = res.mktResInstId;
                        subData.deviceNumId = res.deviceNumId;
                        subData.mktResInstNbr = res.mktResInstNbr;
                        subData.busiType = widget.global.is4GFlag;
                        subData.operType = widget.getConstant("CONST_OPER_TYPE_ADD");
                        subData.othMktResInstNbr = res.othMktResInstNbr;
                        widget.model.set("data", subData);
                        widget.global.validateFlag = true;
                        widget.global.oldFlag = false;
                        widget.global.uimInfo = subData;
                        btnEl = element.find("#checkUimBtn")
                        btnEl.html('取消');
                        btnEl.removeData();
                        btnEl.siblings("button").hide();
                        element.find('#uimText').attr("readonly", true);
                        element.find('#uimText').val(res.mktResInstNbr);
                    }else {
                        widget.popup("当前卡信息查询有误！"+res);
                    }
                }
            }, {async: false});
        },
        _satelliteOccupy: function () {
            var widget = this, el = widget.$el,retFlag = false,gSession = widget.gSession;
            var uimCode = el.find("#uimText").val().trim();
            if (!uimCode) {
                widget.popup("UIM卡不能为空!");
                return;
            }
            if(uimCode != "" && uimCode == widget.global.defaultUimCode){
                widget.popup("请输入新的UIM卡再做校验...");
                return;
            }
            var param = {
                "actionType": "A",
                "mktResInstNbr": uimCode,
                "channelNbr":gSession.curChannelNbr,
                "channelId" : gSession.curChannelId,
                "regionId":gSession.installArea,
                "lanId":widget.convertArea(gSession.installArea),
                "staffCode":gSession.staffCode,
                "routeParam":{
                    "regionId":gSession.installArea
                }
            };
            if(widget.accNumData.anId){
                param.anId = widget.accNumData.anId;
            }
            if(widget.global.is4GFlag && widget.global.is4GFlag != "null"){
                param.busiType = widget.global.is4GFlag;
            }
            if(widget.global.is4GFlag == widget.getConstant("OFFER_BUSITYPE_4G") && widget.accNumData.accNum) {
                param.accNum = widget.accNumData.accNum;
            }
            //使用旧uim查询接口的入参
            if("N"==widget.getMDA("WX_NEW_INTERFACE_SWITCH")){
                param={
                    mktResourceCode:uimCode
                }
            }
            var prodId = el.closest("[data-widget=prodBar]").data().prodData.objId;
            var prodIds = widget.getConstant("WLW_PROD_IDS");//物联网类产品id
            var index = $.inArray(prodId,prodIds);
            //临时分支物联网、卫星
            if(index >= 0){
                /*var rgexp = new RegExp('^\d{19}$');
                if(!rgexp.test(parseInt(uimCode))){
                    widget.popup("UIM卡格式不对...");
                    return;
                }*/
                var subData = widget.model.get("data");
                subData[0].mktResInstNbr = uimCode;
                subData[0].busiType = widget.global.is4GFlag;
                subData[0].mktResId = el.data().data.data[0].mktResId;
                subData[0].mktResType =el.data().data.data[0].mktResType;
                widget.model.set("data", subData);
                widget.submit(widget.model.toJSON());
                widget.global.validateFlag = true;
                widget.global.oldFlag = false;
                retFlag = true;
            }else {
                widget.callService("satelliteCheck", param, function (res) {
                    if (res) {
                        if (res["mktResouceInfo"].statusCd == "1001") {

                            if(!res.mktResInstNbr){
                                res.mktResInstNbr = uimCode;
                            }
                            console.info("uim卡号检验返回结果"+uimCode,res);
                            // widget._submitData(res,uimInfo);
                            var subData = widget.model.get("data");
                            subData[0].mktResInstNbr = res["mktResouceInfo"].mktResourceCode;
                            subData[0].busiType = widget.global.is4GFlag;
                            //subData[0].mktResId = res["mktResouceInfo"].mktResCd;
                            subData[0].mktResType =res["mktResouceInfo"].mktResTypeCd;
                            var Cimsi="";
                            $.each(res["mktResouceInfo"].attrInfos,function(i,e){
                                if(e.attrName=="C-IMSI(3G)"){
                                    Cimsi=e.attrValue;
                                }
                            });
                            subData[0].mktResInstId =Cimsi ;
                            /* subData[0].anTypeCd = res.anTypeCd;


                             subData[0].deviceNumId = res.deviceNumId ? res.deviceNumId : uimInfo.deviceNumId;
                             subData[0].mktResInstNbr = res.mktResInstNbr;
                             subData[0].operType = widget.getConstant("CONST_OPER_TYPE_ADD");*/
                            widget.model.set("data", subData);
                            widget.submit(widget.model.toJSON());
                            widget.global.validateFlag = true;

                            widget.global.oldFlag = false;
                            retFlag = true;
                        } else {
                            widget.popup("uim错误或不可用");
                        }
                    }
                }, {async: false})
            };
            return retFlag;
        },
        _checkValue : function(condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty ? false : true;
        },
        _dealRegionForGz:$.noop,
        _occupyEnd: vita.noop,
        _occupyBegin: vita.noop,
        _occupy: function () {
            var widget = this, el = widget.$el,retFlag = false,gSession = widget.gSession;
            var uimCode = el.find("#uimText").val().trim();
            if (!uimCode) {
                widget.popup("UIM卡不能为空!");
                return;
            }
            if(uimCode != "" && uimCode == widget.global.defaultUimCode){
                widget.popup("请输入新的UIM卡再做校验...");
                return;
            }
            //widget._setAccNum();
            // 判断uim卡是否存在,存在获取uim卡信息与准备出来的类型作判断,通过后再做uim预占
             var uimInfo = widget._qryUimInfo(uimCode);
             if(!uimInfo){
                 return retFlag;
             }
            var param = {
                "actionType": "A",
                "mktResInstNbr": uimCode,
                "channelNbr":gSession.curChannelNbr,
                "channelId" : gSession.curChannelId,
                "regionId":gSession.installArea,
                "lanId":widget.convertArea(gSession.installArea),
                "staffCode":gSession.staffCode,
                "routeParam":{
                    "regionId":gSession.installArea
                }
            };
            if(widget.accNumData.anId){
                param.anId = widget.accNumData.anId;
            }
            if(widget.global.is4GFlag && widget.global.is4GFlag != "null" && !widget.global.isProvinceUim){
                param.busiType = widget.global.is4GFlag;
            }
            if(widget.global.is4GFlag == widget.getConstant("OFFER_BUSITYPE_4G") && widget.accNumData.accNum) {
                param.accNum = widget.accNumData.accNum;
            }
            //贵州补换卡对地址进行特殊处理
            widget._dealRegionForGz(param);
            //预占请求发起时，埋点
            widget._occupyBegin('uim卡预占开始');
            widget.callService("occupy", param, function (res) {
                if (res) {
                    if(res.handleResultCode == "0"){
                        el.find("#correctMark").show();
                        // if(!res.mktResInstNbr){
                        //     res.mktResInstNbr = uimCode;
                        // }
                         widget._submitData(res,uimInfo);
                        widget.global.oldFlag = false;
                        retFlag = true;
                        //预占成功发起时，埋点
                        widget._occupyEnd('uim卡预占成功')
                    }else{
                        //预占失败发起时，埋点
                        widget._occupyEnd(res.handleResultMsg)
                        widget.popup(res.handleResultMsg);
                    }
                }
            }, {async: false});
            return retFlag;
        },
        _submitData:function(res,uimInfo){
            var widget = this;
            var subData = widget.global.uimInfo;
            if(res.smdpAddress){
                subData.smdpAddress = res.smdpAddress;
            }
            if(res.matchingId){
                subData.matchingId = res.matchingId;
            }
            if(res.eId){
                subData.eId = res.eId;
            }
            subData.anTypeCd = res.anTypeCd;
            subData.mktResId = res.mktResId ? res.mktResId : uimInfo.mktResId ;
            subData.mktResType = uimInfo.terminalDevSpecId;
            subData.mktResInstId = res.mktResInstId ? res.mktResInstId : uimInfo.mktResInstId;
            subData.deviceNumId = res.deviceNumId ? res.deviceNumId : uimInfo.deviceNumId;
            subData.mktResInstNbr = res.mktResInstNbr;
            subData.busiType = widget.global.is4GFlag;
            subData.operType = widget.getConstant("CONST_OPER_TYPE_ADD");
            subData.othMktResInstNbr = res.othMktResInstNbr;
            widget.global.uimInfo = subData;
            widget.model.set("data", subData);
            widget.global.validateFlag = true;
        },
        _replaceParam : function(param,virtualCardFlag){
            return param;
        },
        _qryUimInfo:function(uimCode,virtualCardFlag){
            var widget = this,
                gSession = widget.gSession;
            var retFlag = false;
            var param = {
                "regionId":gSession.installArea,
                "channelNbr":gSession.curChannelNbr,
                "channelId" : gSession.curChannelId,
                "staffCode":gSession.staffCode,
                "lanId":widget.convertArea(gSession.installArea),
                "mktResInstNbr": uimCode,
                "routeParam":{
                    "regionId":gSession.installArea
                },
                "pageInfo": {
                    "pageSize": "20",
                    "pageIndex": "1"
                }
            };
            //贵州补换卡对地址进行特殊处理
            widget._dealRegionForGz(param);
            widget.callService("getTerminalDevice", param, function (res) {
                if (res) {
                    if(res.handleResultCode == "0"){
                        // 获取uim卡规格
                        debugger
                        var uimSpecId = res.terminalDevSpecId;
                        if(!uimSpecId){
                            widget.popup("UIM卡规格数据未返回,请重新输入...");
                            return retFlag;
                        }
                        retFlag=true;
                        // $.each(specDatas,function(i,obj){
                        //     if(obj && obj.value == uimSpecId){
                        //         retFlag = res;
                        //         return false;
                        //     }
                        // });
                        // if(!retFlag){
                        //     widget.popup("UIM卡号存在,卡类型不匹配,请重新输入...");
                        // }
                        // if(widget._checkValue(retFlag.mktResName) && widget._checkValue(retFlag.manuName)){
                        //     widget.global.uimInfo="UIM类型:"+retFlag.mktResName+retFlag.manuName;
                        // }

                    }else{
                        widget.popup(res.handleResultMsg);
                    }
                }else{
                    widget.popup("您输入的UIM卡号不存在,请重新输入...");
                }
            }, {async: false});
            return retFlag;
        },
        _release: function () {
            var widget = this,
                element = widget.$el;
            //在用物品的释放是在归档时进行处理。如果此处进行释放会导致老物品的变更撤单后原物品变成可用
            if (!widget.global.oldFlag) {
                var rslt;
                rslt = widget._releaseUim(widget.global.uimInfo);
                if (!rslt.flag) {
                    widget.popup(rslt.msg);
                    return false;
                }
                widget.global.validateFlag = false;
            }
            element.find('#uimText').val('');
            element.find("#correctMark").hide();
            return true;
        },
        /**
         * UIM释放
         * @private
         */
        _releaseUim: function (uimData) {
            debugger
            var widget = this,gSession = widget.gSession;
            var param = {
                "actionType": "R",
                "anTypeCd":uimData.anTypeCd,
                "deviceNumId": uimData.deviceNumId,
                "mktResInstId":uimData.mktResInstId,
                "mktResInstNbr":uimData.mktResInstNbr,
                "regionId":gSession.installArea,
                "channelNbr":gSession.curChannelNbr,
                "channelId" : gSession.curChannelId,
                "lanId":widget.convertArea(gSession.installArea),
                "staffCode":gSession.staffCode,
                "routeParam":{
                    "regionId":gSession.installArea
                }
            };
            if(widget.accNumData.anId){
                param.anId = widget.accNumData.anId;
            }
            //4G套餐及非省内卡才能加上此参数
            if(widget.global.is4GFlag && widget.global.is4GFlag != "null" && !widget.global.isProvinceUim){
                param.busiType = widget.global.is4GFlag;
            }
            var returnVal = {flag: false};
            //贵州补换卡对地址进行特殊处理
            widget._dealRegionForGz(param);
            //预占请求发起时，埋点
            widget._occupyBegin('uim卡预占开始');
            widget.callService("occupy", param, function (data) {
                if(data){
                    if (data.handleResultCode == 0) {
                        returnVal.flag = true;
                        widget.global.isProvinceUim = false; //释放后3G标识再次默认为false
                        //预占成功发起时，埋点
                        widget._occupyEnd('uim卡预占成功')
                    } else {
                        returnVal.flag = false;
                        returnVal.msg = "UIM卡释放失败。";
                        //预占失败发起时，埋点
                        widget._occupyEnd('uim卡预占失败')
                    }
                }
            }, {async: false});
            return returnVal;
        },
        getValue: function () {
            var widget = this, el = $(widget.el);
            var compData = widget.model.get("data")[0], chooseCardType = el.find(".btn-default");
            var info = chooseCardType.length ? chooseCardType.data("name"):"UIM";
            if (!widget.global.validateFlag) {
                widget.popup(info + "不能为空且需校验通过.");
                return false;
            }
            return true;
        },
        _putUimCodeToCookie:function(uimCodeInfo){

            var widget=this;
            var prodBar=widget.$el.closest("[data-widget=prodBar]");
            var soUtil = widget.require("soUtil");
            if("Sichuan"==g_regionFlag && prodBar.length>0 && prodBar.data("data").isPackageFlowCode && prodBar.data("data").isPackageFlowCode == "Y"){
                soUtil.removeCookie("uimcodePkg");
                soUtil.setCookie("uimcodePkg",uimCodeInfo);
            }
        },
        _getESIM: function () {
            var widget = this, el = $(widget.el), result = false, gSession = widget.gSession;
            var uimCode = $.trim(el.find("#uimText").val());
            if (!widget.accNumData.accNum) {
                widget.popup("请先选号...");
                return false;
            }
            if (!uimCode) {
                widget.popup("ESIM卡不能为空...");
                return false;
            }
            if (uimCode != "" && uimCode == widget.global.defaultUimCode) {
                widget.popup("请输入新的ESIM卡再做校验...");
                return false;
            }
            widget._setAccNum();


            if ("Y" == widget.getMDA("ESIM_NEW_SWITCH")) {
                return widget._getESIMNew(uimCode);
            }

            var params = {
                eid: uimCode,
                lanId: widget.convertArea(gSession.installArea),
                phoneNum: widget.accNumData.accNum,
                staffId: gSession.staffCode,
                channelId : gSession.curChannelNbr,
                mktResType: widget.getMDA("MKT_RES_TYPE_ESIM"),
                mktResId: "",
                commonRegionId: gSession.installArea
            };
            widget.callService("getESIM", params, function (re) {
                if (re) {
                    if (re.iccid) {
                        el.find("#correctMark").show();
                        var temp = {
                            terminalDevSpecId: widget.getMDA("MKT_RES_TYPE_ESIM")
                        };
                        re.mktResInstNbr = uimCode;
                        re.othMktResInstNbr = re.iccid;
                        re.mktResInstId = -1;
                        widget._submitData(re, temp);
                        el.find("input[name=iccid]").val(re.iccid);
                        widget.global.oldFlag = false;
                        result = true;
                        var data = widget.model.get("data")[0], obj = {
                            prodInstId: data.prodInstId ? data.prodInstId : data.instId,
                            checkIccid: true
                        };
                        widget.dispatchEvent("checkIccid_" + widget.model.get("flowInstId"), obj);
                    } else {
                        widget.popup(re.resultMsg);
                    }
                }
            }, {async: false});
            return result;
        },
        //esim新方式
        _getESIMNew : function(eid){
            var widget = this, el = $(widget.el),
                gSession = widget.gSession,
                result = false;
            var groupEsimMktResType  = widget.getMDA("MKT_RES_TYPE_UIM"),
                esimMktResType = widget.getMDA("MKT_RES_TYPE_ESIM");
            var data = el.data("data");
            var uimData = data.data[0];
            //新装时mktResId从valueRange节点去，补换卡时从data节点下取；
            //mktResId调集团时，有些省份能对应到mktResCd，无法对应的省份暂时默认取MDA中值
            var mktResId = uimData.mktResId;
            if (uimData.serviceOfferId == widget.getMDA("SERVICE_OFFER_ID_NEW") && uimData.valueRange) {
                var curValue = uimData.valueRange.filter(function (range) {
                    return range.value == esimMktResType;
                });
                if (!_.isEmpty(curValue) && curValue[0]) {
                    mktResId = curValue[0].mktResId;
                }
            }

            var params = {
                eid: eid,
                mktResId: mktResId,
                lanId: widget.convertArea(gSession.installArea),
                phoneNum: widget.accNumData.accNum,
                staffId: gSession.staffCode,
                channelId : gSession.curChannelNbr,
                mktResType: groupEsimMktResType,
                commonRegionId: gSession.installArea,
                occupyParam : {
                    esimCardAttrVo : {
                        eId : eid
                    }
                },
                resource : {
                    "actionType": "A",
                    "channelNbr":gSession.curChannelNbr,
                    "channelId" : gSession.curChannelId,
                    "terminalDevSpec": esimMktResType,
                    "regionId":gSession.installArea,
                    "lanId":widget.convertArea(gSession.installArea),
                    "staffCode":gSession.staffCode,
                    "routeParam":{
                        "regionId":gSession.installArea
                    },
                    "esimCardAttrVo" : {
                        eId : eid
                    }

                }
            };
            if(widget.accNumData){
                params.occupyParam.anId = widget.accNumData.anId;
                params.resource.anId = widget.accNumData.anId;
                params.resource.accNum = widget.accNumData.accNum;
            }
            //调集团绑定以及调资源预占
            widget.callService("getESIMNew", params, function (re) {
                if (re) {
                    if (re.resultCode == "0" && re.iccid) {
                        el.find("#correctMark").show();
                        var temp = {
                            terminalDevSpecId: groupEsimMktResType
                        };
                        re.mktResInstNbr = re.iccid;
                        re.othMktResInstNbr = eid;
                        re.eId = eid;
                        widget._submitData(re, temp);
                        el.find("input[name=iccid]").val(re.iccid);
                        widget.global.oldFlag = false;
                        result = true;
                        var data = widget.model.get("data")[0], obj = {
                            prodInstId: data.prodInstId ? data.prodInstId : data.instId,
                            checkIccid: true
                        };
                        widget.dispatchEvent("checkIccid_" + widget.model.get("flowInstId"), obj);
                    } else {
                        widget.popup(re.resultMsg);
                    }
                }
            }, {async: false});
            return result;
        },
        _releaseESIM: function () {
            var widget = this, gSession = widget.gSession, data = widget.model.get("data")[0];

            if ("Y" == widget.getMDA("ESIM_NEW_SWITCH")) {
                return widget._releaseESIMNew();
            }

            var params = {
                eid: data.mktResInstNbr,
                iccid: data.othMktResInstNbr,
                lanId: widget.convertArea(gSession.installArea),
                phoneNum: widget.accNumData.accNum,
                staffId: gSession.staffCode,
                channelId : gSession.curChannelNbr,
                mktResType: data.mktResType,
                mktResId: "",
                commonRegionId: gSession.installArea
            }, result = {flag: false};
            widget.callService("releaseESIM", params, function (re) {
                result.flag = false;
                result.msg = "ESIM卡释放失败...";
                if (re) {
                    if (re.resultCode == "0") {
                        result.flag = true;
                        result.msg = "";
                        $(widget.el).find("input[name=iccid]").val("");
                        var obj = {
                            prodInstId: data.prodInstId ? data.prodInstId : data.instId,
                            checkIccid: false
                        };
                        widget.dispatchEvent("checkIccid_" + widget.model.get("flowInstId"), obj);
                    } else {
                        result.msg = re.resultMsg;
                    }
                }
            }, {async: false});
            return result;
        },
        _releaseESIMNew: function () {
            var widget = this, gSession = widget.gSession, data = widget.model.get("data")[0];
            var params = {
                eid: data.othMktResInstNbr,
                mktResId: data.mktResId,
                iccid: data.mktResInstNbr,
                lanId: widget.convertArea(gSession.installArea),
                phoneNum: widget.accNumData.accNum,
                staffId: gSession.staffCode,
                channelId : gSession.curChannelNbr,
                mktResType: data.mktResType,
                commonRegionId: gSession.installArea,
                releaseParam : {
                    "actionType": "R",
                    "anTypeCd":data.anTypeCd,
                    "deviceNumId": data.deviceNumId,
                    "mktResInstId":data.mktResInstId,
                    "mktResInstNbr":data.mktResInstNbr,
                    "regionId":gSession.installArea,
                    "channelNbr":gSession.curChannelNbr,
                    "channelId" : gSession.curChannelId,
                    "lanId":widget.convertArea(gSession.installArea),
                    "staffCode":gSession.staffCode
                }
            }, result = {flag: false};


            if(widget.accNumData){
                params.releaseParam.anId = widget.accNumData.anId;
                params.releaseParam.accNum = widget.accNumData.accNum;
            }

            widget.callService("releaseESIMNew", params, function (re) {
                result.flag = false;
                result.msg = "ESIM卡释放失败...";
                if (re) {
                    if (re.resultCode == "0") {
                        result.flag = true;
                        result.msg = "";
                        $(widget.el).find("input[name=iccid]").val("");
                        var obj = {
                            prodInstId: data.prodInstId ? data.prodInstId : data.instId,
                            checkIccid: false
                        };
                        widget.dispatchEvent("checkIccid_" + widget.model.get("flowInstId"), obj);
                    } else {
                        result.msg = re.resultMsg;
                    }
                }
            }, {async: false});
            return result;
        },
        _closePage : function() {
            var widget = this,
                element = $(widget.el);
            var dialog = element.closest("[data-widgetfullname=vita-dialog]");
            if (dialog.length) {
                dialog.dialog("close");
            }
        },
    });
    vita.widget.register("uimComp", uimComp, true);
})(window.vita);
