
(function (vita) {
    var getFreePortnfo = vita.Backbone.BizView.extend({
        events: {
            "click #btn_queryNum": "_queryActionByQueryType",
            "click #txtAreaBtn": "_chooseArea",
            "click [name='queryType']": "_chooseType"

        },
        _initialize: function () {
            // $("#tblRelaProdQueryBy2an").hide();
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            var compCode = "chooseArea";
            var dialogId = "chooseAreaDialog";
            //选择地区
            var option = {
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#txtArea").val(data.regionName).attr("value", data.commonRegionId);
                }
            };
            widget.dialog(option);
            /*var option = {
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#txtArea").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            };
            widget.dialog(option);*/

        },
        global: {
            searchType:"0",
            chooseArea: "../comm/chooseArea"
        },
        //点击查询地区
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#txtArea").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        //选择类型
        _chooseType:function (e) {
            var el=$(e.target);
            var widget = this,element = $(widget.el);
            if (el.attr("id")=="queryTpye0") {
                $("#qrylable").empty().append("承载号码:");
                widget.global.searchType="0";
            } else{
                $("#qrylable").empty().append("宽带帐号:");
                widget.global.searchType="1";
            }
        },
        //查询列表
        _queryActionByQueryType: function () {
            var widget = this,element = $(widget.el);
            var params = widget._getConds();
            params.searchType=widget.global.searchType;
            if (params) {
                widget.callService("getFreePortnfo", params, function (retn) {
                    $('#freeportnfoTd').text("");
                    console.info(retn);
                    if(retn.ResultCode==0){
                        if (retn.protnum == -1){
                            alert("资源系统没查到产品信息");
                        }else if (retn.protnum == -2){
                            alert("资源系统数据异常");
                        }else{
                            $('#freeportnfoTd').text(retn.protnum);
                            $('#tblRelaProdQueryBy2an').slideDown("slow");
                        }
                    }else{
                        alert(retn.ResultDesc);
                    }
                });
            };
        },
        //请求参数设置
        _getConds: function () {
            var widget = this;
            var params = {};
            //宽带账号
            var account = $.trim($("#txtfreeportQry").val());
            //地区ID
            var regionId=$("#txtArea").attr("value");
            // var regionId=$("#txtArea").val();
            params.regionId=regionId;
            if(widget._isNullStr(account)) {
                widget.popup("请输入承载号码！");
                return false;
            } else {
                var searchType=widget.global.searchType;
                if(searchType==0){
                    //电话
                    params.accNum=account;
                }else{
                    //宽带
                    params.account = account;
                }
            }
            return params;
        },
        _isNullStr: function (str) {
            if (str == null || str == "") {
                return true;
            }
            return false;
        },
    });
    vita.widget.register("getFreePortnfo", getFreePortnfo, true);
})(window.vita);