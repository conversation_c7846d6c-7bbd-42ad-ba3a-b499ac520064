<div data-widget="numberBundledAccountAccepted" style="height: 100%">
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->

        <!--客户定位 start
        <div class="box-rightmax">
            <div class="positionuser_title"><h5 class="textcolor6"><i
                    class="icon-user glyphicon titledot"></i><span>客户定位</span></h5></div>
            <div class="positionuser_search">
                <div class="searchbutt"><i class="icon-search glyphicon text16"></i></div>
                <input type="text" class="form-control" placeholder="输入客户证件活名称搜索">
            </div>

            <div class="scan_box">
                <span class="icon-scan glyphicon scanf"></span>
                <div>扫描身份证</div>
            </div>
            <div class="adduser_box"><a class="btn btn-primary btn-rounded" href="buttons.html#">新增客户</a></div>
        </div>-->
        <!---客户定位en ReportSummary-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page-nav">
                    <div class="row">
                        <div class="pagenav_box">
                            <div class="page_title">号码捆绑帐户受理</div>
                        </div>
                    </div>
                </div>
                <div class="page_main ">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>

                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <input type="hidden" id="checkChannelId">
					                        <div class="form-group col-md-6">
					                            <label class="col-md-4 control-label lablep">
					                                 		地区
					                             </label>
					                            <div class="col-md-8">
					                                <div class="input-group">
					                                    <input id="regionId"  type="text" class="form-control" placeholder="">
					                                    <div class="input-group-btn">
					                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
					                                    </div>
					                                </div>
					                            </div>
					                    </div>
                                        <div class="form-group col-md-6">
                                            <label class="col-md-4 control-label lablep">
                                                查询条件</label>
                                            <div class="col-md-7">
                                                <select id="queryCondition" class="bth_query">
                                                    <option value="1">接入号码</option>
                                                    <option value="2">账户合同号</option>
                                                </select>
                                                <select id='numberType'>
                                                    <option value="1">主接入号</option>
                                                    <option value="2">宽带账号</option>
                                                </select>
                                                <input id="accessNumber" class="form-control" type="text">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-12">
                                            <label class="col-md-2 control-label lablep"></label>
                                            <div class="col-md-10 text-right">
                                                <!--<button type="button" class="btn btn-white">清除</button>-->
                                                <button id="btn-query" type="button" class="btn btn-primary">查询</button>
                                            </div>
                                        </div>
                                    </form>
                                  
                                 <div class="mincontent"  >
                        		   <form class="form-bordered" id = "acctInfoDiv">
                                    <div class="form-group col-md-6 ">
                                        <label class="col-md-4 control-label lablep">管理号码 </label>
                                        <div class="col-md-7">
                                            <input id="managerNum" readonly="readonly"  type="text" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-6 ">
                                        <label class="col-md-4 control-label lablep">商品名称 </label>
                                        <div class="col-md-7">
                                            <input id="offerName" readonly="readonly" type="text" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-6 ">
                                        <label class="col-md-4 control-label lablep">当前状态 </label>
                                        <div class="col-md-7">
                                            <input id="currentSate" readonly="readonly" type="text" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-6 ">
                                        <label class="col-md-4 control-label lablep">客户名称 </label>
                                        <div class="col-md-7">
                                            <input id="custName" readonly="readonly" type="text" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="col-md-4 control-label lablep">帐户合同号 </label>
                                        <div class="col-md-7">
                                            <input id="acctNum" readonly="readonly" type="text" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                   </form>
                                   #if($options)
									<p class="vita-data">{"data":$options}</p>
								   #end
                    			 </div>
                               </div>
                            </div>
							
                            <div class="container-fluid row" id="bindAccountdiv">
                                <div class="form_title">
                                    <div><i class="bot"></i>号码已有捆绑账户信息</div>
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12 " id="bindAccountResult" >
                                        <div class="mart10">
                                            <table class="table table-bordered" id="bindAccountList">
                                                <tr>
                                                    <th>管理号码</th>
                                                    <th>管理号码地区</th>
<!--                                                     <th>管理号码所属地区区号</th> -->
                                                    <th>帐户合同号</th>
                                                    <th>绑定的合同标识</th>
                                                    <th>绑定号码</th>
                                                    <th>操作权限</th>
                                                    <th>操作</th>
													<th>绑定合同号的相关号码所属地区</th>
													<th>状态</th>
                                                </tr>
                                                </thead>
                                                <tbody >
                                                #if($options != "null" && $options.bindAccountList &&
                                                $options.bindAccountList != "null"  &&
                                                $options.bindAccountList.size() > 0)
                                                #foreach($bindAccountInfo in $options.bindAccountList)
                                                <tr>
                                                    <td>$!{bindAccountInfo.acctNumber}</td>
                                                    <td>$!{bindAccountInfo.regionId}</td>
<!--                                                     <td>$!{bindAccountInfo.areaCode}</td> -->
                                                    <td>$!{bindAccountInfo.acctCd}</td>
                                                    <td>$!{bindAccountInfo.acctId}</td>
                                                    <td>$!{bindAccountInfo.bindNumber}</td>
                                                    <td>$!{bindAccountInfo.bindOperateType}</td>
                                                    <td>
	                                                    <button id ='cancelBindCheck' type="button" class="btn btn-primary btn-sm okbutt">
	                                                                                                         取消绑定<p style ="display: none"class="vita-data">{"bindAccountInfo":$!{bindAccountInfo}}</p>
	                                                     </button>
	                                                </td>
                                                    <td>$!{bindAccountInfo.bindRegionId}</td>
                                                    <td>$!{bindAccountInfo.statusCd}</td>
                                                </tr>
                                                #end
	                                            #elseif($options != "null" && $options.bindAccountList && $options.bindAccountList != "null" &&
	                                            $options.bindAccountList.size() == 0)
	                                            <tr><td align='center' colspan='7'>未查询到数据！<td></tr>
	                                            #end
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                     <!--翻页start -->
                                	<div id="showPageInfo">
                                	</div>
                                    #if($options.totalNumber && $options.totalNumber != "null")
                                    <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                    #end
                                    <!--翻页end -->
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>新增捆绑帐户</div>
                                </div>
                                <!--<div class="wmin_content">-->
                                    <div class="col-lg-12 "  >
                                <table>
                                    <tr>
                                        <td>
                                            <textarea id="numbers" name="numbers" cols="100%" rows="5"></textarea>
                                            <button id="bindCheck" type="button" class="btn btn-primary">绑定校验</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><font size="2" color="red">
                                            注：输入为产品接入号，多个则以英文逗号分隔，且输入接入号数目一次不能超过20个</td>
                                    </tr>
                                </table>
                                    <!--</div>-->
                                </div>
<!--                                 <div class="form-group col-md-4 " align="right"> -->
<!--                                     <label class="col-md-5 control-label lablep">管理号码操作权限 </label> -->
<!--                                     <div class="col-md-7 " > -->
<!--                                         <select id="status" class="form-control"> -->
<!--                                             <option value="2">可查询不可办理</option> -->
<!--                                             <option value="1">可查询可办理</option> -->
<!--                                         </select> -->
<!--                                     </div> -->
<!--                                 </div> -->
                                <div class="form-group col-md-12">
                                    <label class="col-md-2 control-label lablep"></label>
                                    <div class="col-md-10 text-right">
                                        <button type="button" class="btn btn-primary">完成操作</button>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
