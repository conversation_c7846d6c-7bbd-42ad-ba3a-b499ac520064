<div data-widget="invoicePrintLogQuery" style="height:100%">
    #macro(nullNotShowDefault $val)
        #if($val && $val != "null")
            $!val
        #else
           无
        #end
    #end
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_channelId" type="checkbox" name="payment">
                                                </label> 渠道
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="channelId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="channelIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_staffId" type="checkbox" name="payment">
                                                </label> 营业员
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="staffId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="staffIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custOrderNbr" type="checkbox" name="payment">
                                                </label> 流水号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custOrderNbr" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_receiptCode" type="checkbox" name="payment">
                                                </label> 发票代码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="receiptCode" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_logType" type="checkbox" name="payment">
                                                </label>日志类型
                                            </label>
                                            <div class="col-md-7">
                                                <select id="logType" class="form-control">
                                                    #if($options != "null" && $options.logTypeMap != "null" && $options.logTypeMap.size() > 0)
                                                    #foreach($item in $!options.logTypeMap.entrySet())
                                                    <option value="$!{item.key}">$!{item.value}</option>
                                                    #end
                                                    #end
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_statusCd" type="checkbox" name="payment">
                                                </label>日志状态
                                            </label>
                                            <div class="col-md-7">
                                                <select id="statusCd" class="form-control">
                                                    #if($options != "null" && $options.statusCdMap != "null" && $options.statusCdMap.size() > 0)
                                                    #foreach($item in $!options.statusCdMap.entrySet())
                                                    <option value="$!{item.key}">$!{item.value}</option>
                                                    #end
                                                    #end
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-12" id="qryTimeQuantum">
                                            <label class="col-md-2 control-label lablep">
                                                起止时间</label>
                                            <div class="col-md-10 form-inline">
                                                <div class="form-group">
                                                    <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="开始时间">
                                                </div>
                                                <div class="form-group">
                                                    <input name="endDate" id="endDate" type="text" class="form-control" placeholder="结束时间">
                                                </div>
                                                <button type="button" id="timeQuantum_1" name="timeQuantum_1" class="btn btn-gray btn-outline btn-sm">当天</button>
                                                <button type="button" id="timeQuantum_7" name="timeQuantum_7" class="btn btn-gray btn-outline btn-sm">七天</button>
                                                <button type="button" id="timeQuantum_30" name="timeQuantum_30" class="btn btn-gray btn-outline btn-sm">一个月</button>
                                                <button type="button" id="timeQuantum_90" name="timeQuantum_90" class="btn btn-gray btn-outline btn-sm">三个月</button>

                                            </div>
                                        </div>
                                        <div class="form-group col-md-11">
                                            <div class="col-md-12 searchbutt_r" align="right">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-clear" type="button" class="btn btn-white">清除</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title"></div>
                                <div class="wmin_content">
                                    <div class="col-lg-12 mart10" id="orderListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th style="display: none;">选择</th>
                                                <th>日志标识</th>
                                                <th>订单流水</th>
                                                <th>发票标识</th>
                                                <th>发票代码</th>
                                                <th>原因</th>
                                                <th>记录时间</th>
                                                <th>日志类型</th>
                                                <th>受理渠道</th>
                                                <th>受理员工</th>
                                                <th>状态</th>
                                            </tr>
                                            </thead>
                                            <tbody id="invoicePrintLogList">
                                            #if($options != "null" && $options.invoicePrintLogs && $options.invoicePrintLogs != "null" && $options.invoicePrintLogs.size() > 0)
                                            #foreach($invoicePrintLog in $options.invoicePrintLogs)
                                            <tr>
                                                <td style="display: none;">
                                                    <label class="wu-radio full absolute" data-scope="">
                                                        <input type="radio" name="payment">
                                                    </label>
                                                </td>
                                                <td style="text-align: center;">
                                                    <a class="textcolorgreen" link="invoicePrintLogDetail" data-toggle="tooltip" data-placement="top" title="点击查看日志信息">$!{invoicePrintLog.logId}</a>
                                                    <p class="vita-data">{"logId" : $!invoicePrintLog.logId}</p>
                                                </td>
                                                <td>$!{invoicePrintLog.custOrderNbr}</td>
                                                <td style="text-align: center;">
                                                    <a class="textcolorgreen" link="invoiceDetail" data-toggle="tooltip" data-placement="top" title="点击查看发票信息">$!invoicePrintLog.receiptId</a>
                                                    <p class="vita-data">{"receiptId" : $!invoicePrintLog.receiptId}</p>
                                                </td>
                                                <td>#nullNotShowDefault($!invoicePrintLog.receiptCode)</td>
                                                <td>$!{invoicePrintLog.reason}</td>
                                                <td>$!{invoicePrintLog.logDate}</td>
                                                <td>
                                                    #if($options != "null" && $options.logTypeMap != "null" && $options.logTypeMap.size() > 0)
                                                    #foreach($item in $!options.logTypeMap.entrySet())
                                                      #if($!{item.key} == $!{invoicePrintLog.logType})
                                                        $!{item.value}
                                                      #end
                                                    #end
                                                    #end
                                                </td>
                                                <td>$!{invoicePrintLog.createOrgName}</td>
                                                <td>$!{invoicePrintLog.staffName}</td>
                                                <td>
                                                    #if($options != "null" && $options.statusCdMap != "null" && $options.statusCdMap.size() > 0)
                                                    #foreach($item in $!options.statusCdMap.entrySet())
                                                    #if($!{item.key} == $!{invoicePrintLog.statusCd})
                                                        $!{item.value}
                                                    #end
                                                    #end
                                                    #end
                                                </td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.invoicePrintLogs && $options.invoicePrintLogs != "null" &&
                                            $options.invoicePrintLogs.size() == 0)
                                                <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
