package com.asiainfo.crm.assist.app.detail;


import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.service.intf.IBillingSmo;
import com.asiainfo.crm.service.intf.IOrderQueryMultiSMO;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 订单详情.
 *
 * <AUTHOR>
 */
@Component("vita.olDetailInfo")
public class OlDetailInfo extends AbstractSoComponent {

    @Autowired
    private IOrderQueryMultiSMO orderQueryMultiSMO;

    @Autowired
    private IBillingSmo billingSmo;

    @Override
    public Map achieveData(Object... param) throws Exception {
        String p = (String) param[0];
        Map options = new HashMap();
        Map orders = this.qryOlDetail(p);
        Map inParam = jsonConverter.toBean(p, Map.class);
        options.putAll(inParam);
        options.putAll(orders);
        return options;
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
	public Map qryOlDetail(String inStr) throws Exception {
        Map options = new HashMap();
        String retStr = orderQueryMultiSMO.queryCustomerOrderSummaryInfo(inStr);
        Map resultObject = (Map) resolveResult(retStr);
        options.put("detailInfo", resultObject);
        Map inMap = jsonConverter.toBean(inStr, Map.class);
        List retObject = null;
        try {
            Long custOrderId = Long.valueOf(MapUtils.getString(inMap, "coId"));
            String resultStr = billingSmo.queryOneItemResults(custOrderId);
            retObject = (List) resolveResult(resultStr);
        }catch(Exception e){
            retObject = new ArrayList();
        }
        options.put("feeItems", retObject);
        
        //查询规则授信日志
        String ruleStr = orderQueryMultiSMO.queryCustRuleInfos(inStr);
        Map ruleInfos = (Map) resolveResult(ruleStr);
        options.put("ruleInfos", ruleInfos.get("ruleInfos"));
        
        return options;
    }

}
