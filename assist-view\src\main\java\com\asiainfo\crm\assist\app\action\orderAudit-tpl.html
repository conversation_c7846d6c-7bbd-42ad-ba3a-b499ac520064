<div data-widget="orderAudit" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright">
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>实名制甩单审核</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                地 区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        #if($options.isHaveRole && $options.isHaveRole != "null" && $options.isHaveRole == "Y")
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                                        #end
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                购物车流水
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custOrderNbr" type="text" class="form-control">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                业务类型
                                            </label>
                                            <div class="col-md-7">
                                                <select class="form-control" name="serviceOffers">
                                                    <option value="-1" selected="selected">全部</option>
                                                    #if($options.serviceOffers && $options.serviceOffers != "null" && $options.serviceOffers.size() > 0)
                                                        #foreach($serviceOffer in $options.serviceOffers)
                                                        <option value="$serviceOffer.serviceOfferId">$serviceOffer.serviceOfferName</option>
                                                        #end
                                                    #end
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                工单状态
                                            </label>
                                            <div class="col-md-7">
                                                <select class="form-control" name="statusMenu">
                                                    #if($options.orderAuditStatusMenu && $options.orderAuditStatusMenu != "null" && $options.orderAuditStatusMenu.size() > 0)
                                                    #foreach($statusMenu in $options.orderAuditStatusMenu)
                                                    <option value="$statusMenu.statusNbr">$statusMenu.statusName</option>
                                                    #end
                                                    #end
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-7" id="qryTimeQuantum">
                                            <label class="col-md-5 control-label lablep">
                                                起止时间</label>
                                            <div class="col-md-7 form-inline">
                                                <div class="form-group">
                                                    <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="开始时间">
                                                </div>
                                                <div class="form-group">
                                                    <input name="endDate" id="endDate" type="text" class="form-control" placeholder="结束时间">
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                    <div class="form-group col-md-1">
                                        <label>&nbsp</label>
                                        <div class="col-md-10 searchbutt_r">
                                            <button id="btn-query" type="button" class="btn btn-primary">查询</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="wmin_content">
                                    <div class="col-lg-12 mart10" id="orderListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>归属分公司</th>
                                                <th>购物车流水</th>
                                                <th>购物车ID</th>
                                                <th>接入号</th>
                                                <th>操作</th>
                                                #if($options != "null" && $options.orderAuditHisSwitch && $options.orderAuditHisSwitch != "null" && $options.orderAuditHisSwitch == "Y")
                                                <th>审核历史</th>
                                                #end
                                                <th>审核人</th>
                                                <th>工单状态</th>
                                                <th>业务类型</th>
                                                <th>客户名称</th>
                                                <th>人证匹配度</th>
                                                <th>创建时间</th>
                                            </tr>
                                            </thead>
                                            <tbody id="orderList">
                                            #if($options != "null" && $options.auditOrderInfos && $options.auditOrderInfos != "null" && $options.auditOrderInfos.size() > 0)
                                                #foreach($auditOrderInfo in $options.auditOrderInfos)
                                                <tr>
                                                    #if($auditOrderInfo.acceptLanName && $auditOrderInfo.acceptRegionName)
                                                        <td>
                                                            $auditOrderInfo.acceptLanName<span>_</span>$auditOrderInfo.acceptRegionName
                                                        </td>
                                                    #elseif($auditOrderInfo.acceptLanName)
                                                        <td>
                                                            $auditOrderInfo.acceptLanName
                                                        </td>
                                                    #elseif($auditOrderInfo.acceptRegionName)
                                                        <td>
                                                            $auditOrderInfo.acceptRegionName
                                                        </td>
                                                    #end
                                                    <td>$!{auditOrderInfo.custOrderNbr}</td>
                                                    <td>$!{auditOrderInfo.custOrderId}</td>
                                                    <td>$!{auditOrderInfo.accNum}</td>
                                                    #if($auditOrderInfo.compareValue && $auditOrderInfo.compareValue > 0)
                                                    <td>
                                                        <button name="orderAuditBtn" type="button" class="btn btn-primary">
                                                            #if($auditOrderInfo.auditResult && $auditOrderInfo.auditResult == "0")
                                                            <span name="orderAuditBtnName">提取</span>
                                                            #elseif($auditOrderInfo.auditResult && $auditOrderInfo.auditResult == "1")
                                                            <span name="orderAuditBtnName">审核</span>
                                                            #else
                                                            <span name="orderAuditBtnName">查看</span>
                                                            #end
                                                        </button>
                                                        <p class="vita-data">{"custOrderId" : "$!auditOrderInfo.custOrderId", "custOrderNbr" : "$!auditOrderInfo.custOrderNbr", "auditResult" : "$!auditOrderInfo.auditResult", "accNum" : "$!auditOrderInfo.accNum","serviceOfferId" : "$!auditOrderInfo.serviceOfferId"}</p>
                                                    </td>
                                                    #else
                                                    <td>不可操作[未拍照]</td>
                                                    #end
                                                    #if($options != "null" && $options.orderAuditHisSwitch && $options.orderAuditHisSwitch != "null" && $options.orderAuditHisSwitch == "Y")
                                                    <td>
                                                        <button name="orderAuditHis" type="button" class="btn btn-primary">查看历史</button>
                                                        <p class="vita-data">{"custOrderId" : "$!auditOrderInfo.custOrderId", "custOrderNbr" : "$!auditOrderInfo.custOrderNbr"}</p>
                                                    </td>
                                                    #end
                                                    <td name="auditorName">$!{auditOrderInfo.auditorName}</td>
                                                    <td name="auditResultName">$!{auditOrderInfo.auditResultName}</td>
                                                    <td>$!{auditOrderInfo.serviceOfferName}</td>
                                                    <td>$!{auditOrderInfo.custName}</td>
                                                    #if($auditOrderInfo.compareValue && $auditOrderInfo.compareValue > 0)
                                                    <td>$!{auditOrderInfo.compareValue}<span>%</span></td>
                                                    #else
                                                    <td>无人证比对值</td>
                                                    #end
                                                    <td name="statusDate">$!{auditOrderInfo.statusDate}</td>
                                                </tr>
                                                #end
                                            #elseif($options != "null" && $options.auditOrderInfos && $options.auditOrderInfos != "null" && $options.auditOrderInfos.size() == 0)
                                            <tr>
                                                <td align='center' colspan='8'>未查询到数据！
                                                </td>
                                            </tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
