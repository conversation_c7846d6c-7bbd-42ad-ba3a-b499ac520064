package com.asiainfo.crm.assist.app.query;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.bcomm.exception.BError;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.util.SmLogUtil;
import com.google.common.collect.Maps;

/**
 * Created by niewq on 2018/6/20.
 */
@Component("vita.transDealCertNumFive")
public class TransDealCertNumFive extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(TransDealCertNumFive.class);

    @Autowired
	private ICustMultiSMO custMultiSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
    	return  null;
    }

    @SuppressWarnings("unchecked")
	public Map<String, Object> queryTransDealCertNumFive(String jsonStr) throws Exception {
    	Map<String, Object> options = Maps.newHashMap();
    	logger.debug(jsonStr);
        String res = custMultiSMO.queryTransAcceptOneCertNumFive(jsonStr);
        Map<String, Object> resultObjectMap =(Map<String, Object>) resolveResult(res);
        if (resultObjectMap == null) {
        	 options.put("transDealCertNumFiveList", null);
             return options;
		}
        List<Map<String, Object>> transDealCertNumFiveList = new ArrayList<Map<String, Object>>();
        if (resultObjectMap.containsKey("resultObject")) {
        	Map<String, Object> resultObjects = (Map<String, Object>)resultObjectMap.get("resultObject");
        	transDealCertNumFiveList = (List<Map<String, Object>>) MapUtils.getObject(resultObjects,"transAcceptList");
		}else {
			transDealCertNumFiveList = (List<Map<String, Object>>) MapUtils.getObject(resultObjectMap,"transAcceptList");
		}
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int totalCount = MapUtils.getIntValue(pageInfoMap, "totalCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageSize = MapUtils.getIntValue(pageInfoMap, "pageSize");
        options.put("pageCount",pageIndex);
        options.put("pageIndex",pageSize);
        options.put("totalNumber", totalCount);
        options.put("transDealCertNumFiveList", transDealCertNumFiveList);
        return options;
    }

    @SuppressWarnings("unchecked")
	public  Map<String, Object> updateTransCertNumFive(String jsonStr) throws Exception {
    	Map<String, Object> retMap = new HashMap<String, Object>();
		String resultJson = "";
		try {
			logger.debug(jsonStr);
			jsonStr = SmLogUtil.supplyOneFivePatams(jsonStr);
			resultJson = custMultiSMO.dealTransOneCertNumFive(jsonStr);
			Map<String, Object> resultObject = (Map<String, Object>) resolveResult(resultJson);
			retMap.put("retCode", resultObject.get("resultCode"));
			retMap.put("retDesc", resultObject.get("resultMsg"));
			return retMap;
		} catch (Exception e) {
			retMap.put("retCode", "-1");
			if (e instanceof BError){
	            BError bError = (BError)e;
	            retMap.put("retDesc", bError.getMsg());
			}else{
				retMap.put("retDesc", e.getMessage());
			}
			return retMap;
		}
    	
    }
	
}
