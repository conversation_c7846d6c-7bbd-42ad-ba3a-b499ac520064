
<div data-widget="transCertNumFiveDetail" style="overflow: auto; height: 100%;">
<p class="vita-data">{"data":$options}</p>
	#macro( nullNotShow $val)
        #if($val && $val != "null")
            $!val
        #end
    #end
    <div class="calctitle">
        <div class="titlefont">订单详情</div>
        <div class="toolr">
            
        </div>
    </div>
    <div class="calcw_rightcont">
    		#set($transCertNumFiveDetail = $options.transCertNumFiveDetail)
            <div class="container-fluid">
            <h5 class="meal_htitle nobrder"><i class="bot"></i>基本信息</h5>
            <table class="table table-bordered conttd-w">
                <tbody>
                <tr>
                    <td class="labellcont">流水号</td>
                    <td>#nullNotShow($transCertNumFiveDetail.orderNbr)</td>
                    <td class="labellcont">受理人</td>
                    <td>#nullNotShow($transCertNumFiveDetail.staffName)</td>
                </tr>
                <tr>
                    <td class="labellcont">受理地区</td>
                    <td>#nullNotShow($transCertNumFiveDetail.regionName)
                    </td>
                    <td class="labellcont">受理渠道</td>
                    <td>#nullNotShow($transCertNumFiveDetail.channelName)</td>
                </tr>
                <tr>
                    <td class="labellcont">客户信息</td>
                    <td>#nullNotShow($transCertNumFiveDetail.custName)</td>
                    <td class="labellcont">证件类型</td>
                    <td>#if($transCertNumFiveDetail.certType=='1') 身份证  #else 其他  #end</td>
                </tr>
                <tr>
                    <td class="labellcont">证件号</td>
                    <td>#nullNotShow($transCertNumFiveDetail.certNumber)</td>
                    <td class="labellcont">联系方式</td>
                    <td>#nullNotShow($transCertNumFiveDetail.accNbr)</td>
                </tr>
               
                </tbody>
            </table>
        </div>
        
         <div class="container-fluid">
            <h5 class="meal_htitle nobrder"><i class="bot"></i>号码信息</h5>
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered" id="customerInfo">
                        <thead>
                        <tr>
                            <th>号码</th>
                            <th>地区</th>
                            <th>状态</th>
                            <th>接单人</th>
                            <th class="hidden-xs hidden-sm">操作</th>
                            
                        </tr>
                        </thead>
                        <tbody>
												#if($options != "null" && $options.transCertNumFiveDetail && $options.transCertNumFiveDetail != "null" && $options.transCertNumFiveDetail.size() > 0) 
												#foreach($transCertNumFive in $options.transCertNumFiveDetail.collectionItemInfos)
												<tr>
													
													<td>$!{transCertNumFive.accNbr}</td>
													<td>$!{transCertNumFive.regionName}</td>
													<td>
													#if($transCertNumFive.statusCd=='100002') 
														未接单
													#elseif($transCertNumFive.statusCd=='201300')
														已接单
													#elseif($transCertNumFive.statusCd=='301200')
														成功
													#elseif($transCertNumFive.statusCd=='100004')
														已取消
													#end
													</td>
													<td>$!{transCertNumFive.handleStaffName}</td>
													<td>
														#if($transCertNumFive.statusCd=='100002')
                                                		<button id="dealOrder" type="button" class="btn btn-primary">接单</button>
                                                		<p class="vita-data">{"order":"$!options.transCertNumFiveDetail.orderNbr","collectionItemId":"$!transCertNumFive.collectionItemId","accNbr":"$!transCertNumFive.accNbr"}</p>
                                                		#elseif($transCertNumFive.statusCd=='201300') 
                                                		<button id="backOrder" type="button" class="btn btn-primary">回退</button>
                                                		<p class="vita-data">{"order":"$!options.transCertNumFiveDetail.orderNbr","collectionItemId":"$!transCertNumFive.collectionItemId","accNbr":"$!transCertNumFive.accNbr"}</p>
                                                		<button id="cancelOrder" type="button" class="btn btn-primary">作废</button>
                                                		<p class="vita-data">{"order":"$!options.transCertNumFiveDetail.orderNbr","collectionItemId":"$!transCertNumFive.collectionItemId","accNbr":"$!transCertNumFive.accNbr"}</p>
                                                		<button id="successOrder" type="button" class="btn btn-primary">成功</button>
                                                		<p class="vita-data">{"order":"$!options.transCertNumFiveDetail.orderNbr","collectionItemId":"$!transCertNumFive.collectionItemId","accNbr":"$!transCertNumFive.accNbr"}</p>
                                                		#else 
                                                		无可用操作
                                                		#end
                                                		
													</td>
													
												</tr>
												#end
												#elseif( $options.transCertNumFiveDetail == "null")
												<tr>
													<td align='center' colspan='7'>未查询到数据！
													<td>
												</tr>
												#end
											</tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="container-fluid">
            <h5 class="meal_htitle nobrder"><i class="bot"></i>附件信息</h5>
            <div class="toolr">            
            <button id="showImage" type="button" class="btn btn-primary btn-sm okbutt">查看附件</button>
        	</div>
        	<div class="form-group col-md-6">
        	<h5 class="meal_htitle nobrder">回执内容</h5>
            </div>
            <div class="form-group col-md-12 overflowh" id="imageDiv">
            #if($options.imageDetail &&$options.imageDetail != "null" && $options.imageDetail.size() > 0) 
				#foreach($imageDetail in $options.imageDetail)
					#if($imageDetail.picFlag=='F')
						<div class="col-md-10">
							<iframe class="maxpicwh" id="$imageDetail.picFlag" src="data:application/pdf;base64,$imageDetail.picture" >
							</iframe>
						</div>
					#else
						<div class="col-md-10">
							<img class="maxpicwh" id="$imageDetail.picFlag" src="data:image/jpg;base64,$imageDetail.picture"/>
			            </div>
					#end
				#end
            #end  
            </div>
         </div>   
</div>
</div>

