
(function (vita) {
    var handleGroupMain = vita.Backbone.BizView.extend({
        events: {

        },
        _initialize:function(){
            var widget = this,
                element = $(widget.el);
            var gSession = widget.gSession;
            var data= $options.data;
            // console.info(data);
            var onLoadURL = data.URL;
            var flag = data.flag;
            var fixedUrl = data.fixedUrl;
            $("#onLoadIf").attr("src",onLoadURL);
            // alert(onLoadURL);
            $("#onLoadIf").bind("load",function(){
                widget._doLogin(flag,fixedUrl);
            });
            //
        },
         _doLogin:function(flag,fixedUrl){

            debugger;
            if(flag&&flag=="fixed"){
                location.href = fixedUrl;
            }else{
                location.href = "https://ct.crm.189.cn/ltePortal/main/home";
            }
    }

    });
    vita.widget.register("handleGroupMain", handleGroupMain, true);
})(window.vita);
