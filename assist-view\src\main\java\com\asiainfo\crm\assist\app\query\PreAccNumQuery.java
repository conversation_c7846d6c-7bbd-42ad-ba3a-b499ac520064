package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IResourceSMO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by wenhy on 2017/7/7.
 */
@Component("vita.preAccNumQuery")
public class PreAccNumQuery extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(PreAccNumQuery.class);

    @Autowired
    private IResourceSMO resourceSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map preAccNumQuery(String jsonStr) throws Exception {
        String preAccNumListStr = resourceSMO.queryPreAccNum(jsonStr);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(preAccNumListStr);
        Map<String, Object> options = Maps.newHashMap();
        List preAccNumList = (List) MapUtils.getObject(resultObjectMap, "preAccNumList");
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", total);
        options.put("preAccNumList", preAccNumList);
        return options;
    }

}
