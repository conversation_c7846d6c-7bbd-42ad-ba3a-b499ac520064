
var assist = {
    prototype : "vita.Backbone.BizView.prototype",
    nodes : [
        {
            name : "sesameCreditApply",
            url : "../action/sesameCreditApply",
            assist : function(){

            },
            end : function(flag,params){
                debugger;
                return [[flag, params, true]];
            }
        },
        {
            name : "sesameCreditVerify",
            url : "../action/sesameCreditVerify",
            assist : function(){

            },
            end : function(flag,params){
                debugger;
                return [[flag, params, true]];
            }
        },
        {
            name : "sesameCreditBusiTips",
            url : "../action/sesameCreditBusiTips",
            assist : function(){

            },
            end : function(flag,params){
                return [[flag, params, true]];
            }
        },
        {
            name : "sesameCreditSign",
            url : "../action/sesameCreditSign",
            assist : function(){

            },
            end : function(flag,params){
                return [[flag, params, true]];
            }
        },
        {
            name : "sesameCreditLabelBind",
            url : "../action/sesameCreditLabelBind",
            assist : function(){

            },
            end : function(flag,params){
                return [[flag, params, true]];
            }
        }


    ]
};