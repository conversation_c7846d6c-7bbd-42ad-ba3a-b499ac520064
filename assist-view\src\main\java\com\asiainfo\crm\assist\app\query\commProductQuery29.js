(function (vita) {
    var commProductQuery29 = vita.Backbone.BizView.extend({
        events: {
            "click #qryBtn": "_commProductQuery29",
            "click #regionIdBtn": "_chooseArea",
            "click #edOrderProdDtl": "_edOrderProdDtl",
            "click #sameCustProdQuery": "_sameCustProdQuery",
            "click #qryType": "_qryTypeCheck"
        },

        _edOrderProdDtl: function () {
            var widget = this,element = $(widget.el);
            var prodInfos = element.find('table input[name="prodInfo"]:checked');
            if(prodInfos.length == 0) {
                widget.popup("请选择一条产品!");
                return false;
            }
            var prodInfo = $(prodInfos[0]).data("data");
            var accNum = prodInfo.accessNumber;
            var partyId = prodInfo.partyId;
            var prodInstId = prodInfo.prodId;
            var regionId = $("#regionId").attr("value");
            window.open("../../crm/so/prodInstInfo?prodInstId="+prodInstId+"&regionId="+regionId+"&accNbr="+accNum+"&partyId="+partyId);
        },
        _sameCustProdQuery: function () {
        	var widget = this,element = $(widget.el);
       
        	var prodInfos = element.find('table input[name="prodInfo"]:radio');
            if(prodInfos.length == 0) {
                return false;
            }
            var prodInfo = $(prodInfos[0]).data("data");
            var params = {
                    pageInfo: {
                        pageIndex: widget.global.pageIndex,
                        pageSize: widget.global.pageSize
                    }
                };
            var regionId = $("#regionId").attr("value");
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.regionId = regionId;
            }
            params.partyId = prodInfo.partyId;
            params.qryType = "6";
            
            if (params) {
                widget.refreshPart("queryProdList", JSON.stringify(params), "#prodListResult", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if (widget._getConds()) {
                                    widget.refreshPart("queryProdList", JSON.stringify(params), "#prodListResult");
                                }
                                ;
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }else{
                    	widget.popup("未查到相关数据。")
                    	return;
                    }

                }, {
                    async: false
                });
            }

        },

        _initialize: function () {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
        },
        global: {
            pageIndex: 1,
            pageSize: 5,
            preset: "date",
            chooseArea: "../comm/chooseArea",
            chooseCust: "../comm/chooseCust",
            chooseChannel: "../comm/chooseChannel"
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },

        _commProductQuery29: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("queryProdList", JSON.stringify(params), "#prodListResult", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if (widget._getConds()) {
                                    widget.refreshPart("queryProdList", JSON.stringify(params), "#prodListResult");
                                }
                                ;
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }else{
                    	widget.popup("未查到相关数据。")
                    	return;
                    }

                }, {
                    async: false
                });
            }
            ;
        },

        _qryTypeCheck: function () {

            var qryType = $("#qryType").val();
            if("1" == qryType) {
                $("#queryLabel").text("接入号码")
            }
            if("2" == qryType) {
                $("#queryLabel").text("身份证")
            }
            if("3" == qryType) {
                $("#queryLabel").text("账户合同号")
            }
            if("4" == qryType) {
                $("#queryLabel").text("客户名")
            }
            if("5" == qryType) {
                $("#queryLabel").text("客户简拼")
            }
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this;
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            var paramCnt = 0;
            var regionId = $("#regionId").attr("value");
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.regionId = regionId;
            }
            var qryType =  $("#qryType").val();//查询类型 1、接入号码 2、身份证 3、账户合同号 4、客户名 5、客户简称
            params.qryType = qryType;
            var qryStr = $("#qryStr").val();
            if (widget._isNullStr(qryStr)) {
                widget.popup("请输入查询条件！");
                return;
            } else {
                params.qryStr = qryStr;
            }
            return params;
        }
    });
    vita.widget.register("commProductQuery29", commProductQuery29, true);
})(window.vita);