(function (vita) {
    var releaseRsBatch = vita.Backbone.BizView.extend({
        events: {
            "click #releaseRsBatchBtn": "_releaseRsBatchBtnClick",
            "click #releaseRsBtn": "_releaseRsBtnClick",
            "click #downloadBtn": "_downloadBtnClick"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
        },
        global: {
            pageIndex: 1,
            pageSize: 100
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _releaseRsBtnClick: function () {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var params = {};
            var regionId = element.find("#regionId").attr("value");
            params.busiRegionId = regionId;
            params.regionId = regionId;
            var accNum = element.find("#accNum").val();
            var mktNum = element.find("#mktNum").val();
            var batchNum = element.find("#batchNum").val();
            if (widget._isNullStr(accNum) && widget._isNullStr(mktNum) && widget._isNullStr(batchNum)) {
                widget.popup("请输入需要查询释放的资源号码！");
                return;
            } else {
                params.accNum = accNum;
                params.mktNum = mktNum;
                params.batchNum = batchNum;
            }
            params.channelId = gSession.curChannelId;
            params.staffId = gSession.staffId;
            element.find("#releaseRsBtn").attr("disabled", true);
            element.find("#tr").nextAll().empty();//移除所有兄弟节点
            widget.callService("releaseRs",JSON.stringify(params),function (ret) {
                if (0 == ret.retCode){
                    var resultList = ret.retObj;var excelDataHtml = "";
                   for (var i =0;i<resultList.length;i++) {
                       var state = "";
                       var acc = "";
                       var mkt = "";
                       if (resultList[i].statusCd == 0){
                           state="待处理";
                       } else if(resultList[i].statusCd == 1){
                           state="处理中";
                       }else if (resultList[i].statusCd == 2) {
                           state="处理成功";
                       }else if(resultList[i].statusCd == 3){
                           state="处理失败";
                       } else {
                           state="不需要处理";
                       }
                       if (resultList[i].accNbr!=undefined) acc = resultList[i].accNbr;
                       if (resultList[i].mktResNbr!=undefined) mkt = resultList[i].mktResNbr;
                       excelDataHtml+="<tr><td>"+acc+"</td><td>"+mkt+"</td><td>"+resultList[i].batchId+"</td><td>"+state+"</td></tr>";
                   }
                    element.find("#tr").after(excelDataHtml);
                    element.find("#releaseRsBtn").attr("disabled", false);
                    return;
                }else {
                    widget.popup(ret.retDesc);
                    element.find("#releaseRsBtn").attr("disabled", false);
                    return;
                }
            })
        },
        _releaseRsBatchBtnClick: function () {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            var params = {};
            var regionId = $("#regionId").attr("value");
            params.busiRegionId = regionId;
            params.regionId = regionId;
            params.channelId = gSession.curChannelId;
            params.staffId = gSession.staffId;
            if (document.querySelector("#file").files[0] == null || document.querySelector("#file").files[0] == undefined){
                widget.popup("请上传资源文件！");
                return;
            }
            element.find("#releaseRsBatchBtn").attr("disabled", true);
            widget.callMultiService("releaseRsBatch", [JSON.stringify(params),document.querySelector("#file").files[0]],function (ret) {
                if (0 == ret.retCode){
                    //widget.popup(ret.retDesc+ret.retNo);
                    element.find("#td").append(ret.retDesc+ret.retNo);
                    element.find("#releaseRsBatchBtn").attr("disabled", false);
                    return;
                }else {
                    element.find("#td").append(ret.retDesc+ret.retNo+",详细如下:"+ret.retInfo);
                    element.find("#releaseRsBatchBtn").attr("disabled", false);
                    return;
                }
            });
        },
        _downloadBtnClick: function () {
            var widget = this,param={};
            var url = "../query/downLoadExcel";
            location.href=url+"?jsonStr="+encodeURI(encodeURI(JSON.stringify(param)));
        }
    });
    vita.widget.register("releaseRsBatch", releaseRsBatch, true);
})(window.vita);