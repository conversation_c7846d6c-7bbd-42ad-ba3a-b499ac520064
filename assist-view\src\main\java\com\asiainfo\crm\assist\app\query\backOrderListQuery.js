(function (vita) {
    var backOrderListQuery = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_backOrderListQuery",
            "click #regionIdBtn": "_chooseArea",
            "click #channelIdBtn": "_chooseChannel",
            "click #backOrderCommit": "_backOrderCommit",
            "click #c_all": "_checkAll",
        },

        _initialize: function () {
            var widget = this, element = $(widget.el);
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
            var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
            var now = widget._getNowFormatDate();
            beginDate.val(now);
            endDate.val(now);
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd"
                });
            }
            ;
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd"
                });
            }
            var gSession = widget.gSession;
            element.find("#acceptRegionId").val(gSession.commonRegionName).attr("value", gSession.commonRegionId);
            var channelName = "";
            for(var channels=gSession.channels,i=0; channels.length && i<channels.length; i++) {
                if(gSession.curChannelId == channels[i].orgId) {
                    channelName = channels[i].channelName;
                    break;
                }
            }
            element.find("#channelId").val(channelName).attr("value", gSession.curChannelId);
        },
        global: {
            pageIndex: 1,
            pageSize: 5,
            preset: "date",
            chooseArea: "../comm/chooseArea",
            chooseChannel: "../comm/chooseChannel"
        },

        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#acceptRegionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _chooseChannel: function () {
            var widget = this,element = $(widget.el);
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseChannel,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);
                }
            });
        },
        _backOrderListQuery: function () {
        	
        	var ifDeal = $("#ifDeal").val();
        	if('1'== ifDeal){
        		document.getElementById("delayResonDiv").style.visibility="hidden";
        	}else{
        		document.getElementById("delayResonDiv").style.visibility="visible";
        	}
        	
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("queryCustomOrderList", JSON.stringify(params), "#orderListResult", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if (widget._getConds()) {
                                    widget.refreshPart("queryCustomOrderList", JSON.stringify(params), "#orderList");
                                }
                                ;
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }

                }, {
                    async: false
                });
            }
            ;
        },
        
        _checkAll: function(){
        	var orderChecks = $("#orderListResult").find("input[type='checkbox']");
        	for (i = 0; i < orderChecks.length; i++) {
        		if (orderChecks[i].type == "checkbox") {
        			orderChecks[i].checked = $("#c_all").is(":checked");
        		}
        	}
        },
        _backOrderCommit: function () {
            
            var widget = this,element = $(widget.el);
            var orders = element.find('table input[name="payment"]:checked');
            
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            $("#backOrderCommit").attr("disabled", true);
            
            var reasonRemark = $("#reasonRemark").val();
            
            var boIdArr= [];
            for(var i=0;i<orders.length;i++){
            	var $order = $(orders[i]);
            	var data = $order.data("data")
            	boIdArr.push(data.orderItemId);
            }
            var custOrderId = $(orders[0]).data("data").custOrderId;
            var param = {
            		custOrderId : custOrderId,
        			orderItemIds : boIdArr,
        			reasonRemark : reasonRemark
        		};
            
            widget.callService("backOrderCommit",JSON.stringify(param),function (ret) {

                if (ret.retCode != null && ret.retCode != ""){
                    widget.popup("成功")
                    widget._backOrderListQuery();
                    return;
                }else {
                    widget.popup("失败")
                    return;
                }
            })

            
            $("#backOrderCommit").attr("disabled", false);

        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this;
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            var paramCnt = 0;
            var acceptRegionId = $("#acceptRegionId").attr("value");
            if (widget._isNullStr(acceptRegionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.acceptRegionId = acceptRegionId;
            }

            if ($("#c_channelId").is(":checked")) {
                paramCnt++;
                var channelId = $("#channelId").attr("value");
                if(widget._isNullStr(channelId)) {
                    widget.popup("请选择渠道！");
                    return false;
                } else {
                    params.createOrgId = channelId;
                }
            }

            if ($("#c_custOrderNbr").is(":checked")) {
                paramCnt++;
                var custOrderNbr = $("#custOrderNbr").val();
                if(widget._isNullStr(custOrderNbr)) {
                    widget.popup("请输入购物车号！");
                    return false;
                } else {
                    params.custOrderNbr = custOrderNbr;
                }
            }

            if ($("#c_custOrderId").is(":checked")) {
                paramCnt++;
                var custOrderId = $("#custOrderId").val();
                if(widget._isNullStr(custOrderId)) {
                    widget.popup("请输入号码！");
                    return false;
                } else {
                    params.custOrderId = custOrderId;
                }
            }

            var beginDate = $("#beginDate").val();
            if (widget._isNullStr(beginDate)) {
                widget.popup("请选择起始日期！");
                return false;
            } else {
                params.beginDate = beginDate;
            }

            var endDate = $("#endDate").val();
            if (widget._isNullStr(endDate)) {
                widget.popup("请选择结束日期！");
                return false;
            } else {
                params.endDate = endDate;
            }

            var ifDeal = $("#ifDeal").val();
            if (widget._isNullStr(ifDeal)) {
                widget.popup("请选择查询范围！");
                return false;
            } else {
                params.ifDeal = ifDeal;
            }
            if(paramCnt < 1) {
                widget.popup("请至少选择一个查询条件！");
                return false;
            }
            return params;
        },
        _getNowFormatDate: function() {
            var date = new Date();
            var seperator1 = "-";
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var strDate = date.getDate();
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            if (strDate >= 0 && strDate <= 9) {
                strDate = "0" + strDate;
            }
            var currentdate = year + seperator1 + month + seperator1 + strDate;
            return currentdate;
        }
    });
    vita.widget.register("backOrderListQuery", backOrderListQuery, true);
})(window.vita);