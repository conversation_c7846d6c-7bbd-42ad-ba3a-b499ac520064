(function (vita) {
    var allowanceManage = vita.Backbone.BizView.extend({
        events: {
            "click #qryGroupUserBtn": "_qryGroupUser",
            "click #regionIdBtn": "_chooseArea",
            "click [name='payment']": "_queryBusiTypeCheck",
            "click #accNumType": "_accNumTypeCheck",
            "click #qryAddMemBtn": "_qryAddMemBtnClick",
            "click #addMemCommitBtn": "_addMemCommitBtnClick",
            "click #quitSubsidyBtn": "_quitSubsidyBtnClick",
            "click #memMode": "_memModeCheck"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            var option = {
                id : dialogId,
                url : widget.global.chooseChannel,
                maskClose : false,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);
                   
                }
            };
            widget.dialog(option);
            $("#baseInofDiv").hide();
        	$("#addMemDiv").hide();
        	$("#memInfoDiv").hide();
        },
        global: {
            pageIndex: 1,
            pageSize: 100,
            chooseArea: "../comm/chooseArea",
            chooseChannel: "../comm/chooseChannel"
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _queryBusiTypeCheck: function (e) {
            var widget = this;
            var el = $(e.target);
            if(el.attr("id") == "busiAccNum") {
//                $("#accNumTypeDiv").show();
            	document.getElementById("accNumTypeDiv").style.visibility="visible";
                $("#queryLabel").text("接入号码");
                $("#accNumType").val("1");
            } else {
//                $("#accNumTypeDiv").hide();
            	document.getElementById("accNumTypeDiv").style.visibility="hidden";
                $("#queryLabel").text("合同号");
            }
        },
        _accNumTypeCheck: function () {
//        	alert("aaaa");
            var accNumType = $("#accNumType").val();
            if("1" == accNumType) {
                $("#queryLabel").text("接入号码")
            } else {
                $("#queryLabel").text("宽带账号")
            }
        },
        _qryGroupUser: function () {
            var widget = this;
            var params = widget._getConds();
            
            if (params) {
                widget.refreshPart("qryGroupUser", JSON.stringify(params), "#baseInofDiv", function (res) {
                    var r = $(res);
                    var resultInfo = r.find("#resultInfo").data("data");
                    if(0!= resultInfo.retCode){
                    	widget.popup(resultInfo.retDesc);
                    	$("#baseInofDiv").hide();
                    	$("#addMemDiv").hide();
                    	$("#memInfoDiv").hide();
                    }else{
                    	
                    	if(0!= resultInfo.groupUserCount){
                    		$("#baseInofDiv").show();
                    		$("#memInfoDiv").show();
                    		$("#addMemDiv").hide();
                    		widget._qryMemUser(resultInfo);
                        	
                    	}else{
                    		$("#baseInofDiv").hide();
                    		$("#memInfoDiv").hide();
                    	}
                    }
                }, {
                    async: false
                });
            };
        },
        _qryMemUser: function (groupInfo) {
//        	alert(prodInstId);
            var widget = this;
            var params = {
            		groupInfo: groupInfo,
                    pageInfo: {
                        pageIndex: widget.global.pageIndex,
                        pageSize: widget.global.pageSize
                    }
                };
            
            if (params) {
                widget.refreshPart("qryMemUser", JSON.stringify(params), "#memInfoDiv", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if (widget._getConds()) {
                                    widget.refreshPart("qryMemUser", JSON.stringify(params), "#memUserList");
                                }
                                ;
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }

                }, {
                    async: false
                });
            };
        },
        _qryAddMemBtnClick: function () {
        	var widget = this;
        	var resultInfo = $("#resultInfo").data("data");
        	var params = {
        			groupModeId : resultInfo.subsidyMode,
            		subRateKeyValue : resultInfo.subRateKeyValue
                };
            widget.refreshPart("qryAddMem", JSON.stringify(params), "#addMemDiv", function (res) {
                var r = $(res);
                var resultInfo = r.find("#qryAddMemInfo").data("data");
                if(0!= resultInfo.retCode){
                	widget.popup(resultInfo.retDesc);
                }else{
                	$("#addMemDiv").show();
                }
//            	$("#addMemDiv").show();
            }, {
                async: false
            });
        },
        _addMemCommitBtnClick: function () {
        	var widget = this;
        	var gSession = widget.gSession;
        	
        	var resultInfo = $("#resultInfo").data("data");
            var prodInstId = resultInfo.prodInstId;//单位用户实例ID
            
            var busiNumber = $("#busiNumber").val();//业务号码
            var memAccNumType = $("#memAccNumType").val();//号码类型
            var memMode = $("#memMode").val();//补贴模式
            var orderOfferId = $("#orderOfferId").val();//订购销售品ID
            
            var subsidyMode = resultInfo.subsidyMode;
            var amount;
            if(subsidyMode == 3){
            	amount = $("#amountInput").val();
            	if (widget._isNullStr(amount)) {
                    widget.popup("请输入补贴金额！");
                    return false;
                }
            }else{
            	amount = $("#amount").val();
            }
            
            if (widget._isNullStr(busiNumber)) {
                widget.popup("请输入业务号码！");
                return false;
            }
            var acctCd = resultInfo.acctCd;//单位合同号
            var busiRegionId = resultInfo.busiRegionId;
            var offerType = resultInfo.offerType;
            var offerId = resultInfo.offerId;
            var subRateKeyValue = resultInfo.subRateKeyValue;
            var offerInstId = resultInfo.offerInstId;
            var totalAmount = resultInfo.amont;
            var rate = resultInfo.rate;
            //渠道ID
            var createOrgId = $("#channelId").attr("value");
            if(widget._isNullStr(channelId)) {
            	createOrgId = gSession.curChannelId;
            }
            var createStaff = gSession.staffId;
            
            $("#addMemCommitBtn").attr("disabled", true);
        	var param = {
        			prodInstId : prodInstId,
        			busiNumber : busiNumber,
        			memAccNumType : memAccNumType,
        			memMode : memMode,
        			orderOfferId : orderOfferId,
        			amount : amount,
        			totalAmount : totalAmount,
        			rate : rate,
        			offerInstId : offerInstId,
        			subRateKeyValue : subRateKeyValue,
        			offerType : offerType,
        			offerId : offerId,
        			busiRegionId : busiRegionId,
        			acctCd : acctCd,
        			createOrgId : createOrgId,
        			createStaff : createStaff
                };
        	
            widget.callService("addMemCommit",JSON.stringify(param),function (ret) {
                if (0 == ret.retCode){
                    widget.popup(ret.retDesc);
                    widget._qryMemUser(resultInfo);
                    $("#addMemCommitBtn").attr("disabled", false);
                    return;
                }else {
                    widget.popup(ret.retDesc);
                    $("#addMemCommitBtn").attr("disabled", false);
                    return;
                }
            })
        },
        _quitSubsidyBtnClick: function () {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var users = element.find('table input[name="memInfo"]:checked');
            if(users.length == 0) {
                widget.popup("请选择退出补贴的成员用户！");
                return false;
            }
            var memDetail = $(users[0]).data("data").memDetail;
            //渠道ID
            var createOrgId = $("#channelId").attr("value");
            if(widget._isNullStr(channelId)) {
            	createOrgId = gSession.curChannelId;
            }
            var createStaff = gSession.staffId;
            
            var param = {
            		memDetail : memDetail,
            		createOrgId : createOrgId,
        			createStaff : createStaff
        		};
            $("#quitSubsidyBtn").attr("disabled", true);
            var resultInfo = $("#resultInfo").data("data");
            var prodInstId = resultInfo.prodInstId;
//            alert(prodInstId);
            widget.callService("quitSubsidy",JSON.stringify(param),function (ret) {
                if (0 == ret.retCode){
                    widget.popup(ret.retDesc);
                    widget._qryMemUser(resultInfo);
                    $("#quitSubsidyBtn").attr("disabled", false);
                    return;
                }else {
                    widget.popup(ret.retDesc);
                    $("#quitSubsidyBtn").attr("disabled", false);
                    return;
                }
            })

        },
        _memModeCheck: function () {
        	
        	var resultInfo = $("#resultInfo").data("data");
        	if("2" == resultInfo.subsidyMode){
            	var memMode = $("#memMode").val();
                if("21" == memMode) {
                	document.getElementById("memOfferDiv").style.visibility="visible";
                	document.getElementById("amountDiv").style.visibility="visible";
//                	$("#memOfferDiv").show();
//                	$("#amountDiv").show();
                } else {
                	document.getElementById("memOfferDiv").style.visibility="hidden";
                	document.getElementById("amountDiv").style.visibility="hidden";
//                	$("#memOfferDiv").hide();
//                	$("#amountDiv").hide();

                }
        	}
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this;
            var params = {};

            var regionId = $("#regionId").attr("value");
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.busiRegionId = regionId;
                params.regionId = regionId;
            }
            var qryType = $("#qryType").val();//查询类型 1、单位产品 2、个人产品
//            if(qryType == 1) {
//                if(!$("#busiAccNum").is(":checked")) {
//                    widget.popup("单位产品只能根据业务号码查询！");
//                    return false;
//                }
//            }
            params.qryType = qryType;
            
            var accNumType =  $("#accNumType").val();//号码类型 1、接入号码 2、宽带账号 3、合同号
            
            
            var queryValue = $("#queryValue").val();
            if (widget._isNullStr(queryValue)) {
                widget.popup("请输入查询号码！");
                return false;
            } else {
                params.queryValue = queryValue;
            }
            //合同号
            if ($("#busiAcct").is(":checked")) {
            	accNumType = "3";
            }
            
            params.accNumType = accNumType;
            return params;
        }
    });
    vita.widget.register("allowanceManage", allowanceManage, true);
})(window.vita);