package com.asiainfo.crm.assist.app.orgCust;

import com.asiainfo.appframe.ext.flyingserver.org.apache.commons.collections.MapUtils;
import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.service.intf.IImportBatchOrgMultiSmo;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 导入文件下载
 * <AUTHOR>
 */
@Component("vita.batchOrgFilesDownLoad")
public class BatchOrgFilesDownLoad extends AbstractSoComponent {

    @Autowired
    private IImportBatchOrgMultiSmo smo;

    @Override
    public Map achieveData(Object... objects) throws Exception {
        String json = objects[0].toString();

        Map options = jsonConverter.toBean(json, Map.class);
        return options;
    }

    public Map queryFiles(String json) throws Exception {
        Map fileOptions = Maps.newHashMap();
        String resultJson = smo.queryOrgFiles(json);
        Map<String, Object> resultObject = (Map<String, Object>) resolveResult(resultJson);

        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObject,"pageInfo");
        int totalCount = MapUtils.getIntValue(pageInfoMap, "totalCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageSize = MapUtils.getIntValue(pageInfoMap, "pageSize");
        fileOptions.put("pageCount",pageIndex);
        fileOptions.put("pageIndex",pageSize);
        fileOptions.put("totalNumber", totalCount);
        fileOptions.put("orgFileVoList", MapUtils.getObject(resultObject,"orgFileVoList"));
        return fileOptions;
    }

}
