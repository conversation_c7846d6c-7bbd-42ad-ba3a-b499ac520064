(function(vita) {
	var certNumFiveRepair = vita.Backbone.BizView.extend({
		events : {
			"click #btn-certNumRepair" : "_certNumRepair"
		},
		_initialize : function() {
			var widget = this, element = $(widget.el);
		},

		_getConds : function() {
		},

		_certNumRepair : function() {
			var widget = this;
            var gsession = widget.gSession;
            var phone = $("#phone").val();
            var certNum = $("#certNum").val();
            var res = widget._checkParams(phone,certNum);
            if (res == false) return;
            var params = {
            	phoneNum: phone,
            	certNum: certNum,
            	serviceType:1100,
            	actionType:12,
            	businessType:1,
            	lanId: gsession.staffLanId,
            	channelNbr: gsession.curChannelNbr,
            	staffCode : gsession.staffCode,
            	glbSessionId: gsession.glbSessionId
            };
			widget.callService("exeForceRepairRelation", params, function(res) {
					widget.popup(res.retDesc)
					return;
			}, {
				async: false 
			});
		},
		_certNumBind : function() {
			var widget = this;
            var gsession = widget.gSession;
            var phone = $("#phone").val();
            var certNum = $("#certNum").val();
            var res = widget._checkParams(phone,certNum);
            if (res == false) return;
            var params = {
            	phoneNum: phone,
            	certNum: certNum,
            	serviceType:1100,
            	actionType:11,
            	lanId: gsession.staffLanId,
            	channelNbr: gsession.curChannelNbr,
            	staffCode : gsession.staffCode,
            	glbSessionId: gsession.glbSessionId
            };
			widget.callService("addexeForceRepairRelation", params, function(res) {
					widget.popup(res.retDesc)
					return;
			}, {
				async: false 
			});
		},
		

		_checkParams: function(phone,certNum) {
				if (phone.trim().length == 0) {
	                this.popup("请输入手机号码！");
	                return false;
	            }
	            if (certNum.trim().length == 0) {
	                this.popup("请输入原身份证号！");
	                return false;
	            }
	            return true;
		}
	});
	vita.widget.register("certNumFiveRepair", certNumFiveRepair, true);
})(window.vita);