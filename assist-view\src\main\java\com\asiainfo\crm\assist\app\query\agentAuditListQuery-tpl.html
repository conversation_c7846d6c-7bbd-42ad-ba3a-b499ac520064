<div data-widget="agentAuditListQuery" style="height: 100%">

    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>稽核厅查询</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                地 区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_channelId" type="checkbox" name="payment">
                                                </label> 渠道
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="channelId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="channelIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_auditName" type="checkbox" name="payment">
                                                </label> 稽核厅名称
                                            </label>
                                            <div class="col-md-7">
                                                <input id="auditName" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_date" type="checkbox" name="payment">
                                                </label>起始日期</label>
                                            <div class="col-md-7">
                                                <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">结束日期</label>
                                            <div class="col-md-7">
                                                <input name="endDate" id="endDate" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <div class="col-md-12 searchbutt_r" align="center">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-clear" type="button" class="btn btn-white">清除</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <!--<div><i class="bot"></i>查询结果</div>-->
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12 mart10" id="agentAuditListResult">
                                        <table class="table table-hover" id="agentAuditTable">
                                            <thead>
                                            <tr>
                                                <th>稽核厅名称</th>
                                                <th>创建时间</th>
                                                <th>信用额度</th>
                                                <th>提醒阀值</th>
                                                <th>业务阀值</th>
                                                <th>是否提醒</th>
                                                <th>稽核厅状态</th>
                                                <th>提醒号码</th>
                                                <th>关联渠道</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            #if($options != "null" && $options.agentAuditList && $options.agentAuditList != "null" &&
                                            $options.agentAuditList.size() > 0)
                                            #foreach($agentAudit in $options.agentAuditList)
                                            <tr>
                                                <td>$!{agentAudit.name}</td>
                                                <td>$!{agentAudit.createDate}</td>
                                                <td>$!{agentAudit.credibility}</td>
                                                <td>$!{agentAudit.remindThreshold}</td>
                                                <td>$!{agentAudit.serviceThreshold}</td>
                                                <td>$!{agentAudit.remindSms}</td>
                                                <td>$!{agentAudit.statusCd}</td>
                                                <td>$!{agentAudit.remindNum}</td>
                                                <td ><button class="agencyInfoId"><p class="vita-data">{"agencyInfoId":$!{agentAudit.agencyInfoId}}</p>∨</button></td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.agentAuditList && $options.agentAuditList != "null" &&
                                            $options.agentAuditList.size() == 0)
                                            <tr>
                                                <td align='center' colspan='9'>未查到相关数据</td>
                                            </tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
