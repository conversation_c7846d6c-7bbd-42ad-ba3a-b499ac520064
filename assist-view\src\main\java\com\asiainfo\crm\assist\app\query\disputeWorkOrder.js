/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/9/23.
 */
(function (vita) {
    var disputeWorkOrder= vita.Backbone.BizView.extend({
        events:{
          "click #searchBtn":"_qrydisputeWorkOrders",
          "click #disputeUploadBtn": "_uploaDispute",//发起争议单
        },
        global : {
            pageIndex: 1,
            pageSize: 10,
            disputeUpload: "../so/disputeUpload"//发起争议单
        },
        _initialize:function () {
            var widget = this,
                element = $(widget.el),
                global = widget.global,
                data = element.data("data");
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var nowDate = new Date();
            var createDate = element.find("input[name='createDate']");
            var startYear = nowDate.getFullYear() - 5, endYear = nowDate.getFullYear();
            if (createDate.length) {
                datetime.register(createDate, {
                    preset: global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear:endYear
                });
            }
            if(data.disputeUploadSwitch!="Y") element.find("#disputeUploadBtn").hide();
            widget._disputeUploadBtnLocal();
        },
        _disputeUploadBtnLocal :$.noop,
        _qrydisputeWorkOrders:function (e) {
            var widget = this,
                element = $(widget.el);
            var pageIndex;
            if(e&&e.target){
                pageIndex=1;
                widget.model.set("exceptionInfoConfigId",null);
            }else{
                pageIndex=widget.model.get("pageIndex");
                if(pageIndex == null || pageIndex == undefined || isNaN(pageIndex)){
                    pageIndex = 1;
                }
            }
            var param = widget._getConds(pageIndex);
            if(param.staffCode==null||param.staffCode==""){
                widget.popup("请输入受理工号");
                return;
            }
             element.find('#showPageInfo').empty();
            debugger;
            widget.refreshPart("qrydisputeWorkOrderLogs", param, "#disputeWorkOrder", function(res) {
                var paging = widget.require("paging"), r = $(res);
                var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if (parseInt(totalNumber) > 0) {
                        var e = paging.new({
                            recordNumber : widget.global.pageSize,
                            total : totalNumber,
                            pageIndex : pageIndex,
                            callback : function(pageIndex){
                                //判断当前页
                                if(pageIndex == null || pageIndex == undefined || isNaN(pageIndex)){
                                    pageIndex = 1;
                                }
                                widget.model.set("pageIndex",pageIndex);
                                widget.refreshPart("qrydisputeWorkOrderLogs", widget._getConds(pageIndex),  ".table-hover");
                                element.find("#showPageInfo").append(e.getElement());
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    } else{
                        element.find('#table').empty();
                    }
                }, {
                async: false
            });
        },
        _getConds:function (pageIndex) {
            var widget = this,
                element = $(widget.el);
            gSession = widget.gSession;
            var staffCode = $.trim(widget.model.get("staffCode"));
            var channelId = $.trim(widget.model.get("channelId"));
            if(channelId==""||channelId==null){
                channelId=undefined;
            }
            var createDate= element.find("#createDate").val();
            if(createDate==""||createDate==null){
                createDate=undefined;
            }
            var custName =  $.trim(widget.model.get("custName"));
            if(custName==""||custName==null){
                custName=undefined;
            }
            var certNum =   $.trim(widget.model.get("certNum"));
            if(certNum==""||certNum==null){
                certNum=undefined;
            }
            var disputeWorkOrderStatus= element.find("#disputeWorkOrderStatus").val();
            if(disputeWorkOrderStatus==""||disputeWorkOrderStatus==null){
                disputeWorkOrderStatus=undefined;
            }
            var param = {
                "pageIndex" : pageIndex,
                "pageSize" : widget.global.pageSize,
                "staffCode" : staffCode,
                "channelId" : channelId,
                "createDate":createDate,
                "custName" : custName,
                "certNum" : certNum,
                "statusCd":disputeWorkOrderStatus,
            };
            return param;
        },
        
        _uploaDispute:function(){
            var widget = this;
            var option = {
                url: "../action/disputeUpload",
                onClose: function (res) {
                }
            };
            widget.dialog(option);
            return false;
        },
    })
    vita.widget.register("disputeWorkOrder",disputeWorkOrder,true);
})(window.vita)