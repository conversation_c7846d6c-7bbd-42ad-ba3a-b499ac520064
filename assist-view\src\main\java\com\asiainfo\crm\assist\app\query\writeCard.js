(function (vita) {
    var _haveReadCard = false;//默认未读卡
    var g_realWriteCard = false;//true真实写卡，false模拟写卡
    var _haveWriteCard = false;
    var productId = "";
    var _rscJson = {
        "iccid":"",
        "imsi":"",
        "imsig":"",
        "data":"",
        "dataLength":"0",
        "state":"N",
        "prodId":""
    };
    var _cardInfoJson = {
        "serialNumber":"",
        "factoryCode":"",
        "cardTypeId":"",
        "cardName":"",
        "materialId":"",
        "canWrite":"N"
    };
    var _essWriteCard = {};
    var ocx;
    var writeCard = vita.Backbone.BizView.extend({
        events: {
            "click #btnReadCard": "_readCard",
            "click #btnWriteCard": "_writeCard",
            "click #btn_confirm": "_close",
            "click #btn_close": "_close"
        },
        _initialize: function () {
            var widget = this,
                element = $(widget.el);
            ocx = document.getElementById("ocx");

        },
        _readCard : function(prodId){
            var widget = this;
            _haveReadCard = true;
            /*TODO:实际本地化读卡为住*/
            if(!g_realWriteCard){
                var date = new Date();
                _rscJson.imsi = "1111111111111"+date.getMilliseconds();
                _rscJson.iccid = "89860314105350023801";
                if (_rscJson.iccid.length==20){
                    _rscJson.iccid = _rscJson.iccid.substr(0,_rscJson.iccid.length-1);
                }
                //$("#serialNum"+prodId).val(_rscJson.iccid);
                var testSerialNumber =date.getMilliseconds();
                $("#serialNum").val("33333333333" + testSerialNumber);
                _cardInfoJson.cardTypeId = 32432;
                _cardInfoJson.serialNumber = testSerialNumber;
                _cardInfoJson.factoryCode = 30;
              /*  if(!_getCardType()){//此判断暂时不用
                    return false;
                }*/
                // if(_cardInfoJson.cardTypeId){//此判断暂时不用
                //     if(prod.changeUim.is4GProdInst&&(OrderInfo.actionFlag==22 || OrderInfo.actionFlag==23)){//判断是否有开通4G功能产品
                //         //var flag=false;//默认3G卡
                //         //for(var i=0;i<_4GCARDS.length;i++){
                //         //	if(_cardInfoJson.cardTypeId==_4GCARDS[i]){//4G卡
                //         //		flag=true;
                //         //		break;
                //         //	}
                //         //}
                //         //if(!flag){
                //         if(CONST.UIMTYPE3G4G.IS3G==prod.uim.getCardType("","",_cardInfoJson.serialNumber)){
                //             $.alert("信息提示","您已开通了【4G（LTE）上网】功能产品，而UIM卡是3G卡，请使用4G的白卡！");
                //             return false;
                //         }
                //     }
                // }
                return true;
            }
            // 读取ICCID 转
            var iccidResult =null;
            try{
                // iccidResult = ec.util.defaultStr(ocx.strGetICCID());
                iccidResult = ocx.strGetICCID();
            }catch(e){
                // $.alert("提示","加载读卡插件失败,请检查是否已正确安装插件或浏览器正确设置!错误信息："+e.message,"error");
                widget.popup("加载读卡插件失败,请检查是否已正确安装插件或浏览器正确设置!错误信息："+e.message);
                return false;
            }
            //alert("iccidResult:"+iccidResult);
            var opCode1 = iccidResult.substring(iccidResult.length - 4,iccidResult.length);
            if (opCode1 == '') {
                widget.popup("读取卡失败!请确认卡连接是否正常!");
                return false;
            }
            if(prodId == undefined){
                if (opCode1 != '' && opCode1 != "FFFF") {
                    widget.popup("卡片已有数据,不能重复写入!");
                    return false;
                }
            }

            //var iccid = iccidResult.substring(0,iccidResult.length-5);
            var iccid = iccidResult;

            //读取空卡序列号
            var serialNum = "";
            var result = ocx.GetEmptyCardFile();//读卡器读取结果
            var opCode = result.substring(result.length - 5,result.length);
            //alert("opCode::"+opCode);

            // add by wangdan 9/11 start
            /*var columnValue = "";
            try{
                var qryConfigUrl=contextPath+"/mktRes/queryApConf";
                var jr = $.callServiceAsJsonGet(qryConfigUrl);
                if(jr.code==0 && jr.data){
                    for(var n=0;n<jr.data.length;n++){
                        if(jr.data[n].SERIALNUM_FILTER){
                            columnValue = jr.data[n].SERIALNUM_FILTER[0].COLUMN_VALUE;
                        }

                    }
                }
            }catch(e){

            }#13318，所有的白卡都不截取，现直接入库*/
            //end
            if (opCode != '' && opCode != "FFFF") {
                //update by huangjj3 #13318，如果选择的是空白卡，cardNo需要做下逻辑处理
                // var serviceName = contextPath + "/mktRes/writeCard/getAsciiToHex";
                var asciiFStr = result.substring(result.length-4,result.length);
                var param = {
                    "asciiFStr":asciiFStr
                };
                // var response = $.callServiceAsJson(serviceName, param);
                var response;
                widget.callService("getAsciiToHex",param,function(res){
                    response = res;
                },{
                    async: false,
                    mask:true,
                    headers : {
                        "regionId" : widget.gSession.installArea
                    }
                });

                var selUimCard = result.substring(0,result.length-4)+response.asciiStr;
                // $("#uim_txt_"+prodId).val(selUimCard);
                // $("#resultCardAsciiFStr").val(selUimCard);
                // $("#resultCardNo").val(result);
                // if($("#selUimType").val()!="4"){
                //     $("#selUimType").val("3");
                //     $("#selUim").val("2");//下拉框选择为空白卡
                // }else{
                //     $("#selUim").val("4");
                // }
                // $("#uim_lable").attr("disabled",true);
                serialNum = result;//#13318，所有的白卡都不截取，现直接入库
                factoryCode = result.substring(result.length - 2,result.length);
                //if(factoryCode==42){
                //	factoryCode=30;
                //}
            }
            // var areaId = OrderInfo.getProdAreaId(prodId);
            /*areaId = areaId.substring(0,3) + "0000";
            //不要截取的  add by wangdan 9/11 start
            for(var i=0,nserialnumValues = columnValue.split(",");i< columnValue.length;i++){
                if (areaId == nserialnumValues[i]) {
                    serialNum = result;
                    break;
                }
            }#13318，所有的白卡都不截取，现直接入库*/
            //end
            //alert("serialNumber:="+_cardInfoJson.serialNumber);
            //如果读取到了空卡序列号就加载卡片信息
            if (serialNum) {
                _cardInfoJson.serialNumber = serialNum;
                _cardInfoJson.factoryCode = factoryCode;
                _cardInfoJson.cardTypeId = serialNum.substr(5,5);
                //alert("cardTypeId:"+_cardInfoJson.cardTypeId);

                //加载卡片信息
                /*chylg
                if (_initBlankCardTypeInfo() == false) {
                    alert("加载卡片信息失败");
                    return false;
                } else {
                    selectMaterialId = _cardInfoJson.materialId;
                    $("#cardType").find("option[value=" + selectMaterialId + "]").attr("selected",
                            "selected");
                }
                */
            }

            /*if(!_getCardType()){//暂时不做判断
                return false;
            }*/
            /*if(ec.util.isObj(_cardInfoJson.cardTypeId)){//此判断暂时不用
                if(prod.changeUim.is4GProdInst&&(OrderInfo.actionFlag==22 || OrderInfo.actionFlag==23)){//判断是否有开通4G功能产品
                    //var flag=false;//默认3G卡
                    //for(var i=0;i<_4GCARDS.length;i++){
                    //	if(_cardInfoJson.cardTypeId==_4GCARDS[i]){//4G卡
                    //		flag=true;
                    //		break;
                    //	}
                    //}
                    if(CONST.UIMTYPE3G4G.IS3G==prod.uim.getCardType("","",_cardInfoJson.serialNumber)){
                        $.alert("信息提示","您已开通了【4G（LTE）上网】功能产品，而UIM卡是3G卡，请使用4G的白卡！");
                        return false;
                    }
                }
            }*/
            //alert("_cardInfoJson:="+JSON.stringify(_cardInfoJson));
            if (iccid == 'FFFFFFFFFFFFFFFFFFFF' || iccid == '00000000000000000000') {
                _cardInfoJson.canWrite = 'Y';
                // $("#btnWriteCard"+prodId).css("display","");
                // $("#serialNum"+prodId).val(_cardInfoJson.serialNumber);
                // $("#iccid"+prodId).val(_rscJson.iccid);
                // $("#imsi"+prodId).val(_rscJson.imsi);
                // $("#cardTypeId"+prodId).val(_cardInfoJson.cardTypeId);
            } else {//成品卡
                _cardInfoJson.canWrite = 'N';
                //如果是受理中写卡，成品卡隐藏写卡按钮
                if (_settings.params.scene == "1") {
                    $("#btnWriteCard"+prodId).css("display","none");
                }
                if (serialNum != null) {//白卡写出的成品卡
                    $("#serialNum"+prodId).val(_cardInfoJson.serialNumber);
                    $("#cardTypeId"+prodId).val(_cardInfoJson.cardTypeId);
                } else {//厂商生成的成品卡
                    var bcdcode = iccid.substring(0,19); //原有成品卡取19位做终端码
                    $("#serialNum"+prodId).val(bcdcode);
                }
            }
            $(this.el).find("#serialNum").val(serialNum);
            widget.popup("读卡成功!");
            return true;
        },
         _writeCard : function(prodId){
            var widget = this;
            _haveWriteCard = false;//false;
            productId = prodId;
            //[start]模拟写卡
            /*TODO:实际本地化写卡为主*/
            if(!g_realWriteCard){
                _haveWriteCard = true;
                widget.popup("写卡成功卡!");
                //$("#uim_txt_"+prodId).val(_rscJson.iccid);
//                var coupon = {
//                    couponUsageTypeCd : "3", //物品使用类型
//                    inOutTypeId : "1",  //出入库类型
//                    inOutReasonId : 0, //出入库原因
//                    saleId : 1, //销售类型
//                    couponId :"10", //物品ID
//                    couponinfoStatusCd : "A", //物品处理状态
//                    chargeItemCd : CONST.ACCT_ITEM_TYPE.UIM_CHARGE_ITEM_CD, //物品费用项类型
//                    couponNum : "1", //物品数量
//                    storeId : "8230010", //仓库ID
//                    storeName : "11", //仓库名称
//                    agentId : 1, //供应商ID
//                    apCharge : 0, //物品价格
//                    couponInstanceNumber : "8230007407312006106", //物品实例编码
//                    ruleId : "", //物品规则ID
//                    partyId : OrderInfo.cust.custId, //客户ID
//                    prodId :prodId, //产品ID
//                    state : "ADD", //动作
//                    relaSeq : "" //关联序列
//                };
//                if(OrderInfo.actionFlag==1 || OrderInfo.actionFlag==6 || OrderInfo.actionFlag==14){
//                    coupon.offerId = "-1";
//                }else{
//                    coupon.offerId = order.prodModify.choosedProdInfo.prodOfferInstId; //销售品实例ID
//                }
//                OrderInfo.clearProdUim(prodId);
//                OrderInfo.boProd2Tds.push(coupon);
                return true;
            }
            //[end]模拟写卡

            //写卡之前必须要先读卡
            if (!_haveReadCard) {
                widget.popup("请先读卡!");
                return false;
            }

            //写卡之前再读卡
            if (!this._readCard()) {
                widget.popup("写卡前验证读卡验证时读卡失败，请确认设备是否连接好!");
                return false;
            }

            //if (_cardInfoJson.canWrite != 'Y') {
            //	$.alert("提示","插入的卡片是不是可写的白卡,该卡在库存中状态不是可写卡或者已经是成品卡，请换卡!","error");
            //	return false;
            //}
            //如果是黑莓手机写卡一定要是支持EVDO的CG双模卡
           /* var hmPesn = $("#pesn").val();//此判断暂时不做

            if (hmPesn != undefined && hmPesn != "" && hmPesn != null) {
                cardType = _cardInfoJson.serialNumber.substr(6,2);
                if (cardType != '10') {
                    $.alert("提示","黑莓手机终端配套的手机卡必须是支持EVDO的CG双模卡，请换支持EVDO的CG双模卡进行写卡!","error");
                    return false;
                }
            }*/
            //alert("加载动态链接库");
            //加载动态链接库
            if (!this._updateCardDll()) {
                widget.popup("加载动态链接库失败");
                return false;
            }

            //alert("加载动态链接库 end");

            //写卡之前必须调用DisConnectReader()函数断开写卡器，江苏恒宝的写卡方式
            var dkResult = ocx.DisConnectReader();
            if (dkResult != "0") {
                widget.popup("写卡之前,断开写卡器失败!");
                return false;
            }
            //客户端调用插件函数获取随机数
            var getResult = ocx.GetRandNum();
            //alert("随机数="+getResult);

            if (getResult.length != 16) {
                widget.popup("写卡异常:获取随机数失败！");
                return false;
            }
            var randomNum = getResult;
            //判断是否为8字节长的随机数
            if (randomNum.length != 16) {
                //alert('判断8字节');
                widget.popup("写卡异常:从卡商组件获取随机数不是8字节长的随机数！");
                return false;
            }
            //判断密码是否为空
            if (_cardDllInfoJson.dllPassword == "") {
                //alert('密码'+_cardDllPassword);
                widget.popup("写卡异常(组件鉴权密码不可为空)，请检查！");
                return false;
            }
            //alert("用获取的随机数生成鉴权码");
            /*-----------------------------用获取的随机数生成鉴权码 -------------------*/
            var authCode = null;
            // serviceName = contextPath + "/mktRes/writeCard/authCode";
            try {
                var param = {
                    randomNum:randomNum,
                    dllPassword:_cardDllInfoJson.password,
                    factoryCode:_cardDllInfoJson.factoryCode,
                    authCodeType:_cardDllInfoJson.authCodeType
                };

                // var response = $.callServiceAsJson(serviceName, param);
                var response;
                widget.callService("getAuthCode",param,function(res){
                    response = res;
                },{
                    async: false,
                    mask:true,
                    headers : {
                        "regionId" : widget.gSession.installArea
                    }
                });
                if(response.retCode == 0){
                    authCode = response.retObj;
                }else{
                    // $.alertM(response.data);
                    widget.popup(response.retMsg);
                    return false;
                }

                /*var code = authCodeJson.code;
                if (code != null && code == "POR-0000") {
                    authCode = authCodeJson.authCode;
                    //authCode ='B9488FBC60982B48';
                    //alert('鉴权码='+authCode);
                } else {
                    $.alert("提示","用获取的随机数生成鉴权码失败！","error");
                    return false;
                }*/
            } catch(e) {
                $.alert("提示","用获取的随机数生成鉴权码异常!" + e.message,"error");
                return false;
            }
            //alert("用获取的随机数生成鉴权码authCode:" + authCode);
            /*-----------------------------用获取的鉴权密钥和生产的随机数生产鉴权码完成 -----------------*/

            //获得鉴权结果
            var auResult = ocx.Authentication(authCode);

            var opCode = auResult;
            if (opCode != "0") {
                widget.popup("鉴权失败！" + auResult);
                return false;
            }
            /*------------------------准备工作完成，开始调用Active控件写卡----------------------------------------*/
            //先申请资源数据
            //if (!_applyResourceData(scope)){
            if (!this._applyResourceData(prodId)) {
                //alert('申请卡数据以后');
                //如果申请资源数据失败
                return false;
            }

            //alert("调用组件写卡函数进行写卡");

            _haveWriteCard = true;
            //申请资源成功，写卡过程已经开始
            //调用组件写卡函数进行写卡，调用dep接口传入写卡数据。
            //$.alert("提示","写入数据_rscJson.data="+_rscJson.data+"\n\n写卡结果;authCode:"+authCode+";writeCardResult="+writeCardResult,"error");
            //alert("写入数据_rscJson.data="+_rscJson.data+"\n\n写卡结果;writeCardResult="+writeCardResult);
            //return false;
            //_rscJson.data = "89860311805101572698,***************,8097808C,3760,9,FFFF,0bf0ad8280d2b0a0,1234,55900648,85232888,49600368,59EF72FB,<EMAIL>,75d6ecc6632fac57,<EMAIL>,30ffc6952e03ccd0;37623582e5265d5a;cdfd165333f66519,<EMAIL>,e43472656c30e82e;671ab8c55dbdb14f;16a7e838ae5db0fa,e51c11352e62e0ac;98e3ce89414d982a;cac02f2ef7d2544d,,,,";
            //_rscJson.data = "89860313007580411850,***************,805BA245,3619,3,FFFF,123013c243731bc8,1234,51154902,74059570,96831988,90892996,<EMAIL>,059f7bdf8e3f4e12,<EMAIL>,e99eeb2f8c1ddf16;a4316f058ba1aedb;54034ca47e67108c;edfa87035ebe8a18;90719b0fea72386f,<EMAIL>,9aebd4cdfeeccfcb;529995a75de27bf9;f868720e8cc7ee48;5ee4410b4ca89979;9666d03a637b22ee,2479e9c1334e9337;795e86be92e2a431;df518cdb3c732c8f;d6c98a059b44ff3c;edb8698d619dc2d1,204043153514753,3,19e7126871e1eb11a85a4ae90b8daa7c,+316540942000";
            //_rscJson.data = "89860313900100000102,***************,8000A876,13824,0,FFFF,a471b07640a45918,1234,66709721,78778797,85087049,B8C32115,<EMAIL>,63044385d047084c,<EMAIL>,c1d4f3f021172c63,<EMAIL>,5c3a505e7f018f7d,0dc6e3733d291f52,,,";
            //alert("xj:"+_rscJson.data);
             debugger;
            var writeCardResult = ocx.YXPersonalize(authCode,"",1,_rscJson.data,"");
            //alert("writeCardResult:"+writeCardResult);
            if (writeCardResult != "0") {
                //客户端提示：写卡失败时各卡商返回各自定义的错误信息
               /* var paramLog = {
                    mkt_res_inst_code : $("#resultCardNo").val(),
                    iccid : $("#resultCardNo").val(),
                    card_source : _cardDllInfoJson.dllName,
                    err_desc :"写卡失败，请将白卡取出！错误编码=" + writeCardResult+"详细错误请联系卡商["+_cardDllInfoJson.remark+"]确认",
                    acc_nbr : '',
                    contact_record : ''
                };
                _writeCardLogInfo(paramLog);
                $.alert("提示","写卡失败，请将白卡取出！错误编码=" + writeCardResult+"详细错误请联系卡商["+_cardDllInfoJson.remark+"]确认","error");
                this._completeWriteCard("1",writeCardResult);//写卡失败回填*/
               this._wrtRsltReport(writeCardResult,"失败");
                widget.popup("写卡失败，请将白卡取出！错误编码=" + writeCardResult+ "详细错误请联系卡商[" + _cardDllInfoJson.remark + "]确认");
                return false;
            } else {
                //写卡成功
                //集团4g受理，仅供参考
                /*if (this._completeWriteCard("00000000",writeCardResult)) {
                    if(OrderInfo.actionFlag == 41){
                        var url = contextPath + "/ess/order/writeCardMakeUp";
                        $.callServiceAsJson(url,_essWriteCard, {
                            "before" : function() {
                                $.ecOverlay("正在串码回填，请稍等...");
                            },
                            "always" : function() {
                                $.unecOverlay();
                            },
                            "done" : function(response) {
                                if (response.code == 0) {
                                    $.confirm("提示","写卡成功,资源补录成功！",{
                                        yes:function(){
                                            window.location.href = contextPath+"/ess/order/remoteWriteCard";
                                        },
                                        no:function(){
                                            window.location.href = contextPath+"/ess/order/remoteWriteCard";
                                        }
                                    });
                                    return;
                                } else if (response.code == -2) {
                                    $.alertM(response.data);
                                    return;
                                } else if (response.code == 1002) {
                                    $.alert("错误", response.data);
                                    return;
                                } else {
                                    $.alert("异常", "写卡成功,资源补录异常");
                                    return;
                                }
                            },
                            fail : function(response) {
                                $.unecOverlay();
                                $.alert("提示", "请求可能发生异常，请稍后再试！");
                            }
                        });
                    }else{
                        $.alert("提示","恭喜，您已经成功写卡！");
                    }
                    $("#uim_check_btn_"+prodId).attr("disabled",true);
                    $("#uim_check_btn_"+prodId).removeClass("purchase").addClass("disablepurchase");
                    return true;
                } else {
                    //$.alert("提示","写卡成功，但卡资源下发异常","error");
                    return false;
                }*/
                var resp;
                var reportResult = this._wrtRsltReport(writeCardResult,"成功");
                if(reportResult && reportResult.resultCode == 0){
                    var params ={
                    		//TODO:具体本地化实际参数为准
                    		"mktResType": "103006000",
                            "mktResInstCode": _cardInfoJson.serialNumber,
                            "statusDate": "20150401120000",
                            "lanId": data.replPhoneNumLanId,
                            "channelNbr": widget.gSession.curChannelId,
                            "staffCode": widget.gSession.staffCode,
        					"mktResInstId": "1234567889012",
        					"mktResId": "9000000002",
        					"salesPrice": "2000",
        					"iccid": "8986111425029197396"
                    };
                    widget.callService("prhldngRlsUimCard",params,function(res){
                        resp = res;
                        widget.popup("写卡成功");
                    },{
                        async: false
                    });
                }
            }
        },
        /**
         * 集团4g写卡结果处理，仅供参考
         * */
        _completeWriteCard :function(result,resultCode){
            var serviceName = contextPath + "/mktRes/writeCard/completeWriteCard";
            var srInParam = {
                "areaId": OrderInfo.getProdAreaId(_rscJson.prodId),
                "InoutInfo": {
                    "ApplyNo": "001",
                    "InoutId": "",
                    "BillType": "2",
                    "SaleNo": "",
                    "BatchId": "",
                    "OperationType": "1100",
                    "MktResStoreId": "",
                    "ChannelId": OrderInfo.staff.channelId,
                    "AreaId": OrderInfo.getProdAreaId(_rscJson.prodId),
                    "StaffId": OrderInfo.staff.staffId
                },
                "MktResInstInfos": [
                    {
                        "MktResInstInfo": {
                            "BaseInfo": {
                                "MktResTypeCd": "",
                                "StatusCd" : "2",
                                "MktResId": "",
                                "MktResCd": _cardInfoJson.cardTypeId,
                                "MktResInstCode": _cardInfoJson.serialNumber,    //_rscJson.iccid,
                                "SalesPrice": "0",
                                "CostPrice": "0",
                                "Quantity": "1",
                                "Unit": "101"
                            },

//			                60020005：ICCID
//			                60020002：C-IMSI(3G)
//			                60020003：G-IMSI
//			                60020004:L-IMSI(4G)
//			                65010026:PIN1
//			                65010027:PIN2
//			                65010028:PUK1
//			                65010029:PUK2
//			                65010030:管理密码(ADM)
//			                65010033:UIMID
//			                65010035:AKEY
//			                65010037:NID
//			                65010038:ACCOL

                            "AttrList": [

                                {
                                    "AttrId": "60020005",
                                    "AttrValue": _rscJson.iccid
                                },
                                {
                                    "AttrId": "60020002",
                                    "AttrValue": _rscJson.imsi
                                },
                                {
                                    "AttrId": "60020003",
                                    "AttrValue":  _rscJson.imsig
                                },
                                {
                                    "AttrId": "60020004",
                                    "AttrValue": _rscJson.imsilte
                                },
                                {
                                    "AttrId": "65010026",
                                    "AttrValue": _rscJson.pin1
                                },
                                {
                                    "AttrId": "65010027",
                                    "AttrValue": _rscJson.pin2
                                },
                                {
                                    "AttrId": "65010028",
                                    "AttrValue": _rscJson.puk1
                                },
                                {
                                    "AttrId": "65010029",
                                    "AttrValue": _rscJson.puk2
                                },
                                {
                                    "AttrId": "65010030",
                                    "AttrValue": _rscJson.adm
                                },
                                {
                                    "AttrId": "65010033",
                                    "AttrValue":  _rscJson.uimid
                                },
                                {
                                    "AttrId": "65010035",
                                    "AttrValue": _rscJson.akey
                                },
                                {
                                    "AttrId": "65010037",
                                    "AttrValue": _rscJson.nid
                                },
                                {
                                    "AttrId": "65010038",
                                    "AttrValue": _rscJson.accolc
                                },
                                {
                                    "AttrId": "60029003",
                                    "AttrValue": OrderInfo.staff.channelId
                                },
                                {
                                    "AttrId": "60029004",
                                    "AttrValue": _cardDllInfoJson.remark
                                },
                                {
                                    "AttrId": "60029005",
                                    "AttrValue": _cardDllInfoJson.dllVersion
                                },
                                {
                                    "AttrId": "65010019",
                                    "AttrValue": OrderInfo.getProdAreaId(_rscJson.prodId)
                                }
                            ]
                        }
                    }
                ]

            };
            var param = {
                imsi:_rscJson.imsi,
                iccserial:_cardInfoJson.serialNumber,
                iccid:_rscJson.iccid,
                resultCode:result,
                resultMessage:"写卡器返回结果编码"+resultCode,
                phoneNumber:OrderInfo.getAccessNumber(_rscJson.prodId),
                cardType:_cardInfoJson.cardTypeId,
                eventType:"2",
                areaId:OrderInfo.getProdAreaId(_rscJson.prodId),
                serviceCode:OrderInfo.busitypeflag,//新增一个动作表示，用于记日志update by huangjj
                TransactionID:_TransactionID,
                remark:_cardDllInfoJson.remark,
                "srInParam":srInParam
            };
            if(OrderInfo.actionFlag != null && (OrderInfo.actionFlag == 41 || OrderInfo.actionFlag == 42)){
                param.essWriteCard = 'Y';//标志ESS页面的操作
                param.extCustOrderId = OrderInfo.essOrderInfo.essOrder.extCustOrderId;
            }
            try {

                var eventJson;
                var response = $.callServiceAsJson(serviceName, param);
                eventJson = response.data;
                //alert(JSON.stringify(response));

                if (!response) {
                    $.alert("提示","<br/>写卡成功后且未响应数据卡资源回填到卡管系统异常,请稍后重试。");
                    var paramLog = {
                        mkt_res_inst_code : $("#resultCardNo").val(),
                        iccid : _rscJson.iccid,
                        card_source : _cardDllInfoJson.dllName,
                        err_desc : "写卡成功后未响应数据卡资源回填到卡管系统异常,请稍后重试。",
                        acc_nbr : OrderInfo.getAccessNumber(_rscJson.prodId),
                        contact_record : _TransactionID
                    };
                    _writeCardLogInfo(paramLog);
                    return;
                }
                if(response.code != 0) {
                    $.alert("提示","<br/>写卡成功后调用卡资源回填到卡管系统异常,请稍后重试。");
                    if(response.data !="" && response.data !=undefined){
                        alertMM(response.data);
                    }
                    var paramLog = {
                        mkt_res_inst_code : $("#resultCardNo").val(),
                        iccid : _rscJson.iccid,
                        card_source : _cardDllInfoJson.dllName,
                        err_desc : "写卡成功后卡资源回填到卡管系统异常,请稍后重试。错误原因："+response.data.errMsg,
                        acc_nbr : OrderInfo.getAccessNumber(_rscJson.prodId),
                        contact_record : _TransactionID
                    };
                    _writeCardLogInfo(paramLog);
                    return;
                }
                //var eventJson = response.data;
                if (eventJson.code != 0) {
                    $.alert("提示","写卡成功后调用卡资源回填到卡管系统异常！错误原因：" + eventJson.message,"error");
                    return;
                }
//				var flag = eventJson.code;
//				var msg = eventJson.message;
//				if (flag != "0") {
//					$.alertM(response.data);
//					//$.alert("提示","调用卡资源回填到卡管系统异常！" + msg,"error");
//					return false;
//				}

                if(response.code == 0) {
                    //写卡成功后把卡数据入库便于异常单释放
                    var phoneNumber = OrderInfo.getAccessNumber(_rscJson.prodId);
                    _rscJson.phoneNumber = phoneNumber;
                    var inParam = {
                        "instCode" : $("#resultCardAsciiFStr").val(),
                        "phoneNum" : phoneNumber,
                        "remark": "3",//
                        "areaId"   : OrderInfo.getProdAreaId(_rscJson.prodId)
                    };
                    var serviceUrl = contextPath + "/mktRes/writeCard/intakeSerialNumber";
                    $.callServiceAsJson(serviceUrl, inParam);
                }
                _backFillOrderCardInfo(eventJson.result);
                return true;
            } catch(e) {
                $.alert("提示","写卡入库成功后订单数据填充异常！错误原因" + e.message,"error");
                var paramLog = {
                    mkt_res_inst_code : $("#resultCardNo").val(),
                    iccid :_rscJson.iccid,
                    card_source : _cardDllInfoJson.dllName,
                    err_desc : "写卡入库成功后订单数据填充异常！错误原因" + e.message,
                    acc_nbr : OrderInfo.getAccessNumber(_rscJson.prodId),
                    contact_record : _TransactionID
                };
                _writeCardLogInfo(paramLog);
                return false;
            }
        },
        _backFillOrderCardInfo : function(result){
            //要求截取前19位编码
            try {
                if (_rscJson.iccid.length==20){
                    _rscJson.iccid = _rscJson.iccid.substr(0,_rscJson.iccid.length-1);
                }
                $("#uim_txt_"+_rscJson.prodId).val($("#resultCardAsciiFStr").val());
                var resp = $.callServiceAsJson(contextPath+"/mktRes/terminal/infoQueryByCode", {instCode:_cardInfoJson.serialNumber,"areaId": OrderInfo.getProdAreaId(_rscJson.prodId)});//mark 根据串码获取卡类型，异地补换卡，获取产品的地区
                if(resp.code ==0&&ec.util.isObj(resp.data.mktResBaseInfo)&&ec.util.isObj(resp.data.mktResBaseInfo.mktResId)){
                    result.mktResId = resp.data.mktResBaseInfo.mktResId;
                }else{
                    if(ec.util.isObj(resp.data.resultMsg)){
                        $.alert("信息提示","根据串码："+_cardInfoJson.serialNumber+"获取卡类型失败！失败原因："+resp.data.resultMsg);
                        return ;
                    }else{
                        $.alert("信息提示","根据串码："+_cardInfoJson.serialNumber+"获取卡类型失败！失败原因未返回！");
                        return ;
                    }
                }
            } catch(e) {
                $.alert("提示","根据串码："+_cardInfoJson.serialNumber+"获取卡类型失败！错误原因" + e.message,"error");
                var paramLog = {
                    mkt_res_inst_code : $("#resultCardNo").val(),
                    iccid :_rscJson.iccid,
                    card_source : _cardDllInfoJson.dllName,
                    err_desc : "根据串码："+_cardInfoJson.serialNumber+"获取卡类型失败！错误原因" + e.message,
                    acc_nbr : OrderInfo.getAccessNumber(_rscJson.prodId),
                    contact_record : _TransactionID
                };
                _writeCardLogInfo(paramLog);
                return false;
            }
            if (OrderInfo.actionFlag == 41) {// ESS远程写卡
                _essWriteCard = {
                    extCustOrderId : OrderInfo.essOrderInfo.essOrder.extCustOrderId,
                    commonRegionId : OrderInfo.essOrderInfo.essOrder.commonRegionId,
                    mktResInst :{
                        mktResCd : resp.data.mktResBaseInfo.mktResCd+"",
                        mktResType : resp.data.mktResBaseInfo.mktResTypeCd,
                        mktResInstCode : resp.data.mktResBaseInfo.instCode,
                        mktResStoreId : resp.data.mktResBaseInfo.mktResStoreId+"",
                        orderItemGroupId : OrderInfo.essOrderInfo.essOrder.orderItemGroupId,
                        quantity : "1",
                        salesPrice : "0",
                        attr :[{
                            attrId: "21011203",
                            attrVal: _rscJson.imsi
                        },{
                            attrId: "21011202",
                            attrVal: _rscJson.iccid
                        },{
                            attrId: "20002010",
                            attrVal: _rscJson.imsig
                        },{
                            attrId: "60020004",
                            attrVal: _rscJson.imsilte
                        }]
                    }
                };
            }
            var coupon= {
                couponUsageTypeCd : "3", // 物品使用类型
                inOutTypeId : "1",  //出入库类型
                inOutReasonId : 0, //出入库原因
                saleId : 1, //销售类型
                couponId :result.mktResId, //物品ID
                couponinfoStatusCd : "A", //物品处理状态
                chargeItemCd : CONST.ACCT_ITEM_TYPE.UIM_CHARGE_ITEM_CD, //物品费用项类型
                couponNum : "1", //物品数量
                storeId : result.mktStoreId, //仓库ID
                storeName : "11", //仓库名称
                agentId : 1, //供应商ID
                apCharge : 0, //物品价格
                couponInstanceNumber : _cardInfoJson.serialNumber,  //物品实例编码 _rscJson.iccid
                ruleId : "", //物品规则ID
                partyId : OrderInfo.cust.custId, //客户ID
                prodId :_rscJson.prodId, //产品ID
                offerId : "", //销售品实例ID
                state : "ADD", //动作
                cardTypeFlag:2,
                uimType:"2",//标识用于订单成功更新订单状态
                relaSeq : "" //关联序列
            };
            try{
                if(ec.util.isObj(_cardInfoJson.cardTypeId)) {
                    var uimCardType = prod.uim.getCardType("", "", _cardInfoJson.serialNumber);
                    if (CONST.UIMTYPE3G4G.IS4G == uimCardType) {//4G卡
                        coupon.cardTypeFlag = 1;
                    }
                    //补换卡和异地补换卡增加4g卡不能补3g卡的限制
                    if(prod.changeUim.is4GProdInst&&(OrderInfo.actionFlag==22 || OrderInfo.actionFlag==23)){//判断是否有开通4G功能产品
                        if(CONST.UIMTYPE3G4G.IS3G==uimCardType){
                            $.alert("信息提示","您已开通了【4G（LTE）上网】功能产品，而UIM卡是3G卡，请使用4G的白卡！");
                            return false;
                        }
                    }
                }
                if(OrderInfo.actionFlag==1 || OrderInfo.actionFlag==6 || OrderInfo.actionFlag==14){
                    coupon.offerId = "-1";
                }else{
                    coupon.offerId = order.prodModify.choosedProdInfo.prodOfferInstId; //销售品实例ID
                }
                OrderInfo.clearProdUim(_rscJson.prodId);
                OrderInfo.boProd2Tds.push(coupon);
                if((OrderInfo.actionFlag==22 || OrderInfo.actionFlag==23) && coupon.cardTypeFlag==1 && order.prodModify.choosedProdInfo.productId != '280000000'){
                    AttachOffer.openServList = [];
                    AttachOffer.openList = [];
                    AttachOffer.queryCardAttachOffer(coupon.cardTypeFlag);  //加载附属销售品
                }
            } catch(e) {
                $.alert("提示","您已开通了【4G（LTE）上网】功能产品，而UIM卡是3G卡，请使用4G的白卡！错误原因：" + e.message,"error");
                var paramLog = {
                    mkt_res_inst_code : $("#resultCardNo").val(),
                    iccid :_rscJson.iccid,
                    card_source : _cardDllInfoJson.dllName,
                    err_desc : "您已开通了【4G（LTE）上网】功能产品，而UIM卡是3G卡，请使用4G的白卡！错误原因：" + e.message,
                    acc_nbr : OrderInfo.getAccessNumber(_rscJson.prodId),
                    contact_record : _TransactionID
                };
                _writeCardLogInfo(paramLog);
                return false;
            }
            //3转4弹出促销窗口//查询卡类型
            var oldCardis4GCard = "";
            if(ec.util.isObj(_rscJson.prodId)&& _rscJson.prodId>=0){//不为新装
                var param ={
                    prodInstId	: _rscJson.prodId,
                    areaId		: order.prodModify.choosedProdInfo.areaId,
                    acctNbr		: _rscJson.phoneNumber
                };
                var terminalInfo = query.prod.getTerminalInfo2(param);
                if(terminalInfo!=null&&terminalInfo.is4GCard!=null&&terminalInfo.is4GCard!=""){
                    if(terminalInfo.is4GCard =="Y"){
                        oldCardis4GCard = "Y";
                    }else{
                        oldCardis4GCard = "N";
                    }
                }else{
                    if(order.prodModify.choosedProdInfo.prodClass== "3"){
                        oldCardis4GCard = "N";
                    }else{
                        oldCardis4GCard = "Y";
                    }
                }
            }
            if(oldCardis4GCard == "N" && coupon.cardTypeFlag==1){
                var prodId = _rscJson.prodId;
                $("#isShow_"+prodId).show();
                var prodSpecId = OrderInfo.getProdSpecId(prodId);
                var param = {
                    prodSpecId : prodSpecId,
                    offerSpecIds : [],
                    queryType : "3",
                    prodId : productId,
                    partyId : OrderInfo.cust.custId,
                    ifCommonUse : "",
                    if3Up4:"Y"
                };
                var prodInfo = order.prodModify.choosedProdInfo; //获取产品信息
                var accNbr = prodInfo.accNbr;
                param.acctNbr = accNbr;
                if(!ec.util.isObj(prodInfo.prodOfferId)){
                    prodInfo.prodOfferId = "";
                }
                var offerRoleId = CacheData.getOfferMember(prodInfo.prodInstId).offerRoleId;
                if(offerRoleId==undefined){
                    offerRoleId = "";
                }
                try{
                    param.offerRoleId = offerRoleId;
                    param.offerSpecIds.push(prodInfo.prodOfferId);
                    var data = query.offer.queryCanBuyAttachSpec(param);
                    if(data != undefined && data.resultCode == "0" && data.result.offerSpecList.length > 0){
                        var attachOfferList = CacheData.getOfferList(_rscJson.prodId);//已订购附属销售品
                        var content = '<form id="promotionForm"><table>';
                        var selectStr = "";
                        var optionStr = "";
                        selectStr = selectStr + "<tr><td>可订购促销包: </td><td><select class='inputWidth183px' id=" + accNbr + "><br>";

                        //循环遍历可订购附属销售品
                        $.each(data.result.offerSpecList,function(){
                            var offerSpec = this;
                            var offerSpecId = this.offerSpecId;
                            var offerSpecName = this.offerSpecName;
                            var ifOrderAgain = this.ifOrderAgain;//是否可以重复订购
                            var ifDueOrderAgain = this.ifDueOrderAgain;//当月到期是否可以重复订购

                            if(attachOfferList != undefined && attachOfferList.length > 0){
                                //循环遍历已订购附属销售品
                                $.each(attachOfferList,function(){
                                    //如果可订购附属在已订购列表中
                                    if(this.offerSpecId == offerSpecId && this.isDel != "C"){
                                        var expireDate = this.expDate;//已订购的附属销售品的失效时间
                                        expireDate = expireDate.substring(4,6);//截取失效时间(20150201000000)的月份(02)
                                        var currentMonth = new Date().getMonth() + 1;//获取当前月份(0-11,从0开始，如0为1月份，1为2月份)
                                        if(currentMonth < 10){
                                            currentMonth = "0" + currentMonth;//如果月份为个位数，则补充"0"于首位
                                        }
                                        if(expireDate == currentMonth){
                                            //如果已订购是当月到期
                                            if(ifOrderAgain == "Y" || ifDueOrderAgain == "Y"){
                                                //如果该附属可重复订购或者到期当月可重复订购，则展示；否则屏蔽不展示
                                                optionStr += '<option value="' + offerSpecId + '">' + offerSpecName + '</option>';
                                            }
                                        }
                                    } else{
                                        //如果该可订购附属没有在已订购列表中，则不过滤
                                        optionStr += '<option value="' + offerSpecId + '">' + offerSpecName + '</option>';
                                    }
                                });
                            } else{
                                optionStr += '<option value="' + offerSpecId + '">' + offerSpecName + '</option>';
                            }
                        });
                        selectStr += optionStr + "</select></td></tr>";
                        content +=selectStr;
                        var offerSpecId;
                        $.confirm("促销包选择",content,{
                            yes:function(){

                            },
                            no:function(){

                            }
                        });
                        $('#promotionForm').bind('formIsValid', function(event, form) {
                            offerSpecId = $('#'+accNbr+' option:selected').val();
                            $(".ZebraDialog").remove();
                            $(".ZebraDialogOverlay").remove();
                            AttachOffer.selectAttachOffer(productId,offerSpecId);
                        }).ketchup({bindElementByClass:"ZebraDialog_Button1"});

                    }
                } catch(e) {
                    $.alert("提示","促销可选包弹出框弹出！错误原因：" + e.message,"error");
                    var paramLog = {
                        mkt_res_inst_code : $("#resultCardNo").val(),
                        iccid :_rscJson.iccid,
                        card_source : _cardDllInfoJson.dllName,
                        err_desc : "促销可选包弹出框弹出！错误原因：" + e.message,
                        acc_nbr : OrderInfo.getAccessNumber(_rscJson.prodId),
                        contact_record : _TransactionID
                    };
                    _writeCardLogInfo(paramLog);
                    return false;
                }
            }
        },
        _wrtRsltReport:function(resultCode, resultMsg){
        	 /*TODO:实际本地化写卡结果上报为主*/
            var widget = this,el = $(this.el),data = el.data();
            var response;
            var params = {
                "mdn": data.replPhoneNum,
                "imsi": _rscJson.imsi,
                "iccSerial": _cardInfoJson.serialNumber,
                "iccId": _rscJson.iccid,
                "resultCode": resultCode,
                "resultMessage": resultMsg
            };
            widget.callService("reportEmptyCardWrtRslt",params,function(res){
                response = res;
            },{
                async: false,
                mask:true,
                headers : {
                    "regionId" : widget.gSession.installArea
                }
            });
            return response;
        },
        _updateCardDll : function(){
            var widget = this;
            //先清空组件信息JSON
            _cardDllInfoJson = {
                "authCodeType":"",
                "dllId":"",
                "dllName":"",
                "dllVersion":"",
                "password":"",
                "factoryCode":""
            };//动态链接库JSON
            var serialNum = _cardInfoJson.serialNumber;
            //alert('serialNum'+serialNum);
            if (serialNum != undefined) {
                // 提取卡商代码

                var cardFatctoyCode = _cardInfoJson.factoryCode;


                // 根据卡商编码获取卡商最新版本插件DLL信息
                if (!this._getCardDllInfo(cardFatctoyCode)) {
                    widget.popup("不能获取该厂商的卡组件信息!");
                    return false;
                }
                // 加载卡商组件
                var isOk = this._updateOrLoadDll();
                if (!isOk) {
                    widget.popup("不能把厂商写卡组件下载到本机客户端!");
                    return false;
                }
                return true;
            } else {
                widget.popup("卡序列号值(serialNum=" + serialNum + ")不符合规范!");
                return false;
            }
        },
        _getCardDllInfo:function(factoryCode,scope){
            var widget = this;
            //先清掉g_cardDllInfoJson
            _cardDllInfoJson = null;
            try {
                if (factoryCode) {
                    var writeCardNewDLL = $("#selUimType").val();//写卡新组件开关
                    //alert("厂商编码2："+factoryCode);
                        // var serviceName = contextPath + "/mktRes/writeCard/cardDllInfo";
                    var param ;
                    if(writeCardNewDLL=='4'){
                        param = {
                            "factoryCode":factoryCode,
                            "cardType":"newCard"
                        };
                    }else{
                        param = {
                            "factoryCode":factoryCode,
                            "cardType":"oldCard"
                        };
                    }
                    var cardDllInfoJson;
                    // var response = $.callServiceAsJson(serviceName, param);
                    var response;
                    widget.callService("cardDllInfo",param,function(res){
                        response = res;
                    },{
                        async: false,
                        mask:true,
                        headers : {
                            "regionId" : widget.gSession.installArea
                        }
                    });

                    if(response.retCode == 0){
                        cardDllInfoJson = response.retObj;
                    }else{
                        // $.alertM(response.data);
                        widget.popup("获取动态链接库信息失败");
                        return false;
                    }
                    //alert("cardDllInfoJson:"+JSON.stringify(cardDllInfoJson));
                    var dllId = cardDllInfoJson.dllId;
                    if (!dllId) {
                        widget.popup("卡商(编码=" + factoryCode + ")获取不到对应的组件信息！");
                        return false;
                    }
                    _cardDllInfoJson = cardDllInfoJson;
                    return true;
                } else {
                    widget.popup("卡商编码值不可为空！");
                    return false;
                }
            } catch(e) {
                widget.popup("获取卡商组件信息时异常!");
                return false;
            }
        },
        _updateOrLoadDll : function(){
            //_cardDllInfoJson = {"dllId":"","dllName":"","dllVersion":"","dllPassword":"","factoryCode":""};//动态链接库JSON
            //先判断组件是否存在
            //alert("_updateOrLoadDll=="+_cardDllInfoJson.dllName);
            var fso = new ActiveXObject('Scripting.FileSystemObject');
            try {
                fso.GetFile("C:\\Windows\\SysWOW64\\" + _cardDllInfoJson.dllName + ".DLL");
               /* var serviceName = contextPath + "/mktRes/writeCard/writecardLog";
                var param = {
                    factoryCode:_cardDllInfoJson.factoryCode,
                    authCodeType:_cardDllInfoJson.authCodeType,
                    version:_cardDllInfoJson.dllName,
                    serviceCode:OrderInfo.busitypeflag,
                    isUpdate:'0',//表示不需要更新
                    cardSource:_cardDllInfoJson.remark
                };
                $.callServiceAsJson(serviceName, param);*/
            } catch(e) {
                $("#cardupdate").attr("href","https://ct.crm.189.cn/phoneimg/card/"+ _cardDllInfoJson.dllName+".DLL");
                $("#writeTitle").html("写卡组件更新");
                $("#rcard").hide();
                $("#dllName").html(_cardDllInfoJson.dllName+".DLL");
                $("#cardt").show();
               /* var serviceName = contextPath + "/mktRes/writeCard/writecardLog";
                var param = {
                    factoryCode:_cardDllInfoJson.factoryCode,
                    authCodeType:_cardDllInfoJson.authCodeType,
                    version:_cardDllInfoJson.dllName,
                    serviceCode:OrderInfo.busitypeflag,
                    isUpdate:'1',//表示需要更新
                    cardSource:_cardDllInfoJson.remark
                };
                $.callServiceAsJson(serviceName, param);*/
                //$.alert("提示","您插入白卡的卡商写卡组件不存在，请将组件下载保存到C:\\WINDOWS\\system32 目录下！","error");
                //$.alert("提示","您当前使用的卡组件的版本已更新，请下载更新至最新的版本[" + _cardDllInfoJson.dllVersion + "]后重新写卡。");
                //var url = contextPath + "/card/"+ _cardDllInfoJson.dllName+".DLL";
                //location.href = url;
              /*  var paramLog = {
                    mkt_res_inst_code : $("#resultCardNo").val(),
                    iccid : $("#resultCardNo").val(),
                    card_source : _cardDllInfoJson.dllName,
                    err_desc : '写卡组件未更新',
                    acc_nbr : '',
                    contact_record : ''
                };
                _writeCardLogInfo(paramLog);*/
                this.popup("获取本地dll组件文件异常");
                return false;
            }
            //alert("fso=="+fso);
            //装载组件
            var loaddll = ocx.TransferDll(_cardDllInfoJson.dllName + ".DLL");
            if (loaddll != '0') {
                // $.alert("提示","您插入白卡的卡商写卡组件不存在，请将组件下载保存到以下目录：<br/>32位系统 C:\\Windows\\System32<br/>64位系统 C:\\Windows\\SysWOW64<br/>","error");
                this.popup("您插入白卡的卡商写卡组件不存在，请将组件下载保存到以下目录：<br/>32位系统 C:\\Windows\\System32<br/>64位系统 C:\\Windows\\SysWOW64<br/>");
                var url = "https://ct.crm.189.cn/phoneimg/card/"+ _cardDllInfoJson.dllName + ".DLL";
                location.href = url;
                return false;
            }
            //alert("loaddll=="+loaddll);
            // 读取组件版本号
            var version = ocx.GetDllVersion();
            //alert("version: " + version);
            // 组件版本不是最新, 执行更新
            if (version != _cardDllInfoJson.dllVersion) {
                var writeCardNewDLL = $("#selUimType").val();//写卡新组件开关
                if(writeCardNewDLL == "4"){
                    // $.alert("提示","您当前您使用的是测试卡组件版本为[" + version + "]，目前系统对应的卡组件版本为[" + _cardDllInfoJson.dllVersion + "]请使用与系统卡组件相对应的卡进行写卡。");
                    this.popup("您当前使用的白卡组件版本为[" + version + "]，目前系统对应的卡组件版本为[" + _cardDllInfoJson.dllVersion + "]请使用与系统卡组件相对应的卡进行写卡。");
                }else{
                    // $.alert("提示","您当前使用的白卡组件版本为[" + version + "]，目前系统对应的卡组件版本为[" + _cardDllInfoJson.dllVersion + "]请使用与系统卡组件相对应的卡进行写卡。");
                    this.popup("您当前使用的白卡组件版本为[" + version + "]，目前系统对应的卡组件版本为[" + _cardDllInfoJson.dllVersion + "]请使用与系统卡组件相对应的卡进行写卡。");
                }
//			alert("您插入白卡的卡商写卡组件不是最新版本，请将最新版本组件下载保存到C:\\WINDOWS\\system32 目录下!");
//			var url = contextPath + "/card/"+ _cardDllInfoJson.dllName;
//			location.href = url;
                return false;
            }
            return true;
        },
        _applyResourceData : function(prodId){
            //先清空之前的资源数据
            //写卡请求的参数： 手机号码，卡产品编号，归属地区号
            //漫游地区号，登陆工号，工号名称，营业厅标识，营业厅名称
            var widget = this,el = $(this.el),data = el.data();
            _rscJson = {
                "iccid":"",
                "imsi":"",
                "data":"",
                "dataLength":"0",
                "state":"N",
                "prodId":""
            };//卡数据资源JSON state:N 为不可用来写卡 Y 为可以

            //请求后返回给客户端的数据直接是可以写卡的暗文
            // var serviceName = contextPath + "/mktRes/writeCard/cardInfo";

            try {
                //alert(JSON.stringify(order.prodModify.choosedProdInfo));
                // 提取卡商代码
               /*
                //参考集团4g受理
                var areaCode = OrderInfo.getAreaCode(prodId);
                // if (areaCode == undefined || areaCode ==""){
                //     areaCode = OrderInfo.staff.areaCode;
                // }
                var param = {
                    factoryCode:_cardDllInfoJson.factoryCode,
                    authCodeType:_cardDllInfoJson.authCodeType,
                    hmUimid:'',//黑莓
                    cardNo:_cardInfoJson.cardTypeId,
                    phoneNumber:OrderInfo.getAccessNumber(prodId),
                    areaId:OrderInfo.getProdAreaId(prodId),
                    areaCode:areaCode,//归属地区号
                    fromAreaCode:OrderInfo.staff.areaCode//漫游地区号
                };
                if(OrderInfo.actionFlag != null && (OrderInfo.actionFlag == 41 || OrderInfo.actionFlag == 42)){
                    param.essWriteCard = 'Y';//标志ESS页面的操作
                    param.iccserial = _cardInfoJson.serialNumber;
                    param.iccid = $("#resultCardNo").val();
                    param.serviceCode = OrderInfo.busitypeflag;//新增一个动作表示，用于记日志update by huangjj
                    param.remark = _cardDllInfoJson.remark;
                    param.extCustOrderId = OrderInfo.essOrderInfo.essOrder.extCustOrderId;
                }
                var resourceDataJson;
                var response = $.callServiceAsJson(serviceName, param);*/
                var resourceDataJson;
                var param = {
                    factoryCode:_cardDllInfoJson.factoryCode,
                    authCodeType:_cardDllInfoJson.authCodeType,
                    hmUimid:'',//黑莓
                    cardNo:_cardInfoJson.cardTypeId,
                    phoneNumber:data.replPhoneNum,
                    areaId:data.replPhoneNumLanId,
                    fromAreaCode:data.replPhoneNumLanId,//漫游地区号
                    toAreaCode:widget.gSession.staffLanId,
                    staffId:widget.gSession.staffId,
                    staffName:widget.gSession.staffName,
                    channelId:widget.gSession.curChannelId,
                    channelName:widget.gSession.curChannelName,
                    toRegionId:widget.gSession.staffRegionId
                };
                debugger;
                var response;
                widget.callService("gainBlankCardRsrc",JSON.stringify(param),function(res){
                    response = res;
                },{
                    async: false,
                    mask:true,
                    headers : {
                        "regionId" : widget.gSession.installArea
                    }
                });

                if(response.retCode == 0){
                    // resourceDataJson = response.data.cardInfo;
                    // _TransactionID = response.data.TransactionID;
                    resourceDataJson = response.retObj;
                }else{
                    /* var paramLog = {
                         mkt_res_inst_code : $("#resultCardNo").val(),
                         iccid : $("#resultCardNo").val(),
                         card_source : _cardDllInfoJson.dllName,
                         err_desc : response.data.errMsg,
                         acc_nbr : OrderInfo.getAccessNumber(prodId),
                         contact_record : ''
                     };
                     _writeCardLogInfo(paramLog);
                     $.alertM(response.data);*/
                   widget.popup(response.retMsg);
                    return false;
                }
                //alert(JSON.stringify(response));
                /*
                //参考集团4g受理
                var flag = resourceDataJson.flag;
                if (flag != undefined && flag == "0") {
                    _rscJson = {
                        "iccid":"",
                        "imsi":"",
                        "imsig":"",
                        "data":"",
                        "state":"Y",
                        "uimid":"",
                        "sid":"",
                        "accolc":"",
                        "nid":"",
                        "akey":"",
                        "pin1":"",
                        "pin2":"",
                        "puk1":"",
                        "puk2":"",
                        "imsilte":"",
//						"adm":"",
//						"hrpdupp":"",
//						"hrpdss":"",
//						"imsig":"",
//						"acc":"",
//						"smsp":"",
                        "prodId":""
                    };
                    _rscJson.iccid = resourceDataJson.iccid;
                    _rscJson.imsi = resourceDataJson.imsi;
                    _rscJson.imsig = resourceDataJson.imsig;
                    _rscJson.uimid = resourceDataJson.uimid;
                    _rscJson.sid = resourceDataJson.sid;
                    _rscJson.accolc = resourceDataJson.accolc;
                    _rscJson.nid = resourceDataJson.nid;
                    _rscJson.akey = resourceDataJson.akey;
                    _rscJson.pin2 = resourceDataJson.pin2;
                    _rscJson.pin1 = resourceDataJson.pin1;
                    _rscJson.puk1 = resourceDataJson.puk1;
                    _rscJson.puk2 = resourceDataJson.puk2;
                    _rscJson.imsilte = resourceDataJson.imsilte;
                    _rscJson.data = resourceDataJson.data;
                    _rscJson.dataLength = resourceDataJson.dataLength;
                    _rscJson.prodId = prodId;
                    return true;
                } else {
                    var msg = resourceDataJson.msg;
                    var paramLog = {
                        mkt_res_inst_code : $("#resultCardNo").val(),
                        iccid : $("#resultCardNo").val(),
                        card_source : _cardDllInfoJson.dllName,
                        err_desc : "请求可写卡的资源数据失败:" + msg,
                        acc_nbr : OrderInfo.getAccessNumber(prodId),
                        contact_record : _TransactionID
                    };
                    _writeCardLogInfo(paramLog);
                    if (msg != undefined) {
                        $.alert("提示","请求可写卡的资源数据失败:" + msg,"error");
                    } else {
                        $.alert("提示","请求可写卡的资源数据失败" ,"error");
                    }
                    return false;
                }*/
                _rscJson = {
                    "iccid":"",
                    "imsi":"",
                    "imsig":"",
                    "data":"",
                    "state":"Y",
                    "uimid":"",
                    "sid":"",
                    "accolc":"",
                    "nid":"",
                    "akey":"",
                    "pin1":"",
                    "pin2":"",
                    "puk1":"",
                    "puk2":"",
                    "imsilte":"",
                    "prodId":""
                };
                _rscJson.iccid = resourceDataJson.iccId;
                _rscJson.imsi = resourceDataJson.imsi;
                _rscJson.imsig = resourceDataJson.imsiG;
                _rscJson.uimid = resourceDataJson.uimId;
                _rscJson.sid = resourceDataJson.sId;
                _rscJson.accolc = resourceDataJson.accolc;
                _rscJson.nid = resourceDataJson.nId;
                _rscJson.akey = resourceDataJson.aKey;
                _rscJson.pin2 = resourceDataJson.pin2;
                _rscJson.pin1 = resourceDataJson.pin1;
                _rscJson.puk1 = resourceDataJson.puk1;
                _rscJson.puk2 = resourceDataJson.puk2;
                _rscJson.imsilte = resourceDataJson.imsiLte;
                _rscJson.data = resourceDataJson.data;
                _rscJson.dataLength = resourceDataJson.dataLength;
                _rscJson.prodId = prodId;
                return true;
            } catch(e) {
                // $.alert("提示","请求可写卡的资源数据异常!" + e.message ,"error");
                widget.popup("请求可写卡的资源数据异常!");
                return false;
            }
        },
        _close : function () {
            $(this.el).closest("[data-dialog=true]").dialog("close");
        },
        getValue : function() {
			return _cardInfoJson;
		}
    });
    vita.widget.register("writeCard", writeCard, true);
})(window.vita);