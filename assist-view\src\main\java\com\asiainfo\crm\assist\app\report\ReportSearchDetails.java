package com.asiainfo.crm.assist.app.report;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IReportSMO;
/**
 * 日报弹出购物差流水
 * <AUTHOR>
 *20190520
 */
@Component("vita.reportSearchDetails")
public class ReportSearchDetails extends AbstractComponent{
	
	private static final Logger logger = LoggerFactory.getLogger(ReportSearch.class);
	@Autowired
    private IReportSMO reportSMO;
	@Override
	public Map achieveData(Object... params) throws Exception {
		 Map options = new HashedMap();
		 List list =  new ArrayList<>();
		 String jsonString = params[0].toString();
		 Map<String, Object> param = jsonConverter.toBean(jsonString, Map.class);
		 Map rul = reportSMO.queryStaffDealOrderNbrList(jsonString);

		return rul;
	}

}
