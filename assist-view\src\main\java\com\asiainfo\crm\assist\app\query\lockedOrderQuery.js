(function (vita) {
    var lockedOrderQuery = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_customerOrderListQuery",
            "click #btn-clear": "_clearCond",
            "click #regionIdBtn": "_chooseArea",
            "click #channelIdBtn": "_chooseChannelClick",
            "click [link=customerOrder]": "_queryCustomerOrderDetailLink",
            "click [link=orderItem]": "_queryOrderItemDetailLink",
            "click #orderDetail": "_queryCustomerOrderDetail",
            "click #c_custOrderNbr": "_custOrderNbrClick",
            "click #cancelOrder": "_cancelOrder"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
            var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd"
                });
            }
            ;
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd"
                });
            }
            beginDate.val(widget._getFormatDate(-30));
            endDate.val(widget._getFormatDate(0));
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
            var channelName = "";
            for(var channels=gSession.channels,i=0; channels.length && i<channels.length; i++) {
                if(gSession.curChannelId == channels[i].orgId) {
                    channelName = channels[i].channelName;
                    break;
                }
            }
            // element.find("#channelId").val(channelName).attr("value", gSession.curChannelId);
            if(gSession.curChannelId && gSession.curChannelId != "") {
                element.find("#channelId").val(channelName).attr("value", gSession.curChannelId);
            } else {
                widget._chooseChannel(true);
            }
        },
        _getFormatDate: function(days) {
            var date = new Date();
            var yesterday_milliseconds = date.getTime()+days*1000*60*60*24;
            var yesterday = new Date();
            yesterday.setTime(yesterday_milliseconds);
            var strYear = yesterday.getFullYear();
            var strDay = yesterday.getDate();
            var strMonth = yesterday.getMonth()+1;
            if(strMonth<10)
            {
                strMonth="0"+strMonth;
            }
            if(strDay<10)
            {
                strDay="0"+strDay;
            }

            /*var hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
            var minute = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
            var second = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
            var time = hour+":"+minute+":"+second;*/
            var datastr = strYear+"-"+strMonth+"-"+strDay;
            return datastr;
        },
        global: {
            pageIndex: 1,
            pageSize: 3,
            preset: "date",
            chooseArea: "../comm/chooseArea",
            chooseChannel: "../comm/chooseChannel"
        },
        _clearCond: function () {
            var $checked = $(".form-group").find("input[type=checkbox]:checked");
            $.each($checked, function (i,chkInput) {
                chkInput.click();
                var $input = $(chkInput).closest(".form-group").find("input.form-control");
                $input.val("");
                $input.attr("value", null);
            })
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _chooseChannelClick: function() {
            var widget = this,element = $(widget.el);
            widget._chooseChannel(false);
        },
        _chooseChannel: function(isNeedRefreshSession) {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseChannel,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);

                    if(isNeedRefreshSession) {
                        gSession.channelId   = data.orgId;
                        gSession.curChannelId= data.orgId;
                        gSession.curChannelNbr  = data.channelNbr;
                        gSession.channelName = data.channelName;
                        gSession.channelRegionId = data.channelRegionId;
                        gSession.channelRegionName = data.channelRegionName;
                        gSession.channelClass = data.channelClass;
                        gSession.orgId       = data.orgId;
                        gSession.orgName     = data.orgName;

                        gSession.orderArea   	 = data.channelRegionId; // 订单地区
                        gSession.installArea 	 = data.channelRegionId; // 安装地区
                        gSession.installAreaName = data.channelRegionName;
                        var param = {
                            curChannelId  : data.orgId,
                            curChannelNbr : data.channelNbr,
                            orderArea     : gSession.orderArea,
                            installArea   : gSession.installArea
                        };
                        widget.refreshSession(param);
                    }
                }
            });
        },
        _custOrderNbrClick: function() {
            var widget = this,element = $(widget.el);
            //界面样式渲染后才会調click事件，所以当状态是checked的时候才去清空其他选项
            var nbrChk = element.find(".form-group").find("input[type=checkbox]:checked#c_custOrderNbr");
            if(nbrChk.length != 0){
                var $checked = $(".form-group").find("input[type=checkbox]:checked").not('#c_custOrderNbr');
                $.each($checked, function (i,chkInput) {
                    $(chkInput).attr('checked', false);
                    // var $input = $(chkInput).closest(".form-group").find("input.form-control");
                    // $input.val("");
                    // $input.attr("value", null);
                });
                var beginDate = element.find("#beginDate");
                var endDate =  element.find("#endDate");
                beginDate.val("");
                endDate.val("");
                beginDate.prop("disabled", true);
                endDate.prop("disabled", true);
            } else {
                var beginDate = element.find("#beginDate");
                var endDate = element.find("#endDate");
                beginDate.val(widget._getFormatDate(-1));
                endDate.val(widget._getFormatDate(0));
                beginDate.prop("disabled", false);
                endDate.prop("disabled", false);
            }
        },
        _customerOrderListQuery: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("queryCustomOrderList", JSON.stringify(params), "#orderListResult", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if (widget._getConds()) {
                                    widget.refreshPart("queryCustomOrderList", JSON.stringify(params), "#orderList");
                                }
                                ;
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }

                }, {
                    async: false
                });
            }
            ;
        },
        _queryCustomerOrderDetail: function () {
            var widget = this,element = $(widget.el);
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var $order = orders[0];
            var $tr = $($order.closest("tr"));
            var custOrderId = $($tr.find("[link=customerOrder]")).data("custOrderId");
            window.open("../query/olDetailInfo?custOrderId="+custOrderId);
        },
        _queryCustomerOrderDetailLink: function(e){
            var widget = this;
            var aEl = $(e.target).closest("a");
            var custOrderId = aEl.data("custOrderId");
            if(!custOrderId){
                widget.popup("订单不存在,请重新查询后再试.");
                return;
            }
            var data = {
                custOrderId:custOrderId
            };
            // widget.end("olDetailInfo",data); 这里没有配流程不能用框架的end
            window.open("../query/olDetailInfo?custOrderId="+custOrderId);
        },
        _queryOrderItemDetailLink: function (e) {
            var widget = this;
            var aEl = $(e.target).closest("a");
            var custOrderId = aEl.parent().data("custOrderId");
            var orderItem = aEl.parent().data("orderItem");
            if (!custOrderId) {
                widget.popup("订单不存在,请重新查询后再试.");
                return;
            }
            if (!orderItem.orderItemId) {
                widget.popup("订单不存在,请重新查询后再试.");
                return;
            }
            var uri = "";
            if (orderItem && orderItem.orderItemCd && orderItem.orderItemCd != "") {
                switch (parseInt(orderItem.orderItemCd)) {
                    case 1200:
                        uri = "../query/boOfferDetailInfo"
                        break;
                    case 1300:
                        uri = "../query/boProdDetailInfo"
                        break;
                }
            }
            if (uri == "") {
                widget.popup("订单项类型不存在,请重新查询后再试.");
                return;
            }

            var param = "?custOrderId="+custOrderId+"&orderItemId="+orderItem.orderItemId+
                    "&orderItemCd="+orderItem.orderItemCd+"&isIndependent="+orderItem.isIndependent+
                    "&offerType="+orderItem.offerType+"&applyObjSpecName="+orderItem.applyObjSpecName;
            window.open(uri+param);
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this,element = $(widget.el);
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            var paramCnt = 0;
            var regionId = $("#regionId").attr("value");
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.regionId = regionId;
            }

            if ($("#c_channelId").is(":checked")) {
                paramCnt++;
                var channelId = $("#channelId").attr("value");
                if(widget._isNullStr(channelId)) {
                    widget.popup("请选择渠道！");
                    return false;
                } else {
                    params.channelId = channelId;
                }
            }

            if ($("#c_custOrderNbr").is(":checked")) {
                paramCnt++;
                var custOrderNbr = $("#custOrderNbr").val();
                if(widget._isNullStr(custOrderNbr)) {
                    widget.popup("请输入流水号！");
                    return false;
                } else {
                    params.custOrderNbr = custOrderNbr;
                }
            }

            if ($("#c_accNum").is(":checked")) {
                paramCnt++;
                var accNum = $("#accNum").val();
                if(widget._isNullStr(accNum)) {
                    widget.popup("请输入号码！");
                    return false;
                } else {
                    params.accNum = accNum;
                }
            }

            var nbrChk = element.find(".form-group").find("input[type=checkbox]:checked#c_custOrderNbr");
            if(nbrChk.length == 0) {
                var beginDate = element.find("#beginDate").val();
                if (widget._isNullStr(beginDate)) {
                    widget.popup("请选择起始日期！");
                    return false;
                } else {
                    beginDate = beginDate + " 00:00:00";
                    params.acceptDateScope = {};
                    params.acceptDateScope.beginDate = beginDate;
                }

                var endDate = element.find("#endDate").val();
                if (widget._isNullStr(endDate)) {
                    widget.popup("请选择结束日期！");
                    return false;
                } else {
                    endDate = endDate + " 23:59:59";
                    params.acceptDateScope.endDate = endDate;
                }
            }

            params.statusCds = [201800];
            return params;
        },
        _cancelOrder: function () {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条订单!");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            var custOrderId = $($tr.find("[link=customerOrder]")).data("custOrderId");
            var params = {
                custOrderId:custOrderId,
                staffId: gSession.staffId,
                regionId: gSession.staffRegionId,
                channelId: gSession.curChannelId
            }
            widget.callService("cancelLockedOrder",JSON.stringify(params),function (ret) {
                widget.popup(ret.handleResultMsg);
                widget._customerOrderListQuery();
            });
        }
    });
    vita.widget.register("lockedOrderQuery", lockedOrderQuery, true);
})(window.vita);