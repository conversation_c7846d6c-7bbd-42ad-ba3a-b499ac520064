package com.asiainfo.crm.assist.app.detail;

import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.service.intf.ICustSMO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


/**
 * 客户详情
 *
 * <AUTHOR>
 */
@Component("vita.custDetailInfo")
public class CustDetailInfo extends AbstractSoComponent {

    @Autowired
    private ICustSMO custSMO;

    @Override
    public Map achieveData(Object... param) throws Exception {
        String p = (String) param[0];
        Map options = new HashMap();
        Map orders = this.qryAcctDetailInfo(p);
        Map inParam = jsonConverter.toBean(p,Map.class);
        options.putAll(inParam);
        options.putAll(orders);
        return options;
    }

    public Map qryAcctDetailInfo(String inStr) throws Exception {
        Map options = new HashMap();
        String retStr = custSMO.qryMultiCustomerDetailByCustId(inStr);

        Map edOrderMap = (Map) resolveResult(retStr);
        options.put("detailInfo", edOrderMap);

        return options;
    }
}
