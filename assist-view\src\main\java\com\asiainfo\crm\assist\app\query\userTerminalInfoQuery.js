(function (vita) {
    var userTerminalInfoQuery = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_userTerminalInfoQuery"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
        },
        global: {
        },
        _userTerminalInfoQuery: function () {
            var widget = this;
            var gsession = widget.gSession;
            var params = {
                mktResInstNbr: $("#mktResNbr").val(),
                staffId: gsession.staffId,
                regionId: gsession.staffRegionId,
                routeParam : {
                    "regionId": gsession.staffRegionId
                }
            };
            debugger;
            widget.refreshPart("userTerminalInfoQuery", JSON.stringify(params), "#materialCodeResult", function (re) {
            }, { async: false, headers:{} });
        }
    });
    vita.widget.register("userTerminalInfoQuery", userTerminalInfoQuery, true);
})(window.vita);