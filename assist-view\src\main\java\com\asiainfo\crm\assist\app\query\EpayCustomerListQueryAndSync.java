package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IEpayOrderSmo;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created on 2017/7/7.
 */
@Component("vita.epayCustomerListQueryAndSync")
public class EpayCustomerListQueryAndSync extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(EpayCustomerListQueryAndSync.class);

    @Autowired
    private IEpayOrderSmo epayOrderSmo;

    @Override
    public Map achieveData(Object... params) throws Exception {

        return null;
    }

    /**
     * 查询客户内容
     */
    public Map qryCustomerInfo(String jsonStr) throws Exception {
        Map<String, Object> param = jsonConverter.toBean(jsonStr, Map.class);
        String accNum = MapUtils.getString(param, "accNum", "");
        Map<String, Object> options = Maps.newHashMap();
        if (StringUtils.isEmpty(accNum)) {
            return options;
        }

        String str = jsonConverter.toJson(param);

        String retStr = epayOrderSmo.qryEpayCustomerInfo(str);
        Map<String, Object> resMap = jsonConverter.toBean(retStr, Map.class);
        options.put("customerInfoResult", resMap.get("resultObject"));

        return options;
    }

    /**
     * 同步客户
     */
    public Map syncCustomerInfo(String jsonStr) throws Exception {
        Map<String, Object> param = jsonConverter.toBean(jsonStr, Map.class);
        String accNum = MapUtils.getString(param, "accNum", "");
        Map<String, Object> options = Maps.newHashMap();
        if (StringUtils.isEmpty(accNum)) {
            return options;
        }

        String str = jsonConverter.toJson(param);

        String retStr = epayOrderSmo.syncEpayCustomerInfo(str);
        Map<String, Object> resMap = jsonConverter.toBean(retStr, Map.class);
        options.put("syncResult", resMap.get("resultObject"));

        return options;
    }


}
