package com.asiainfo.crm.assist.app.logistics;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.ISoQuerySMO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("vita.logisticsOptionalEditAttach")
public class LogisticsOptionalEditAttach extends AbstractComponent {
    @Autowired
    private IOrderQuerySMO orderQuerySMO;
    @Override
    public Map achieveData(Object... objects) throws Exception {
        Map params  = jsonConverter.toBean(objects[0].toString(),Map.class);
        //查询物流状态主数据
        Map logisticsStatusMap = MDA.LOGISTICS_STATUS_ATTR_ID;
        List<Map<String,Object>> logisticsStatusList = new ArrayList<>();
        if(logisticsStatusMap != null){
            Iterator iterator = logisticsStatusMap.entrySet().iterator();
            while(iterator.hasNext()){
                Map status = new HashMap();
                Map.Entry<String, Object> entry = (Map.Entry<String, Object>)iterator.next();
                status.put("name",entry.getKey());
                status.put("value",entry.getValue());
                logisticsStatusList.add(status);
            }
        }
        Map options = new HashMap();
        options.put("item",params);
        options.put("logisticsStatusList",logisticsStatusList);
        return options;
    }
    public Map saveLogisticsInfo(String inString) throws Exception{
        String res = orderQuerySMO.saveLogisticsInfo(inString);
        Map map = jsonConverter.toBean(res,Map.class);
        return map;
    }
}
