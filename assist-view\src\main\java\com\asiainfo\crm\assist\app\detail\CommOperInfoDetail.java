package com.asiainfo.crm.assist.app.detail;

import com.asiainfo.crm.common.AbstractSoComponent;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公共日志详情
 */
@Component("vita.commOperInfoDetail")
public class CommOperInfoDetail extends AbstractSoComponent {
    @Override
    public Map achieveData(Object... params) throws Exception {
        String jsonStr = (String) params[0];
        Map<String, Object> map = (Map<String, Object>) invokeMethod("commOperInfo", "queryCommOperInfoLogPage", new Object[]{jsonStr});
        List<Map<String, Object>> commOperInfos = (List<Map<String, Object>>) MapUtils.getObject(map, "commOperInfos");
        Map<String, Object> commOperInfoDetail = new HashMap<String, Object>();
        if (commOperInfos != null && commOperInfos.size() > 0) {
            commOperInfoDetail = commOperInfos.get(0);
        }
        Map<String, Object> wrappedResult = new HashMap<String, Object>();
        wrappedResult.put("commOperInfoDetail", commOperInfoDetail);
        return wrappedResult;
    }
}
