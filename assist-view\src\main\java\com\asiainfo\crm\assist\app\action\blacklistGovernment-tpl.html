<div data-widget="blacklistGovernment" style="height: 100%">

    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="wmin_content row">
                                <div class="form_title">
                                    <div><i class="bot"></i>政企黑名单管理</div>
                                </div>
                                <form class=" form-bordered">
                                    <div class="form-group col-md-5 ">
                                        <label class="col-md-4 control-label lablep">
                                            证件号码：</label>
                                        <div class="col-md-8">
                                            <input type="text" id="certNum" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-5 ">
                                        <label class="col-md-4 control-label lablep">
                                            证件类型：</label>
                                        <div class="col-md-7">
                                            <select id="certType" class="form-control">
                                                <option value="6">营业执照</option>
                                                <option value="34">事业单位法人证书</option>
                                                <option value="49">统一社会信用代码证书</option>
                                                <option value="43">社会团体法人登记证书</option>
                                                <option value="7">单位介绍信/公函+公章（仅用于党政军客户）</option>
                                                <option value="15">组织机构代码证</option>
                                                <option value="99">其它</option>
                                                    
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12" align="right">
                                            <button type="button" id="qryButton" class="btn btn-primary">查询</button>
                                            <button type="button" id="savaButton" class="btn btn-primary">加入黑名单</button>
                                            <button type="button" id="deleteButton" class="btn btn-primary">移出黑名单</button>
                                    </div>
                                </form>
                            </div>

                            <div class="container-fluid row" id="blacklistGovernmentResult">
                                <div class="wmin_content">
                                    <div class="col-lg-12 mart10">
                                        <table class="table table-hover" >
                                            <thead>
                                            <tr>
                                           		<th>选择</th>
                                                <th>客户ID</th>
                                                <th>客户名称</th>
                                                <th>客户归属地区</th>
                                                <th>特殊客户类型</th>
                                                <th>操作工号</th>
                                                <th>操作时间</th>
                                                <th>操作原因</th>
                                            </tr>
                                            </thead>
                                            <tbody id="blacklistGovernment">
                                            #if($options != "null" && $options.customerDetailsList && $options.customerDetailsList != "null"
                                            )
                                             #foreach($customerDetails in $options.customerDetailsList)
                                            	<tr>
                                            		<td>
                                                    <label class="wu-radio full absolute" data-scope="">
                                                        <input id="checkModify" type="checkbox" name="payment">
                                                        <p class="vita-data">{"data": $customerDetails}</p>
                                                    </label>
                                                    </td>
                                            		<td>$!{customerDetails.custId}</td>
                                            		<td>$!{customerDetails.custName}</td>
                                            		<td>$!{customerDetails.regionName}</td>
                                            		<td>
                                            		  #if($!{customerDetails.checkFlag} == 1)
                                            		  	是
                                            		  #else
                                            		        否
                                            		  #end
                                            		</td>
                                            		<td>$!{customerDetails.createStaffId}</td>
                                            		<td>$!{customerDetails.createDates}</td>
                                            		<td>$!{customerDetails.specialListReason}</td>
                                            	</tr>
                                             #end
                                            #elseif(($options != "null" && $options.customerDetailsList && $options.customerDetailsList != "null" &&
		                                        $options.customerDetailsList.size() == 0) || !$options ||$options.customerDetailsList =="null")
                                            <tr><td align='center' colspan='12'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                    </div>
                                     <div id="showPageInfo">
				                        </div>
				                        #if($options.totalNumber && $options.totalNumber != "null")
				                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
				                        #end
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->

                </div>
            </div>
        </div>
    </div>
</div>
