<div data-widget="dumpLogListQuery" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">

                                      <div class="form-group col-md-6">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custOrderNbr" type="checkbox" name="payment">
                                                </label> 流水号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custOrderNbr" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                         <div class="form-group col-md-6">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custOrderId" type="checkbox" name="payment">
                                                </label> 订单Id
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custOrderId" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        
                                        <div class="form-group col-md-12" id="qryTimeQuantum">
                                            <label class="col-md-2 control-label lablep">
                                              		  起止时间</label>
                                            <div class="col-md-10 form-inline">

                                                <div class="form-group">
                                                    <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="开始时间">
                                                </div>
                                                <div class="form-group">
                                                    <input name="endDate" id="endDate" type="text" class="form-control" placeholder="结束时间">
                                                </div>
                                                <button type="button" id="timeQuantum_1" name="timeQuantum_1" class="btn btn-gray btn-outline btn-sm">当天</button>
                                                <button type="button" id="timeQuantum_7" name="timeQuantum_7" class="btn btn-gray btn-outline btn-sm">七天</button>
                                                <button type="button" id="timeQuantum_30" name="timeQuantum_30" class="btn btn-gray btn-outline btn-sm">一个月</button>
                                                <button type="button" id="timeQuantum_90" name="timeQuantum_90" class="btn btn-gray btn-outline btn-sm">三个月</button>

                                            </div>
                                        </div>
                                        <div class="form-group col-md-11">
                                            <div class="col-md-12 searchbutt_r" align="right">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-clear" type="button" class="btn btn-white">清除</button>
                                            </div>
                                        </div>


                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询结果</div>
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12 mart10" id="dumpListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th width="20%">转储订单号</th>
                                                <th width="20%">状态</th>
                                                <th width="20%">创建时间</th>
                                                <th width="20%">转储失败信息</th>
                                                <th width="20%">备注</th>
                                            </tr>
                                            </thead>
                                            <tbody id="dumpList">
                                            #if($options != "null" && $options.dumpLogList && $options.dumpLogList != "null" &&
                                            $options.dumpLogList.size() > 0)
                                            #foreach($dumpLog in $options.dumpLogList)
                                            <tr>
                                                <td>$!{dumpLog.custOrderId}</td>
                                                <td>$!{dumpLog.statusCd}</td>
                                                <td>$!{dumpLog.createDate}</td>
                                                <td>$!{dumpLog.errMsg}</td>
                                                <td>$!{dumpLog.remark}</td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.dumpLogList && $options.dumpLogList != "null" &&
                                            $options.dumpLogList.size() == 0)
                                                <tr><td align='center' colspan='7'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
