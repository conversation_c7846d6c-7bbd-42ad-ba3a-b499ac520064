package com.asiainfo.crm.assist.app.query;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IConfigDiscreateValueQuerySMO;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import com.google.common.collect.Maps;

/**
 * Created by Administrator on 2017-7-24.
 */

@Component("vita.configDiscreateValueQuery")
public class ConfigDiscreateValueQuery extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(ConfigDiscreateValueQuery.class);

    @Autowired
    private IConfigDiscreateValueQuerySMO configDiscreateValueQuerySMO;
    @Autowired
    private ISoQuerySMO  iSoQuerySMO;
    @Override
    public Map achieveData(Object... params) throws Exception {
        Map map = (Map) params[0];
        String staffLanId = String.valueOf(map.get("staffLanId"));
        String staffLanName = String.valueOf(map.get("staffLanName"));
        String proviceName = String.valueOf(map.get("proviceName"));
        //String proviceId = String.valueOf(map.get("proviceId"));
        
        Map regionMap = qryCommregion(staffLanId);
        
        Map options = new HashMap();
        options.put("managerAreaId", regionMap.get("regionName").toString()==""?"":regionMap.get("regionName").toString());
        options.put("staffLanId", regionMap.get("commonRegionId").toString()==""?"":regionMap.get("commonRegionId").toString());
        return options;
    }

    public Map qryCommregion(String regionId) throws IOException{
    	//根据的地区编码查询市级地区
    	Map<String, Object> regionIdMap = new HashMap();
        regionIdMap.put("regionId", regionId);
        String regionIdStr = iSoQuerySMO.qryCommonRegionByIds(regionIdMap);
        String regionIdStrs =  (String) resolveResult(regionIdStr);
        Map<String, Object> regionIdMaps = jsonConverter.toBean(regionIdStrs, Map.class);
        if (regionIdMaps.containsKey("regionLevel")) {
			if ("30".equals(regionIdMaps.get("regionLevel").toString())) {
				Map regionIdCityMap = new HashMap<>();
				String parRegionId = regionIdMaps.get("parRegionId").toString();
				regionIdCityMap.put("regionId",parRegionId);
			    String regionIdCityStr = iSoQuerySMO.qryCommonRegionByIds(regionIdCityMap);
		        String regionIdCityStrs =  (String) resolveResult(regionIdCityStr);
		        Map regionIdCityMaps = jsonConverter.toBean(regionIdCityStrs, Map.class);
		        return regionIdCityMaps;
			}else if ("20".equals(regionIdMaps.get("regionLevel").toString()) || "10".equals(regionIdMaps.get("regionLevel").toString())) {
				  return regionIdMaps;
			}
		}
        return regionIdMaps;
    }
    
    public Map configDiscreateValueQuery(String json) throws Exception {
        Map<String, Object> paramMap = jsonConverter.toBean(json, Map.class);
        paramMap.put("attrId", AssistMDA.ITEM_SPEC_XMBS);
        String projectDetails = configDiscreateValueQuerySMO.queryReigonAttrValue(jsonConverter.toJson(paramMap));
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(projectDetails);
        List resultObjectlist = (List) MapUtils.getObject(resultObjectMap, "attrValues");
        //List resultObjectlist  = (List) resolveResult(resultObjectMap,"resultObject");
        Map<String, Object> options = Maps.newHashMap();
       // List resultObjectlist = (List) MapUtils.getObject(resultObjectMap,"resultObject");
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap, "pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount", pageCount);
        options.put("pageIndex", pageIndex);
        options.put("totalNumber", total);
        options.put("configDispersedList", resultObjectlist);
        return options;
    }

    public Map saveDiscreateValue(String json)throws Exception {
        Map<String, Object> paramMap = jsonConverter.toBean(json, Map.class);
        paramMap.put("attrId", AssistMDA.ITEM_SPEC_XMBS);
        Map saveResult = configDiscreateValueQuerySMO.insertAttrValue(jsonConverter.toJson(paramMap));
        Map ret = new HashMap();
        boolean boo = (boolean) saveResult.get("operationResult");
        if (boo){
            ret.put("boo" ,1);
            return ret;
        }else {
            return null;
        }
    }

    public Map updateDiscreateValue(String json)throws Exception {
        Map<String, Object> paramMap = jsonConverter.toBean(json, Map.class);
        paramMap.put("attrId", AssistMDA.ITEM_SPEC_XMBS);
        Map uodateResult = configDiscreateValueQuerySMO.updateAttrValue(jsonConverter.toJson(paramMap));
        Map ret = new HashMap();
        boolean boo = (boolean) uodateResult.get("operationResult");
        if (boo){
            ret.put("boo" ,1);
            return ret;
        }else {
            return null;
        }
    }

}
