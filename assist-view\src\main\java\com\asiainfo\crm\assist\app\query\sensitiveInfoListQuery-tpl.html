<div data-widget="sensitiveInfoListQuery" style="height: 100%">

    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>敏感信息查询</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_channelId" type="checkbox">
                                                </label> 渠道
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="channelId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="channelIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_qryObj" type="checkbox">
                                                </label> 实例ID
                                            </label>
                                            <div class="col-md-7">
                                                <input id="qryObj" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custId" type="checkbox">
                                                </label> 客户
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="custId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="custIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_qryPoints" type="checkbox">
                                                </label> 入口类型
                                            </label>
                                            <div class="col-md-7">
                                                <div class="col-md-12">
                                                    <select name="qryPointsSelect" id="qryPointsSelect" class="form-control" >

                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_staffCode" type="checkbox">
                                                </label> 员工编码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="staffCode" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_qryObjType" type="checkbox">
                                                </label> 对象类型
                                            </label>
                                            <div class="col-md-7">
                                                <div class="col-md-12">
                                                    <select name="qryObjTypeSelect" id="qryObjTypeSelect" class="form-control" >
                                                    	

                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_date" type="checkbox">
                                                </label>起始日期</label>
                                            <div class="col-md-7">
                                                <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">结束日期</label>
                                            <div class="col-md-7">
                                                <input name="endDate" id="endDate" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_verifyFlag" type="checkbox">
                                                </label> 临时鉴权
                                            </label>
                                            <div class="col-md-7">
                                                <div class="col-md-12">
                                                    <select name="qryVerifyFlag" id="qryVerifyFlag" class="form-control" >
                                                    	<option value=""></option>
														<option value="true">是</option>
														<option value="false">否</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-12">
                                            <div class="col-md-12 searchbutt_r" align="center">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-clear" type="button" class="btn btn-white">清除</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <!--<div><i class="bot"></i>查询结果</div>-->
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12 mart10" id="sensitiveInfoListResult">
                                        <table class="table table-hover" id="sensitiveInfoTable">
                                            <thead>
                                            <tr>
                                                <th>员工编码</th>
                                                <th>创建时间</th>
                                                <th>客户名称</th>
                                                <th>入口类型</th>
                                                <th>主对象类型</th>
                                                <th>操作ip</th>
                                                <th>操作mac</th>
                                                <th>授权员工编码</th>
                                                <th>渠道</th>
                                                <th>临时鉴权</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            #if($options != "null" && $options.sensitiveInfoList && $options.sensitiveInfoList != "null" &&
                                            $options.sensitiveInfoList.size() > 0)
                                            #foreach($sensitiveInfo in $options.sensitiveInfoList)
                                            <tr>
                                                <td>$!{sensitiveInfo.staffName}</td>
                                                <td>$!{sensitiveInfo.createDt}</td>
                                                <td>$!{sensitiveInfo.custName}</td>
                                                <td>$!{sensitiveInfo.qryPoint}</td>
                                                <td>$!{sensitiveInfo.qryObjType}</td>
                                                <td title="$!{sensitiveInfo.ip}">
                                                    #set($strIp=$!{sensitiveInfo.ip})
                                                    #if($strIp.length()>15)
                                                        #set($strIp=$strIp.substring(0,15))
                                                        $strIp ...
                                                    #else
                                                        $!strIp
                                                    #end
                                                </td>
                                                <td title="$!{sensitiveInfo.mac}">
                                                #set($strMac=$!{sensitiveInfo.mac})
                                                #if($strMac.length()>17)
                                                    #set($strMac=$strMac.substring(0,17))
                                                    $strMac ...
                                                #else
                                                    $!strMac
                                                #end
                                                </td>

                                                <td>$!{sensitiveInfo.agentStaffName}</td>
                                                <td>$!{sensitiveInfo.orgName}</td>
                                                <td title="$!{sensitiveInfo.verifyFlag}">
                                                	#set($strAuth=$!{sensitiveInfo.verifyFlag})
                                                	#if($strAuth == "true")
                                                    	#set($strAuth="是")
                                                    	$strAuth
                                                	#else
                                                    	#set($strAuth="否")
                                                    	$strAuth
                                                	#end
                                                </td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.sensitiveInfoList && $options.sensitiveInfoList != "null" &&
                                            $options.sensitiveInfoList.size() == 0)
                                            <tr>
                                                <td align='center' colspan='9'>未查到相关数据</td>
                                            </tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
