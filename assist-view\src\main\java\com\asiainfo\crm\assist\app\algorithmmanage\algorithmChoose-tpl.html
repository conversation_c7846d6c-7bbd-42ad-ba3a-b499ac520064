<div data-widget="algorithmChoose" style="height:100%">
    <p class="vita-data">{ "data" : $options }</p>
    <div class="calcw_leftbox">
        <div class="calc-sidetitle">
            <i class="icon-search glyphicon text18 mar15"></i>算法查询
        </div>
        <div class="sidetcont">
            <div class="btn-group bootstrap-select form-control">
                <input type="text" id="algoNbr" name="algoNbr" class="form-control" placeholder="请输入算法编码">
            </div>

            <div class="btn-group bootstrap-select form-control">
                <input type="text" id="algoKey" name="algoKey" class="form-control" placeholder="请输入密钥">
            </div>
        </div>
        <div class="col-lg-12">
            <button type="button" class="btn btn-block btn-white" id="searchBtn">立即查询</button>
        </div>
    </div>
    <div class="calcw_rightbox">
        <div class="calctitle">
            <div class="titlefont">
                <i class="fa-lightn glyphicon textcolorgreen text18 mar15"></i>查询结果
            </div>
            <div class="toolr">
                <button id="submitBtn" type="button" class="btn btn-primary btn-sm okbutt">确认</button>
                <button id="closeBtn" type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
        </div>
        <div id="algorithmListResult" class="calcw_rightcont">
            <table class="table table-hover">
                <thead>
                <tr>
                    <th>选择</th>
                    <th>算法ID</th>
                    <th>算法编码</th>
                    <th>密钥</th>
                    <th>算法描述</th>
                    <th>状态</th>
                    <th>创建时间</th>
                    <th>更新时间</th>
                </tr>
                </thead>
                <tbody id="algorithmList">
                #if($options != "null" && $options.algorithms && $options.algorithms != "null" && $options.algorithms.size() > 0)
                    #foreach($algorithm in $options.algorithms)
                    <tr>
                        <td>
                            <label class="wu-radio full absolute" data-scope="">
                                <input type="radio" name="algorithmRadio">
                            </label>
                            <p class="vita-data">{"algorithmInfo":$!algorithm}</p>
                        </td>
                        <td>$!{algorithm.algoId}</td>
                        <td>$!{algorithm.algoNbr}</td>
                        <td>$!{algorithm.algoKey}</td>
                        <td>$!{algorithm.algoDesc}</td>
                        <td>
                            #foreach($item in $!options.dataSecurityStatusCds.entrySet())
                                #if($!{item.key} == $!{algorithm.statusCd})
                                    $!{item.value}
                                #end
                            #end
                        </td>
                        <td>$!{algorithm.createDate}</td>
                        <td>$!{algorithm.updateDate}</td>
                    </tr>
                    #end
                #elseif($options != "null" && $options.algorithms && $options.algorithms != "null" && $options.algorithms.size() == 0)
                    <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                #end
                </tbody>
            </table>
            <div id="showPageInfo"></div>
            #if($options.totalNumber && $options.totalNumber != "null")
            <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
            #end
        </div>
    </div>
</div>