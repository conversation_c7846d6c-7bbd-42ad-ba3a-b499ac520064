(function(vita) {
    var ICTInputItem = vita.Backbone.BizView.extend({
        events : {
            "click #btnClose": "_closeBtnClick",
            "click #myTab li" : "_clickTab",
        },
        global : {
            pageSize : 1
        },
        _initialize : function () {
            var widget = this,
                el = $(widget.el);
            widget._queryPeriodInputItem();
            widget._queryUnPeriodInputItem();
        },
        _closeBtnClick: function (e) {
            var widget = this,
                el = $(widget.el);
            el.closest("[data-widgetfullname=vita-dialog]").dialog("close");
        },
        _clickTab : function(e) {
            var widget = this,
                element = $(widget.el),
                global = widget.global,
                $tar = $(e.target),
                li = $tar.closest("li");
            li.addClass("active").siblings("li").removeClass("active");
            var tabIndex = li.attr("id").replace(/\D/g, '');
            var content = element.find("#content" + tabIndex);
            if (content.length) {
                content.fadeIn(300).addClass("in active")
                    .siblings("div").hide().removeClass("in active");
            }
        },

        _queryPeriodInputItem : function() {
            var widget = this,el = $(widget.el);
            var subItemCode = el.data("subItemCode");
            var params = widget._getConds(1, subItemCode);
            params.cycleType=1;
            var showPageInfo = el.find("#showPageInfo1");
            var recordNumber=widget.global.pageSize;
            showPageInfo.empty();
            widget.refreshPart("queryInputItem", params, '.table-hover1',
                "result1", function(res) {
                    var totalNumber = $(res).find("td").eq(0).data("totalNumber");
                    if (parseInt(totalNumber) > 0) {
                        var paging = widget.require("paging");
                        var e = paging.new({
                            recordNumber : recordNumber,
                            total : totalNumber,
                            pageIndex : 1,
                            callback : function(pageIndex,recordNumber) {
                                //判断当前页
                                if(pageIndex == null || pageIndex == undefined || isNaN(pageIndex)){
                                    pageIndex = 1;
                                }
                                var params = widget._getConds(pageIndex, subItemCode);
                                params.cycleType=1;
                                params.pageInfo.rowCount = totalNumber;
                                widget.refreshPart("queryInputItem", params, '.table-hover1', $.noop, {
                                    headers : {
                                        "regionId" : widget.gSession.installArea
                                    }
                                });
                            }
                        });
                        showPageInfo.append(e.getElement());
                    }
                }, {
                    mask : true,
                    headers : {
                        "regionId" : widget.gSession.installArea
                    }
                });
        },
        _queryUnPeriodInputItem : function() {

            var widget = this,el = $(widget.el);
            var subItemCode = el.data("subItemCode");
            var params = widget._getConds(1, subItemCode);
            params.cycleType=2;
            var showPageInfo = el.find("#showPageInfo2");
            var recordNumber=widget.global.pageSize;
            showPageInfo.empty();
            widget.refreshPart("queryInputItem", params, '.table-hover2',
                "result2", function(res) {
                    var totalNumber = $(res).find("td").eq(0).data("totalNumber");
                    if (parseInt(totalNumber) > 0) {
                        var paging = widget.require("paging");
                        var e = paging.new({
                            recordNumber : recordNumber,
                            total : totalNumber,
                            pageIndex : 1,
                            callback : function(pageIndex,recordNumber) {
                                //判断当前页
                                if(pageIndex == null || pageIndex == undefined || isNaN(pageIndex)){
                                    pageIndex = 1;
                                }
                                var params = widget._getConds(pageIndex, subItemCode);
                                params.cycleType=2;
                                params.pageInfo.rowCount = totalNumber;
                                widget.refreshPart("queryInputItem", params, '.table-hover2', $.noop, {
                                    headers : {
                                        "regionId" : widget.gSession.installArea
                                    }
                                });
                            }
                        });
                        showPageInfo.append(e.getElement());
                    }
                }, {
                    mask : true,
                    headers : {
                        "regionId" : widget.gSession.installArea
                    }
                });
        },
        _getConds : function(pageIndex,subItemCode) {
            var widget = this,el = $(widget.el);
            var param = {
                "subItemCode":subItemCode,
                "pageInfo" : {
                    "pageIndex" : pageIndex,
                    "pageSize" : widget.global.pageSize
                }
            };
            return param;
        },
    });
    vita.widget.register("ICTInputItem",ICTInputItem, true);
})(window.vita);