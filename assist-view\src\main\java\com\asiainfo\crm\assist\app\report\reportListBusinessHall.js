/**
 * Created by Administrator on 2017-9-11.
 */
/**
 * Created by Administrator on 2017-9-8.
 */
(function (vita) {
    var reportListBusinessHall = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_queryReportListBusinessHall",
            "click #channelIdBtn": "_chooseChannel",
            "click #beginDates":"_beginDates",
            "click #export":"_export",
            "click #print":"_print"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            $("#staffId").val(gSession.staffCode);
            $("#staffName").val(gSession.staffName);
            debugger;
            var datetime = widget.require("datetime");
            var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
            var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
            var now = widget._getNowFormatDate();
            var endMis=widget._getEndFormatDate();
            beginDate.val(now);
            endDate.val(endMis);
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    timeFormat: "HH:ii:ss ",
                    timeWheels:"HHiiss"
                });
            }
            ;
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    timeFormat: "HH:ii:ss ",
                    timeWheels:"HHiiss"
                });
            }
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.commonRegionName).attr("value", gSession.commonRegionId);
        },
        global: {
            preset: "datetime",
            chooseChannel: "../comm/chooseChannel",
        },
        _queryReportListBusinessHall:function () {
            var widget = this,element = $(widget.el);
            var beginDate = $("#beginDate").val();
            var endDate = $("#endDate").val();
            if (widget._isNullStr(beginDate) || widget._isNullStr(endDate)) {
                widget.popup("请选择日期范围！");
                return false;
            }
            var d1 = new Date(beginDate.replace(/\-/g, "\/"));
            var d2 = new Date(endDate.replace(/\-/g, "\/"));
            if(beginDate!=""&&endDate!=""&&d1 >d2)
            {
                widget.popup("起始时间不能大于截止时间！");
                return false;
            }
           var a =  $("#c_channelId").is(":checked");
            var orgId = $("#channelId").attr("value");
            if(a == true){
                if (orgId == null || orgId == ""){
                    widget.popup("请选择渠道.")
                    return false;
                }

            }else {
                orgId = "";
            }
            var params = {
                channelId:orgId,
                startDt : beginDate,
                endDt : endDate
            };
            var gsession = widget.gSession;
            //如果不选择渠道，则传当前登陆人的ID，统计当前登陆人所有已挂渠道
            if(params.channelId==null||params.channelId==""){
                params.staffId=gsession.staffId;
            }
            var d = new Date();
           widget.refreshPart("ReportListBusinessHallQuery",JSON.stringify(params),"#ReportListBusinessHallList",function (ter) {
                // var tableDate = d.getFullYear() + "-" + (d.getMonth() + 1) + "-" + d.getDate();
               var tableDate = d.toLocaleString();
                var tableTd = gsession.staffName;
                widget.model.set("tableTd",tableTd);
                widget.model.set("tableDate",tableDate);
               widget.model.set("startDt",params.startDt);
               widget.model.set("endDt",params.endDt);
               widget.model.set("channelId",params.channelId);
                $("#tableTd").html(tableTd);
                $("#tableDate").html(tableDate);
                $("#row").attr("style","display: show");

               var paging = this.require("paging"), r = $(ter);
               var totalNumber = r.find("#showPageInfo").data("totalNumber");
               var exportNum = r.find("#showPageInfo").data("exportNum");
               var tax="导出功能限制一次性最多导出"+exportNum+"条，本次查询结果";
               var exportStr="能导出";
               var exportStrNo="不能导出";
               if (totalNumber > 0) {
                   if(totalNumber>exportNum){
                       $("#countSize").text(tax+totalNumber+"条,"+exportStrNo);
                       $("#out_excel").attr("disabled",true);
                   }else {
                       $("#countSize").text(tax+totalNumber+"条,"+exportStr);
                       $("#out_excel").attr("disabled",false);
                   }
               }else{
                   $("#countSize").text(tax+0+"条,"+exportStr);
                   $("#out_excel").attr("disabled",false);
               }
           })
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _export:function () {
            var widget = this, element = $(widget.el);
            debugger;
            var params = {
                //channelId: channelId,
                channelId: "",
                startDt: widget.model.get("startDt"),
                endDt: widget.model.get("endDt"),
                tableTd:widget.model.get("tableTd"),//制表人
                tableDate:widget.model.get("tableDate")//制表时间
            }
            var channelId = widget.model.get("channelId")
            if (channelId != null && channelId != ""){
                params.channelId=channelId
            }else {
                params.channelId="-9999"
            }
            var gsession = widget.gSession;
            debugger
            var url = "../query/reportListBusinessHallExport";
            debugger;
            location.href=url+"?channelId="+params.channelId+"&startDt="+params.startDt+"&endDt="+params.endDt+
                "&tableTd="+encodeURI(encodeURI(params.tableTd))+"&tableDate="+encodeURI(encodeURI(params.tableDate));
        },
        _chooseChannel: function () {
            var widget = this,element = $(widget.el);
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            debugger;
            var option = {
                id : dialogId,
                url : widget.global.chooseChannel,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);
                }
            };
            widget.dialog(option);
            return false;
        },

        _print:function () {
            var widget = this, element = $(widget.el);
            // widget.popup("功能开发中")

            debugger;
            // var widget = this;
            var orderhtml = "";
            orderhtml = $("#divTable").html();  //获取div view的内容
            /* 创建iframe */
            var doc = document;
            var headobj = doc.getElementsByTagName("head").item(0); //提取head
            var pp = doc.getElementById("lldxi_printRegionFrame_2012_0112"); //iframe的ID
            if (pp) { doc.body.removeChild(pp); } //如果存在则remove
            pp = doc.createElement("iframe");
            pp.setAttribute("src", "about:blank");
            pp.setAttribute("id", "lldxi_printRegionFrame_2012_0112");
            pp.setAttribute("marginheight", "0");
            pp.setAttribute("marginwidth", "0");
            pp.style.display = "none";
            doc.body.appendChild(pp);
            if(widget.isChromeBrowser()){  //Chrome
                //Chrome体验最佳
                //var htmlstr = "<html>" + headobj.outerHTML + "<body>" + orderhtml + "<\/body>" + "<\/html>";
                //pp.contentWindow.document.write(htmlstr);
                pp.contentWindow.document.write(orderhtml);
                pp.contentWindow.print();

                //还要打印所有的协议, 每个调用一次print()保证页数完整性
                var ptFrames = $("iframe[name=protocolFrames]");
                $.each(ptFrames, function(i, pt){
                    var toPrintHtml = $(pt).contents().find("div[name=protocolPrintDiv]").html();
                    var doc = document;
                    var pp = doc.getElementById("lldxi_printRegionFrame_2012_0112"); //iframe的ID
                    if (pp) { doc.body.removeChild(pp); } //如果存在则remove
                    pp = doc.createElement("iframe");
                    pp.setAttribute("src", "about:blank");
                    pp.setAttribute("id", "lldxi_printRegionFrame_2012_0112");
                    pp.setAttribute("marginheight", "0");
                    pp.setAttribute("marginwidth", "0");
                    pp.style.display = "none";
                    doc.body.appendChild(pp);
                    toPrintHtml = '<html><head></head><body>' + toPrintHtml + '</body></html>';
                    pp.contentWindow.document.write(toPrintHtml);
                    pp.contentWindow.print();
                });
            }else if(widget.isFfBrowser()){ //FireFox
                //同理mozilla firefox也改为同IE一样
                var htmlstr = "<html>" + headobj.outerHTML + "<body>" + orderhtml + "<\/body>" + "<\/html>";
                var newWin= window.open();
                newWin.document.write(htmlstr);
                newWin.document.close();
                newWin.focus();
                newWin.print();
                newWin.close();
            }else{ //others like IE
                //IE浏览器实测iframe调用打印无反应，改为new一个window， 打印完毕自动关闭窗口
                var htmlstr = "<html>" + headobj.outerHTML + "<body>" + orderhtml + "<\/body>" + "<\/html>";
                var newWin= window.open();
                newWin.document.write(htmlstr);
                //防止write完之前就close,设置延时1s
                setTimeout(function(){
                    newWin.document.close();
                    newWin.focus();
                    newWin.print();
                    newWin.close();
                },1000);
            }

        },
        /**
         * 判断是否Chrome
         * @return {TypeName}
         */
        isChromeBrowser:function(){
            var match = this.bssUtilParam.rChrome.exec(this.bssUtilParam.userAgent);
            if (match != null) {
                return true;
            }else{
                return false;
            }
        },
        bssUtilParam : {
            userAgent : navigator.userAgent.toLowerCase(), // userAgent
            rMsie : /.*(msie) ([\w.]+).*/, // ie
            rFirefox : /.*(firefox)\/([\w.]+).*/, // firefox
            rOpera : /(opera).+version\/([\w.]+)/, // opera
            rChrome : /.*(chrome)\/([\w.]+).*/, // chrome
            rSafari : /.*version\/([\w.]+).*(safari).*/  // safari
        },
        _getNowFormatDate: function() {
            var date = new Date();
            var seperator1 = "-";
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var strDate = date.getDate();
            var hour = date.getHours();
            var mins=date.getMinutes();
            var seconds = date.getSeconds();
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            if (strDate >= 0 && strDate <= 9) {
                strDate = "0" + strDate;
            }
            if (mins >= 0 && mins <= 9) {
                mins = "0" + mins;
            }
            if (seconds >= 0 && seconds <= 9) {
                seconds = "0" + seconds;
            }
            if (hour >= 0 && hour <= 9) {
                hour = "0" + hour;
            }
            var currentdate = year + seperator1 + month + seperator1 + strDate
                +" 00" + ":00" +":00";
            return currentdate;
        },
        _getEndFormatDate: function() {
            var date = new Date();
            var seperator1 = "-";
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var strDate = date.getDate();
            var hour = date.getHours();
            var mins=date.getMinutes();
            var seconds = date.getSeconds();
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            if (strDate >= 0 && strDate <= 9) {
                strDate = "0" + strDate;
            }
            if (mins >= 0 && mins <= 9) {
                mins = "0" + mins;
            }
            if (seconds >= 0 && seconds <= 9) {
                seconds = "0" + seconds;
            }
            if (hour >= 0 && hour <= 9) {
                hour = "0" + hour;
            }
            var currentdate = year + seperator1 + month + seperator1 + strDate
                +" 23"+ ":59" +":59";
            return currentdate;
        }
    });
    vita.widget.register("reportListBusinessHall", reportListBusinessHall, true);
})(window.vita);