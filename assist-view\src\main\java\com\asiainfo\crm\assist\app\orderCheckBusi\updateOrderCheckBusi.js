(function(vita){
    var updateOrderCheckBusi = vita.Backbone.BizView.extend({
        events : {
            "click #modifyBtn":"_saveOrderCheckBusi",
            "click #closeBtn" : "_closePage",
            "click #regionIdBtn": "_chooseArea",
        },
        _initialize:function(){
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            var data = element.data("data");
            var checkBusiInfo = data.checkBusiInfo;
            widget.model.set("serviceName", checkBusiInfo.serviceName);
            widget.model.set("statusCd", checkBusiInfo.statusCd);
            widget.model.set("checkSite", checkBusiInfo.checkSite);
            widget.model.set("checkType",checkBusiInfo.checkType);
            widget.model.set("specType",checkBusiInfo.specType);
            widget.model.set("tacheCode",checkBusiInfo.tacheCode);
            element.find("#applyRegionId").val(checkBusiInfo.regionName).attr("value",checkBusiInfo.applyRegionId);
            element.find("#serviceName").val(checkBusiInfo.serviceName).attr("value",checkBusiInfo.serviceName);
            element.find("#statusCd").val(checkBusiInfo.statusCd).attr("value",checkBusiInfo.statusCd);
            element.find("#checkSite").val(checkBusiInfo.checkSite).attr("value",checkBusiInfo.checkSite);
            element.find("#checkType").val(checkBusiInfo.checkType).attr("value",checkBusiInfo.checkType);
            element.find("#specType").val(checkBusiInfo.specType).attr("value",checkBusiInfo.specType);
            element.find("#tacheCode").val(checkBusiInfo.tacheCode).attr("value",checkBusiInfo.tacheCode);
        },
        global:{
            pageIndex:1,
            pageSize:5,
            chooseArea: "../comm/chooseArea",
            preset : "date"
        },
        _saveOrderCheckBusi: function () {
            var widget = this, element = $(widget.el);
            var params = {
                "serviceName": widget.model.get("serviceName"),
                "statusCd": element.find("#statusCd").val(),
                "checkType": element.find("#checkType").val(),
                "checkSite": element.find("#checkSite").val(),
                "specType": element.find("#specType").val(),
                "tacheCode": element.find("#tacheCode").val(),
                "applyRegionId": element.find('#applyRegionId').attr("value")
            };
            var data = element.data("data");
            var checkBusiInfo = data.checkBusiInfo;
            var orderCheckBusiId = checkBusiInfo.orderCheckBusiId;
            params.orderCheckBusiId = orderCheckBusiId;
            if (!widget._validate()) {
                return false;
            }
            widget.callService("editOrderCheckBusi", params, function (res) {
                var ret = JSON.parse(res);
                if (ret.resultCode == 0) {
                    widget.popup("修改成功！",function(){
                        widget._closePage();
                    });
                }else{
                    widget.popup("修改失败！"+ret.resultMsg,function(){
                        widget._closePage();
                    });
                }

            }, {
                async: true,
                mask: true
            });
        },
        _checkValue: function (condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty ? false : true;
        },
        _validate: function () {
            var widget = this, element = $(widget.el);
            var serviceName = widget.model.get("serviceName");
            if (!widget._checkValue(serviceName)) {
                widget.popup("稽核业务名称不能为空，请输入稽核业务名称");
                return false;
            }
            var statusCd = element.find("#statusCd").val();
            return true;
        },
        //取消
        _closePage : function() {
            var widget = this,
                element = $(widget.el);
            element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#applyRegionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
    });
    vita.widget.register("updateOrderCheckBusi", updateOrderCheckBusi, true);
})(window.vita);
