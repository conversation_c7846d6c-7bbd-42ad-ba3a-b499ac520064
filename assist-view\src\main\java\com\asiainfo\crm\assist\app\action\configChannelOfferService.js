(function (vita) {
    var configChannelOfferService = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_forbidChannelQuery",
            "click #m_file":"_selectExcel",
            "click #upload_excel":"_uploadExcel",
            "click #out_excel":"_pcExcelPrint",
            "change .m_file":"_fileOnChange"
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        global: {
            pageIndex: 1,
            pageSize: 10,
            isChanged : false
        },
        _fileOnChange:function () {
            isChanged = true;
            var filename = $(".m_file").get(0).files[0].name;
            $('#m_file span').html(filename);
        },
        _selectExcel:function () {
            return $('.m_file').click();
        },
        _uploadExcel:function () {
            var widget = this;
            var m_fileVal = $('.m_file').val();
            if(m_fileVal=="") {
                widget.popup('请选择文件！');
                return ;
            }
            if('-1' == m_fileVal.indexOf('.xls')) {
                widget.popup('只能上传Excel文件！');
                return ;
            }
            var gsession = widget.gSession;
            var params = {
                areaId: gsession.staffRegionId,
                staffId: gsession.staffId
            }
            $('.route_input').val(JSON.stringify(params));
            var url = "../action/uploadForbidChannelInfoExcel";
            $('#m_form').ajaxSubmit({
                type:'post',
                url:url,
                success:function(jsonData){
                    var data = JSON.parse(jsonData);
                    if(data.resultObject.resultCode == 0){
                        var dataInfo = data.resultObject.dataInfo
                        localStorage.setItem('marking',dataInfo.marking);
                        alert('操作批次号：'+dataInfo.marking+' 成功'+
                            dataInfo.successSum+'个; 失败'+dataInfo.errorSum+'个');
                    }else if(data.resultObject.resultCode == -1){
                        alert('一次上传不能超过500条文件，请重新上传');
                    }else {
                        alert('上传失败，请检查内容是否合法');
                    }
                },
                error:function(e){
                    alert('上传失败');
                }
            })
        },
        _pcExcelPrint:function () {
            var url = "../action/pcExcelPrint";
            var marking = localStorage.getItem('marking');
            var params = {
                marking: marking
            }
            location.href=url+"?jsonStr="+JSON.stringify(params);
        },
        _forbidChannelQuery:function () {
            var widget = this;
            var channelId = $("#channelId").val();
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            }
            if (widget._isNullStr(channelId)) {
                widget.popup("请输入渠道编号！");
                return false;
            } else {
                params.channelId = channelId;
            }
            debugger;
            widget.refreshPart("qryForbidChannelsById", JSON.stringify(params), "#forbidChannelResult", function (re) {
                debugger;
                var r = $(re), paging = this.require("paging");
                var e = paging.new({
                    recordNumber : widget.global.pageSize,
                    total : r.find("#showPageInfo").data("totalNumber"),
                    pageIndex : widget.global.pageIndex,
                    callback : function (pageIndex, pageSize) {
                        var params = {
                            channelId: $("#channelId").val(),
                            pageInfo: {
                                pageIndex: pageIndex,
                                pageSize: widget.global.pageSize
                            }
                        };
                        widget.refreshPart("qryForbidChannelsById", JSON.stringify(params), "#forbidChannelTable");
                    }
                });
                r.find("#showPageInfo").append(e.getElement());
            }, { async: false });
        }
    });
    vita.widget.register("configChannelOfferService", configChannelOfferService, true);
 })(window.vita);