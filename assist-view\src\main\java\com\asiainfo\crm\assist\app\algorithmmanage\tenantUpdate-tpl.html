<div data-widget="tenantUpdate" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="calctitle">
        <div class="titlefont">
            <i class="fa-lightn glyphicon textcolorgreen text18 mar15"></i>租户修改
        </div>
        <div class="toolr">
            <button id="submitBtn" type="button" class="btn btn-primary btn-sm okbutt">确认</button>
            <button id="cancelBtn" type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">×</span>
            </button>
        </div>
    </div>
    <div class="calcw_rightcont marltb15">
        <form>
            <div class="form-group col-md-12 ">
                <label class="col-md-2 control-label lablep">租户编码</label>
                <div class="col-md-6">
                    <input id="tenantNbr" name="tenantNbr" type="text" class="form-control" placeholder="输入租户编码">
                    <p class="vita-bind" model="tenantNbr"></p>
                </div>
            </div>
            <div class="form-group col-md-12 ">
                <label class="col-md-2 control-label lablep">租户描述</label>
                <div class="col-md-6">
                    <textarea id="tenantDesc" name="tenantDesc" class="form-control" rows="3" placeholder="输入租户描述"></textarea>
                    <p class="vita-bind" model="tenantDesc"></p>
                </div>
            </div>
            <div class="form-group col-md-12 ">
                <label class="col-md-2 control-label lablep">状态</label>
                <div class="col-md-6">
                    <select id="statusCd" class="form-control">
                        #foreach($item in $!options.dataSecurityStatusCds.entrySet())
                        <option value="$!{item.key}">$!{item.value}</option>
                        #end
                    </select>
                    <p class="vita-bind" model="statusCd"></p>
                </div>
            </div>
            <div class="form-group col-md-12 ">
                <label class="col-md-2 control-label lablep">选择算法</label>
                <div class="col-md-6">
                    <div class="input-group">
                        <input id="algoId" type="text" class="form-control" placeholder="" readonly="readonly">
                        <div class="input-group-btn">
                            <button id="algoIdBtn" class="btn btn-green" type="button">选择</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group col-md-12 ">
                <label class="col-md-2 control-label lablep">生效时间</label>
                <div class="col-md-2">
                    <input id="effDate" name="effDate" type="text" class="form-control"/>
                </div>
                <label class="col-md-2 control-label lablep">失效时间</label>
                <div class="col-md-2">
                    <input id="expDate" name="expDate" type="text" class="form-control"/>
                </div>
            </div>
        </form>
    </div>
</div>