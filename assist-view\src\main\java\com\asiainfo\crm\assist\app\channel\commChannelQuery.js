(function (vita) {
    var commChannelQuery = vita.Backbone.BizView.extend({
        //前台框架可以加个localEvents append子类的事件？
        events : {
            "click #btn-query": "_commChannelQuery",
            "click #regionIdBtn": "_chooseArea"
        },
        global : {
            pageIndex : 1, //当前页
            pageSize : 5, //每页记录数
            initPageIndexFlag : "Y",//初始化当前页标识
            chooseArea: "../comm/chooseArea"
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _commChannelQuery : function () {
            
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                },

            };
            var regionId = $("#regionId").attr("value");
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.regionId = regionId;
            }
            var channelVal=$("#channelIdInput").val();
            if(widget._isNullStr(channelVal)){
                widget.popup("请输入渠道查询值！");
                return false;
            }
            if(widget._isChannelName(channelVal)){
                params.channelName=channelVal;
            }else if(widget._isChannelId(channelVal)){
                params.channelId=channelVal;
            }
            widget._refreshChannelList(params);
        },
        _isChannelName:function (value) {
            if (/.*[\u4e00-\u9fa5]+.*$/.test(value)) {
                return true;
            }
            return false;
        },
        _isChannelId:function (value) {
            if (/^\d{2,}$/.test(value)) {
                return true;
            }
            return false;
        },
        _refreshChannelList: function (params) {
            var widget = this;
            widget.refreshPart("qryChannelList", JSON.stringify(params), "#orderedProdListAll", function (re) {
                debugger;
                var r = $(re), paging = this.require("paging");
                var totalNumber = r.find("#showPageInfo").data("totalNumber");
                var e = paging.new({
                    recordNumber : widget.global.pageSize,
                    total : totalNumber,
                    pageIndex :1,
                    callback : function (pageIndex, pageSize) {
                        params.pageInfo.pageIndex = pageIndex;
                        widget.refreshPart("qryChannelList", JSON.stringify(params), "#orderedProdList",null,null,{
                            headers : {
                                "regionId" : widget.gSession.installArea
                            },
                            mask:true
                        });
                    }
                });
                r.find("#showPageInfo").append(e.getElement());
            }, {
                async: false,
                mask:true,
                headers : {
                    "regionId" : widget.gSession.installArea
                }
            });
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        }
    });
    vita.widget.register("commChannelQuery", commChannelQuery, true);
})(window.vita);