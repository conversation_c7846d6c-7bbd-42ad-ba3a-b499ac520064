<div data-widget="mktResTypeAttr">
    <vita-data>{"data":$options}</vita-data>
    <p class="vita-data">$options</p>
    <div class="sypage-nav">
        <div class="nav_alink"><strong class="text16">营销资源类别属性</strong></div>
    </div>
    <div class="sypage-min">
        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">营销资源类别属性列表</strong></div>
        </div>
        <div class="sypagemin_content ">
            <div class="col-lg-12 mart10">
                <table class="table table-hover" id="systemRoleList">
                    <thead>
                    <tr>
                        <th>选择</th>
                        <th>营销资源属性标识</th>
                        <th>营销资源CD标识</th>
                        <th>属性标识</th>
                        <th>默认值</th>
                     <!--   <th>状态</th>-->
                        <th>创建时间</th>
                    </tr>
                    </thead>
                    <tbody>
                    #if($options != "null" && $options.mktResTypeAttrs && $options.mktResTypeAttrs != "null" && $options.mktResTypeAttrs.size() > 0)
                    #foreach($mktResTypeAttr in $options.mktResTypeAttrs)
                    <tr >
                        <td>
                            <label class="wu-radio full absolute" data-scope="">
                                <input type="radio" name="moduleRadio" />
                                <p class="vita-data">{"data":$mktResTypeAttr,"totalNumber":$options.totalNumber}</p>
                            </label></td>
                        <td>$!mktResTypeAttr.mktResTypeAttrId</td>
                        <td>$!mktResTypeAttr.mktResTypeId</td>
                        <td>$!mktResTypeAttr.attrName</td>
                        <td>$!mktResTypeAttr.defaultValue</td>
                     <!--   <td>#if($!mktResTypeAttr.statusCd == 1000)有效
                            #elseif($!mktResTypeAttr.statusCd == 1100)无效
                            #end
                        </td>-->
                        <td>$!mktResTypeAttr.createDate</td>
                    </tr>
                    #end
                    #end
                    </tbody>
                </table>
                <!--翻页start -->
                <div class="page-box" id="showPageInfo">
                </div>
                <!--翻页end -->
            </div>
        </div>
    </div>
</div>