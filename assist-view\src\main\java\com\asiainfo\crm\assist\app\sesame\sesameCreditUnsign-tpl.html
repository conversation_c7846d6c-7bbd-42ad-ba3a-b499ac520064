<div data-widget="sesameCreditUnsign" style="height: 100%">

    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>芝麻信用购机解约</div>
                                </div>

                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-5 ">
                                            <label class="col-md-4 control-label lablep">
                                                请输入身份证号码:</label>
                                            <div class="col-md-8">
                                                <input type="text" id="certNum" class="form-control" placeholder="">
                                            </div>
                                            <div id="sesameUserIdListTips" class="tips-container" style="display: none;">
                                                #if($options != "null" && $options.sesameUserIdList && $options.sesameUserIdList != "null" && $options.sesameUserIdList.size() > 0)
                                                <table class="table table-hover">
                                                    <thead>
                                                    <tr>
                                                        <th>支付宝UserID</th>
                                                        <th>客户名</th>
                                                        <th>身份证号</th>
                                                        <th>手机</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody id="sesameUser_list">
                                                    #foreach($user in $options.sesameUserIdList)
                                                    <tr>
                                                        <td>$!{user.alipayUserId} <span name="trData"></span><p class="vita-data">{"user":$user}</p></td>
                                                        <td>$!{user.custName}</td>
                                                        <td>$!{user.certNum}</td>
                                                        <td>$!{user.mobile}</td>
                                                    </tr>
                                                    #end
                                                    </tbody>
                                                </table>
                                                #end
                                            </div>
                                        </div>
                                        <div class="form-group col-md-5">
                                            <div class="col-md-5" align="left">
                                                <button id="btn-query" type="button" class="btn btn-primary">查询</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询结果</div>
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12">
                                        <a id="unsign" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 解约
                                        </a>
                                    </div>
                                    <div class="col-lg-12 mart10" id="sesameCreditUnsignResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>选择</th>
                                                <th>支付宝user_id</th>
                                                <th>芝麻征信工单</th>
                                                <th>创建时间</th>
                                                <th>是否签约支付宝代扣</th>
                                                <th>绑定手机号</th>
                                                <th>是否订购芝麻征信标签销售品</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                                #if($options != "null" && $options.sesameCreditInfos && $options.sesameCreditInfos != "null" &&
                                                $options.sesameCreditInfos.size() > 0)
                                                #foreach($info in $options.sesameCreditInfos)
                                                <tr>
                                                    <td><label class="wu-radio full absolute" data-scope=""><input
                                                            type="radio" name="payment"></label> <span name="trData"></span><p class="vita-data">{"info":$info}</p></td>
                                                    <td>$!{info.alipayUserId}</td>
                                                    <td>$!{info.transactionId}</td>
                                                    <td>$!{info.createDt}</td>
                                                    #if($info.isAlipaySign && $info.isAlipaySign != "null" && $info.isAlipaySign == "Y")
                                                    <td>已签约</td>
                                                    #elseif($info.isAlipaySign && $info.isAlipaySign != "null" && $info.isAlipaySign == "D")
                                                    <td>已失效</td>
                                                    #elseif($info.isAlipaySign && $info.isAlipaySign != "null" && $info.isAlipaySign == "W")
                                                    <td>解约中</td>
                                                    #else
                                                    <td>未签约</td>
                                                    #end
                                                    #if($info.accNum && $info.accNum != "null" )
                                                    <td>$!{info.accNum}【$!{info.accNumStatus}】</td>
                                                    #else
                                                    <td></td>
                                                    #end
                                                    #if($info.accNum && $info.accNum != "null")
                                                    <td>已订购【$!{info.offerInstStatus}】</td>
                                                    #else
                                                    <td>未订购</td>
                                                    #end
                                                </tr>
                                                #end
                                                #elseif($options != "null" && $options.sesameCreditInfos && $options.sesameCreditInfos != "null" &&
                                                $options.sesameCreditInfos.size() == 0)
                                                <tr><td align='center' colspan='7'>未查询到已签约记录！<td></tr>
                                                #end
                                            </tbody>
                                        </table>
                                    </div>
                                    <!--翻页start -->
                                    <div id="showPageInfo">
                                    </div>
                                    #if($options.totalNumber && $options.totalNumber != "null")
                                    <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                    #end
                                    <!--翻页end -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
