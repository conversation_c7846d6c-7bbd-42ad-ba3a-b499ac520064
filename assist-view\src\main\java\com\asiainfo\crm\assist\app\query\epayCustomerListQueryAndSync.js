(function (vita) {
    var epayCustomerListQueryAndSync = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_epayQryEpayCustomerInfo",
            "click #btn-Sync": "_customerInfoSync"

        },


        //查找 翼支付客户
        _epayQryEpayCustomerInfo: function () {
            var widget = this;
            var params = widget._getConds();
            params.glbSessionId = widget.gSession.glbSessionId;
            if (params) {

                widget.refreshPart("qryCustomerInfo", JSON.stringify(params), "#customerInfoResult", function (res) {

                }, {
                    async: false
                });
            }
        },

        //同步翼支付客户
        _customerInfoSync: function () {
            var widget = this;
            var params = widget._getConds();
            params.glbSessionId = widget.gSession.glbSessionId;
            if (params) {

                widget.callService("syncCustomerInfo", params, function(re){
                    if(re && re.syncResult && re.syncResult.responseCode == '0') {
                        widget.popup("同步成功");
                    } else {
                        widget.popup("同步失败");
                    }
                });
            }
        },


        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this, element = $(widget.el);
            var params = {};
            var accNum = $.trim(element.find("#accNum").val());
            if (widget._isNullStr(accNum)) {
                widget.popup("请输入用户接入号码！");
                return false;
            }
            params.accNum = accNum;
            return params;
        }
    });
    vita.widget.register("epayCustomerListQueryAndSync", epayCustomerListQueryAndSync, true);
})(window.vita);
