<div data-widget="brandRel">
    <vita-data>{"data":$options}</vita-data>
    <p class="vita-data">$options</p>
    <div class="sypage-min">
        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">关联品牌列表</strong></div>
        </div>
        <div class="sypagemin_content ">
            <div class="col-lg-12 mart10">
                <table class="table table-hover" id="systemRoleList">
                    <thead>
                    <tr>
                        <th>选择</th>
                        <th>品牌编码</th>
                        <th>品牌名称</th>
                        <th>品牌描述</th>
                     <!--   <th>状态</th>-->
                        <th>创建时间</th>
                    </tr>
                    </thead>
                    <tbody>
                    #if($options != "null" && $options.brands && $options.brands != "null" && $options.brands.size() > 0)
                    #foreach($brand in $options.brands)
                    <tr >
                        <td>
                            <label class="wu-radio full absolute" data-scope="">
                                <input type="radio" name="moduleRadio" />
                                <p class="vita-data">{"data":$brand,"totalNumber":$options.totalNumber}</p>
                            </label></td>
                        <td>$!brand.brandCode</td>
                       <!-- <td>
                            <a class="textcolorgreen" link="brandRel" data-toggle="tooltip" data-placement="top">$!brand.brandName</a>
                            <p class="vita-data">{"brandId" : $!brand.brandId}</p>
                        </td>-->
                        <td>$!brand.brandName</td>
                        <td>$!brand.brandDesc</td>
                     <!--   <td>#if($!offerInformProg.statusCd == 1000)有效
                            #elseif($!offerInformProg.statusCd == 1100)无效
                            #end
                        </td>-->
                        <td>$!brand.createDate</td>
                    </tr>
                    #end
                    #end
                    </tbody>
                </table>
                <!--翻页start -->
                <div class="page-box" id="showPageInfo">
                </div>
                <!--翻页end -->
            </div>
        </div>
    </div>
</div>