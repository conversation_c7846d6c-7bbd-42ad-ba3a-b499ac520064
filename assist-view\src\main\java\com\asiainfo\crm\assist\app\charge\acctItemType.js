(function (vita) {
    var acctItemType = vita.Backbone.BizView.extend({
        events: {
            "click #queryBtn": "_qryAcctItemType",
            "click #resetBtn": "_clearConditions",
            "click #addBtn": "_addPopup",
            "click #editBtn": "_editPopup",
            "click #deleteBtn": "_deleteSubmit",
            "click #addSubmitBtn": "_addSubmit",
            "click #editSubmitBtn": "_editSubmit",
            "click button.close": "_closeBtnClick",
            "click #closeBtn": "_closeBtnClick",
        },
        _initialize: function () {
            var widget = this,
                el = $(widget.el);
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            this._clearConditions();
            this._qryAcctItemType();
        },
        global: {
            totalNumber: 0,
            pageIndex: 1,
            pageSize: 5,
            datetime: 'time',
            date: 'date',
        },

        /**
         * 打开添加弹框
         */
        _addPopup: function () {
            var widget = this,
                el = $(widget.el);
            widget._cleanPopupData();
            widget._openPopup("add");
        },
        _openPopup: function (action) {
            var widget = this,
                el = $(widget.el);

            switch (action) {
                case "add":

                    el.find(".titlefont").html("添加");
                    el.find(".hideAndShow").hide();
                    el.find("#addSubmitBtn").show();
                    el.find("#_version").attr("disabled","disabled");
                    el.find("#editSubmitBtn").hide();
                    el.find(".popup").show();
                    break;
                case "edit":

                    el.find(".titlefont").html("编辑");
                    el.find(".hideAndShow").show();
                    el.find("#_acctItemTypeId").attr("disabled","disabled");
                    el.find("#addSubmitBtn").hide();
                    el.find("#editSubmitBtn").show();
                    el.find(".popup").show();
                    break;
                default:
                    break;
            }
        },

        /**
         * 新增
         * @private
         */
        _addSubmit: function () {
            var widget = this,el = $(widget.el);
            var param = widget._getPopupData();
            if(!this._checkParam(param)){
                return;
            }
            var options = el.data("data");
            param.createStaff = options.staffId;
            widget.callService("createAcctItemType", JSON.stringify(param), function (res) {
                    if("0" == res.resultCode){
                        widget.popup("添加成功");
                        widget._closeBtnClick();
                        widget._qryAcctItemType();
                    }else{
                        widget.popup(res.resultMsg);
                    }
            }, {async: false, mask: false});
        },
        /**
         * 编辑更新
         * @private
         */
        _editSubmit: function () {
            var widget = this,el = $(widget.el);
            var param = widget._getPopupData();
            param["acctItemTypeId"] = el.find("#_acctItemTypeId").val().trim()
            if(!this._checkParam(param)){
                return;
            }
            var options = el.data("data");
            param.updateStaff = options.staffId;
            widget.callService("updateAcctItemType", JSON.stringify(param), function (res) {
                if (res) {
                    if("0" == res.resultCode){
                        widget.popup("修改成功");
                        widget._closeBtnClick();
                        widget._qryAcctItemType();
                    }else{
                        widget.popup(res.resultMsg);
                    }
                }
            }, {async: false, mask: false});
        },
        /**
         * 打开编辑弹框
         */
        _editPopup: function () {
            var widget = this,
                el = $(widget.el);
            var payment = el.find("table").find("input[name='payment']:checked");
            if (payment.length > 0) {
                widget._cleanPopupData();
                var tr = payment.parent().parent().parent();
                el.find("#_acctItemTypeId").val(tr.find("td:eq(1)").text());
                el.find("#_acctItemClassId").val(tr.find("td:eq(2)").text());
                el.find("#_partnerId").val(tr.find("td:eq(3)").text());
                el.find("#_name").val(tr.find("td:eq(4)").text());
                el.find("#_chargeMark").val(tr.find("td:eq(5)").attr("value"));
                el.find("#_totalMark").val(tr.find("td:eq(6)").attr("value"));
                el.find("#_acctItemTypeCode").val(tr.find("td:eq(7)").text());
                el.find("#_priority").val(tr.find("td:eq(8)").text());
                el.find("#_parentItemTypeId").val(tr.find("td:eq(9)").text());
                var statusCds = document.getElementById("_statusCd").options;
                for (i=0;i<statusCds.length;i++){
                    if (statusCds[i].text == tr.find("td:eq(12)").text()){
                        statusCds[i].selected = true;
                    }
                }
                var isInvoiceItems = document.getElementById("_isInvoiceItem").options;
                for (i=0;i<isInvoiceItems.length;i++){
                    if (isInvoiceItems[i].text == tr.find("td:eq(12)").text()){
                        isInvoiceItems[i].selected = true;
                    }
                }
                var isLimitRedos = document.getElementById("_isLimitRedo").options;
                for (i=0;i<isLimitRedos.length;i++){
                    if (isLimitRedos[i].text == tr.find("td:eq(13)").text()){
                        isLimitRedos[i].selected = true;
                    }
                }
                var isLimitChanges = document.getElementById("_isLimitChange").options;
                for (i=0;i<isLimitChanges.length;i++){
                    if (isLimitChanges[i].text == tr.find("td:eq(14)").text()){
                        isLimitChanges[i].selected = true;
                    }
                }
                el.find("#_ifAdjust").val(tr.find("td:eq(15)").text());
                widget._openPopup("edit");
            } else {
                widget.popup("请选择要编辑的一行!");
                return;
            }
        },
        /**
         * 弹出框关闭事件
         */
        _closeBtnClick: function () {
            var widget = this,
                el = $(widget.el);
            el.find(".popup").hide();
            el.find(".popup1").hide();
        },

        /**
         * 清除弹框数据
         */
        _cleanPopupData: function () {
            var widget = this,
                el = $(widget.el);
            el.find("form[name=_popupForm]").resetForm();
            el.find("#_lanId").val('').attr("value","");
        },


        /**
         * 重置查询条件
         */
        _clearConditions: function () {
            var widget = this,
                el = $(widget.el);
            el.find("#acctItemClassId").val('');
            el.find("#partnerId").val('');
            el.find("#name").val('');
            el.find("#chargeMark").val('');
            el.find("#totalMark").val('');
            el.find("#acctItemTypeCode").val('');
            el.find("#isInvoiceItem").val('');
            el.find("#isLimitChange").val('');
            el.find("#isLimitRedo").val('');
            el.find("#statusCd").val('');
        },
        /**
         * 获取弹出框数据
         * @private
         */
        _getPopupData: function () {
            var widget = this,
                el = $(widget.el);
            var params = {
                "acctItemClassId": el.find("#_acctItemClassId").val().trim(),
                "partnerId": el.find("#_partnerId").val().trim(),
                "name": el.find("#_name").val().trim(),
                "chargeMark": el.find("#_chargeMark").val().trim(),
                "priority": el.find("#_priority").val().trim(),
                "parentItemTypeId": el.find("#_parentItemTypeId").val().trim(),
                "totalMark": el.find("#_totalMark").val().trim(),
                "acctItemTypeCode": el.find("#_acctItemTypeCode").val().trim(),
                "isInvoiceItem": el.find("#_isInvoiceItem").val().trim(),
                "isLimitChange": el.find("#_isLimitChange").val().trim(),
                "isLimitRedo": el.find("#_isLimitRedo").val().trim(),
                "statusCd": el.find("#_statusCd").val().trim(),
                "ifAdjust": el.find("#_ifAdjust").val().trim(),
            };
            return params;
        },
        _checkParam: function (param){
            var widget = this,
                el = $(widget.el);
            var soUtil = widget.require("soUtil");
            if(soUtil.isNullOrEmpty(param.acctItemTypeCode)||soUtil.isNullOrEmpty(param.acctItemClassId)||soUtil.isNullOrEmpty(param.name)||soUtil.isNullOrEmpty(param.chargeMark)){
                widget.popup("参数必填，请检查后重新提交!");
                return false;
            }
            return true;
        },
        /**
         * 查询
         */
        _qryAcctItemType: function () {
            var widget = this,
                el = $(widget.el);
            var param = widget._getConds();
            widget.refreshPart("qryAcctItemTypePageInfo", param, "#loadData",
                "reqList", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = $(res).find("table").data("totalNumber");
                    if (parseInt(totalNumber) > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                param.pageInfo.pageIndex = _pageIndex;
                                param.pageInfo.pageSize = _recordNumber;
                                widget.refreshPart("qryAcctItemTypePageInfo", param, "div[name=tableDiv]",null,null,{mask: true});

                                ;
                            }
                        });
                        r.find("#pageInfo").append(e.getElement());
                    }
                }, {mask: true});
        },
        _getConds: function () {
            var widget = this, el = $(widget.el);
            var param = {
                "pageInfo": {
                    "pageIndex": widget.global.pageIndex,
                    "pageSize": widget.global.pageSize,
                }
            };
            this._chooseParams(param,"acctItemClassId",$("#acctItemClassId").val());
            this._chooseParams(param,"partnerId",$("#partnerId").val());
            this._chooseParams(param,"name",$("#name").val());
            this._chooseParams(param,"chargeMark",$("#chargeMark").val());
            this._chooseParams(param,"totalMark",$("#totalMark").val());
            this._chooseParams(param,"acctItemTypeCode",$("#acctItemTypeCode").val());
            this._chooseParams(param,"isInvoiceItem",$("#isInvoiceItem").val());
            this._chooseParams(param,"isLimitChange",$("#isLimitChange").val());
            this._chooseParams(param,"isLimitRedo",$("#isLimitRedo").val());
            this._chooseParams(param,"statusCd",$("#statusCd").val());
            return param;
        },

        _chooseParams: function(param,name,val) {
            if (null!=val&&val!="") {
                param[name] = val
            }
        },

        _getNowFormatDate: function() {
            var date = new Date();
            var seperator1 = "-";
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var strDate = date.getDate();
            var hour = date.getHours();
            var mins=date.getMinutes();
            var seconds = date.getSeconds();
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            if (strDate >= 0 && strDate <= 9) {
                strDate = "0" + strDate;
            }
            if (mins >= 0 && mins <= 9) {
                mins = "0" + mins;
            }
            if (seconds >= 0 && seconds <= 9) {
                seconds = "0" + seconds;
            }
            if (hour >= 0 && hour <= 9) {
                hour = "0" + hour;
            }
            var currentdate = year + seperator1 + month + seperator1 + strDate
                +" 00" + ":00" +":00";
            return currentdate;
        },
    });
    vita.widget.register("acctItemType", acctItemType, true);})(window.vita);
