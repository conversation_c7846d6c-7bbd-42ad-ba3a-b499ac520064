package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IResourceSMO;
import com.asiainfo.crm.service.intf.ISoReceiptSMO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by liyong on 2017/11/24.
 */
@Component("vita.reverseInvoic")
public class ReverseInvoic extends AbstractComponent {

    private final Logger logger = LoggerFactory.getLogger(ReverseInvoic.class);

    @Autowired
    private IResourceSMO resourceSMO;
    @Autowired
    private ISoReceiptSMO soReceiptSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    /**
     * 根据购物车流水号查询发票信息
     * @param jsonStr
     * @return
     * @throws Exception
     */
    public Map qryInvoiceListByCoNbr(String jsonStr) throws Exception {
        //根据购物号号查询发票信息
        String preAccNumListStr = soReceiptSMO.queryReceiptByCustOrderNbr(jsonStr);

        Map<String, Object> resultObjectMap = jsonConverter.toBean(preAccNumListStr,Map.class);
        Map<String, Object> options = Maps.newHashMap();

        List resultObject = (List) MapUtils.getObject(resultObjectMap, "resultObject");

        options.put("invoiceList", resultObject);

        return options;
    }

    /**
     * 发票作废
     * @param jsonString
     * @return
     */
    public String deleteReceipt(String jsonString){
        String resultJson = soReceiptSMO.deleteReceipt(jsonString);
        return resultJson;
    }
}
