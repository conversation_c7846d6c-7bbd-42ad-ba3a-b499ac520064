<link rel="stylesheet" type="text/css" href="../bundles/assist/app/order/ActionChain.css">
<div data-widget="orderInfoHis" class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
    <p class="vita-data">{"data":$options}</p>
    <div class="box-maincont">
        <div class="homenofood">
            <div class="page_main notopnav">
                <!--填单start-->
                <div class="col-lg-12">
                    <div class="box-item dzlaction">
                        <div class="container-fluid row">
                            <div class="wmin_content overflowh padt15">
                                <ol class="breadcrumb">
                                    <li><span class="glyphicon glyphicon-home mar15"></span>调用历史</li>
                                    <li class="active">订单详情</li>
                                </ol>
                                <div class="col-md-12 marb10">
                                    <h5 class="textcolorgreen">入参：</h5>
                                    <div>
                                        <textarea class="form-control mart10" rows="3" >$!{options.invokeParam}</textarea>
                                    </div>

                                </div>
                                <div class="col-md-12 marb10">
                                    <h5 class="textcolorgreen">出参：</h5>
                                    <div>
                                        <textarea class="form-control mart10" rows="3" >$!{options.chainResult}</textarea>
                                    </div>

                                </div>
                                <div class="col-md-12 marb10">
                                    <h5 class="textcolorgreen">调用前：</h5>
                                    <div>
                                        <textarea class="form-control mart10" rows="3" >$!{options.beforeChain}</textarea>
                                    </div>

                                </div>
                                <div class="col-md-12 marb10">
                                    <h5 class="textcolorgreen">调用后：</h5>
                                    <div>
                                        <textarea class="form-control mart10" rows="3" >$!{options.afterChain}</textarea>
                                    </div>

                                </div>
                            </div>


                        </div>
                    </div>
                    <!--填单end-->


                </div>

            </div>
        </div>
    </div>

