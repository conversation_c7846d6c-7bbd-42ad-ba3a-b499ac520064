package com.asiainfo.crm.assist.app.query;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.asiainfo.crm.service.intf.IResourceSMO;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import com.google.common.collect.Maps;

/**
 * Created by wenhy on 2017/7/10.
 */
@Component("vita.materialCodeQuery")
public class MaterialCodeQuery extends AbstractComponent {

    private static final Logger LOGGER = LoggerFactory.getLogger(MaterialCodeQuery.class);

    @Autowired
    private IResourceSMO resourceSMO;

    @Autowired
    private IProdInstSMO prodInstSMO;

    @Autowired
    private IOrderQuerySMO orderQuerySMO;
    
    @Autowired
    private ISoQuerySMO  iSoQuerySMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map materialCodeQuery(String jsonStr) throws Exception {
        String materialInfoStr = resourceSMO.queryMaterialInfo(jsonStr);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(materialInfoStr);
        if(null != resultObjectMap && "0".equals(String.valueOf(resultObjectMap.get("handleResultCode")))
                && !"A".equals(String.valueOf(resultObjectMap.get("statusCd")))
                && !"L".equals(String.valueOf(resultObjectMap.get("statusCd")))) {
            Map paramMap = jsonConverter.toBean(jsonStr, Map.class);
            paramMap.remove("regionId");
            paramMap.remove("routeParam");
            //先查资产实例数据
            String resInstRelStr = prodInstSMO.qryOfferProdResInstByMktResInstNbr(jsonConverter.toJson(paramMap));
            Map<String, Object> resInstRelMap = (Map<String, Object>) resolveResult(resInstRelStr);
            if(null != resInstRelMap && (null != resInstRelMap.get("prodResInstRels") || null != resInstRelMap.get("offerResInstRels"))) {
                if(null != resInstRelMap.get("prodResInstRels")) {
                    List<Map<String, Object>> prodResInstRels = (List<Map<String, Object>>)resInstRelMap.get("prodResInstRels");
                    if(prodResInstRels.size() > 0) {
                        for(Map<String, Object> map : prodResInstRels) {
                            if(null != map.get("statusCd") && "1000".equals(map.get("statusCd").toString())) {
                                Map<String, Object> prodInstIdMap = new HashMap();
                                Long prodInstId = Long.valueOf(map.get("prodInstId").toString());
                                prodInstIdMap.put("prodInstId", String.valueOf(prodInstId));
                                String str = prodInstSMO.qryProdInstListAtom(jsonConverter.toJson(prodInstIdMap));
                                Map<String, Object> resultObjectMaps = (Map<String, Object>) resolveResult(str);
                                List<Map<String, Object>> prodInstsList = (List<Map<String, Object>>)resultObjectMaps.get("prodInsts");
                                if (null != prodInstsList && prodInstsList.size() > 0) {
                                    resultObjectMap.put("bindAccNum", prodInstsList.get(0).get("accNum"));
                                    if (null !=prodInstsList.get(0).get("regionId")) {
                                        this.setRegionNameById(String.valueOf(prodInstsList.get(0).get("regionId")), resultObjectMap);
                                    }
                                }
                                if (null == map.get("lastOrderItemId")) {
                                    continue;
                                }
                                Long lastOrderItemId = Long.valueOf(map.get("lastOrderItemId").toString());
                                this.setSoInfoByOrderItemId(lastOrderItemId, resultObjectMap);
                            }
                        }
                    }
                } else if(null != resInstRelMap.get("offerResInstRels")){
                    List<Map<String, Object>> offerResInstRels = (List<Map<String, Object>>)resInstRelMap.get("offerResInstRels");
                    if(offerResInstRels.size() > 0) {
                        for(Map<String, Object> map : offerResInstRels) {
                            if(null != map.get("statusCd") && "1000".equals(map.get("statusCd").toString())) {
                                Map offerInstParam = new HashMap();
                                offerInstParam.put("offerInstId", map.get("offerInstId"));
                                String prodInstRet = prodInstSMO.qryOfferProdInstRelListLocal(jsonConverter.toJson(offerInstParam));
                                Map prodInstMap = (Map)resolveResult(prodInstRet);
                                if(null != prodInstMap.get("offerProdInstRelDetailVos")) {
                                    List<Map> offerProdInstRelDetailVos = (List)prodInstMap.get("offerProdInstRelDetailVos");
                                    if(null != offerProdInstRelDetailVos) {
                                        for(Map oPmap : offerProdInstRelDetailVos) {
                                            if(null != oPmap.get("statusCd") && "1000".equals(oPmap.get("statusCd").toString())) {
                                                Map prodInst = (Map)oPmap.get("prodInst");
                                                if(null != prodInst.get("statusCd") && 
                                                		("100000".equals(prodInst.get("statusCd").toString()) || "120000".equals(prodInst.get("statusCd").toString()))) {
                                                    resultObjectMap.put("bindAccNum", prodInst.get("accNum"));
                                                    this.setRegionNameById(String.valueOf(prodInst.get("regionId")), resultObjectMap);
                                                }
                                            }
                                        }
                                    }
                                }
                                if (null == map.get("lastOrderItemId")) {
                                    continue;
                                }
                                Long lastOrderItemId = Long.valueOf(map.get("lastOrderItemId").toString());
                                this.setSoInfoByOrderItemId(lastOrderItemId, resultObjectMap);
                            }
                        }
                    }
                }
            } else {
                //没实例再查受理过程数据
                String ordResInstRelStr = orderQuerySMO.qryOrdResInstRels(jsonConverter.toJson(paramMap));
                Map<String, Object> ordResInstRelMap = (Map<String, Object>) resolveResult(ordResInstRelStr);
                if(null != ordResInstRelMap && (null != ordResInstRelMap.get("prodResInstRels") || null != ordResInstRelMap.get("offerResInstRels"))) {
                    if (null != ordResInstRelMap.get("prodResInstRels")) {
                        List<Map<String, Object>> prodResInstRels = (List<Map<String, Object>>) ordResInstRelMap.get("prodResInstRels");
                        if (prodResInstRels.size() > 0) {
                            for (Map<String, Object> map : prodResInstRels) {
                                if (null != map.get("operType") && "1000".equals(map.get("operType"))) {
                                    Map<String, Object> prodInstIdMap = new HashMap();
                                    Long prodInstId = Long.valueOf(map.get("prodInstId").toString());
                                    prodInstIdMap.put("prodInstId", String.valueOf(prodInstId));
                                    String str = orderQuerySMO.qryOrdProdInst(jsonConverter.toJson(prodInstIdMap));
                                    Map<String, Object> resultObjectMaps = (Map<String, Object>) resolveResult(str);
                                    if (null != resultObjectMaps.get("ordProdInsts")) {
                                        List<Map<String, Object>> prodInstsList = (List<Map<String, Object>>) resultObjectMaps.get("ordProdInsts");
                                        if (null != prodInstsList && prodInstsList.size() > 0) {
                                            resultObjectMap.put("bindAccNum", prodInstsList.get(0).get("accNum"));
                                            if (null != prodInstsList.get(0).get("regionId")) {
                                                this.setRegionNameById(String.valueOf(prodInstsList.get(0).get("regionId")), resultObjectMap);
                                            }
                                        }
                                    }
                                    if (null == map.get("orderItemId")) {
                                        continue;
                                    }
                                    Long orderItemId = Long.valueOf(map.get("orderItemId").toString());
                                    this.setSoInfoByOrderItemId(orderItemId, resultObjectMap);
                                }
                            }
                        }
                    } else if (null != ordResInstRelMap.get("offerResInstRels")) {
                        List<Map<String, Object>> offerResInstRels = (List<Map<String, Object>>) ordResInstRelMap.get("offerResInstRels");
                        if (offerResInstRels.size() > 0) {
                            for (Map<String, Object> map : offerResInstRels) {
                                if (null != map.get("operType") && "1000".equals(map.get("operType"))
                                        && null != map.get("statusCd") && "1000".equals(map.get("statusCd").toString())) {
                                    Map offerInstParam = new HashMap();
                                    offerInstParam.put("offerInstId", map.get("offerInstId"));
                                    String prodInstRet = orderQuerySMO.qryOrdOfferProdInstRelForAssist(jsonConverter.toJson(offerInstParam));
                                    Map prodInstMap = (Map) resolveResult(prodInstRet);
                                    if (null != prodInstMap.get("prodInsts")) {
                                        List<Map> prodInsts = (List) prodInstMap.get("prodInsts");
                                        for (Map oPmap : prodInsts) {
                                            if (null != oPmap.get("operType") && "1000".equals(oPmap.get("operType").toString())) {
                                                Map prodInst = (Map) oPmap.get("prodInst");
                                                resultObjectMap.put("bindAccNum", prodInst.get("accNum"));
                                                this.setRegionNameById(String.valueOf(prodInst.get("regionId")), resultObjectMap);
                                            }
                                        }
                                    } else {
                                        //过程表没有再去实例表查
                                        prodInstRet = prodInstSMO.qryOfferProdInstRelListLocal(jsonConverter.toJson(offerInstParam));
                                        prodInstMap = (Map) resolveResult(prodInstRet);
                                        if (null != prodInstMap.get("offerProdInstRelDetailVos")) {
                                            List<Map> offerProdInstRelDetailVos = (List) prodInstMap.get("offerProdInstRelDetailVos");
                                            if (null != offerProdInstRelDetailVos) {
                                                for (Map oPmap : offerProdInstRelDetailVos) {
                                                    if (null != oPmap.get("statusCd") && "1000".equals(oPmap.get("statusCd").toString())) {
                                                        Map prodInst = (Map) oPmap.get("prodInst");
                                                        if (null != prodInst.get("statusCd") && "100000".equals(prodInst.get("statusCd").toString())) {
                                                            resultObjectMap.put("bindAccNum", prodInst.get("accNum"));
                                                            this.setRegionNameById(String.valueOf(prodInst.get("regionId")), resultObjectMap);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    if (null == map.get("orderItemId")) {
                                        continue;
                                    }
                                    Long orderItemId = Long.valueOf(map.get("orderItemId").toString());
                                    this.setSoInfoByOrderItemId(orderItemId, resultObjectMap);
                                }
                            }
                        }
                    }
                }
            }
        }
        Map<String, Object> options = Maps.newHashMap();
        options.put("materialInfo", resultObjectMap);
        return options;
    }

    private void setRegionNameById(String regionId, Map resultObjectMap) throws IOException {
        Map<String, Object> regionIdMap = new HashMap();
        regionIdMap.put("regionId", regionId);
        String strs = jsonConverter.toJson(regionIdMap);
        String regionIdStr = iSoQuerySMO.qryCommonRegionByIds(regionIdMap);
        String regionIdStrs =  (String) resolveResult(regionIdStr);
        Map<String, Object> regionIdMaps = jsonConverter.toBean(regionIdStrs, Map.class);
        if (null !=  regionIdMaps.get("regionName")) {
            resultObjectMap.put("soRegionName", regionIdMaps.get("regionName"));
        }else{
            resultObjectMap.put("soRegionName", "");
        }
    }

    private void setSoInfoByOrderItemId(Long orderItemId, Map resultObjectMap) throws IOException {
        List<Long> orderItemIds = new ArrayList<>();
        orderItemIds.add(orderItemId);
        String re = orderQuerySMO.queryOrderItemsByOrderItemsIds(jsonConverter.toJson(orderItemIds));
        List<Map> resultObject = (List<Map>)resolveResult(re);
        if(null != resultObject && resultObject.size() > 0) {
            Map inParam = new HashMap();
            inParam.put("custOrderId", resultObject.get(0).get("custOrderId"));
            String coStr = orderQuerySMO.qryCustOrderByCoIdOrCoNbr(jsonConverter.toJson(inParam));
            Map custOrder = (Map)resolveResult(coStr);
            resultObjectMap.put("custOrderNbr", custOrder.get("custOrderNbr"));
            resultObjectMap.put("orderItemId", orderItemId);
            resultObjectMap.put("soStaffName", custOrder.get("createStaffName"));
            resultObjectMap.put("soChannelName", custOrder.get("createOrgName"));
            resultObjectMap.put("soDate", custOrder.get("acceptDate"));
        }
    }

}
