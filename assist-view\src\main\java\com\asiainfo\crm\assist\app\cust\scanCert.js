(function(vita) {
    var websocketclient = null;
    var isOnMessage="0";
    var pageInit="0";
	var scanCert = vita.Backbone.BizView.extend({
		events : {
			"click #scanCert" : "_scanCert",  //读卡
			"click #closeBtn" : "_closePage",
			"click #submitBtn" : "_submit",
		},
		_initialize : function() {		
			var widget = this,
			element = $(widget.el);
            var certOpt = element.data("data");
            var useDeskTop=certOpt.useDeskTop;
			widget.model.set("scaned", 0);
            if(useDeskTop=="0"){
				widget._isReadyCont();
				widget._init();
            }else{
                var sp =  element.find("#controlsIdentify");
                sp.html("请确认已启动智慧桌面");
                widget._deskTopInit('0');
            }
            var container = element.parent();
            if (container.length) {
                container.css({
                    overflow : 'auto'
                });
            }
            element.find("#panel").slideToggle("slow");
		},
		global : {
			addCustUrl : "../so/addCust"
		},
		_submit : function (){
			var widget = this,
			element = $(widget.el);				
			var scaned =  widget.model.get("scaned");
			if(scaned == 0){
				widget.popup("请扫描证件后在点击确认");
				return;
			}			
			widget._closePage();					
		},
		_custQuery : function(num) {
			var widget = this,
			    element = $(widget.el);
			var customer = widget.model.get("customer");
			var params = widget._getConds(num, customer);
			var compData = element.data("data"), oldCertNumber = customer.oldCertNumber;
			if (compData.newPermantOn == "Y" && compData.certOperType == "custQry" && oldCertNumber) {
				params.oldCertNumber = oldCertNumber;
				params.certType = "50";
				params.queryType = "identity";
			}
			widget.callService("qryCustomerList", params, function(res) {				
				if(res && res.customers && res.customers[0]){
					res.customers[0].custAuthSwitch = "N";
					widget.dispatchEvent("refreshCustView", res.customers[0]);					
					widget._closePage();					
				}else{				
					widget._showAddCustPage();					
					widget._closePage();					
				}
			}, {
				async : false
			});
		},
		
		_showAddCustPage : function() {
			var widget = this,
				element = $(widget.el);			
			var  customer =  widget.model.get("customer");			
			var option = {
				url : widget.global.addCustUrl,
				params : customer,
				onClose : function(res) {
					var addCust = $(res).closest("[data-widget=addCust]");
					if (addCust.length) {						
						var cust = addCust.addCust("getValue");						
						if (!cust || !cust.custId ) {							
							return false;
						}
						//TODO 新增客户之后  
						//过滤不要验证
						cust.custAuthSwitch = "N";
						widget.dispatchEvent("refreshCustView", cust);						
					}
				}
			};			
			widget.dialog(option);			
			return false;
		},
		
		_getConds : function(condValue, customer) {
			var widget = this,
				gSession = widget.gSession;
			var param = {
				value : condValue,
				glbSessionId : gSession.glbSessionId,
				certType : "1"
			};
			if (customer && customer.certNumber && customer.certType) {
				param.value = customer.certNumber;
				param.queryType = "identity";
				param.certType = customer.certType;
			}
			return param;
		},
		
		_init : function () {  // 初始化
			var widget = this,
			element = $(widget.el);
			isRead=0;
			// 绑定按钮事件
			
	        var isReady=widget._isReadyCont(); 
	        
	        
			if (isReady) {
				return;
	        }
			else{
				var hdSrc = "http://133.37.135.116/四川电信(鸿达).exe";
			    var krSrc = "http://133.37.135.116/四川电信(卡尔).exe";
			    var ynSrc = "http://133.37.135.116/四川电信(因纳伟盛).exe";
			    var hdSrcA =  element.find("#hdSrc")[0];
			    var krSrcA =  element.find("#krSrc")[0];
			    var ynSrcA =  element.find("#ynSrc")[0];
			    var loadInfo =  element.find("#loadInfo")[0];
			    hdSrcA.style.display = 'inline-block';
			    krSrcA.style.display = 'inline-block';
			    ynSrcA.style.display = 'inline-block';
			    loadInfo.style.display = 'inline-block';
			    hdSrcA.href = hdSrc;
			    krSrcA.href = krSrc;
			    ynSrcA.href = ynSrc;
			}
			
		},
		_connect : function() {
			var widget = this,
			element = $(widget.el);				
			var CertCtl =  element.find("#CertCtl")[0] ;
			var ret = CertCtl.connect();				
			ret = widget._JStrToObj(ret);	
			widget._DisplayInfo(ret);		
			return ret;
		},
		
		_JStrToObj : function(str){
			return eval("(" + str + ")");
		},
		
		_DisplayInfo : function (ret){
			 var widget = this,
			 element = $(widget.el);			
			if (ret.resultFlag==0){
				//成功
			}else{
				alert(ret.errorMsg);
			}
		},
		_scanCert : function () {
			
		 	/*var widget = this,
			element = $(widget.el);		
		 	widget.model.set("scaned", 0);
			var CertCtl =  element.find("#CertCtl")[0] ;			
			widget._connect();
			var ret = CertCtl.readCert();				
			ret = widget._JStrToObj(ret);
			if (ret.resultFlag==-1){		
				widget._DisplayInfo(ret);		
			}else{
				
				widget._fillForm(ret);
				CertCtl.disconnect();
				widget.model.set("scaned", 1);
			}*/
			var widget = this,
				element = $(widget.el);
            var certOpt=element.data("data");
            var useDeskTop=certOpt.useDeskTop;
			widget.model.set("scaned", 0);
            if(useDeskTop=="0") {
				var CertCtl =  element.find("#CertCtl")[0] ;
				var soConst = vita.widget.require("soConst");
				var soUtil = widget.require("soUtil");
				var connect=widget._connect();
				if (connect.isCloud != undefined && connect.isCloud == "0") {
					if (soConst.get("CONST_CLOUD_DIRECT_INFO") == "0") {
						widget._cloudRead();
						widget.model.set("scaned", 1);
					} else {
						var ret = soUtil.cloudRead(widget, CertCtl);
						ret.resultContent.isCloud = "0";
						soUtil.fillForm(ret, widget);
						soUtil.saveCertLog(ret, widget, connect.venderId, connect.signature);
						CertCtl.disconnect();
						widget.model.set("scaned", 1);
					}

				} else {
					var ret = CertCtl.readCert();
					ret = widget._JStrToObj(ret);
					if (ret.resultFlag==-1){
						widget._DisplayInfo(ret);
					}else{
						widget._fillForm(ret);
						CertCtl.disconnect();
						widget.model.set("scaned", 1);
					}
				}
            }else{
                widget._deskTopScanCert();
            }
		},
		_cloudRead : function () {
			var widget = this,element = $(widget.el),
                soConst = widget.require("soConst");
			var appSecret = "30b5c231a8ea42c09c87f75d22ebc9ea"; // appId对应的加密密钥
			var appId = "1035";
			var nonce = "jfoiiuylkjljpohi";
            var secretKey = "D34AE719CE3246E40729411452759F86D34AE719CE3246E4";
            if("Y" == soConst.getMda("GZ_CLOUDREAD_SWITCH")){
                appSecret = soConst.getMda("IDENTITY_NFC_APP_SECRET");
                appId = soConst.getMda("IDENTITY_NFC_APP_ID");
                nonce = soConst.getMda("IDENTITY_NFC_NONCE");
                secretKey = soConst.getMda("IDENTITY_NFC_SECRET_KEY");
            }
			var date = new Date();
			var timestamp = ""+date.getTime();
			var business = "{'areaCode':'020','busiSerial':'12345','channelCode':'2001','clientIP':'','deviceModel':'llllllllsdfsdffsdfjka  sdfjfsdfsdfsddddddddddd','deviceSerial':'','osType':'','srcSystem':'CRM','staffCode':'110011','teminalType':'PC'}";
			var signatureData = appId + appSecret + business + nonce + timestamp;
			var shaObj = new jsSHA("SHA-1", "TEXT");
			shaObj.update(signatureData);
			var signatureData = shaObj.getHash("HEX");
			json = CertCtl.cloudReadCert(appId,timestamp,nonce,business,signatureData);
			json = eval('(' + json + ')');
			var cert;
			if(json.resultFlag == 0){
				var IDCard = json.resultContent.certificate;
				var url = window.location.href;
				var data = CertCtl.DecryptInfo(IDCard,secretKey);
				certIdent= CertCtl.photoMagick(data);
				try //Internet Explorer
				{
					xmlDoc=new ActiveXObject("Microsoft.XMLDOM");
					xmlDoc.async="false";
					xmlDoc.loadXML(data);
					cert=widget._filCloudlForm();
				}
				catch(e){
					try //Firefox, Mozilla, Opera, etc.
					{
						parser=new DOMParser();
						xmlDoc=parser.parseFromString(data,"text/xml");
						cert=widget._filCloudlForm();
					}
					catch(e){
						widget.popup(e.errorMsg);
					}
				}
			}else{
				widget.popup(json.errorMsg);
			}
			json = CertCtl.getStatus();
			json = eval('(' + json + ')');
			if(json.resultFlag == 0){
			}else{
				document.getElementById("ActivityLTo").value=fmtDate(new Date());
				widget.popup(json.errorMsg);
				return;
			}
			json = CertCtl.disconnect();
			json = eval('(' + json + ')');
			if(json.resultFlag != 0){
				widget.popup(json.errorMsg);
			}
		},
		_filCloudlForm : function () {
			var widget = this,
				element = $(widget.el);

			var pName = xmlDoc.getElementsByTagName("partyName")[0].childNodes[0].nodeValue;
			var pSex = xmlDoc.getElementsByTagName("gender")[0].childNodes[0].nodeValue;
			var pNation = xmlDoc.getElementsByTagName("nation")[0].childNodes[0].nodeValue;
			var pBorn = xmlDoc.getElementsByTagName("bornDay")[0].childNodes[0].nodeValue;
			var pCardNo = xmlDoc.getElementsByTagName("certNumber")[0].childNodes[0].nodeValue;
			var pAddress = xmlDoc.getElementsByTagName("certAddress")[0].childNodes[0].nodeValue;
			var pPolice = xmlDoc.getElementsByTagName("certOrg")[0].childNodes[0].nodeValue;
			var pActivityLFrom = xmlDoc.getElementsByTagName("effDate")[0].childNodes[0].nodeValue;
			var pActivityLTo=xmlDoc.getElementsByTagName("expDate")[0].childNodes[0].nodeValue;
			var pPhotoPic=xmlDoc.getElementsByTagName("identityPic")[0].childNodes[0].nodeValue;

			var pPhotoBuffer;
			if(certIdent==""||certIdent==null){
				pPhotoBuffer=xmlDoc.getElementsByTagName("identityPic")[0].childNodes[0].nodeValue;
			}else{
				pPhotoBuffer=certIdent;
			}


			pBorn=pBorn.substring(0,4)+"-"+pBorn.substring(4,6)+"-"+pBorn.substring(6,8)+" 00:00:00";
			pActivityLFrom=pActivityLFrom.substring(0,4)+"-"+pActivityLFrom.substring(4,6)+"-"+pActivityLFrom.substring(6,8)+" 00:00:00";
			if(pActivityLTo.indexOf('长期')>= 0){
				pActivityLTo = '3000-01-01 00:00:00';
			}else{
				pActivityLTo=pActivityLTo.substring(0,4)+"-"+pActivityLTo.substring(4,6)+"-"+pActivityLTo.substring(6,8)+" 00:00:00";
			}
			var cert={};
			cert.resultFlag=0;
			cert.resultContent={};
			cert.resultContent.partyName=pName;
			cert.resultContent.gender=pSex;
			cert.resultContent.nation=pNation;
			cert.resultContent.bornDay=pBorn;
			cert.resultContent.certAddress=pAddress;
			cert.resultContent.certNumber=pCardNo;
			cert.resultContent.certOrg=pPolice;
			cert.resultContent.effDate=pActivityLFrom;
			cert.resultContent.expDate=pActivityLTo;
			cert.resultContent.cardImg=pPhotoBuffer;
			cert.resultContent.identityPic=pPhotoPic;

			if(pCardNo.length<18){
				widget.popup("身份证长度小于18位，不符合实名制要求！");
				return false;
			}
			while(pName.indexOf('??')>= 0){
				pName = pName.replace('??','·');
			}
			var name =  element.find("#Name")[0] ;
			name.value = pName;
			var sex =  element.find("#Sex")[0];
			if(pSex == 1){
				sex.value = "男";
			}else{
				sex.value = "女";
			}
			var Nation =  element.find("#Nation")[0] ;
			Nation.value = pNation;
			var Born =  element.find("#Born")[0] ;
			Born.value = pBorn;
			var Address =  element.find("#Address")[0] ;
			Address.value = pAddress;
			var CardNo =  element.find("#CardNo")[0] ;
			CardNo.value = pCardNo;
			var Police =  element.find("#Police")[0] ;
			Police.value = pPolice;
			var ActivityLFrom =  element.find("#ActivityLFrom")[0] ;
			ActivityLFrom.value = pActivityLFrom;
			var ActivityLTo =  element.find("#ActivityLTo")[0] ;
			ActivityLTo.value = pActivityLTo;
			var IdPhoto =  element.find("#IdPhoto")[0] ;
			IdPhoto.src = "data:image/jpg;base64," + pPhotoBuffer;
			//存起来
			var customer = cert.resultContent;
			widget.model.set("customer", customer);

		},
		_fillForm : function (ret) {  
			  var widget = this,
			  element = $(widget.el);
			  
			  widget._DisplayInfo(ret);	 
			  var pName=ret.resultContent.partyName; 
			  var pSex=ret.resultContent.gender;
			  var pNation=ret.resultContent.nation;
			  var pBorn=ret.resultContent.bornDay;
			  var pAddress=ret.resultContent.certAddress;
			  var pCardNo=ret.resultContent.certNumber;
			  var pPolice=ret.resultContent.certOrg;
			  var pActivityLFrom=ret.resultContent.effDate; 
			  var pActivityLTo=ret.resultContent.expDate; 			  

			  var name =  element.find("#Name")[0] ;
			  name.value = pName; 
			  var sex =  element.find("#Sex")[0];			 
			  if(pSex == 1){
				  sex.value = "男";
			  }else{
				  sex.value = "女";
			  }
			  var Nation =  element.find("#Nation")[0] ;
			  Nation.value = pNation; 
			  var Born =  element.find("#Born")[0] ;
			  Born.value = pBorn; 
			  var Address =  element.find("#Address")[0] ;
			  Address.value = pAddress; 
			  var CardNo =  element.find("#CardNo")[0] ;
			  CardNo.value = pCardNo; 
			  var Police =  element.find("#Police")[0] ;
			  Police.value = pPolice;  
			  var ActivityLFrom =  element.find("#ActivityLFrom")[0] ;
			  ActivityLFrom.value = pActivityLFrom; 
			  var ActivityLTo =  element.find("#ActivityLTo")[0] ;
			  ActivityLTo.value = pActivityLTo;			 
			  var IdPhoto =  element.find("#IdPhoto")[0] ;
			  IdPhoto.src = "data:image/jpg;base64," + ret.resultContent.identityPic;
			  //存起来
			  var customer = ret.resultContent;
			  widget.model.set("customer", customer);
			 
		},		
		_isReadyCont :function(){		
			var widget = this,
			element = $(widget.el);
				
			  var sp =  element.find("#controlsIdentify");
			  
		      if(navigator.userAgent.indexOf("Firefox")>0 || navigator.userAgent.indexOf("Chrome")>0){
		    	  
		      var swf=navigator.plugins; 		
		       for(var i=0;i<swf.length;i++){		    	  
		    	   var	fileName=swf[i].filename.toString();
				   if(fileName=="npCertReader.dll"){	
					   sp[0].innerHTML  = "控件识别结果：控件正常。 ";
					   
						return true;
				   }
				}		       
		       return false;
		       }
		      else if(navigator.userAgent.indexOf("MSIE")>0 ){
		    	  
		    	if (window.ActiveXObject){		
		    		
		    		sp[0].innerHTML = "控件识别结果：控件正常";
				    return true; 
		        }
		    	else {		    		
					return false;  
		        }
		    }
		},
		//取消
		_closePage : function() {
			var widget = this,
				element = $(widget.el);
			element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
		},
		getValue:function(){
			//模拟读卡数据
		/*	var ret = {};
			ret.resultContent = {};
			ret.resultContent.partyName = "张三";
			ret.resultContent.gender = "1";
			ret.resultContent.nation = "";
			ret.resultContent.bornDay = "";
			ret.resultContent.certAddress = "成都市";
			ret.resultContent.certNumber = "510105198703051101";
			ret.resultContent.certOrg = "";
			ret.resultContent.effDate = "1988-01-01";
			ret.resultContent.expDate = "2018-01-01";
			ret.resultContent.identityPic = "";
			var customer = ret.resultContent;
			customer.validate = true;
			this.model.set("customer",customer);
*/
			var customer =  this.model.get("customer");
			if(!customer){
				return false;
			}
			return customer;
		},
        //安全桌面初始化websocket
        _deskTopInit:function(flag){
            var widget = this;
            pageInit=flag;
            if (window["WebSocket"]) {
                if(websocketclient==null){
                    widget._createWebsocket();
                }
            }
        },
        //创建websocket
        _createWebsocket:function () {
            var widget = this, element = $(widget.el);
            var startWS = false;
            var wsUrl = "";
            if (startWS) {
                wsUrl = "ws://127.0.0.1:8888/echo";
            } else {
                wsUrl = "wss://127.0.0.1:8889/echo";
            }
            websocketclient = new WebSocket(wsUrl);
            websocketclient.onclose = function (evt) {
                websocketclient = null;
            },
			websocketclient.onerror = function (evt) {
				websocketclient = null;
			},
			websocketclient.onopen = function (evt) {
				isOnMessage="1";
                if(pageInit=='1'){
                    widget._deskTopScanCert();
                }
			},
			websocketclient.onmessage = function (evt) {
				if (evt.data=="ping"){
					websocketclient.send("pong");
					return;
				}else if (evt.data=="pong"){
					websocketclient.send("ping");
					return;
				}else if (evt.data=="PING"){
					websocketclient.send("PONG");
					return;
				}
				var soUtil = widget.require("soUtil");
				var jsonObj = eval('(' + evt.data + ')');
				var pContent = widget.aesDecryptReadCardParam(jsonObj.p);
				var pObj = eval('(' + pContent + ')');
				if(pObj.resultcode=='0'&&pObj.result!=undefined){
					var pObjResult = pObj.result;
					var partyName = widget._decode(widget,pObjResult.partyName);
					var nation = widget._decode(widget,pObjResult.nation);
					var certAddress= widget._decode(widget,pObjResult.certAddress);
					var certOrg = widget._decode(widget,pObjResult.certOrg);
					var bornDay= pObjResult.bornDay;
					var certNumber= pObjResult.certNumber;
					var identityPic= pObjResult.identityPic;
                    var activityL=widget._decode(widget,pObjResult.activityL);
					var pActivityLFrom=activityL.split("-")[0];
					pActivityLFrom=pActivityLFrom.substring(0,4)+"-"+pActivityLFrom.substring(4,6)+"-"+pActivityLFrom.substring(6,8)+" 00:00:00";
					var pActivityLTo=activityL.split("-")[1];
					if(pActivityLTo.indexOf('长期')>= 0){
						pActivityLTo = '3000-01-01 00:00:00';
					}else{
						pActivityLTo=pActivityLTo.substring(0,4)+"-"+pActivityLTo.substring(4,6)+"-"+pActivityLTo.substring(6,8)+" 00:00:00";
					}
					bornDay=bornDay.substring(0,4)+"-"+bornDay.substring(4,6)+"-"+bornDay.substring(6,8)+" 00:00:00";
					var sex=widget._decode(widget,pObjResult.sex);
					if(sex=='男'){
						sex="1";
					}else{
						sex="0";
					}
					var cust={
						partyName:partyName,
						nation:nation,
						certAddress:certAddress,
						certOrg:certOrg,
						bornDay:bornDay,
						certNumber:certNumber,
						identityPic:identityPic,
						effDate:pActivityLFrom,
						expDate:pActivityLTo,
						gender:sex,
						isCloud:"0"
					};
					soUtil.appendNewPermanentCert(pObjResult, cust);
					var ret={
						resultContent:cust
					}
					ret.resultFlag=0;
					soUtil.fillForm(ret, widget);
					widget.model.set("scaned", 1);
				}
			}
        },
        //读卡请求
        _deskTopScanCert:function(){
            var widget = this,
                element = $(widget.el),
                gSession = widget.gSession;
            var partyId="";
            if(gSession.custInfo!=undefined){
                partyId=gSession.custInfo.custId;
            }
            if(websocketclient!=null&&isOnMessage=="1"){
                var staffCode=gSession.staffCode;
		var channelCode="";
		if(gSession.curChannelNbr!=undefined&&gSession.curChannelNbr!=null){
			channelCode=gSession.curChannelNbr;
		}
                var sendData = '{"actiontype":"readIdCardInfo","staffNumber":"'+staffCode+'","channelCode":"'+channelCode+'","partyId":"'+partyId+'"}';
                //安全桌面参数加密特殊处理
                var base64Str = widget.aesReadCardParam(sendData);

                var jsonStr = '{"p":"' + base64Str + '"}';
                websocketclient.send(jsonStr);
            }else{
                widget.popup("智慧桌面连接失败，请连接后重试。");
                widget._deskTopInit('1');
            }
        },
        _decode:function (widget,input) {
            var output = base64decode(input);
            output = widget._utf8_decode(output);
            return output;
        },
        aesReadCardParam : function (data) {
            return window.btoa(data);
        },
        aesDecryptReadCardParam : function (data) {
		        var widget = this;
            return widget._decode(widget,data);
        },
        _utf8_decode : function (utftext) {
            var string = "";
            var i = 0;
            var c = c1 = c2 = 0;
            while ( i < utftext.length ) {
                c = utftext.charCodeAt(i);
                if (c < 128) {
                    string += String.fromCharCode(c);
                    i++;
                } else if((c > 191) && (c < 224)) {
                    c2 = utftext.charCodeAt(i+1);
                    string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
                    i += 2;
                } else {
                    c2 = utftext.charCodeAt(i+1);
                    c3 = utftext.charCodeAt(i+2);
                    string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
                    i += 3;
                }
            }
            return string;
        }
	});
	vita.widget.register("scanCert", scanCert, true);
})(window.vita);
