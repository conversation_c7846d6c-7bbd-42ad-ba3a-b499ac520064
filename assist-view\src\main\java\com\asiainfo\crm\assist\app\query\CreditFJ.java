package com.asiainfo.crm.assist.app.query;

import com.al.common.utils.DateUtil;
import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.Constant;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.ICreditSMO;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.asiainfo.crm.service.intf.ISoBuildInOrderMultiSMO;
import com.asiainfo.crm.service.intf.ISpecSMO;
import com.asiainfo.crm.service.intf.IStarLevelSMO;
import com.asiainfo.crm.util.ListUtil;
import com.asiainfo.crm.util.MessageUtil;
import com.google.common.collect.Maps;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 授信复机   一键受理  （信用度与权益复机）
 *
 * <AUTHOR>
 * @version v3.0
 * @Company 亚信科技
 * @Copyright 2017-2019 AsiaInfo Corporation, All Rights Reserved
 * @CreateDate 0009, 2019.05.09
 * @see
 * @since v3.0
 */
@Component("vita.creditFJ")
public class CreditFJ extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(CreditFJ.class);
    @Autowired
    private IProdInstSMO prodInstSMO;
    @Autowired
    private ISoBuildInOrderMultiSMO soBuildInOrderMultiSMO;
    @Autowired
    private IStarLevelSMO starLevelSMO;
    @Autowired
    private ICreditSMO creditSMO;
    @Autowired
    private ISpecSMO iSpecSMO;

    @Override
    public Map<String, Object> achieveData(Object... params) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        options.put("ifCredit", MDA.CREDIT_FJ_SWITCH);
        return options;
    }

    /**
     * 查询号码对应的信息
     * 包括客户信息，产品信息，星级信息，信用度信息
     *
     * @param json
     * @return
     * @throws IOException
     */
    public Map<String, Object> queryProduct(String json) throws Exception {
        //调用资产中心
        Map<String, Object> userMap = Maps.newHashMap();
        //通过产品实例编码再查询资产中心获取产品规格和客户编码、号码
        Map<String, Object> paramMap = this.jsonConverter.toBean(json, Map.class);
        String areaCode = MapUtils.getString(paramMap, "areaCode", "");
        Map<String, Object> scope = Maps.newHashMap();
        List<Map<String, Object>> scopes = new ArrayList<>();
        scope.put("scope", "prodInstState");
        scope.put("pageIndex", 1);
        scope.put("pageSize", 10);
        scopes.add(scope);
        paramMap.put("scopeInfos", scopes);
        json = this.jsonConverter.toJson(paramMap);

        String prodInst = this.prodInstSMO.qryAccProdInstDetailLocal(json);
        Map<String, ?> instObjectMap = (Map<String, Object>) resolveResult(prodInst);
        if (MapUtils.isNotEmpty(instObjectMap)) {
            Map<String, ?> instInfo = (Map<String, ?>) MapUtils.getObject(instObjectMap, "prodInstDetail");
            List<Map<String, ?>> instStateList = (List<Map<String, ?>>) MapUtils.getObject(instInfo, "prodInstStateList");

            String prodId = MapUtils.getString(instInfo, "prodId");
            String custId = MapUtils.getString(instInfo, "useCustId");
            //优先查询使用人星级权益，没有使用人，再用归属人员 ,这块逻辑后移到星级系统
            custId = StringUtils.isBlank(custId) || StringUtils.equals("0", custId) ? MapUtils.getString(instInfo, "ownerCustId") : custId;
            String accNum = MapUtils.getString(instInfo, "accNum");
            String lanName = MapUtils.getString(instInfo, "lanName");
            String custName = MapUtils.getString(instInfo, "custName");
            String prodName = MapUtils.getString(instInfo, "prodName");
            String statusCd = MapUtils.getString(instInfo, "statusCd");
            String stopType = statusCd;
            String stopReason = Constant.PRO_INST_SCTATUS_USING.equals(statusCd) ? "正常" : "";
            if (!ListUtil.isListEmpty(instStateList)) {
                Map<String, ?> state = instStateList.get(0);
                stopType = Constant.PRO_INST_SCTATUS_USING.equals(statusCd)?stopType : MapUtils.getString(state, "stopType");
                stopReason = Constant.PRO_INST_SCTATUS_USING.equals(statusCd)?stopReason: MapUtils.getString(state, "stopReason", "");
                //查询码表对应的停机类型，如果停机原因为空时
                if (StringUtils.isBlank(stopReason)) {
                    Map attrParams = new HashMap();
                    List<String> attrIdList = new ArrayList<>();
                    attrIdList.add(AssistMDA.STOP_TYPE_ATTR_ID);
                    attrParams.put("attrIdList", attrIdList);
                    String attrResultStr = this.iSpecSMO.qryAttrValues(jsonConverter.toJson(attrParams));
                    List<Map<String, String>> attrList = (List<Map<String, String>>) resolveResult(attrResultStr);
                    for (Map<String, String> attr : ListUtil.nvlList(attrList)) {
                        if (attr.get("attrValue").equals(stopType)) {
                            stopReason = attr.get("attrValueName");
                            break;
                        }
                    }
                }
            }
            userMap.put("custId", custId);
            userMap.put("accNum", accNum);
            userMap.put("areaName", lanName);
            userMap.put("custName", custName);
            userMap.put("stopType", stopType);
            userMap.put("stopReason", stopReason + ":" + stopType);
            userMap.put("prodName", prodName);
            userMap.put("prodId", prodId);
            userMap.put("ifCredit", MDA.CREDIT_FJ_SWITCH);

            Map<String, Object> creditMap = this.calcCredit(areaCode, accNum, custId, prodId, stopType);
            userMap.putAll(creditMap);
        }
        return userMap;
    }

    /**
     * 复机
     *
     * @param json
     * @return
     * @throws Exception
     */
    public Map<String, Object> recoverConnect(String json) throws Exception {

        Map<String, Object> retnMap = new HashMap<String, Object>();
        Map<String, Object> paramMap = jsonConverter.toBean(json, Map.class);
        String glbSessionId = MapUtils.getString(paramMap, "glbSessionId");
        String accNum = MapUtils.getString(paramMap, "accNum");
        String areaCode = MapUtils.getString(paramMap, "areaCode");
        String prodId = MapUtils.getString(paramMap, "prodId");
        String custId = MapUtils.getString(paramMap, "custId");
        String stopType = MapUtils.getString(paramMap, "stopType");
        //判断业务动作
        Map<String, Object> creditMap = this.calcCredit(areaCode, accNum, custId, prodId, stopType);
        Long serviceOfferId = MapUtils.getLong(creditMap, "serviceOfferId");
        String restore = MapUtils.getString(creditMap, "restore");

        if (!StringUtils.equals("credit", restore) && !StringUtils.equals("right", restore)) {
            return MessageUtil.buildMsg("-1", restore);
        }

        String custOrderNbr = "";
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("accNum", accNum);
            jsonObject.put("glbSessionId", glbSessionId);
            jsonObject.put("serviceOfferId", serviceOfferId);
            String resultStr = this.soBuildInOrderMultiSMO.commitOrderForCreditFJ(jsonObject.toString());
            Map<String, Object> orderInfoMap = (Map<String, Object>) this.resolveResult(resultStr);
            custOrderNbr = MapUtils.getString(orderInfoMap, "custOrderNbr");
        } catch (Exception e) {
            throw MessageUtil.buildBError("-1", "授信紧急复机异常：" + e.getMessage(), e);
        }
        retnMap = MessageUtil.buildSuccessMsg("紧急复机提交成功，订单流水: " + custOrderNbr);
        return retnMap;
    }

    //查询星级权益
    private Map<String, Object> queryStartInfo(String areaCode, String accNum, String custId, String prodId)
            throws Exception {
        Map<String, Object> result = new HashMap<String, Object>();
        //信用度与星级权益查询
        String nbrType = MDA.STAR_EQUIT_NUM_TYPE_MAP.get(prodId);
        //业务动作选择判断
        String serviceOfferId = MDA.STAR_SERVICE_OFFER_MAP.get(prodId);
        //根据业务动作取星级权益值
        String rightCode = MDA.STAR_EQUIT_MAP.get(serviceOfferId);

        if (nbrType == null) {
            throw MessageUtil.buildWarnBError("-1", "产品规格:" + prodId
                    + " 没有配置相应的星级权益号码类型STAR_EQUIT_NUM_TYPE_MAP");
        }
        if (serviceOfferId == null) {
            throw MessageUtil.buildWarnBError("-1", "产品规格:" + prodId + " 暂不支持授信紧急复机业务");
        }
        if (rightCode == null) {
            throw MessageUtil.buildWarnBError("-1", "业务动作:" + serviceOfferId
                    + " 没有配置相应的星级权益编码STAR_EQUIT_MAP");
        }

        JSONObject requestMess = new JSONObject();
        requestMess.put("nbrType", nbrType);
        requestMess.put("qryNbr", accNum);
        requestMess.put("rightCode", rightCode);
        requestMess.put("areaCode", areaCode);
        //逻辑后移到星级
        //requestMess.put("partyId", custId);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("requestMess", requestMess);
        String json = jsonObject.toString();
        String starInfo = this.starLevelSMO.qryStarMember(json);
        String right = "";
        if (StringUtils.isNotEmpty(starInfo)) {
            starInfo = this.resolveStarResult(starInfo, custId);
            String rightInfo = this.starLevelSMO.qryRightTimesInfo(json);

            if (StringUtils.isNotEmpty(rightInfo)) {
                right = this.resolveRightResult(rightInfo, rightCode);
                starInfo += " 权益剩余："+right;
            }
        } else {
            starInfo = "非星级会员";
        }

        result.put("starInfo", starInfo);
        result.put("rightTimes", right);

        return result;
    }

    //查询信用度
    private Map<String, Object> queryCreditInfo(String accNum) throws IOException {
        JSONObject jsonObject = new JSONObject();
        String seq = DateUtil.getNow("yyyyMMDDHHmmsss");
        jsonObject.put("requestId", seq);
        jsonObject.put("destinationId", accNum);
        jsonObject.put("requestTime", seq);
        //移动业务为2
        jsonObject.put("destinationAttr", "2");

        String creditInfo = this.creditSMO.queryCredit(jsonObject.toString());
        Map<String, Object> creditResult = this.resolveCreditResult(creditInfo);
        return creditResult;
    }

    //解析星级权益接口
    private String resolveRightResult(String resultStr, String rightCode) throws IOException {
        Map<String, Object> resultMap = this.jsonConverter.toMap(resultStr, String.class, Object.class);
        resultMap = MapUtils.getMap(resultMap, "responseMess");
        String result = MapUtils.getString(resultMap, "result");
        if (StringUtils.equals("0", result)) {
            Map<String, Object> rightTimesInfo = MapUtils.getMap(resultMap, "rightTimesInfo");
            if (MapUtils.isEmpty(rightTimesInfo)) {
                return "该用户没有星级权益值";
            }
            List<Map<String, Object>> rightInfos = (List<Map<String, Object>>) MapUtils.getObject(rightTimesInfo, "rightInfo");

            if (CollectionUtils.isNotEmpty(rightInfos)) {
                Map<String, Object> rightInfo = rightInfos.get(0);
                String rCode = MapUtils.getString(rightInfo, "rightCode");
                if (StringUtils.equals(rightCode, rCode)) {
                    return MapUtils.getString(rightInfo, "leftTimes");
                }
            }
        } else {
            String msg = MapUtils.getString(resultMap, "rspDesc");
            String code = MapUtils.getString(resultMap, "rspCode");
            return msg;
        }
        return "该用户没有星级权益值";
    }

    //解析星级信息接口
    private String resolveStarResult(String resultStr, String custId) throws IOException {
        Map<String, Object> resultMap = this.jsonConverter.toMap(resultStr, String.class, Object.class);
        resultMap = MapUtils.getMap(resultMap, "responseMess");
        String result = MapUtils.getString(resultMap, "result");
        if (StringUtils.equals(Constant.LOGIC_STR_0, result)) {
            result = "非星级会员";
            Map<String, Object> memberInfo = MapUtils.getMap(resultMap, "memberInfo");
            if (MapUtils.isNotEmpty(memberInfo)) {
                String isStarMember = MapUtils.getString(memberInfo, "isStarMember");
                String memberLevel = MapUtils.getString(memberInfo, "memberLevel");
                if (Constant.LOGIC_STR_Y.equals(isStarMember)) {
                    result = "等级：" + AssistMDA.STAR_RIGHT_CODE_REL.get(memberLevel);
                }
            }
        } else {
            String msg = MapUtils.getString(resultMap, "rspDesc", "") + " 用户标识：" + custId;
            String code = MapUtils.getString(resultMap, "rspCode");
            result = msg;
        }
        return result;
    }

    //解析信用度接口
    private Map<String, Object> resolveCreditResult(String resultStr) throws IOException {
        Map<String, Object> resultMap = this.jsonConverter.toMap(resultStr, String.class, Object.class);
        String result = MapUtils.getString(resultMap, "result");
        if (StringUtils.equals(Constant.LOGIC_STR_1, result)) {
            return resultMap;
        } else {
            String msg = MapUtils.getString(resultMap, "errMsg");
            String code = MapUtils.getString(resultMap, "result");
            throw MessageUtil.buildWarnBError(code, msg);
        }
    }

    //计算信用度与星级权益入口
    protected Map<String, Object> calcCredit(String areaCode, String accNum, String custId, String prodId, String stopType)
            throws Exception {
        Map<String, Object> calcMap = new HashMap<String, Object>();
        //查询星级权益
        Map<String, Object> starInfo = this.queryStartInfo(areaCode, accNum, custId, prodId);
        calcMap.putAll(starInfo);

        //授信复机的调用规则：1.先从权益再信用度（信用度有开关，某些省份没有）
        //2.是否本月使用过，使用过不能复机
        //3.权益是否还有次数，没有不能复机，或通过信用度
        //4.信用度不到3级不能复机
        int rightTimes = MapUtils.getIntValue(starInfo, "rightTimes");
        String restore = rightTimes <= 0 ? "用户：没有星级权益可使用；<br/>" : "right";
        //默认使用权益时的业务动作
        String serviceOfferId = MDA.STAR_SERVICE_OFFER_MAP.get(prodId);
        String credit = "不支持";
        //信用度只支持移动业务
        if (MDA.CREDIT_FJ_SWITCH.equals(Constant.LOGIC_STR_Y) && !"right".equals(restore)
                && MDA.PSID_CDMA.equals(prodId)) {
            //right代表有星级权益

            //查询信用度
            Map<String, Object> creditInfo = this.queryCreditInfo(accNum);
            String servState = MapUtils.getString(creditInfo, "servState","");
            //String state = MapUtils.getString(creditInfo, "state", "0");
            int onlineLevel = MapUtils.getIntValue(creditInfo, "onlineLevel");
            String stopReason = "";
            meet:
            {
                switch (servState) {
                    case "2HA":
                        stopReason = "正常：" + servState;
                        break;
                    case "2HD":
                        stopReason = "欠费双停：" + servState;
                        break;
                    case "2HS":
                        stopReason = "欠费单停：" + servState;
                        break;
                    default:
                        stopReason = "状态：未知";
                }
                if ("2HA".equals(servState)) {
                    restore = "当前服务状态正常，无需进行紧急复机";
                    break meet;
                }
                if (!"2HD".equals(servState) && !"2HS".equals(servState)) {
                    restore += " 当前服务状态非单停或双停，不能进行授信复机";
                    break meet;
                }
                String tempCredit = MapUtils.getString(creditInfo, "tempCredit");
                if (Constant.LOGIC_STR_1.equals(tempCredit)) {
                    restore += " 本月已使用过紧急授信复机，不允许再使用";
                    break meet;
                }

                if (onlineLevel <= 0) {
                    restore += " 信用等级：未评级，不允许使用授信复机";
                    break meet;
                }
                if (onlineLevel > 0 && onlineLevel < 3) {
                    restore += " 当前用户信用等级小于3，不允许使用授信复机";
                    break meet;
                }
                //以上条件都不满足时，执行下面这条语句
                restore = "credit";

            }
            if (onlineLevel < 0) {
                credit = "信用等级：未评级";
            } else {
                credit = "信用等级：" + onlineLevel;
            }
            //如果有使用信用度判断的，停机状态使用计费的状态，否则使用受理
            calcMap.put("stopReason", stopReason);
            //使用信用度时的业务动作
            serviceOfferId = MDA.CREDIT_SERVICE_OFFER_MAP.get(prodId);
        } else {
            if (Constant.STOP_TYPE_USER_STOP.equals(stopType)) {
                restore = "当前服务状态正常，无需进行紧急复机";
            } else if (!Constant.STOP_TYPE_SINGLE_STOP.equals(stopType)
                    && !Constant.STOP_TYPE_DOUBLE_STOP.equals(stopType)
                    && !Constant.STOP_TYPE_USER_STOP.equals(stopType)) {
                restore = "当前服务状态非单停或双停，不能进行授信复机";
            }
        }
        calcMap.put("restore", restore);
        calcMap.put("credit", credit);
        calcMap.put("serviceOfferId", serviceOfferId);
        return calcMap;
    }

}
