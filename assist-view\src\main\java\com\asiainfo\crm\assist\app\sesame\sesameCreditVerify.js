(function (vita) {
    var sesameCreditVerify = vita.Backbone.BizView.extend({
        events: {
            "click #btn-verify": "_sesameCreditAction",
            "click .nextbutt" : "_nextClick"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            var hasChecked = false;
            widget.model.set("hasChecked", hasChecked);
        },
        _nextClick: function() {
            var widget = this;
            var hasChecked = widget.model.get("hasChecked");
            if(!hasChecked) {
                widget.popup("请先完成芝麻信用校验！");
                return false;
            }
            var transactionId = widget.model.get("transactionId");
            //下一步
            this.end("sesameCreditBusiTips", {"transactionId":transactionId});
        },
        _sesameCreditAction: function () {
            var widget = this;
            var gsession = widget.gSession;
            debugger;
            var transactionId = $("#digitalOrderId").val();
            if(transactionId == null || transactionId == "") {
                widget.popup("请输入芝麻征信数字工单ID！");
                return false;
            }
            var params = {
                transactionId : transactionId,
                staffId : gsession.staffId,
                regionId : gsession.staffRegionId,
                staffRegionId : gsession.staffRegionId,
                channelId : gsession.curChannelId,
                step : "2"
            };
            widget.refreshPart("sesameCreditAction", JSON.stringify(params), "#sesameCreditVerifyResult", function (re) {
                var r = $(re);
                var result = r.find("#sesameVerifyResult").data("result");
                if(result == "0") {
                    widget.model.set("hasChecked", true);
                    widget.model.set("transactionId", transactionId);
                }
                }, { async: false });
        }
    });
    vita.widget.register("sesameCreditVerify", sesameCreditVerify, true);
})(window.vita);