<div data-widget="blackListManage" class="popup-container">
	<p class="vita-data">{"data":$options}</p>
	<div style="width: 100%; height: 40px; background: #7CA63E; padding-left: 10px; line-height: 40px; color: white; font-size:16px">
			黑名单管理
	</div>
	<div style="margin:10px;padding:10px;border:1px solid #DDDDDD;">
		<div>
			<table style="margin-left:0;">
				<tr>
					<td>身份证号&nbsp</td>
					<td>&nbsp<input class="form-control" type="text" id="certNum" name="certNum"/>&nbsp</td>
					<td>&nbsp<button type="button" id="readCard" class="btn btn-white">读卡</button>&nbsp</td>
					<td>&nbsp<button type="button" id="qryBlacklist" class="btn btn-white">查询</button>&nbsp</td>
					<td width="50px"></td>
					<td>&nbsp<button type="button" id="addTempletDownload" class="btn btn-white">批量新增模板</button></td>
					<td>
						<form id="m_formAdd" action="../action/uploadForbidChannelInfoExcel"
							method="post" enctype="multipart/form-data">
							<input style="display: none" class="m_fileAdd" type="file"
								name="data" accept="xls" />
							<button type="button" id="m_fileAdd" class="btn btn-default">
								<span>选择要导入的Excel文件</span>
							</button>
							<input style="display: none" class="route_input" type="text"
								value="" name="jsonStr" />
							<button type="button" id="batchAdd" class="btn btn-white">批量新增</button>
						</form>
					</td>
					<td width="50px"></td>
					<td>&nbsp<button type="button" id="delTempletDownload" class="btn btn-white">批量删除模板</button></td>
					<td>
						<form id="m_formDel" action="../action/uploadForbidChannelInfoExcel"
							method="post" enctype="multipart/form-data">
							<input style="display: none" class="m_fileDel" type="file"
								name="data" accept="xls" />
							<button type="button" id="m_fileDel" class="btn btn-default">
								<span>选择要导入的Excel文件</span>
							</button>
							<input style="display: none" class="route_input" type="text"
								value="" name="jsonStr" />
							<button type="button" id="batchDel" class="btn btn-white">批量删除</button>
						</form>
					</td>
					<td width="50px"></td>
					<td>
						<p style="color: red">一次导入最多2000条</p>
					</td>
				</tr>
			</table>
		</div>
	</div>
	<div style="margin:10px;border:1px solid #DDDDDD;">
		<div style="width: 100%; height: 40px; background: #F6F6F6; padding-left: 10px; line-height: 40px; font-size:16px; border: 1px solid #eee">
			<i class="fa-lightn glyphicon textcolorgreen text18 mar15"></i>
			黑名单信息	
		</div>
		<div id="result" class="col-lg-12" style="height:320px;overflow:auto;display: none;">
			<table class="table">
				<tr>
					<th>身份证号</th>
					<th>号码</th>
					<th>状态</th>
					<th>来源</th>
					<th>黑名单原因</th>
					<th>有效期</th>
					<th>备注</th>
					<th>新建操作人</th>
					<th>生效时间</th>
					<th>删除操作人</th>
					<th>失效时间</th>
				</tr>
				#if($options != "null" && $options.customers && $options.customers != "null" && $options.customers.size() > 0)
					#foreach($customer in $options.customers)
					<tr>
						<td>$!customer.blackListValue</td>
						<td>$!customer.phoneNumber</td>
						<td>
							#if($customer && $customer.statusCd != "null" && $customer.statusCd == "A")
								生效
                           	#elseif($customer && $customer.statusCd != "null" && $customer.statusCd == "X")
								退出违规
                            #else
                            #end
						</td>
						<td>$!customer.stemFrom</td>
						<td>$!customer.reason</td>
						<td>$!customer.validDate</td>
						<td>$!customer.remark</td>
						<td>$!customer.createStaff</td>
						<td>$!customer.effDate</td>
						<td>$!customer.updateStaff</td>
						<td>$!customer.expDate</td>
					</tr>
					#end
				#end
			</table>
		</div>
	</div>
</div>     