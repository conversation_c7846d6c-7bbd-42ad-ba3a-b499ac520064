(function (vita) {


    var qryComboValue = vita.Backbone.BizView.extend({
        events : {
            "click #btn-query": function () {
                this._qryOrderList();
            },
            "click #regionIdBtn": "_chooseArea"
        },
        global : {
            pageIndex : 1, //当前页
            pageSize : 5, //每页记录数
            chooseArea: "../comm/chooseArea"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            widget._initialRegionId();
        },
        _initialRegionId: function () {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
        },
        _qryOrderList : function () {
            var widget = this,el = $(widget.el),gSession = widget.gSession;
             var regionId = $("#regionId").attr("value");
             if (!regionId) {
                 widget.popup("请选择地区！");
                 return false;
             }
             var account = el.find("#account").val();
             if(!account){
                 widget.popup("请输入宽带上网账号！");
                 return false;
             }
            var params = {
                "regionId":regionId,
                "account": account,
                "prodIds": [9,10,*********]
            };
            widget._refeshOrderList(params);
        },
        _refeshOrderList: function (params) {
            var widget = this;
            widget.refreshPart("qryComboValue", JSON.stringify(params), "#orderListResult", function (re) {

            }, {
                async: false,
                mask : true
            });
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        }
    });
    vita.widget.register("qryComboValue", qryComboValue, true);
})(window.vita);