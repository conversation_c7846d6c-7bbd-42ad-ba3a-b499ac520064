<div data-widget="factorParamConfig">
    <div class="box-maincont">
        <div class="page_main notopnav">
            <div class="col-lg-12">
                <div class="box-item">
                    <div class="container-fluid row">
                        <div class="form_title">
                            <div><i class="bot"></i>查询条件</div>
                        </div>
                        <div class="wmin_content row">
                            <form class=" form-bordered">
                                <div class="form-group col-md-4">
                                <label class="col-md-4 control-label lablep">因子Id</label>
                                <div class="col-md-7">
                                    <input type="text" class="form-control" value=""
                                           id="factorParamId">
                                </div>
                                </div>
                                <div class="form-group col-md-4">
                                    <label class="col-md-4 control-label lablep">参数编码</label>
                                    <div class="col-md-7">
                                        <input type="text" class="form-control" value=""
                                               id="paramCode">
                                    </div>
                                </div>
                                <div class="form-group col-md-4">
                                    <label class="col-md-4 control-label lablep">参数名称</label>
                                    <div class="col-md-7">
                                        <input type="text" class="form-control" value=""
                                               id="paramName">
                                    </div>
                                </div>
                                <div class="form-group col-md-4">
                                    <label class="col-md-4 control-label lablep">状态</label>
                                    <div class="col-md-4">
                                        <select id="statusCd" class="form-control">
                                            <option value="1000">有效</option>
                                            <option value="1100">失效</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group col-md-4 ">
                                    <label class="col-md-2 control-label lablep"></label>
                                    <div class="col-md-9 text-right">
                                        <button type="button" class="btn btn-white" id="resetBtn">清除</button>
                                        <button type="button" class="btn btn-primary" id="queryBtn">查询</button>
                                        <button type="button" class="btn btn-primary" id="addBtn">添加</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="container-fluid row">
                        <div class="form_title">
                            <div><i class="bot"></i>查询结果</div>
                        </div>
                        <div class="wmin_content" id="loadData">
                            <div class="col-lg-12">
                                <a class="btn btn-gray btn-outline" id="editBtn">
                                    <i class="glyphicon icon-progress text18"> </i> 编辑
                                </a>
<!--                                <a style="pointer-events: none" class="btn btn-gray btn-outline"  id="deleteBtn">-->
<!--                                    <i class="glyphicon icon-batch text18" > </i> 删除-->
<!--                                </a>-->
                            </div>
                            <div class="col-lg-12 mart10" name="tableDiv">
                                <table class="table table-hover">
                                    <thead>
                                    <tr>
                                        <th>选择</th>
                                        <th>参数因子ID</th>

                                        <th>参数编码</th>
                                        <th>参数名称</th>
                                        <th>介绍</th>
                                        <th>因子Id</th>
                                        <th>状态</th>
<!--                                        <th>操作</th>-->
                                    </tr>
                                    </thead>
                                    <tbody>
                                    #if($options.reqList!="null" && $options.reqList.size()>0)
                                    #foreach($fc in $options.reqList)
                                    <tr>
                                        <td>
                                            <label class="wu-radio full absolute" data-scope="">
                                                <input type="radio" name="payment">
                                                <p class="vita-data">{"Id":"$!fc.factorParamId"}</p>
                                            </label>
                                        </td>
                                        <td>$!fc.factorParamId</td>
                                        <td>$!fc.paramCode</td>
                                        <td>$!fc.paramName</td>
                                        <td>$!fc.descInfo</td>
                                        <td>$!fc.factorId</td>
                                        <td>$!fc.statusCd</td>
<!--                                        <td><button type="button" class="btn btn-primary" value= $!fc.id id="checkZkValue">查看</button></td>-->
                                    </tr>
                                    #end
                                    #end
                                    </tbody>
                                </table>
                                #if($options.totalNumber)
                                <p class="vita-data" id = "_totalNumber">{"totalNumber":$options.totalNumber}</p>
                                #end
                            </div>
                            <div id="pageInfo">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--弹窗 start-->
    <div class="popup" style="display:none;">
        <div class="popup-mask active"></div>
        <div class="agile-popup right active" style="width: 75%">
            <div class="popup-container">
                <!--内容-->
                <div class="calctitle">
                    <div class="titlefont">添加</div>
                    <div class="toolr">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                    </div>
                </div>

                <div class="wmin_content row" style="margin-top: 10px">
                    <form class=" form-bordered" name="_popupForm">

                        <div class="hideAndShow form-group col-md-6" >
                            <label class="col-md-4 control-label lablep">参数因子ID</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_factorParamId" placeholder="必填"  required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">参数编码</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_paramCode" placeholder="必填"  required>
                            </div>
                        </div>

                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">参数名称</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_paramName"  required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">介绍</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_descInfo"  required>
                            </div>
                        </div>

                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">因子Id</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_factorId"  placeholder="必填" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label class="col-md-4 control-label lablep">状态</label>
                            <div class="col-md-8">
                                <select id="_statusCd" class="form-control">
                                    <option value="1000">有效</option>
                                    <option value="1100">失效</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group col-md-12">
                            <div class="col-md-12 text-center">
                                <button type="button" class="btn btn-primary" id="addSubmitBtn">添加</button>
                                <button type="button" class="btn btn-primary" id="editSubmitBtn">修改</button>
                                <button type="button" class="btn btn-primary" id="closeBtn">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!--end-->


</div>
</div>
</div>
<style>
    .modal{z-index: 9910;}
    .agile-popup{z-index: 9000;}
</style>
