package com.asiainfo.crm.assist.app.query;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.service.intf.ISoQuerySMO;

/**
 * 选择业务动作组件
 * Copyright: Copyright (c) 2017 Asiainfo
 * 
 * @ClassName: ChooseServiceOffer.java
 * @Description: 该类的功能描述
 *
 * @version: v1.0.0
 * @author:  z<PERSON>jun
 * @date: 2018年02月28日 下午2:33:31 
 *
 * Modification History:
 * Date         Author          Version            Description
 *------------------------------------------------------------
 */
@Component("vita.chooseServiceOffer")
public class ChooseServiceOffer extends AbstractComponent {

    @Autowired
    private ISoQuerySMO soQuerySMO;
	
	@Override
    public Map achieveData(Object... params) throws Exception {
        String paramStr = (String) params[0];
        Map options = jsonConverter.toMap(paramStr, String.class, Object.class);
        Map pageInfo = new HashMap();
        pageInfo.put("pageIndex", 1);
        pageInfo.put("pageSize", 10);
        options.putAll(qryItems(jsonConverter.toJson(pageInfo)));
        return options;
    }

	public Map qryItems(String jsonString) throws Exception {
        String  retStr = soQuerySMO.qryServiceOffers(jsonString);
        Map retMap = (Map) resolveResult(retStr);
        if ("Y".equals(AssistMDA.SD_PROTOCOL_TEMPLET_SWITCH)){
            List<Map> dimensions = (List<Map>) MapUtils.getObject(retMap, "dimensions");
            for (int i = 0; i < dimensions.size(); i++) {
                Map map = dimensions.get(i);
                String attrValue = String.valueOf(map.get("attrValue"));
                List<Map<String, Object>> replacementServiceId = AssistMDA.REPLACEMENT_SERVICE_ID;
                for (Map<String, Object> stringObjectMap : replacementServiceId) {
                    if (String.valueOf(stringObjectMap.get("serviceOfferId")).equals(attrValue)){
                        map.put("attrName",stringObjectMap.get("serviceOfferName"));
                        map.put("attrValue",stringObjectMap.get("defaultServiceOfferId"));
                        dimensions.set(i,map);
                    }
                }
            }
            retMap.put("totalNumber", MapUtils.getString(retMap, "total"));
            retMap.put("items", dimensions);
        } else {
            retMap.put("totalNumber", MapUtils.getString(retMap, "total"));
            retMap.put("items", MapUtils.getObject(retMap, "dimensions"));
        }
        return retMap;
    }
}
