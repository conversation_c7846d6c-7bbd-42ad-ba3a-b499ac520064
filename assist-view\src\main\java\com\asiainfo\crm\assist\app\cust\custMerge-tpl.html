<div data-widget="custMerge" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright">

        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="form_title">
                                <div><i class="bot"></i>客户合并</div>
                            </div>
                            <div class="wmin_content row">
                                <div class="form-group col-md-6 ">
                                    <label class="col-md-4 control-label lablep">证件类型</label>
                                    <div class="col-md-8">
                                        <div class="input-group">
                                            <div class="input-group-btn" id="identitype">
                                                <button id="identitya" type="button"
                                                        class="btn btn-default dropdown-toggle" data-toggle="dropdown"
                                                        aria-haspopup="true" aria-expanded="false">
                                                    请选择<span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu identitys">
                                                    #if($options.partyIndCertTypes != "null" && $options.partyIndCertTypes.size() > 0)
                                                    #foreach($partyIndCertType in $options.partyIndCertTypes)
                                                    #foreach($busiObjAttrValue in $partyIndCertType.busiObjAttrValues)
                                                    <li><a href="javascript:void(0);">$busiObjAttrValue.attrValueName</a>
                                                    </li>
                                                    <p class="vita-data">{"data":$busiObjAttrValue.attrValue}</p>
                                                    #end
                                                    #end
                                                    #foreach($partyOrgCertType in $options.partyOrgCertTypes)
                                                    #foreach($busiObjAttrValue in $partyOrgCertType.busiObjAttrValues)
                                                    <li><a href="javascript:void(0);">$busiObjAttrValue.attrValueName</a>
                                                    </li>
                                                    <p class="vita-data">{"data":$busiObjAttrValue.attrValue}</p>
                                                    #end
                                                    #end
                                                    #end
                                                </ul>
                                            </div>
                                            <input id="identityNum" type="text" class="form-control"
                                                   aria-label="Text input with dropdown button">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="col-md-12">
                                        <button type="button" id="queryInfo" class="btn btn-primary">查询</button>
                                        <button type="button" id="resetBtn" class="btn btn-green">重置</button>
                                    </div>
                                </div>
                            </div>
                            <div class="wmin_content row">
                                <div class="form-group col-md-8">
                                    <label class="col-md-4 control-label lablep">原、目的客户接入号</label>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <input id="yuanAccNum" type="text" class="form-control"
                                                   aria-label="Text input with dropdown button">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <input id="mudiAccNum" type="text" class="form-control"
                                                   aria-label="Text input with dropdown button">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="qryCustomerList">
                                <p class="vita-data">$!options</p>
                                #if($options.resultObject && $options.resultObject.customers)
                                <div class="form_title">
                                    <div><i class="bot"></i>客户信息</div>
                                </div>
                                <div class="col-lg-12 mart10">
                                    <table class="table table-hover">
                                        <thead>
                                        <tr>
                                            <th>客户编号</th>
                                            <th>客户名称</th>
                                            <th>证件类型</th>
                                            <th>证件号码</th>
                                            <th>业务号码</th>
                                            <th>操作</th>
                                        </tr>
                                        </thead>
                                        <tbody id="qryCustomertbody">
                                        #if($options != "null" && $options!= "")
                                        #if($options.resultObject.customers!= "null" && $options.resultObject.customers.size() > 0)
                                        #set($customers = $options.resultObject.customers)
                                        #foreach($module in $customers)
                                        <tr>
                                            <td>$!module.custId</td>
                                            <td>$!module.custName</td>
                                            <td>$!module.certTypeName</td>
                                            <td>$!module.certNum</td>
                                            <td><input value="$!module.accNum" readonly/></td>
                                            <td>
                                                <p class="vita-data">{"data":$module}</p>
                                                <!--<label class="wu-radio full absolute col-md-4">-->
                                                <!--<input name="mudi" type="radio" value="$!module.custId"/>目的客户-->
                                                <!--</label>-->
                                                <!--<label class="wu-checkbox full absolute col-md-4">-->
                                                <!--<input name="yuan" type="checkbox" value="$!module.custId"/>源客户-->
                                                <!--</label>-->
                                                <span class="lablep full absolute wu-checkbox">
                                                            <input type="radio" name="yuan"
                                                                   value="$!module.custId">源客户&emsp;
                                                            </span>
                                                <span class="lablep full absolute wu-radio">
                                                            <input type="radio" name="mudi"
                                                                   value="$!module.custId">目的客户&emsp;
                                                            </span>
                                            </td>
                                            <!-- <p for="$!{velocityCount}" class="vita-data">{"data":"123"}</p>-->
                                        </tr>

                                        #end
                                        #end
                                        #end
                                        </tbody>
                                    </table>
                                    <!--翻页start -->
                                    <div id="showPageInfo">
                                    </div>
                                    #if($options.resultObject.pageInfo.rowCount && $options.resultObject.pageInfo.rowCount != "null")
                                    <p class="vita-data">{"totalNumber":$options.resultObject.pageInfo.rowCount}</p>
                                    #end
                                    <!--翻页end -->
                                    #widgetCreate("handler",$option)
                                </div>
                                <div class="box-item">
                                    <div class="container-fluid digg">
                                        <button class="btn btn-green borderl" type="button" id="printBtn">打印回执
                                        </button>
                                        <button class="btn btn-green borderl" type="button" id="mergeInfo">合并信息</button>
                                        <button class="btn btn-green borderl" type="button" id="perserve">提交订单</button>
                                        <!--<button class="btn btn-green borderl" type="button" id="submit" >提交订单</button>-->
                                    </div>
                                </div>
                                #end
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--<div class="page_food">-->
            <!--<div class="floatr">-->
            <!--<a class="nextbutt submit">免填单打印</a>-->
            <!--<a class="nextbutt submit">合并信息</a>-->
            <!--<a class="nextbutt submit" id ="submit">订单提交</a>-->
            <!--</div>-->
            <!--</div>-->
        </div>
    </div>
</div>
