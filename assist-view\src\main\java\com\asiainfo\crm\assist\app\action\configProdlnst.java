package com.asiainfo.crm.assist.app.action;

import com.alibaba.fastjson.JSON;
import com.asiainfo.crm.assist.app.query.ConfigDiscreateValueQuery;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IAssetCenterSMO;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import com.asiainfo.crm.sm.vo.GlbSessionVo;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Created by Administrator on 2017-7-26.
 */
@Component("vita.configProdlnst")
public class configProdlnst extends AbstractComponent {

    @Autowired
    private ISoQuerySMO soQuerySMO;
    @Autowired
    private IAssetCenterSMO assetCenterSMO;

    private static final Logger logger = LoggerFactory.getLogger(ConfigDiscreateValueQuery.class);

    @Override
    public Map achieveData(Object... objects) throws Exception {
        Map options = new HashMap();
        List valueList = soQuerySMO.queryBoActionTypeCdByNum();
        //System.out.println(valueList);
        options.put("serviceOfferIds",valueList);
        return options;
    }

    public Map configProdlnstQuery(String json) throws Exception {
        Map<String, Object> paramMaps = jsonConverter.toBean(json,Map.class);
        Map map = new HashMap();
        map.put("accNum", paramMaps.get("useNnumber"));
        map.put("regionId",paramMaps.get("sadffID"));
        String projectDetailsname = assetCenterSMO.qryAccProdInstListLocal(jsonConverter.toJson(map));
        String projectDetails = soQuerySMO.configProdlnstQuery(json);
        List queryList = soQuerySMO.configProdlnstQueryServiceOfferName(json);
        Map<String, Object> resultObjecnametMap = (Map<String, Object>) resolveResult(projectDetailsname);
        //取到客户名称
        List custNameList =(List)resultObjecnametMap.get("accProdInsts");
        String custName="";
        if(custNameList!=null && custNameList.size()>0){
            for (int i=0; i<custNameList.size(); i++){
                Map msg = (Map) custNameList.get(i);
                custName =(String) msg.get("custName");
            }
        }
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(projectDetails);
        Map<String, Object> options = Maps.newHashMap();
        List valueList = (List) MapUtils.getObject(resultObjectMap, "configProdlnstList");
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap, "pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount", pageCount);
        options.put("pageIndex", pageIndex);
        options.put("totalNumber", total);
        options.put("configProdlnstList", valueList);
        options.put("serviceOfferIdsQuery",queryList);
        Map<String, ?> paramMap = jsonConverter.toBean(json, Map.class);
        options.put("userNum", paramMap.get("useNnumber"));
        options.put("projectDetailsname",custName);
        return options;
    }

    public Map addProdlnst(String json)throws Exception{
        String projectDetails = soQuerySMO.addProdlnst(json);
//        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(projectDetails);
        Map<String, Object> resultObjectMap =new HashedMap();
        Map<String, Object> paramMaps = jsonConverter.toBean(projectDetails,Map.class);
        if(paramMaps.get("resultObject").equals("0")){
            resultObjectMap.put("result",0);
        }else{
            resultObjectMap.put("result",-1);
        }
        return resultObjectMap;
    }

    public Map delectProdlnst(String json){
        String projectDetails = soQuerySMO.updateProdlnst(json);
        Map rets = new HashMap();
        rets.put("delectProdlnstBackResult",projectDetails);
        return rets;
    }
}
