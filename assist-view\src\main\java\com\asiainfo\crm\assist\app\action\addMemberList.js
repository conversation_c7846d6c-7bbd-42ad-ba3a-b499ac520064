
(function (vita) {
	var manageRegionId = null;
    var addMemberList = vita.Backbone.BizView.extend({
        events: {
            "click #bindCheck": "_addMemberInfo"
        },
        _initialize: function () {
        	 var widget = this;
             element = $(widget.el);
	         var data= $options.data;
	         debugger;
	         var accessNums = data.accessNums;
	         var manageNum = data.manageNum;
	         var regionId = data.regionId;
	         manageRegionId = regionId;
	         widget._queryAddInfo(accessNums,manageNum);
       },
       global:{
           pageIndex: 1,
           pageSize: 4
       },
       _queryAddInfo : function (accessNums,manageNum) {
    	   var widget = this;
    	   var params = {
    			accNums:accessNums,
    			manageNum:manageNum,
    			pageInfo: {
                     pageIndex: widget.global.pageIndex,
                     pageSize: widget.global.pageSize
                }
    	   }
    	   debugger;
	       widget.refreshPart("queryAddInfo", JSON.stringify(params), "#addListDiv", function (res) {
           var paging = widget.require("paging"), r = $(res);
           var totalNumber = r.find("#showPageInfo").data("totalNumber");
           if (totalNumber > 0) {
               var e = paging.new({
                   recordNumber: widget.global.pageSize,
                   total: totalNumber,
                   pageIndex: widget.global.pageIndex,
                   callback: function (_pageIndex, _recordNumber) {
                       params.pageInfo.pageIndex = _pageIndex;
                       params.pageInfo.pageSize = _recordNumber;
                       if (widget._getConds()) {
                           widget.refreshPart("queryAddInfo", JSON.stringify(params), "#member");
                       }
                       ;
                   }
               });
               r.find("#showPageInfo").append(e.getElement());
           }

       }, {
           async: false
       }); 
       },
       _addMemberInfo: function (e) {
    	   var widget = this;
    	   element = $(widget.el);
    	   debugger;
    	   var button = $(e.target).closest("button");
    	   var orderInfo  = button.data("orderInfo");
    	   orderInfo.regionId = manageRegionId;
    	   widget.callService("bindNumberMsg", orderInfo,function (re) {
               if(re.resultCode=='0'){
            	   widget.popup("账户绑定成功");
                   return;
               }else{
            	   widget.popup(re.resultDesc);
                   return;
               }
           }, { async: false });
        },
        _getConds: function () {
            var widget = this;
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            return params;
        }
      });
    vita.widget.register("addMemberList", addMemberList, true);
})(window.vita);