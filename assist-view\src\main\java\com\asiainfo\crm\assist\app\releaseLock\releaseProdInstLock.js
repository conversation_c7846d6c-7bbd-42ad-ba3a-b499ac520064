(function(vita) {
    var releaseProdInstLock = vita.Backbone.BizView.extend( {
        events : {
            "click .btn-primary" : "_releaseLock",
            "click .btn-white" : "_clearCond",
            "click #regionIdBtn": "_chooseArea"
        },
        _initialize : function() {
            var widget = this, element = $(widget.el),gSession = widget.gSession;
        },
        global:{
            pageIndex: 1,
            pageSize: 4,
            preset: "date",
            chooseArea: "../comm/chooseArea"
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _clearCond: function () {
            var _input = $(".form-group").find("input.form-control");
            _input.val("");
            _input.attr("value", null);
            _input.attr('value','');
        },
        _getParam : function(){
            debugger;
            var widget = this,element = $(widget.el);
            var searchValue=element.find("#searchValue").val();
            var searchType=element.find("#searchType").val();
            var regionId =$("#regionId").attr("value");
            var searchRegix=/^(\d{6,13})$/;
            if(!searchRegix.test(searchValue)){
                widget.popup("请输入号码！");
                return false;
            }
            var param={
                "searchType": searchType,
                "accNum": searchValue
            }
            if(widget._isNullStr(regionId)) {
                widget.popup("请选择地区...");
                return false;
            }else {
                param.regionId = regionId;
            }
            return param;

        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _releaseLock : function() {
            var widget = this;
            var param = widget._getParam();
            debugger;
            if(param){
           widget.callService("releaseLock",param,function (res) {
                widget.popup(res);
            },{
                async: false,
                mask: true
            });
        }
        }


    });
    vita.widget.register("releaseProdInstLock", releaseProdInstLock, true);
})(window.vita);