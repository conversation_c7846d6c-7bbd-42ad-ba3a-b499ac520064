package com.asiainfo.crm.assist.app.action;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICustSMO;
import com.google.common.collect.Maps;

/**
 * Created by wenhy on 2017/7/20.
 */
@Component("vita.blacklistGovernment")
public class BblacklistGovernment extends AbstractComponent {

    private static final Logger LOGGER = LoggerFactory.getLogger(BblacklistGovernment.class);


    @Autowired
    private ICustSMO custSMO;
    
    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }
    
    public Map queryBlacklistGovernment(String str) throws IOException{
    	 String forbidChannelListStr = custSMO.qryCustomerDetailList(str);
    	 List customerDetailsList = null;
         Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(forbidChannelListStr);
         Map<String, Object> options = Maps.newHashMap();
         customerDetailsList = (List) MapUtils.getObject(resultObjectMap, "customerDetails");
         List list = dataProcessing(customerDetailsList);
         Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
         int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
         int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
         int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
         options.put("pageCount",pageCount);
         options.put("pageIndex",pageIndex);
         options.put("totalNumber", total);
         options.put("customerDetailsList", list);
         return options;
    }
    
    List dataProcessing(List customerDetailsList) throws IOException{
    	 List list = null;
    	 if(null != customerDetailsList && customerDetailsList.size() > 0){
    		 list = new ArrayList<>();
        	 for (int i = 0; i < customerDetailsList.size(); i++) {
        		Map customerDetailsMap =  (Map) customerDetailsList.get(i);
        		Map customerDetail = (Map) customerDetailsMap.get("customerDetail");
        		if ("1000".equals(customerDetail.get("custType"))) {
        			 Map rul = new HashMap();
        	    	 rul.put("specialType", 1300);
        	    	 rul.put("objType", 1100);
        	    	 rul.put("objId", customerDetail.get("custId"));
        			String res = custSMO.qrySpecialList(jsonConverter.toJson(rul));
        			Map resMap = (Map) resolveResult(res);
        			List specialListsList = (List) resMap.get("specialLists");
        			if (null != specialListsList && specialListsList.size() > 0) {
						for (int j = 0; j < specialListsList.size(); j++) {
							Map specialLists = (Map) specialListsList.get(j);
							customerDetail.put("specialTypeName",MapUtils.getObject(specialLists,"specialTypeName"));
							customerDetail.put("createStaffId",MapUtils.getObject(specialLists,"createStaff"));
							customerDetail.put("createDates",MapUtils.getObject(specialLists,"createDate"));
							customerDetail.put("checkFlag",MapUtils.getObject(specialLists,"checkFlag"));
							customerDetail.put("specialListReason",MapUtils.getObject(specialLists,"specialListReason"));
							break;
						}
					}
            		list.add(customerDetail);
				}
			}
         }
    	return list;
    }
    
    
    public Map addBlacklistGovernment(String str) throws Exception{
    	boolean boo = true;
    	Map strMap = jsonConverter.toBean(str, Map.class);
    	String ids = strMap.get("ids").toString();
    	String[] splitstr=ids.split(",");
        if (splitstr.length > 0) {
        	  for(String ret : splitstr){
        		 Date d=new Date();//获取时间
        	    SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd kk:mm:ss ");
        	    Calendar calendar = Calendar.getInstance();
        		Map param = new HashMap<>();
        		param.put("specialListReason" , strMap.get("specialListReason"));
        		param.put("specialListId" , calendar.getTime().getTime());
        		param.put("objId" , ret);
              	param.put("createDate" , sdf.format(new Date()));
              	param.put("objType" , 1100);
              	param.put("specialType" , 1300);
              	param.put("statusCd" , 1000);
              	param.put("checkFlag" , 1);
              	param.put("checker" , -1);
              	param.put("fromDept" , -1);
              	param.put("createChannel" , -1);
              	param.put("createStaff" , strMap.get("staffId"));
              	String res = custSMO.saveSpecialListAssist(jsonConverter.toJson(param));
              	String resultCode = (String) MapUtils.getObject(jsonConverter.toBean(res, Map.class), "resultCode");
              	if (!"0".equals(resultCode)) {
					boo = false;
				}
              }
	     	}
	        Map options = new HashMap<>();
	        if (boo) {
	       	 options.put("resultCode", 0);
	       	return options;
			}
	       return null;
    }
    
    public Map deleteBlacklistGovernment(String str) throws Exception{
    	boolean boo = true;
    	Map strMap = jsonConverter.toBean(str, Map.class);
    	String ids = strMap.get("ids").toString();
    	String[] splitstr=ids.split(",");
        if (splitstr.length > 0) {
        	 for(String ret : splitstr){
        		 Map param = new HashMap<>();
        		 param.put("objId" , ret);
        		 String res = custSMO.deletespecialList(jsonConverter.toJson(param));
        		 String resultCode = (String) MapUtils.getObject(jsonConverter.toBean(res, Map.class), "resultCode");
        		 if (!"0".equals(resultCode)) {
 					boo = false;
 				}
        	 }
        }
        Map options = new HashMap<>();
        if (boo) {
        	 options.put("resultCode", 0);
        	 return options;
		}
        return null;
    }
}
