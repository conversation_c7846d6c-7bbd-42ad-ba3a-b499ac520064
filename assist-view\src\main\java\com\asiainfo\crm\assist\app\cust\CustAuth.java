package com.asiainfo.crm.assist.app.cust;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.al.common.utils.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.common.CommMDA;
import com.asiainfo.crm.common.Constant;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IAssetCenterSMO;
import com.asiainfo.crm.service.intf.ICertNumFiveQuerySMO;
import com.asiainfo.crm.service.intf.ICustSMO;
import com.asiainfo.crm.service.intf.IExternalSystemServiceSmo;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.asiainfo.crm.service.intf.IQueryEopsSmo;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import com.asiainfo.crm.service.intf.IStaffDataPrvQuerySMO;
import com.asiainfo.crm.util.ListUtil;

/**
 * 客户鉴权
 */
@Component("vita.custAuth")
public class CustAuth extends AbstractSoComponent {

    @Autowired
    private ICertNumFiveQuerySMO certNumFiveQuerySmo;
    @Autowired
	private IExternalSystemServiceSmo externalSystemServiceSmo;
    @Autowired
	private IAssetCenterSMO assetCenterSMO; 
	@Autowired
	private ISoQuerySMO  iSoQuerySMO;
	@Autowired
	private IQueryEopsSmo eopsSmo;
	@Autowired
	private IStaffDataPrvQuerySMO staffDataPrvQuerySMO;
    @Autowired
    private ICustSMO custSMO;
    @Autowired
    private IProdInstSMO prodInstSmo;
	
    @Override
    public Map achieveData(Object... params) throws Exception {
        String str = (String) params[0];
        Map map = jsonConverter.toBean(str,Map.class);
        Map options = new HashMap();
        List custAuthTypes = new ArrayList();
        String certNum = String.valueOf(map.get("certNum"));
        String certAccNumStr = (String) map.get("certAccNum");
//        JSONArray certAccNumJson = JSONArray.parseArray(certAccNumStr);
        List certAccNum = JSONArray.parseArray(certAccNumStr, Map.class);
        String contactTele = String.valueOf(map.get("contactTele"));
//		String resString = custSMO.qryCustAuthTypeList(jsonString);
        String resString = "{\n" +
                " \"resultCode\":\"0\",\n" +
                " \"resultMsg\":\"处理成功!\",\n" +
                " \"resultObject\":{\n" +
                "  \"custAuthTypes\":[\n" +
                "   {\n" +
                "    \"custAuthCd\":\"1\",\n" +
                "    \"custAuthName\":\"证件识别\"\n" +
                "   },\n" +
                "   {\n" +
                "    \"custAuthCd\":\"2\",\n" +
                "    \"custAuthName\":\"短信认证\"\n" +
                "   },\n" +
                "   {\n" +
                "    \"custAuthCd\":\"3\",\n" +
                "    \"custAuthName\":\"维护短信认证\"\n" +
                "   }\n" +
                "  ]\n" +
                " }\n" +
                "}";
        Map resultMap = jsonConverter.toBean(resString, Map.class);
        String resultCode = MapUtils.getString(resultMap, "resultCode");
        if (!StringUtil.isEmpty(resultCode) && MDA.RESULT_SUCCESS.toString().equals(resultCode)) {
            Map areaObject = (Map) resultMap.get("resultObject");
            custAuthTypes = (List) areaObject.get("custAuthTypes");
            options.put("custAuthTypes", custAuthTypes);
        }
        options.put("certNum",certNum);
        options.put("certAccNum",certAccNum);
        options.put("contactTele",contactTele);
        options.put("number",(int)(999+Math.random()*10000));
        options.put("useDeskTop", MDA.CONST_USESECURE_DESKTOP);
        return options;
    }

	public Map checkCustomerList(Map<String, Object> map) throws Exception {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("resultCode", Constant.LOGIC_STR_1);
		String certType = MapUtils.getString(map, "certType");
		String certNum = MapUtils.getString(map, "certNum");
		String toTel = MapUtils.getString(map, "toTel");
		if (StringUtils.isBlank(certType) || StringUtils.isBlank(certNum)) {
			result.put("resultMsg", "证件类型、证件号码不能为空");
			return result;
		}

		Map<String, Object> params = new HashMap<String, Object>();
		params.put("certType", certType);
		params.put("certNum", certNum);
		String resultStr = custSMO.qryCustomerList(jsonConverter.toJson(params));
		Map custInfoMap = (Map) resolveResult(resultStr);
		List<Map> customers = (List<Map>) custInfoMap.get("customers");
		if (ListUtil.isListEmpty(customers)) {
			result.put("resultMsg", "查询不到客户信息");
			return result;
		}

		List<Long> prodIds = ListUtil.newArrayList();
		for (String prodIdStr : MDA.CDMA_PRODUCT_LIST) {
			prodIds.add(Long.valueOf(prodIdStr));
		}
		params.clear();
		params.put("prodIds", prodIds);

		boolean isMatch = false;
		loop: for (Map cust : customers) {
			Long custId = MapUtils.getLong(cust, "custId");

			params.put("custId", custId);
			String retStr = prodInstSmo.qryAccProdInstListLocal(jsonConverter.toJson(params));
			Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(retStr);
			List<Map<String, Object>> prodInsts = (List<Map<String, Object>>) MapUtils.getObject(resultObjectMap,"accProdInsts");
			for (Map prodInst : ListUtil.nvlList(prodInsts)) {
				String accNum = MapUtils.getString(prodInst, "accNum");
				if (accNum.equals(toTel)) {
					isMatch = true;
					break loop;
				}
			}
		}
		if (!isMatch) {
			result.put("resultMsg", "请输入该证件所属的产品接入号");
			return result;
		}
		result.put("resultCode", Constant.LOGIC_STR_0);
		return result;
	}
    
    
    /**
     * 短信验证发送短信
     * @param json
     * @return
     * @throws Exception
     */
    public String qrySmsCode(String json) throws Exception {
        String str = "";
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DAY_OF_MONTH, 1);
        Map<String,Object> map = jsonConverter.toBean(json,Map.class);
        Map<String, Object> strMap = new HashMap();
	       strMap.put("accNbr", String.valueOf(map.get("toTel")));
	       strMap.put("accNbrType", "1");
        //调用接口获取号码归属地区号
        String resultJosn = eopsSmo.queryAreaByAccNbr(jsonConverter.toJson(strMap));
        Map mapRes = (Map) resolveResult(resultJosn);
        if (null == mapRes) {
        	 return str;
		}
        String widgetName = MapUtils.getString(map, "widgetName");
        if (StringUtils.isNotBlank(widgetName) && CommMDA.AUTH_POINTS.containsKey(widgetName)) {
        	boolean isCheck = Constant.LOGIC_STR_Y.equals(CommMDA.AUTH_POINTS.get(widgetName));
        	if (isCheck) {
        		//查客户
        		Map checkMap = checkCustomerList(map);
        		String resultCode = MapUtils.getString(checkMap, "resultCode");
        		if (!Constant.LOGIC_STR_0.equals(resultCode)) {
        			return jsonConverter.toJson(checkMap);
        		}
        	}
        }
        String areaCode = (String) mapRes.get("zoneNumber");
        if ( areaCode.startsWith("0")) {
	    	   String noPreFix = areaCode.substring(1, areaCode.length());
	    	   map.put("latnId",noPreFix);
	    	   map.put("planId",noPreFix);
	       }else{
	    	   map.put("latnId",999);
	    	   map.put("planId",999);
	       }
	      
	        int randomCode = (int)(99999+Math.random()*10000000);
	        map.put("beginDate",df.format(new Date()));
	        map.put("endDate",df.format(c.getTime()));
	        //900051679 短信实时发送；
	        map.put("businessId",900051679);
	        map.put("sentContent", "您的手机号为"+map.get("toTel")+",当前业务办理的验证码为"+randomCode+"。");
	        //发送短信接口
	        String string = externalSystemServiceSmo.sendMessage(jsonConverter.toJson(map));
	        int stateDesc =string.indexOf("SUCCESS");
	        int state = string.indexOf("000");
	        //System.out.println(randomCode+"短信接口返回"+string);
	        if( state > 0 && stateDesc > 0 ){
	          str = ""+randomCode;
	        }
	        return str;

	      
    }

    public String verifyIdentity(String jsonString) throws Exception {
        String resString = "{\"resultCode\":\"0\",\"resultMsg\":\"处理成功!\",\"resultObject\":{\"isPass\":\"true\"}}";
        return resString;
    }

    /**
     * 短信码校验
     * @param jsonString
     * @return
     * @throws Exception
     */
    public String verifySmsAuth(String jsonString) throws Exception {
    	String resString = "";
    	Map map = jsonConverter.toBean(jsonString, Map.class);
    	if("" != map.get("inputCode") && "" != map.get("randomCodes")){
			String inputCode = (String) map.get("inputCode");
			String randomCodes = (String) map.get("randomCodes");
			if (randomCodes.equals(inputCode)) {
				resString = "1";
				return resString;
			}
    	}
		return resString;
    }

    /**
     * 维护短信码校验
     * @param jsonString
     * @return
     * @throws Exception
     */
    public String verifyProdPwd(String jsonString) throws Exception {
        //String resString = "{\"resultCode\":\"0\",\"resultMsg\":\"处理成功!\",\"resultObject\":{\"isPass\":\"true\"}}";
        String resString ="";
        Map map = jsonConverter.toBean(jsonString,Map.class);
        ;
        String inputCode = (String) map.get("inputCode");
        if(inputCode.equals(map.get("number").toString()) ){
            resString = "1";
            return resString;
        }

        return resString;
    }

    /**
     * 维护短信验证发送短信
     * @param json
     * @return
     * @throws Exception
     */
    public String prodPwdRelease(String json) throws Exception {
        String str = "";
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DAY_OF_MONTH, 1);
        Map<String,Object> map = jsonConverter.toBean(json,Map.class);
        String accNum = (String) map.get("accNum");
        if(!map.get("toTel").equals(accNum)){
            return str;
        }
        map.put("beginDate",df.format(new Date()));
        map.put("endDate",df.format(c.getTime()));
        certNumFiveQuerySmo.sendMessage(jsonConverter.toJson(map));
        str = "1";
        return str;
    }
    /**
     * 维护权限
     * @throws IOException 
     */
    public String maintenanceAuthority(String json) throws IOException{
    	Map inParam = jsonConverter.toBean(json, Map.class);
    	//查询员工是否有身份证查看权限
    	Map addressMap = new HashMap();
    	addressMap.put("sysUserId",inParam.get("sysUserId"));
    	addressMap.put("privCode",MDA.USER_PRIV_RIGHTS.get("maintenanceAuthority"));
    	//根据员工id查权限
    	String permissionReturn = staffDataPrvQuerySMO.checkSysUserPriv(jsonConverter.toJson(addressMap));
    	JSONObject addressPriv = JSONObject.parseObject(permissionReturn).getJSONObject("resultObject");
        if(addressPriv != null && "true".equals(addressPriv.getString("isHave"))){
        	return "";
        }
    	return "1";
    }
}