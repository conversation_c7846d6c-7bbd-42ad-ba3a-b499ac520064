(function (vita) {
    var preAccNumQuery = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_preAccNumQuery"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            var gSession = widget.gSession;
            if(gSession.curChannelId && gSession.curChannelId != "") {
                // element.find("#channelId").val(channelName).attr("value", gSession.curChannelId);
            } else {
                widget._chooseChannel(true);
            }
        },
        global: {
            pageIndex: 1,
            pageSize: 8,
            chooseChannel: "../comm/chooseChannel"
        },
        _chooseChannel: function(isNeedRefreshSession) {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseChannel,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);

                    if(isNeedRefreshSession) {
                        gSession.channelId   = data.orgId;
                        gSession.curChannelId= data.orgId;
                        gSession.curChannelNbr  = data.channelNbr;
                        gSession.channelName = data.channelName;
                        gSession.channelRegionId = data.channelRegionId;
                        gSession.channelRegionName = data.channelRegionName;
                        gSession.channelClass = data.channelClass;
                        gSession.orgId       = data.orgId;
                        gSession.orgName     = data.orgName;

                        gSession.orderArea   	 = data.channelRegionId; // 订单地区
                        gSession.installArea 	 = data.channelRegionId; // 安装地区
                        gSession.installAreaName = data.channelRegionName;
                        var param = {
                            curChannelId  : data.orgId,
                            curChannelNbr : data.channelNbr,
                            orderArea     : gSession.orderArea,
                            installArea   : gSession.installArea
                        };
                        widget.refreshSession(param);
                    }
                }
            });
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _preAccNumQuery: function () {
            var widget = this;
            var gsession = widget.gSession;
            if(!(gsession.curChannelId && gsession.curChannelId != "")) {
                widget.popup("请选择渠道！");
            }
            var params = {
                accNum: $("#accNum").val(),
                staffId: gsession.staffId,
                regionId: gsession.staffRegionId,
                channelId: gsession.curChannelId,
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            if ($("#c_partyCert").is(":checked")) {
                var partyCert = $.trim($("#partyCert").val());
                if(widget._isNullStr(partyCert)) {
                    widget.popup("请输入证件号码！");
                    return false;
                } else {
                    params.partyCert = partyCert;
                }
            }
            debugger;
            widget.refreshPart("preAccNumQuery", JSON.stringify(params), "#preAccNumResult", function (re) {
                debugger;
                var r = $(re), paging = this.require("paging");
                var e = paging.new({
                    recordNumber : widget.global.pageSize,
                    total : r.find("#showPageInfo").data("totalNumber"),
                    pageIndex : widget.global.pageIndex,
                    callback : function (pageIndex, pageSize) {
                        debugger;
                        var params = {
                            accNum: $("#accNum").val(),
                            staffId: gsession.staffId,
                            regionId: gsession.staffRegionId,
                            channelId: gsession.curChannelId,
                            pageInfo: {
                                pageIndex: pageIndex,
                                pageSize: widget.global.pageSize
                            }
                        };
                        widget.refreshPart("preAccNumQuery", JSON.stringify(params), "#preAccNumList");
                    }
                });
                r.find("#showPageInfo").append(e.getElement());
            }, { async: false });
        }
    });
    vita.widget.register("preAccNumQuery", preAccNumQuery, true);
})(window.vita);