package com.asiainfo.crm.assist.app.action;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IResourceSMO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by wenhy on 2017/7/11.
 */
@Component("vita.materialSend")
public class MaterialSend extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(MaterialSend.class);

    @Autowired
    private IResourceSMO resourceSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map materialRelaQuery(String jsonStr) throws Exception {
        String materialRelaListStr = resourceSMO.queryPreAccNum(jsonStr);

        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(materialRelaListStr);
        Map<String, Object> options = Maps.newHashMap();
        List materialRelaList = (List) MapUtils.getObject(resultObjectMap, "materialRelaList");
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", total);
        options.put("materialRelaList", materialRelaList);
        options.put("preAccNumList",(List)MapUtils.getObject(resultObjectMap, "preAccNumList"));
        return options;
    }

    public Map materialSend(String jsonStr) throws Exception {
        String preAccNumListStr = resourceSMO.queryPreAccNum(jsonStr);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(preAccNumListStr);
        Map<String, Object> options = Maps.newHashMap();
        List preAccNumList = (List) MapUtils.getObject(resultObjectMap, "preAccNumList");
        return options;
    }

}
