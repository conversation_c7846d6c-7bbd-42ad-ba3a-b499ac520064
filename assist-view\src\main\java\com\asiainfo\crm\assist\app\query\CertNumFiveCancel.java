package com.asiainfo.crm.assist.app.query;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.bcomm.exception.BError;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.util.SmLogUtil;

/**
 * 强制解除证号关系
 * Created by niewq on 2018/5/5.
 */
@Component("vita.certNumFiveCancel")
public class CertNumFiveCancel extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(CertNumFiveCancel.class);

	@Autowired
	private ICustMultiSMO custMultiSMO;

    @Override
    public Map<String, Object> achieveData(Object... params) throws Exception {
        return null;
    }

	/**
	 * 强制解除证号关系 直接调集团强制解除证号关系接口
	 * 
	 * @param jsonString
	 * @return
	 */
	public Map<String, Object> exeForceCancelRelation(String jsonString) {
		Map<String, Object> retMap = new HashMap<String, Object>();
		String resultJson = "";
		try {
			logger.debug(jsonString);
			jsonString = SmLogUtil.supplyOneFivePatams(jsonString);
			resultJson = custMultiSMO.forceChangeOCFNRelation(jsonString);
			Map<String, Object> resultObject = (Map) resolveResult(resultJson);
			retMap.put("retCode", resultObject.get("resultCode"));
			retMap.put("retDesc", resultObject.get("resultMsg"));
			return retMap;
		} catch (Exception e) {
			retMap.put("retCode", "-1");
			if (e instanceof BError) {
				BError bError = (BError) e;
				retMap.put("retDesc", bError.getMsg());
			} else {
				retMap.put("retDesc", e.getMessage());
			}
			return retMap;
		}
	}

}
