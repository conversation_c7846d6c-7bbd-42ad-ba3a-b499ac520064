(function(vita){
    var specialList = vita.Backbone.BizView.extend({
        events : {
            "click #qryCommonLogSearchBtn" : "_qrySpecialList",
            "click #searchDel": "_searchParamsDel",
            "click [name='moduleRadio'] " : "_clickRadio"
        },
        _initialize : function() {
            var widget = this,
                element = $(widget.el);
            data = element.data("data");
            widget.model.set(data);
            //初始化查询
            widget._qrySpecialList();
        },
        global : {
            pageIndex: 1,
            pageSize: 5, //每页记录数
        },
        _clickRadio : function(e){
            var widget = this,
                element = $(widget.el);
            var radio = $(e.target).closest('input');
            var data = radio.data('data');
            widget.model.set("selectedSpecialList",data);
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        /**
         * 获取查询条件参数对象
         */
        _getConds : function(pageIndex) {
            var widget = this,
                element = $(widget.el);
            gSession = widget.gSession;
            var param = {
                "pageInfo" : {
                    "pageIndex" : pageIndex,
                    "pageSize" : widget.global.pageSize
                }
            };
           /* var specialType = widget.model.get("specialType");
            if (!widget._isNullOrEmpty(specialType)) {
                param.specialType = specialType;
            }*/

            var specialType = element.find("#specialType").val();
            if (!widget._isNullOrEmpty(specialType)) {
                param.specialType = specialType;
            }
            return param;
        },
        _isNullOrEmpty: function (condValue) {
            var widget = this;
            var soUtil = widget.require("soUtil");
            var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
            return isNullOrEmpty;
        },
        /**
         * 清空查询条件
         */
        _searchParamsDel : function() {
            var widget = this,
                element = $(widget.el);
       /*     widget.model.set("specialType","");
            element.find("#specialType").val("");*/
            widget.model.set("specialType","");
            $("#specialType").val("");
        },
        


        _qrySpecialList : function(e) {
            var widget = this, element = $(widget.el);
            var param = widget._getConds(widget.global.pageIndex);
            element.find('#showPageInfo').empty();
            widget.refreshPart("querySpecialList", param, ".table-hover",
                "specialLists", function(res) {
                    var paging = this.require("paging");
                    var totalNumber = $(res).find("input").eq(1).data("totalNumber");
                    if (parseInt(totalNumber) > 0) {
                        var e = paging.new({
                            recordNumber : widget.global.pageSize,
                            total : totalNumber,
                            pageIndex : widget.global.pageIndex,
                            callback : function(pageIndex){
                                //判断当前页
                                if(pageIndex == null || pageIndex == undefined || isNaN(pageIndex)){
                                    pageIndex = 1;
                                }
                                widget.model.set("pageIndex",pageIndex);
                                widget.refreshPart("querySpecialList", widget._getConds(pageIndex), ".table-hover");
                            }
                        });
                        element.find('#showPageInfo').append(e.getElement());
                    }
                }, {
                    mask : true
                });
        },
    });
    vita.widget.register("specialList", specialList, true);
})(window.vita);
