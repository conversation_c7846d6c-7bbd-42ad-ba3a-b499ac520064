package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.aspect.annotations.SmLogAnnotation;
import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.bcomm.exception.BError;
import com.asiainfo.crm.bcomm.route.CommonContext;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.CommMDA;
import com.asiainfo.crm.common.Constant;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.ICertNumFiveQuerySMO;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.service.intf.IMenuCollectionSMO;
import com.asiainfo.crm.service.intf.IReceiptIvoiceQuerySMO;
import com.asiainfo.crm.sm.vo.GlbSessionVo;
import com.asiainfo.crm.util.SmLogUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.IOException;
import java.util.*;

import javax.servlet.http.HttpServletRequest;

/**
 * Created by cheny on 2017/7/10.
 */
@Component("vita.certNumFive")
public class CertNumFive extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(CertNumFive.class);

	@Autowired
	private IMenuCollectionSMO menuCollectionSMO;

    @Autowired
    private ICertNumFiveQuerySMO certNumFiveQrySmo;
	@Autowired
	private ICustMultiSMO custMultiSMO;
	@Autowired
	private IReceiptIvoiceQuerySMO iReceiptIvoiceQuerySMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    @SmLogAnnotation(logType="1000", qryPoint="certNumFive", funName="certNumFiveQuery", paramKeys = {"custName,certNum"})
    public Map certNumFive(String jsonStr) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        options.putAll(this.certNumFiveCore(jsonStr));
        Map<String,Object> mapString = jsonConverter.toBean(jsonStr,Map.class);
        if (AssistMDA.CERT_NUM_FIVE_SENSITIVE_ON) {
        	// 跳过身份证或者读卡
        	boolean passPriv = MapUtils.getBoolean(mapString, "passPriv", Constant.LOGIC_BOOLEAN_FALSE);
        	boolean isScanCertQry = MapUtils.getBoolean(mapString, "isScanCertQry", Constant.LOGIC_BOOLEAN_FALSE);
        	options.put("isHave", passPriv || isScanCertQry ? Constant.LOGIC_STR_1 : Constant.LOGIC_STR_0);
        } else {
        	Map map = new HashMap();
            map.put("sysUserId",mapString.get("staffId"));
            map.put("privCode", AssistMDA.CERT_NUM_FIVE_PASS);//一证五号跳过身份证校验权限
            //根据员工id查权限
            String permissionReturn = menuCollectionSMO.checkSysUserPriv(jsonConverter.toJson(map));
            Map<String,Object> permissionReturnMap = (Map<String, Object>) resolveResult(permissionReturn);
            String stre = MapUtils.getString(permissionReturnMap,"isHave");
            if ("false".equals(stre)){
                options.put("isHave","0");
            }else{
                options.put("isHave","1");
            }
        }
        return options;
    }

	/**
	 * 校验查询权限
	 * @param staffId
	 * @param staffCode
	 * @return
	 * @throws IOException
	 */
	public boolean checkCertNumFive(String staffId, String staffCode) throws IOException {
		String result = certNumFiveQrySmo.certNumFiveCheck(staffId, staffCode);
		Boolean checked = (Boolean) resolveResult(result);
		return checked;
	}

	/**
	 * 插入日志
	 * @param log
	 */
	public Integer createCertNumFiveLog(String log) throws IOException {
		String result = certNumFiveQrySmo.insertLog(log);
		Integer logId = (Integer) resolveResult(result);
		return logId;
	}

	/**
	 * 插入查询到的数据
	 * @param logData
	 */
	public void createCertNumFiveLogData(String logData) {
		certNumFiveQrySmo.insertLogData(logData);
	}

    public Map certNumFiveCore(String jsonStr) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        Map<String,Object> mapString = jsonConverter.toBean(jsonStr,Map.class);
        Map map = new HashMap();
        map.put("sysUserId",mapString.get("staffId"));
        map.put("privCode",AssistMDA.JURISDICTION);
        //根据员工id查权限
        String permissionReturn = menuCollectionSMO.checkSysUserPriv(jsonConverter.toJson(map));
        Map<String,Object> permissionReturnMap = (Map<String, Object>) resolveResult(permissionReturn);
        String stre = MapUtils.getString(permissionReturnMap,"isHave");
        if ("false".equals(stre.toString())){
            //options.put("resultValue","权限不足");
            //return options;
        }
        String regionId = mapString.get("regionId").toString();
        if (MDA.PROVINCE_AREA.equals(regionId)) {
        	mapString.put("regionId", "");
		}
       //查询
        mapString.put("numberAttribution", AssistMDA.SWITCH_CERT_NUM_FIVE_NBR_ATT);
        if (AssistMDA.CERT_NUM_FIVE_SENSITIVE_ON) {
        	boolean passPriv = MapUtils.getBoolean(mapString, "passPriv", Constant.LOGIC_BOOLEAN_FALSE);
        	boolean isScanCertQry = MapUtils.getBoolean(mapString, "isScanCertQry", Constant.LOGIC_BOOLEAN_FALSE);
        	String authCode = null;
        	if (passPriv) {
        		authCode = AssistMDA.CERT_NUM_FIVE_PASS;
        	} else if (isScanCertQry) {
        		authCode = "isScanCertQry";
        	}
        	boolean isHave = passPriv || isScanCertQry;
        	if (!isHave) {
        		isHave = this.isSysUserPriv("view");
        		if (isHave) {
        			authCode = MDA.UNMIXED_PRIVCODE;
        		}
        	}
        	CommonContext.INST.put("isHave", String.valueOf(isHave));
        	HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        	GlbSessionVo gSession = (GlbSessionVo) request.getSession().getAttribute("g_session");
        	Map commOperInfoLog = new HashMap();
        	commOperInfoLog.put("staffId", gSession.getStaffId());
        	commOperInfoLog.put("channelId", gSession.getCurChannelId() != null ? gSession.getCurChannelId() : gSession.getOrgId());
        	commOperInfoLog.put("dealPoint", "一证5号单独菜单");
        	commOperInfoLog.put("regionId", gSession.getStaffRegionId());
        	commOperInfoLog.put("glbSessionId", gSession.getGlbSessionId());
        	commOperInfoLog.put("agentStaffId", gSession.getStaffId());
        	commOperInfoLog.put("isDesens", isHave ? Constant.LOGIC_STR_1 : Constant.LOGIC_STR_0);
        	commOperInfoLog.put("authCode", authCode);
        	mapString.put("commOperInfoLog", commOperInfoLog);
        }
        String jsonString = jsonConverter.toJson(mapString);
        jsonString = SmLogUtil.supplyOneFivePatams(jsonString);
        String resultJson = certNumFiveQrySmo.certNumFiveQuery(jsonString);
        Map<String, Object> resultObjectMap =(Map<String, Object>) resolveResult(resultJson);
        if (resultObjectMap == null) {
        	 options.put("certNumFiveList", null);
             return options;
		}
        List certNumFiveList = new ArrayList<>();
        if (resultObjectMap.containsKey("resultObject")) {
        	Map<String, Object> resultObjects = (Map<String, Object>)resultObjectMap.get("resultObject");
        	certNumFiveList = (List) MapUtils.getObject(resultObjects,"value");
		}else {
			certNumFiveList = (List) MapUtils.getObject(resultObjectMap,"value");
		}
        if(certNumFiveList == null || certNumFiveList.size() < 0){
            options.put("certNumFiveList", null);
            return options;
        }
       // Map<String, Object> AccNum = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"valueAccNum");
       Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int totalCount = MapUtils.getIntValue(pageInfoMap, "totalCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageSize = MapUtils.getIntValue(pageInfoMap, "pageSize");
        options.put("resultValue",null);
        options.put("pageCount",pageSize);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", totalCount);
        options.put("certNumFiveList", certNumFiveList);
//        options.put("AccNum",AccNum);
       // options.putAll(AccNum);
        options.put("statusSwitch", AssistMDA.SWITCH_CERT_NUM_FIVE_STATUS);
        return options;

    }

	/**
	 * 权限查询接口
	 * 
	 * @param type
	 * @return
	 * @throws Exception
	 */
	public boolean isSysUserPriv(String type) throws Exception {
		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		GlbSessionVo gSession = (GlbSessionVo) request.getSession().getAttribute("g_session");
		Map params = new HashMap();
		params.put("sysUserId", gSession.getSystemUserId());
		if ("pass".equals(type)) {
			params.put("privCode", AssistMDA.CERT_NUM_FIVE_PASS);
		} else if ("view".equals(type)) {
			params.put("privCode", MDA.UNMIXED_PRIVCODE);
		} else {
			return false;
		}
		String re = menuCollectionSMO.checkSysUserPriv(jsonConverter.toJson(params));
		Map result = (Map) resolveResult(re);
		return MapUtils.getBoolean(result, "isHave");
	}

    /**
     * 一证五号修复接口
     * 调集团一证五号释放接口修复
     * @param jsonString
     * @return
     */
    public Map exeRepairRelation(String jsonString) throws Exception{
       //调集团一证五号释放接口修复
    	Map<String, Object> retMap = new HashMap<String, Object>();
    	Map<String,Object> mapString = jsonConverter.toBean(jsonString,Map.class);
        Map map = new HashMap();
        map.put("sysUserId",mapString.get("staffId"));
        map.put("privCode",AssistMDA.REPAIR_RELATION);
        //根据员工id查权限
        String permissionReturn = menuCollectionSMO.checkSysUserPriv(jsonConverter.toJson(map));
        Map<String,Object> permissionReturnMap = (Map<String, Object>) resolveResult(permissionReturn);
        String stre = MapUtils.getString(permissionReturnMap,"isHave");
        if ("false".equals(stre.toString())){
        	retMap.put("retCode", "-1");	
         	retMap.put("retDesc", "您没有证号关系变的权限操作！");
     		return retMap;
        }
    	String resultJson = "";
    	 try{
			 jsonString = SmLogUtil.supplyOneFivePatams(jsonString);
    		 resultJson = custMultiSMO.changeOCFNRelation(jsonString);
 			 Map<String, Object> resultObject = (Map) resolveResult(resultJson);
 			 retMap.put("retCode", resultObject.get("resultCode"));
 			 retMap.put("retDesc", resultObject.get("resultMsg"));
 			 return retMap;
 		} catch (Exception e) {
 			retMap.put("retCode", "-1");
			if (e instanceof BError){
	            BError bError = (BError)e;
	            retMap.put("retDesc", bError.getMsg());
			}else{
				retMap.put("retDesc", e.getMessage());
			}
			return retMap;
 		}
    }
    
    /**
     * 查询日志记录
     * @throws IOException 
     */
    public Map logLog(String strJson) throws IOException{
    	Map strMap = jsonConverter.toBean(strJson, Map.class);
    	 Map map = new HashMap();
    	 StringBuffer buffer = new StringBuffer(strMap.get("certNum").toString());
    	/* if ("1".equals(strMap.get("certTypeId"))) {
    		  buffer.replace(6, 14, "********");
		}*/
    	 map.put("certNum", buffer);
    	 map.put("certType", strMap.get("certTypeId"));
    	 map.put("createStaff", strMap.get("staffId"));
    	 map.put("totalNbr", strMap.get("totalNumber"));
    	 String str =  iReceiptIvoiceQuerySMO.saveCertNumFiveJournal(jsonConverter.toJson(map));
    	 Map strmap = new HashMap();
    	 strmap.put("ret", str);
    	 return strmap;
    }

}
