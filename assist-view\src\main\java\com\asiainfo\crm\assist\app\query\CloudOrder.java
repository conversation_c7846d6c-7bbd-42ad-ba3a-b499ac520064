package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("vita.cloudOrder")
public class CloudOrder extends AbstractComponent {

    @Autowired
    private IOrderQuerySMO orderQuerySMO;

    @Override
    public Map achieveData(Object... objects) throws Exception {
        Map<String, Object> data = new HashMap<String, Object>();
        return data;
    }


    public Map qryCloudOrder(String jsonString) throws Exception {

        Map option = new HashMap();
        Map<String,Object> returnMap = new HashMap<String,Object>();
        String resultStr  = orderQuerySMO.qryCloudOrderList(jsonString);
        Map resultMap = jsonConverter.toBean(resultStr, Map.class);
        List cloudOrders = (List) MapUtils.getObject(resultMap, "resultObject");
        option.put("cloudOrders", cloudOrders);
        return  option;

    }
}
