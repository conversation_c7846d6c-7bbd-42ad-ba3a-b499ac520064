(function(vita){
    var logisticsQuery = vita.Backbone.BizView.extend({
        events : {
            "click #btn-qryLogisitcs":"_qryLogistics",
            "click #btn-clear":"_clear",
            "click #modify":"_modify",
            "change #queryType":"_changeQueryType"
        },
        global:{
            pageIndex: 1,
            pageSize: 4,
            preset: "date",
            editLogisticsInfo : "../query/editLogistics"
        },
        _initialize:function(){
            var widget = this,
                element = $(widget.el);
            widget._changeQueryType();
        },
        _clear : function(){
            var widget = this,element = $(this.el);
            element.find("input").val("");
        },
        _getConds : function(){
            var widget = this,element = $(this.el),gSession = widget.gSession;
            var param = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            param.glbSessionId =  gSession.glbSessionId;
            var groupOrderId = element.find("#groupOrderId").val().trim();
            var broadbandCoId = element.find("#broadbandCoId").val().trim();
            var receiverPhone = element.find("#receiverPhone").val().trim();
            var logisticsNbr = element.find("#logisticsNbr").val().trim();
            if(groupOrderId){
                param.groupOrderId = groupOrderId;
            }
            if(broadbandCoId){
                param.broadbandCoId = broadbandCoId;
            }
            if(receiverPhone){
                param.receiverPhone = receiverPhone;
            }
            if(logisticsNbr){
                param.logisticsNbr = logisticsNbr;
            }
            if(!param.groupOrderId && !param.broadbandCoId && !param.receiverPhone && !param.logisticsNbr){
                return null;
            }
            return param;
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _modify : function(e) {
            var widget = this,
                aEl = $(e.target).closest("button");
            var gSession  = widget.gSession;
            var logisticsData = aEl.data("data");
            if(!logisticsData || !logisticsData.logisticsId){
                return;
            }
            var params = {
                logisticsId : logisticsData.logisticsId,
                optionalId : logisticsData.optionalId,
                receiver : logisticsData.receiver,
                receiverAddr : logisticsData.receiverAddr,
                receiverPhone : logisticsData.receiverPhone,
                postCode : logisticsData.postCode,
                logisticsNbr : logisticsData.logisticsNbr,
                logisticsCompany : logisticsData.logisticsCompany,
                statusCd : logisticsData.statusCd,
                createDt : logisticsData.createDt,
                remark : logisticsData.remark
            };
            var option = {
                url : widget.global.editLogisticsInfo,
                params : params,
                onClose : function(res) {
                    //重新查询，刷新页面
                    widget._qryLogistics();
                }
            };
            widget.dialog(option);
        },
        _qryLogistics : function () {
            var widget = this;
            if(!widget._validate()){
                return;
            }
            var param = widget._getConds();
            //传入sessionId
            var gSession = widget.gSession;
            param.glbSessionId = gSession.glbSessionId;

            widget.refreshPart("qryLogisticsList", JSON.stringify(param), "#logisticsListId",function(res){
                var paging = widget.require("paging"), r = $(res);
                var totalNumber = r.find("#showPageInfo").data("totalNumber");
                if (parseInt(totalNumber) > 0) {
                    var e = paging.new({
                        recordNumber : widget.global.pageSize,
                        total : totalNumber,
                        pageIndex : widget.global.pageIndex,
                        callback: function (_pageIndex, _recordNumber) {
                            param.pageInfo.pageIndex = _pageIndex;
                            param.pageInfo.pageSize = _recordNumber;

                            if (widget._getConds()) {
                                widget.refreshPart("qryLogisticsList", JSON.stringify(param), "#logisticsListTbody");
                            };
                        }
                    });
                    r.find("#showPageInfo").append(e.getElement());
                }
            },{mask: true});
        },
        _changeQueryType : function(e){
            var widget = this,element = $(this.el);
            var tar = null;
            if(!e || typeof e == undefined){
                tar = element.find("#queryType");
            }else{
                tar = $(e.target);
            }
            if(tar.prop("checked")){
                element.find("#receiverPhone").val("").closest("div.form-group").css("display","none");
                element.find("#logisticsNbr").val("").closest("div.form-group").css("display","none");
                element.find("#groupOrderId").closest("div.form-group").css("display","block");
                element.find("#broadbandCoId").closest("div.form-group").css("display","block");
            }else{
                element.find("#groupOrderId").val("").closest("div.form-group").css("display","none");
                element.find("#broadbandCoId").val("").closest("div.form-group").css("display","none");
                element.find("#receiverPhone").closest("div.form-group").css("display","block");
                element.find("#logisticsNbr").closest("div.form-group").css("display","block");
            }
        },
        _validate : function(){
            var widget = this,element = $(widget.el);
            var flag = true;
            var groupOrderId = element.find("#groupOrderId").val().trim();
            var broadbandCoId = element.find("#broadbandCoId").val().trim();
            var receiverPhone = element.find("#receiverPhone").val().trim();
            var logisticsNbr = element.find("#logisticsNbr").val().trim();
            if(!groupOrderId && !broadbandCoId && !receiverPhone && !logisticsNbr){
                flag = false;
                widget.popup("请输入查询内容");
                return flag;
            }else{
                if(broadbandCoId){
                    if(broadbandCoId%1 === 0){

                    }else{
                        flag = false;
                        widget.popup("宽带购物车id应为数字类型");
                        return flag;
                    }

                }
            }

            return flag;
        }
    });
    vita.widget.register("logisticsQuery", logisticsQuery, true);
})(window.vita);