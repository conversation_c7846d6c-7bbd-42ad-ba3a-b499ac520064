<div data-widget="specialList">
    <vita-data>{"data":$options}</vita-data>
    <p class="vita-data">$options</p>
    <div class="sypage-nav">
        <div class="nav_alink"><strong class="text16">特殊名单</strong></div>
    </div>
    <div class="sypage-min">
        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">查询条件</strong></div>
        </div>
        <div class="sypagemin_content ">
            <form>

                <div class="form-group col-md-6">
                    <label class="col-md-4 control-label lablep">特殊名单类型</label>
                    <div class="col-md-6">
                        <select class="form-control" id="specialType">
                            <option value="">-请选择-</option>
                            <option value="1000">红名单</option>
                            <option value="1100">黑名单</option>
                            <option value="1200">灰名单</option>
                        </select>
                        <p class="vita-bind" model="specialType"></p>
                    </div>
                </div>

                <div class="form-group col-md-12">
                    <div class="col-md-12 searchbutt_r" align="right">
                        <button type="button" class="btn btn-default" id="searchDel">清除</button>
                        <button type="button" class="btn btn-primary" id="qryCommonLogSearchBtn">搜索</button>
                    </div>
                </div>


            </form>
        </div>

        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">特殊名单列表</strong></div>
        </div>
        <div class="sypagemin_content ">
            <div class="col-lg-12 mart10">
                <table class="table table-hover" id="systemRoleList">
                    <thead>
                    <tr>
                        <th>选择</th>
                        <th>特殊名单类型</th>
                        <th>作用对象类型</th>
                        <th>特殊名单原因</th>
                        <th>创建时间</th>
                        <th>状态</th>
                        <th>生效时间</th>
                        <th>失效时间</th>
                    </tr>
                    </thead>
                    <tbody>
                    #if($options != "null" && $options.specialLists && $options.specialLists != "null" && $options.specialLists.size() > 0)
                    #foreach($specialList in $options.specialLists)
                    <tr >
                        <td>
                            <label class="wu-radio full absolute" data-scope="">
                                <input type="radio" name="moduleRadio" />
                                <p class="vita-data">{"data":$specialList,"totalNumber":$options.totalNumber}</p>
                            </label></td>
                        <td>$!specialList.specialTypeName</td>
                        <td>#if($!specialList.objType == 1000)参与人
                            #elseif($!specialList.objType == 1100)客户
                            #elseif($!specialList.objType == 1200)账户
                            #elseif($!specialList.objType == 1300)产品实例
                            #elseif($!specialList.objType == 1400)银行账号
                            #elseif($!specialList.objType == 1500)证件号码
                            #end
                        </td>
                        <td>$!specialList.specialListReason</td>
                        <td>$!specialList.createDate</td>
                        <td>#if($!specialList.statusCd == 1000)有效
                            #elseif($!specialList.statusCd == 1100)无效
                            #end
                        </td>
                        <td>$!specialList.effDate</td>
                        <td>$!specialList.expDate</td>
                    </tr>
                    #end
                    #end
                    </tbody>
                </table>
                <!--翻页start -->
                <div class="page-box" id="showPageInfo">
                </div>
                <!--翻页end -->
            </div>
        </div>
    </div>
</div>