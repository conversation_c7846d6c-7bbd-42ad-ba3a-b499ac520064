<div data-widget="cloudOrder">
    <vita-data>{"data":$options}</vita-data>
    <p class="vita-data">$options</p>
    <div class="sypage-nav">
        <div class="nav_alink"><strong class="text16">云公司订单查询</strong></div>
    </div>
    <div class="sypage-min">
        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">查询条件</strong></div>
        </div>
        <div class="sypagemin_content ">
            <form>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">*专业公司订单编码</label>
                    <div class="col-md-8">
                        <input type="text" class="form-control" placeholder="" id="bizId">
                        <p class="vita-bind" model="bizId"></p>
                    </div>
                </div>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep"><span class="textcolorred">*</span>地区</label>
                    <div class="col-md-8">
                        <div class="input-group">
                            <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                            <div class="input-group-btn">
                                <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group col-md-12" id="qryTimeQuantum">
                    <label class="col-md-2 control-label lablep">
                        起止时间</label>
                    <div class="col-md-10 form-inline">

                        <div class="form-group">
                            <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="开始时间">
                        </div>
                        <div class="form-group">
                            <input name="endDate" id="endDate" type="text" class="form-control" placeholder="结束时间">
                        </div>
                        <button type="button" id="timeQuantum_1" name="timeQuantum_1" class="btn btn-gray btn-outline btn-sm">当天</button>
                        <button type="button" id="timeQuantum_7" name="timeQuantum_7" class="btn btn-gray btn-outline btn-sm">七天</button>
                        <button type="button" id="timeQuantum_30" name="timeQuantum_30" class="btn btn-gray btn-outline btn-sm">一个月</button>
                        <button type="button" id="timeQuantum_90" name="timeQuantum_90" class="btn btn-gray btn-outline btn-sm">三个月</button>

                    </div>
                </div>
                <div class="form-group col-md-12">
                    <div class="col-md-12 searchbutt_r" align="right">
                        <button type="button" class="btn btn-default" id="searchDel">清除</button>
                        <button type="button" class="btn btn-primary" id="qryCloudOrderSearchBtn">搜索</button>
                    </div>
                </div>

            </form>
        </div>

        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">云公司订单列表</strong></div>
        </div>
        <div class="sypagemin_content ">
            <div class="col-lg-12 mart10">
                <table class="table table-hover" id="systemRoleList">
                    <thead>
                    <tr>

                        <th>主订单编码</th>
                        <th>主订单类型</th>
                        <th>操作类型</th>
                        <th>受理时间</th>
                        <th>总价格</th>
                        <th>订单来源</th>
                    </tr>
                    </thead>
                    <tbody>
                    #if($options != "null" && $options.cloudOrders && $options.cloudOrders != "null" && $options.cloudOrders.size() > 0)
                    #foreach($cloudOrder in $options.cloudOrders)
                    <tr >
                        <td> <a class="textcolorgreen" link="cloudOrderInfo"  data-toggle="tooltip"
                                data-placement="top">$!{cloudOrder.masterOrderNo}</a>
                            <p class="vita-data">{"masterOrderNo" : "$!cloudOrder.masterOrderNo", "masterOrderType": "$!cloudOrder.masterOrderType", "acceptDate":"$!cloudOrder.acceptDate"}</p>
                            <br>
                        </td>
                        <td>$!cloudOrder.masterOrderType</td>
                        <td>$!cloudOrder.operType</td>
                        <td>$!cloudOrder.acceptDate</td>
                        <td>$!cloudOrder.totalPrice</td>
                        <td>$!cloudOrder.source</td>
                    </tr>
                    #end
                    #end
                    </tbody>
                </table>
                <!--翻页start -->
                <div class="page-box" id="showPageInfo">
                </div>
                <!--翻页end -->
            </div>
        </div>
    </div>
</div>