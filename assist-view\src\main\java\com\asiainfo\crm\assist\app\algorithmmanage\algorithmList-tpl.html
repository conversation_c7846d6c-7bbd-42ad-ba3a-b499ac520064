<div data-widget="algorithmList" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright">
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                算法编码
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="algoNbr" type="text" class="form-control" placeholder="" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                算法密钥
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="algoKey" type="text" class="form-control" placeholder="" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                状态
                                            </label>
                                            <div class="col-md-7">
                                                <select id="statusCd" class="form-control">
                                                    <option value="">全部</option>
                                                    #foreach($item in $!options.dataSecurityStatusCds.entrySet())
                                                    <option value="$!{item.key}">$!{item.value}</option>
                                                    #end
                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-12" id="qryTimeQuantum" style="display: none;">
                                            <label class="col-md-2 control-label lablep">
                                                起止时间
                                            </label>
                                            <div class="col-md-10 form-inline">
                                                <div class="form-group">
                                                    <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="开始时间">
                                                </div>
                                                <div class="form-group">
                                                    <input name="endDate" id="endDate" type="text" class="form-control" placeholder="结束时间">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-11">
                                            <div class="col-md-12 searchbutt_r" align="right">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-clear" type="button" class="btn btn-white">清除</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-add" type="button" class="btn btn-info">新增</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title"></div>
                                <div class="wmin_content">
                                    <div class="col-lg-12 mart10" id="algorithmListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>选择</th>
                                                <th>算法ID</th>
                                                <th>算法编码</th>
                                                <th>密钥</th>
                                                <th>算法描述</th>
                                                <th>状态</th>
                                                <th>创建时间</th>
                                                <th>更新时间</th>
                                                <th>操作</th>
                                            </tr>
                                            </thead>
                                            <tbody id="algorithmList">
                                            #if($options != "null" && $options.algorithms && $options.algorithms != "null" && $options.algorithms.size() > 0)
                                                #foreach($algorithm in $options.algorithms)
                                                <tr>
                                                    <td>
                                                        <label class="wu-radio full absolute" data-scope="">
                                                            <input type="radio" name="algorithmRadio">
                                                        </label>
                                                    </td>
                                                    <td>$!{algorithm.algoId}</td>
                                                    <td>$!{algorithm.algoNbr}</td>
                                                    <td>$!{algorithm.algoKey}</td>
                                                    <td>$!{algorithm.algoDesc}</td>
                                                    <td>
                                                        #foreach($item in $!options.dataSecurityStatusCds.entrySet())
                                                            #if($!{item.key} == $!{algorithm.statusCd})
                                                                $!{item.value}
                                                            #end
                                                        #end
                                                    </td>
                                                    <td>$!{algorithm.createDate}</td>
                                                    <td>$!{algorithm.updateDate}</td>
                                                    <td>
                                                        <button name="modifyBtn" type="button" class="btn btn-primary">修改</button>
                                                        <p class="vita-data">{"algoId" : $!algorithm.algoId}</p>
                                                        #if($!{algorithm.statusCd} == "1000")
                                                        <button name="archiveBtn" type="button" class="btn btn-primary">归档</button>
                                                        <p class="vita-data">{"algoId" : $!algorithm.algoId}</p>
                                                        #end
                                                    </td>
                                                </tr>
                                                #end
                                            #elseif($options != "null" && $options.algorithms && $options.algorithms != "null" && $options.algorithms.size() == 0)
                                                <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo"></div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                            <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>