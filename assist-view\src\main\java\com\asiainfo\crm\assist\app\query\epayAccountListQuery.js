(function (vita) {
    var epayAccountListQuery = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_epayAccountListQuery",
            "click #btn-clear": "_clearCond"

        },


        //查找 翼支付账户
        _epayAccountListQuery: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {

                widget.refreshPart("queryEpayAccount", JSON.stringify(params), "#accountListResult", function (res) {
                    var t = res;
                    console.info(res);

                }, {
                    async: false
                });
            }
            ;
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this, element = $(widget.el);
            var params = {};
            var accNbr = $.trim(element.find("#accNbr").val());
            if (widget._isNullStr(accNbr)) {
                widget.popup("请输入电信产品号码！");
                return false;
            }
            params.accNbr = accNbr;
            return params;
        }
    });
    vita.widget.register("epayAccountListQuery", epayAccountListQuery, true);
})(window.vita);