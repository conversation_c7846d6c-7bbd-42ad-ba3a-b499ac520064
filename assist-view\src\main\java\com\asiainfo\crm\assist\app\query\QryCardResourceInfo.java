package com.asiainfo.crm.assist.app.query;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderSaveSmo;
import com.asiainfo.crm.service.intf.IResourceSMO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Created on 2019/4/16.
 */
@Component("vita.qryCardResourceInfo")
public class QryCardResourceInfo extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(QryCardResourceInfo.class);

    @Autowired
    private IResourceSMO resourceSMO;

    @Autowired
    private IOrderSaveSmo orderSaveSmo;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    /**
     * 用户卡资源信息查询
     *
     * @param json
     * @return
     * @throws Exception
     */
    public Map qryCardResourceInfo(String json) throws Exception {
        Map<String, Object> options = new HashMap<>(4);
        Map inputParam = jsonConverter.toBean(json, Map.class);
        String accNum = MapUtils.getString(inputParam, "accNum");
        String umiNum = MapUtils.getString(inputParam, "umiNum");
        String imsiNum = MapUtils.getString(inputParam, "imsiNum");
        String regionId = MapUtils.getString(inputParam, "regionId");
        if (StringUtil.isEmptyStr(regionId) || (StringUtil.isEmptyStr(accNum) && StringUtil.isEmptyStr(umiNum) && StringUtil.isEmptyStr(imsiNum))) {
            return null;
        }
        Map reqParam = new HashMap(4);
        Map routeParam = new HashMap(2);
        routeParam.put("regionId", regionId);
        reqParam.put("routeParam", routeParam);
        Map rsp = new HashMap(8);
        if (!StringUtil.isEmptyStr(accNum)) {
            reqParam.put("accNum", accNum);
            rsp.put("qryNum", accNum);
        }
        if (!StringUtil.isEmptyStr(umiNum)) {
            reqParam.put("mktResInstNbr", umiNum);
            rsp.put("qryNum", umiNum);
        }
        if (!StringUtil.isEmptyStr(imsiNum)) {
            reqParam.put("imsi", imsiNum);
            rsp.put("qryNum", imsiNum);
        }
        rsp.putAll(qryCardResourceByNum(reqParam));
        options.put("cardInfos", rsp);
        return options;
    }

    /**
     * 记录用户卡资源信息查询的日志
     *
     * @param json
     * @return
     * @throws Exception
     */
    public void saveQryCardResourceLog(String json) throws Exception {
        resolveResult(orderSaveSmo.saveCardResourceQryLog(json));
    }


    /**
     * 通过手机号/UIM卡号/IMSI号查询C网、G网和LTE IMSI号
     *
     * @param req
     * @return
     * @throws Exception
     */
    public Map qryCardResourceByNum(Map req) throws Exception {
        Map rsp = new HashMap(8);
        String uimResult = resourceSMO.qryDevNumByTerminalCode(jsonConverter.toJson(req));
        Map<String, Object> retMap = (Map<String, Object>) resolveResult(uimResult);
        //C网IMSI号
        String imis = MapUtils.getString(retMap, "imis", "");
        //G网IMSI号
        String gImis = MapUtils.getString(retMap, "gImis", "");
        //LTE IMSI号
        String lteImis = MapUtils.getString(retMap, "lteImis", "");
        //PIN
        String pin = MapUtils.getString(retMap, "pin1", "");
        //PUK
        String puk = MapUtils.getString(retMap, "puk1", "");
        //ESN
        String esn = MapUtils.getString(retMap, "uimId", "");
        //accNum
        String accNum = MapUtils.getString(retMap, "accNum", "");
        //uim
        String uim = MapUtils.getString(retMap, "mktResInstNbr", "");
        //接入号与uim卡互相反查
        String accNumName = "accNum";
        if (req.containsKey(accNumName)) {
            retMap.remove(accNumName);
            rsp.put("uim", uim);
        }
        String uimName = "mktResInstNbr";
        if (req.containsKey(uimName)) {
            retMap.remove(accNumName);
            rsp.put("accNum", accNum);
        }
        String imsiName = "imsi";
        if (req.containsKey(imsiName)) {
            if (!StringUtil.isEmptyStr(accNum)) {
                rsp.put("accNum", accNum);
            }
            if (!StringUtil.isEmptyStr(uim)) {
                rsp.put("uim", uim);
            }
        }
        rsp.put("imsi", imis);
        rsp.put("gImsi", gImis);
        rsp.put("lteImsi", lteImis);
        rsp.put("pin", pin);
        rsp.put("puk", puk);
        rsp.put("esn", esn);
        return rsp;
    }

}
