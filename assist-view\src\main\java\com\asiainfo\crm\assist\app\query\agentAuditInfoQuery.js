(function (vita) {
    var agentAuditInfoQuery = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_agentAuditInfoQuery",
            "click #btn-clear": "_clearCond",
            "click #regionIdBtn": "_chooseArea",
            "click #channelIdBtn": "_chooseChannel",

            "click #out_excel": "_excelExport"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
            var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd"
                });
            }
            ;
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd"
                });
            }
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
        },
        global: {
            pageIndex: 1,
            pageSize: 10,
            preset: "date",
            chooseArea: "../comm/chooseArea",
            chooseChannel: "../comm/chooseChannel"
        },
        _clearCond: function () {
            var $checked = $(".form-group").find("input[type=checkbox]:checked");
            $.each($checked, function (i,chkInput) {
                chkInput.click();
                var $input = $(chkInput).closest(".form-group").find("input.form-control");
                $input.val("");
                $input.attr("value", null);
            })
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _chooseChannel: function () {
            var widget = this,element = $(widget.el);
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseChannel,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);
                }
            });
        },
        _agentAuditInfoQuery: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("queryAgentAuditInfo", JSON.stringify(params), "#auditAdjustInfoListResult", function (res) {
                    debugger;
                    var paging = this.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    var exportNum = r.find("#showPageInfo").data("exportNum");
                    var tax="导出功能限制一次性最多导出"+exportNum+"条，本次查询结果";
                    var exportStr="能导出";
                    var exportStrNo="不能导出";
                    if (totalNumber > 0) {
                        if(totalNumber>exportNum){
                            $("#countSize").text(tax+totalNumber+"条,"+exportStrNo);
                            $("#out_excel").attr("disabled",true);
                        }else {
                            $("#countSize").text(tax+totalNumber+"条,"+exportStr);
                            $("#out_excel").attr("disabled",false);
                        }
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                var params = widget._getConds();
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if (params) {
                                    widget.refreshPart("queryAgentAuditInfo", JSON.stringify(params), "#auditAdjustInfoListTable");
                                }
                                ;
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }else{
                        $("#countSize").text(tax+0+"条,"+exportStr);
                        $("#out_excel").attr("disabled",false);
                    }

                }, {
                    async: false
                });
            }
            ;
        },
        _excelExport: function () {
            var widget = this;
            var params = widget._getConds();
            params.pageInfo.pageSize = 1000;
            // params.pageInfo = null;
            // delete (params.pageInfo);
            widget.dialog("dkdjf");
            if(window.confirm("根据当前条件导出前数据，确认导出？")){
                var url = "../query/excelExport";
                location.href=encodeURI(encodeURI(url+"?jsonStr="+JSON.stringify(params)));
            }

        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this;
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            var paramCnt = 0;
            var regionId = $("#regionId").attr("value");
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.regionId = regionId;
            }

            if ($("#c_channelId").is(":checked")) {
                paramCnt++;
                var channelId = $("#channelId").attr("value");
                if(widget._isNullStr(channelId)) {
                    widget.popup("请选择渠道！");
                    return false;
                } else {
                    params.channelId = channelId;
                }
            }


            if ($("#c_auditName").is(":checked")) {
                paramCnt++;
                var auditName = $("#auditName").val();
                if(widget._isNullStr(auditName)) {
                    widget.popup("请输入稽核厅名称！");
                    return false;
                } else {
                    params.auditName = auditName;
                }
            }

            if($("#c_date").is(":checked")) {
                paramCnt++;
                var beginDate = $("#beginDate").val();
                var endDate = $("#endDate").val();
                if (widget._isNullStr(beginDate) || widget._isNullStr(endDate)) {
                    widget.popup("请选择日期范围！");
                    return false;
                } else {
                    params.startDt = beginDate;
                    params.endDt = endDate;
                }
                var start  = new Date(beginDate.replace(/-/g,"/")).getTime();
                var end = new Date(endDate.replace(/-/g,"/")).getTime();

                var flag = end - start  > 30*24*60*60*1000
                if (flag) {
                    widget.popup("开始、结束时间跨度不能超出30天!");
                    return false;
                }
            }else{
                widget.popup("日期为必填条件且间隔不能超过30天！");
                return false;
            }

            if((($("#auditName").val()==null || $("#auditName").val()=="") &&
                ($("#channelId").attr("value")==null ||$("#channelId").attr("value")=="")) || paramCnt<2){
                widget.popup("渠道与稽核厅名称不能同时为空！");
                return false;
            }
            return params;
        }
    });
    vita.widget.register("agentAuditInfoQuery", agentAuditInfoQuery, true);
})(window.vita);