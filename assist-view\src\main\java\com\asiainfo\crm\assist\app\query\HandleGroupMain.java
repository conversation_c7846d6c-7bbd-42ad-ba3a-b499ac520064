package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IEopServiceSmo;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by hello on 2017-10-08.
 *
 */
@Component("vita.handleGroupMain")
public class HandleGroupMain extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(HandleGroupMain.class);

    @Override
    public Map achieveData(Object... params) throws Exception {
        String jsonString = params[0].toString();
        Map dataRangeMap = jsonConverter.toBean(jsonString, Map.class);
        if(dataRangeMap.containsKey("fixedUrl")){
            String fixedUrl=dataRangeMap.get("fixedUrl").toString();
            if (fixedUrl.indexOf("provCustIdentityNum=null")!=-1){
                dataRangeMap.put("flag","null");
            }
        }
        Map retMap = new HashMap();
        retMap.put("data", dataRangeMap);

        return retMap;
    }


}
