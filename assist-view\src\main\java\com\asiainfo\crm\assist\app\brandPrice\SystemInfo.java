package com.asiainfo.crm.assist.app.brandPrice;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.ICatalogSMO;
import com.asiainfo.crm.service.intf.ISpecSMO;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component("vita.systemInfo")
public class SystemInfo extends AbstractComponent {

    @Autowired
    private ICatalogSMO catalogSMO;

    @Override
    public Map achieveData(Object... objects) throws Exception {
        Map<String, Object> data = new HashMap<String, Object>();
        return data;
    }

    public Map querySystemInfo(String jsonString) throws Exception {

        Map option = new HashMap();
        Map<String,Object> returnMap = new HashMap<String,Object>();
        String resultStr  = catalogSMO.qrySystemInfoByParam(jsonString);
        Map resultMap = jsonConverter.toBean(resultStr, Map.class);
        String resultCode = MapUtils.getString(resultMap, "resultCode");
        if (!StringUtil.isEmpty(resultCode) && resultCode.equals(MDA.RESULT_SUCCESS.toString())) {
            Map map = (Map) resultMap.get("resultObject");
            Map maps = (Map) map.get("pageInfo");
            option.put("totalNumber",  maps.get("rowCount"));
            option.put("systemInfos", map.get("systemInfos"));
        }
        return option;
    }
}
