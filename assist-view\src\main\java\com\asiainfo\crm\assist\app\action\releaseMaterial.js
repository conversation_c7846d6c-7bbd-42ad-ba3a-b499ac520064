(function (vita) {
    var releaseMaterial = vita.Backbone.BizView.extend({
        events: {
            "click #btn-release": "_releaseMaterial",
            "click #btn-accNum-release": "_releaseAccNum",
            "click #btn-uim-release": "_releaseUimCode"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            //选择渠道
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            var option = {
                id : dialogId,
                url : widget.global.chooseChannel,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#checkChannelId").val(data.channelName).attr("value", data.orgId);
                }
            };
            widget.dialog(option);
        },
        global: {
            chooseChannel: "../comm/chooseChannel"
        },
        _releaseMaterial: function () {
            debugger;
            var widget = this;
            var gsession = widget.gSession;
            var mktResInstNbr = $("#mktResInstNbr").val();
            if (mktResInstNbr.trim().length == 0) {
                widget.popup("请输入终端串码！");
                return false;
            }
            var params = {
                mktResInstNbr: mktResInstNbr,
                staffId: gsession.staffId,
                regionId: gsession.staffRegionId,
                sysUserId : gsession.systemUserId,
                orgId : gsession.orgId,
                glbSessionId:gsession.glbSessionId,
                channelNbr : gsession.channelNbr,
                staffCode : gsession.staffCode,
                routeParam : {
                    "regionId": gsession.staffRegionId
                }
            };
            widget.callService("releaseMaterial",JSON.stringify(params),function (res) {
                if(0 == res.releaseInfo.retCode){
                    widget.popup(res.releaseInfo.retDesc);
                    $("#mktResInstNbr").val("");
                    $("#btn-release").attr("disabled", false);
                    return;
                }else {
                    widget.popup(res.releaseInfo.retDesc);
                    $("#mktResInstNbr").val("");
                    $("#btn-release").attr("disabled", false);
                    return;
                }
            });
        },
        _releaseAccNum: function () {
            var widget = this;
            var gsession = widget.gSession;
            var accNum = $("#accNum").val();
            if (accNum.trim().length == 0) {
                widget.popup("请输入接入号码！");
                return false;
            }
            var params = {
                accNum: accNum,
                staffId: gsession.staffId,
                regionId: gsession.staffRegionId,
                sysUserId : gsession.systemUserId,
                channelNbr : gsession.channelNbr,
                staffCode : gsession.staffCode,
                glbSessionId:gsession.glbSessionId,
                routeParam : {
                    "regionId": gsession.staffRegionId
                }
            };
            widget.callService("releaseAccNum",JSON.stringify(params),function (res) {
                if(0 == res.releaseInfo.retCode){
                    widget.popup(res.releaseInfo.retDesc);
                    $("#accNum").val("");
                    $("#btn-accNum-release").attr("disabled", false);
                    return;
                }else {
                    widget.popup(res.releaseInfo.retDesc);
                    $("#accNum").val("");
                    $("#btn-accNum-release").attr("disabled", false);
                    return;
                }
            });
        },
        _releaseUimCode: function () {
            var widget = this;
            var gsession = widget.gSession;
            var uimCode = $("#uimCode").val();
            if (uimCode.trim().length == 0) {
                widget.popup("请输入UIM卡号！");
                return false;
            }
            var params = {
                uimCode: uimCode,
                staffId: gsession.staffId,
                regionId: gsession.staffRegionId,
                sysUserId : gsession.systemUserId,
                channelNbr : gsession.channelNbr,
                staffCode : gsession.staffCode,
                glbSessionId:gsession.glbSessionId,
                routeParam : {
                    "regionId": gsession.staffRegionId
                }
            };
            widget.callService("releaseUimCode",JSON.stringify(params),function (res) {
                if(0 == res.releaseInfo.retCode){
                    widget.popup(res.releaseInfo.retDesc);
                    $("#uimCode").val("");
                    $("#btn-uim-release").attr("disabled", false);
                    return;
                }else {
                    widget.popup(res.releaseInfo.retDesc);
                    $("#uimCode").val("");
                    $("#btn-uim-release").attr("disabled", false);
                    return;
                }
            });
        }
    });
    vita.widget.register("releaseMaterial", releaseMaterial, true);
})(window.vita);