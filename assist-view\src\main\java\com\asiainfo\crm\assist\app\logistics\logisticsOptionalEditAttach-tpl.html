<div data-widget="logisticsOptionalEditAttach" style="height: 100%">
    <p class="vita-data">{"data":$options}</p>
    #set($item = $options.item)
    #set($logisticsStatusList = $options.logisticsStatusList)
    <div class="calcw_rightbox noneleft">
        <div class="calctitle">
            <div class="titlefont">物流信息</div>
            <div class="toolr">
                <button type="button" btn-type="confirm" class="btn btn-primary btn-sm okbutt">确认</button>
                <button id="closeLogisticEditBtn" btn-type="cancel"type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
            </div>
        </div>
        <div class="calcw_rightcont ">
            <div class="container-fluid mart10">
                <form class=" form-bordered">
                    <div class="form-group col-md-6">
                        <label class="col-md-4 control-label lablep"><span class="textcolorred">*</span>
                            收货人</label>
                        <div class="col-md-8">
                            <input type="text" class="form-control" placeholder=""  id="receiver" value="$!item.receiver">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-4 control-label lablep"><span class="textcolorred">*</span>
                            收货人手机号码</label>
                        <div class="col-md-8">
                            <input type="text" class="form-control" placeholder="" id="receiverPhone" value="$!item.receiverPhone">
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label class="col-md-2 control-label lablep"><span class="textcolorred">*</span>
                            收货地址</label>
                        <div class="col-md-10">
                            <input type="text" class="form-control" placeholder="" id="receiverAddr" value="$!item.receiverAddr"/>
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-4 control-label lablep"><span class="textcolorred">*</span>
                            邮政编码</label>
                        <div class="col-md-8">
                            <input type="text" class="form-control" placeholder="" id="postCode" value="$!item.postCode">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-4 control-label lablep"><span class="textcolorred">*</span>
                            物流单号</label>
                        <div class="col-md-8">
                            <input type="text" class="form-control" placeholder="" id="logisticsNbr" value="$!item.logisticsNbr">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-4 control-label lablep"><span class="textcolorred">*</span>
                            物流公司</label>
                        <div class="col-md-8">
                            <input type="text" class="form-control" placeholder="" id="logisticsCompany" value="$!item.logisticsCompany">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-4 control-label lablep"><span class="textcolorred">*</span>
                            物流状态</label>
                        <div class="col-md-8">
                            <select class="form-control"  id="statusCd" >
                                #if($logisticsStatusList != "null" && $logisticsStatusList.size() > 0)
                                    <option value="">-请选择-</option>
                                    #foreach($logisticsStatus in $logisticsStatusList)
                                        #if($logisticsStatus.value == $item.statusCd)
                                            <option value="$logisticsStatus.value" selected>$logisticsStatus.name</option>
                                        #else
                                            <option value="$logisticsStatus.value">$logisticsStatus.name</option>
                                        #end
                                    #end
                                #else
                                    <option value="">-请选择-</option>
                                #end
                            </select>
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label class="col-md-2 control-label lablep">
                            备注信息</label>
                        <div class="col-md-10">
                            <input type="text" class="form-control" placeholder="" id="remark" value="$!item.remark">
                        </div>
                    </div>

                </form>
                <div class="row col-lg-12 text-center">
                    <button type="button" class="btn btn-primary  okbutt" btn-type="confirm" id="submitCustBtnSave">确认</button>
                    <button type="button" class="btn btn-primary btn-white" btn-type="cancel"id="cancleCustBtnCan">取消</button>
                </div>
            </div>
        </div>
    </div>
</div>