(function (vita) {
    var factorParamConfig = vita.Backbone.BizView.extend({
        events: {
            "click #queryBtn": "_queryFactorParamConfigInfo",
            "click #resetBtn": "_clearConditions",
            "click #addBtn": "_addPopup",
            "click #editBtn": "_editPopup",
            "click #deleteBtn": "_deleteSubmit",
            "click #addSubmitBtn": "_addSubmit",
            "click #editSubmitBtn": "_editSubmit",
            "click button.close": "_closeBtnClick",
            "click #closeBtn": "_closeBtnClick",
        },
        _initialize: function () {
            var widget = this,
                el = $(widget.el);
            // 时间控件类型初始化
            this._queryFactorParamConfigInfo();
        },
        global: {
            totalNumber: 0,
            pageIndex: 1,
            pageSize: 5,
            datetime: 'time',
            date: 'date',
        },

        /**
         * 打开添加弹框
         */
        _addPopup: function () {
            var widget = this,
                el = $(widget.el);
            widget._cleanPopupData();
            widget._openPopup("add");
        },
        _openPopup: function (action) {
            var widget = this,
                el = $(widget.el);
            switch (action) {
                case "add":
                    // 时间控件类型初始化

                    el.find(".titlefont").html("添加");
                    el.find("#_regionIdBtn").removeAttr("disabled");
                    el.find(".hideAndShow").hide();
                    el.find("#addSubmitBtn").show();
                    el.find("#editSubmitBtn").hide();
                    el.find(".popup").show();
                    break;
                case "edit":
                    el.find(".titlefont").html("编辑");
                    el.find(".hideAndShow").show();
                    el.find("#_factorParamId").attr("disabled","disabled");
                    el.find("#addSubmitBtn").hide();
                    el.find("#editSubmitBtn").show();
                    el.find(".popup").show();
                    break;
                default:
                    break;
            }
        },
        /**
         * 新增
         * @private
         */
        _addSubmit: function () {
            var widget = this;
            var param = widget._getPopupData();
            if(!this._checkParam(param)){
                return;
            }
            widget.callService("createFactorParamConfig", JSON.stringify(param), function (res) {
                    if("0" == res.resultCode){
                        widget.popup("添加成功");
                        widget._closeBtnClick();
                        widget._queryFactorParamConfigInfo();
                    }else{
                        widget.popup(res.resultMsg);
                    }
            }, {async: false, mask: false});
        },
        /**
         * 编辑更新
         * @private
         */
        _editSubmit: function () {
            var widget = this,el = $(widget.el);
            var param = widget._getPopupData();
            param["factorParamId"] = el.find("#_factorParamId").val().trim()
            if(!this._checkParam(param)){
                return;
            }
            widget.callService("updateFactorParamConfig", JSON.stringify(param), function (res) {
                if (res) {
                    if("0" == res.resultCode){
                        widget.popup("修改成功");
                        widget._closeBtnClick();
                        widget._queryFactorParamConfigInfo();
                    }else{
                        widget.popup(res.resultMsg);
                    }
                }
            }, {async: false, mask: false});
        },
        /**
         * 打开编辑弹框
         */
        _editPopup: function () {
            var widget = this,
                el = $(widget.el);
            var payment = el.find("table").find("input[name='payment']:checked");
            if (payment.length > 0) {
                widget._cleanPopupData();
                var tr = payment.parent().parent().parent();
                el.find("#_factorParamId").val(tr.find("td:eq(1)").text());
                el.find("#_paramCode").val(tr.find("td:eq(2)").text());
                el.find("#_paramName").val(tr.find("td:eq(3)").text());
                el.find("#_descInfo").val(tr.find("td:eq(4)").text());
                el.find("#_factorId").val(tr.find("td:eq(5)").text());
                var all_options = document.getElementById("_statusCd").options;
                for (i=0;i<all_options.length;i++){
                    if (all_options[i].text == tr.find("td:eq(6)").text()){
                        all_options[i].selected = true;
                    }
                }
                widget._openPopup("edit");
            } else {
                widget.popup("请选择要编辑的一行!");
                return;
            }
        },
        /**
         * 弹出框关闭事件
         */
        _closeBtnClick: function () {
            var widget = this,
                el = $(widget.el);
            el.find(".popup").hide();
            el.find(".popup1").hide();
        },

        /**
         * 清除弹框数据
         */
        _cleanPopupData: function () {
            var widget = this,
                el = $(widget.el);
            el.find("form[name=_popupForm]").resetForm();
        },


        /**
         * 重置查询条件
         */
        _clearConditions: function () {
            var widget = this,
                el = $(widget.el);
            el.find("#factorId").val('');
            el.find("#paramCode").val('');
            el.find("#paramVal").val('');
            el.find("#statusCd").val('');
        },
        /**
         * 获取弹出框数据
         * @private
         */
        _getPopupData: function () {
            var widget = this,
                el = $(widget.el);
            var params = {
                "factorId": el.find("#_factorId").val().trim(),
                "paramCode": el.find("#_paramCode").val().trim(),
                "paramName": el.find("#_paramName").val().trim(),
                "descInfo": el.find("#_descInfo").val().trim(),
                "factorParamId": el.find("#_factorParamId").val().trim(),
                "statusCd":el.find("#_statusCd").val().trim(),
            };
            return params;
        },
        _checkParam: function (param){
            var widget = this,
                el = $(widget.el);
            var soUtil = widget.require("soUtil");
            if(soUtil.isNullOrEmpty(param.factorId)||soUtil.isNullOrEmpty(param.paramCode)||soUtil.isNullOrEmpty(param.statusCd)){
                widget.popup("参数必填，请检查后重新提交!");
                return false;
            }
            return true;
        },
        /**
         * 查询
         */
        _queryFactorParamConfigInfo: function () {
            var widget = this,
                el = $(widget.el);
            var param = widget._getConds(widget.global.pageIndex);
            widget.refreshPart("queryFactorParamConfigPage", param, "#loadData",
                "reqList", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = $(res).find("table").data("totalNumber");
                    if (parseInt(totalNumber) > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                param.pageInfo.pageIndex = _pageIndex;
                                param.pageInfo.pageSize = _recordNumber;
                                widget.refreshPart("queryFactorParamConfigPage", param, "div[name=tableDiv]",null,null,{mask: true});

                                ;
                            }
                        });
                        r.find("#pageInfo").append(e.getElement());
                    }
                }, {mask: true});
        },
        _getConds: function (pageIndex) {
            var widget = this, el = $(widget.el);
            var param = {
                "pageInfo": {
                    "pageIndex": pageIndex,
                    "pageSize": widget.global.pageSize,
                }
            };
            param.paramCode = $("#paramCode").val();
            param.paramName = $("#paramName").val();
            param.factorParamId = $("#factorParamId").val();
            param.statusCd = $("#statusCd").val();
            return param;
        },
    });
    vita.widget.register("factorParamConfig", factorParamConfig, true);})(window.vita);
