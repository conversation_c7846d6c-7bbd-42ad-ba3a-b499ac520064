package com.asiainfo.crm.assist.app.action;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IResourceSMO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by wenhy on 2017/7/7.
 */
@Component("vita.reverseInvoice")
public class ReverseInvoice extends AbstractComponent {

    private final Logger logger = LoggerFactory.getLogger(ReverseInvoice.class);

    @Autowired
    private IResourceSMO resourceSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map qryInvoiceListByCoNbr(String jsonStr) throws Exception {
//        String preAccNumListStr = resourceSMO.queryPreAccNum(jsonStr);
//        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(preAccNumListStr);
//        Map<String, Object> options = Maps.newHashMap();
//        List preAccNumList = (List) MapUtils.getObject(resultObjectMap, "preAccNumList");
//        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
//        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
//        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
//        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
//        options.put("pageCount",pageCount);
//        options.put("pageIndex",pageIndex);
//        options.put("totalNumber", total);
//        options.put("preAccNumList", preAccNumList);

        Map<String, Object> options = new HashMap<String, Object>();
        List<Map> invoiceList = new ArrayList<>();
        Map<String, Object> p1 = new HashMap<>();
        Map<String, Object> p2 = new HashMap<>();
        Map<String, Object> p3 = new HashMap<>();
        p1.put("invoiceId","180014359427");
        p1.put("status","有效");
        p1.put("printDate", "2016-09-22 11:40:35");

        p2.put("invoiceId","18001222222");
        p2.put("status","有效");
        p2.put("printDate", "2016-09-22 11:40:35");

        p3.put("invoiceId","180014333333");
        p3.put("status","有效");
        p3.put("printDate", "2016-09-22 11:40:35");
        invoiceList.add(p1);
        invoiceList.add(p2);
        invoiceList.add(p3);

        options.put("invoiceList", invoiceList);
        return options;
    }

}
