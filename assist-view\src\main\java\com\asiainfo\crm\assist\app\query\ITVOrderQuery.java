package com.asiainfo.crm.assist.app.query;

import java.io.IOException;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.assist.proxy.IEopServiceProxy;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICommQuerySMO;

/**
 * Created on 2017/8/12.
 */
@Component("vita.iTVOrderQuery")
public class ITVOrderQuery extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(ITVOrderQuery.class);

    @Autowired
    private ICommQuerySMO commQuerySMO;
    @Autowired
    private IEopServiceProxy eopServiceProxy;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    /**
     * 调用EPG平台获取订购结果
     *
     * @param json
     * @return
     * @throws IOException
     */
    public Map queryOrderList(String json) throws Exception {
    	Map options = eopServiceProxy.epgQryOrderHistory(json);
        return options;
    }

}
