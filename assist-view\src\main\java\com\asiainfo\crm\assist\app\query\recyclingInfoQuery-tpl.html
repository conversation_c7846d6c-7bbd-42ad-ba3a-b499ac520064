<div data-widget="recyclingInfoQuery" style="height: 100%">

    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="wmin_content row">
                                <div class="form_title">
                                    <div><i class="bot"></i>换机串码查询</div>
                                </div>
                                <form class=" form-bordered">
                                    <div class="form-group col-md-4">
                                        <label class="col-md-5 control-label lablep">
                                            <label class="wu-radio full absolute" data-scope="">
                                                <input id="c_accNum" type="radio" name="payment" checked="checked">
                                            </label> 接入号
                                        </label>
                                        <div class="col-md-7">
                                            <input id="accNum" type="text" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label class="col-md-5 control-label lablep">
                                            <label class="wu-radio full absolute" data-scope="">
                                                <input id="c_mktResInstNbr" type="radio" name="payment">
                                            </label> 串码
                                        </label>
                                        <div class="col-md-7">
                                            <input id="mktResInstNbr" type="text" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <div class="col-md-2 searchbutt_r" align="center">
                                            <button id="btn-query" type="button" class="btn btn-primary">查询</button>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                </div>
                                <div class="wmin_content" id="custListResult">
                                    <div class="col-lg-12 mart10" id="custList">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>订单编码</th>
                                                <th>品牌</th>
                                                <th>机型</th>
                                                <th>串码</th>
                                                <th>成交价</th>
                                                <th>交易时间</th>
                                                <th>联系电话</th>
                                                <th>商家名称</th>
                                                <th>商家门店</th>
                                                <th>操作员</th>
                                                <th>创建时间	</th>
                                                <th>活动ID</th>
                                                <th>状态</th>
                                                <th>绑定手机</th>
                                                <th>订单流水</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            #if($options != "null" && $options.recyclingInfo != "null" &&
                                                $options.recyclingInfo.size() > 0)
                                            #foreach($info in $options.recyclingInfo)
                                            <tr name="trClick">
                                                <td>$!{info.orderId}</td>
                                                <td>$!{info.mktResTypeName}</td>
                                                <td>$!{info.mktResName}</td>
                                                <td>$!{info.mktResInstNbr}</td>
                                                <td>$!{info.dealPrice}</td>
                                                <td>$!{info.dealTime}</td>
                                                <td>$!{info.custAccNum}</td>
                                                <td>$!{info.busiName}</td>
                                                <td>$!{info.busiChannel}</td>
                                                <td>$!{info.dealStaff}</td>
                                                <td>$!{info.createDt}</td>
                                                <td>$!{info.activityId}</td>
                                                <td>$!{info.state}</td>
                                                <td>$!{info.bindAccNum}</td>
                                                <td>$!{info.coNbr}</td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.resultCode && $options.resultCode == "-1")
                                                <tr><td align='center' colspan='15'>$!{options.resultMsg}<td></tr>
                                            #elseif($options != "null" && $options.handleResultCode)
                                                <tr><td align='center' colspan='15'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                    </div>
                                    <!--翻页start -->
                                    <div id="showPageInfo">
                                    </div>
                                    #if($options.totalNumber && $options.totalNumber != "null")
                                    <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                    #end
                                    <!--翻页end -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

