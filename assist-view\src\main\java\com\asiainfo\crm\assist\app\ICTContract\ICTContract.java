package com.asiainfo.crm.assist.app.ICTContract;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IAssetCenterSMO;
import com.asiainfo.crm.service.intf.ICustSaveMultiSMO;
import com.asiainfo.crm.util.ListUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类简述: ICT合同查询
 */
@Component("vita.ICTContract")
public class ICTContract extends AbstractComponent {

    private static final Logger LOGGER = LoggerFactory.getLogger(ICTContract.class);

    @Autowired
    private IAssetCenterSMO assetCenterSMO;

    @Override
    public Map achieveData(Object... params) {
        return null;
    }

    public Map queryICTContract(String jsonStr) throws IOException {
        Map options = new HashMap();
        String re=assetCenterSMO.queryIctContractItemDetailByInfo(jsonStr);
        Map<String, List> resultObject = (Map) resolveResult(re);
        if(resultObject==null || resultObject.isEmpty()){
            return options;
        }
        List<Map> items = resultObject.get("contractItemDetailList");
        if(ListUtil.isListEmpty(items)){
            return options;
        }
        BigDecimal b   =   new   BigDecimal(100);
        for(Map item: items){
           Double tradeSum=item.get("tradeSum")!=null ? (Double)item.get("tradeSum") :0.0;
           if(tradeSum != null){
               BigDecimal a   =   new   BigDecimal(tradeSum);
               a=a.divide(b,2,BigDecimal.ROUND_HALF_UP);
               item.put("tradeSum" ,a);
           }
        }
        Map<String, Integer> pageInfo = (Map) resultObject.get("pageInfo");
        List contracts = new ArrayList();
        long rowCount = 0L;
        if(items != null && !items.isEmpty()){
            rowCount = Long.valueOf(pageInfo.get("rowCount"));
            options.put("totalNumber", rowCount);
            options.put("ICTContracts", items);
            return options;
        }
        options.put("totalNumber", rowCount);
        options.put("ICTContracts", contracts);
        return options;
    }
}
