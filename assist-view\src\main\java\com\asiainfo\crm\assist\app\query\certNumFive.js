(function (vita) {
    var certNumFive = vita.Backbone.BizView.extend({
        events: {
            "click #btn-qryCertNumFive": "_qryCertNumFive",
            "click button[name=readCert]": "_readCert",
            "click #qryType": function (e) {
            	this._setQryType($(e.currentTarget).val());
            },
            "click #claim": "_claim"
        },
        _initialize: function () {
            var widget = this,
                element = $(widget.el);
            if(g_regionFlag && g_regionFlag == "Sichuan"){
                $("#ToClaim").show();
            }else{
            	$("#ToClaim").hide();
            }
            if (widget.getMDA("CERT_NUM_FIVE_SENSITIVE_ON")) {
            	widget.callService("isSysUserPriv", ["pass"], function (re) {
            		widget.global.passPriv = re === true || re === "true";
            		widget._setQryType(element.find("#qryType").val());
            	}, {async: false});
            }
        },
        _clearUserInfo: function () {
        },
        global: {
            custAuthUrl : "../comm/custAuth",
            pageIndex: 1,
            pageSize: 15,
            preset: "date",
            op:"",
            retCertFive:"",//一证5号查询结果
            passPriv: false,
            isScanCertQry: false,
            scanCertUrl: "../../crm/so/scanCert"
        },
        qryParams: null,
        _readCert: function () {
        	var widget = this, soUtil = widget.require("soUtil");
        	soUtil.readCertUtil(widget, "", function (cust) {
        		if (!cust) {
        			return false;
        		}
        		var el = $(widget.el);
        		if(cust.certNumber){
        			widget.global.isScanCertQry = true;
        			el.find("#custName").val(cust.partyName);
        			el.find("#certNum").val(cust.certNumber);
        			el.find("#btn-qryCertNumFive").click();
        		}
        	});
        },
        _setQryType: function (qryType) {
        	if (this.getMDA("CERT_NUM_FIVE_SENSITIVE_ON")) {
        		var global = this.global, el = $(this.el), idx = $.inArray(qryType, this.getMDA("SHOW_READ_CARD_BTN")) > -1;
        		global.isScanCertQry = false;
        		var cName = el.find("#custName"), cNum = el.find("#certNum");
        		cName.prop("disabled", false);
        		cNum.prop("disabled", false);
        		if (idx) {
        			el.find("#readCertBtn").show().parent().addClass("input-group");
        			if (!global.passPriv) {
        				cName.prop("disabled", true);
        				cNum.prop("disabled", true);
        			}
        		} else {
        			el.find("#readCertBtn").hide().parent().removeClass("input-group");
        		}
        	}
        },
        _getConds: function () {
            var widget = this;
            var gsession = widget.gSession;
            
            var param = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize,
                },
                regionId: gsession.staffRegionId,//地区id
                certTypeId: $("#qryType").val(),//证件类型id
                certTypeName:$("#qryType").find("option:selected").attr("certTypeName"),
                custName: $("#custName").val(),//客户名
                certNum: $("#certNum").val(),//证件号码
                staffId: gsession.staffId //员工id
            };
            widget.model.set("certNumFiveData",param);
            return param;
        },
        _isNullStr: function (str) {
            if (str == null || str == "") {
                return true;
            }
            return false;
        },
        _clear: function () {
            $("#value").val("");
        },
        _getChannelName: function () {
            var gSession = this.gSession;
            if(gSession.channels && gSession.channels.length > 0){
                for(var channel of gSession.channels){
                    if(channel.orgId == gSession.orgId){
                        return channel.channelName;
                    }
                }
            }
            return "No Channel Name";
        },
        /**
         * 写入查询日志
         * @param params
         * @param total
         * @private
         */
        _insertLogAndData: function (params, total) {
            var widget = this, session = widget.gSession, total = parseInt(total);
            var data = {
                staffId: session.staffId,
                staffCode: session.staffCode,
                staffLanId: session.staffLanId,
                staffLanName: session.staffLanName,
                orgId: session.curChannelId,
                channelName: session.curChannelName,
                channelNbr: session.curChannelNbr,
                certTypeId: params.certTypeId,
                certTypeName: params.certTypeName,
                certNum: params.certNum,
                custName: params.custName,
                resultTotal: !isNaN(total) ? total : 0
            }
            widget.callService("createCertNumFiveLog", data, function (logId) {
                widget.model.set("logId", logId);
            }, {async: false});
        },
        _insertLogData: function (dataList) {
            if(dataList && dataList.length > 0){
                var params = [], logId = this.model.get("logId");
                for(var data of dataList){
                    params.push({
                        logSeq: logId,
                        resultCode: data.valueAccNum.accNum
                    });
                }
                this.callService("createCertNumFiveLogData", [params]);
            }
        },
        _qryCertNumFive: function(){
        	var widget = this, global= widget.global;
            var param = widget._getConds();
            if (param.certNum == null || param.certNum.trim() == "") {
                widget.popup("请输入证件号");
                return;
            }
            if (widget.getMDA("CERT_NUM_FIVE_SENSITIVE_ON")) {
            	param.isScanCertQry = global.isScanCertQry;
            	param.passPriv = global.passPriv;
            }
            /******** 查询权限、次数校验 start ********/
            var haslimits = false, gSession = this.gSession;
            widget.callService("checkCertNumFive", [gSession.staffId, gSession.staffCode], function (result){
                if(result === true){
                    haslimits = true;
                }
            }, {async: false});
            if(haslimits === false){
                this.popup("您查询一证五卡次数过多，请明天再试");
                return;
            }
            /******** 查询权限、次数校验 end ********/
            widget.qryParams = param;
            if (widget.getMDA("AUTH_POINTS")[widget.widgetName] == "Y") {
                var isHave = false;
                if (widget.getMDA("CERT_NUM_FIVE_SENSITIVE_ON")) {
                	isHave = global.isScanCertQry || global.passPriv;
                } else {
                	widget.callService("isSysUserPriv", ["pass"], function (re) {
                		isHave = re === true || re === "true";
            		}, {async: false});
                }
                if (!isHave) {
                	widget._toCustAuth(null, widget._doQryCertNumFive.bind(widget));
                	return;
                }
            }
            widget._doQryCertNumFive();
        },
        _doQryCertNumFive: function () {
            var widget = this, element = $(widget.el), global= widget.global;
            var gsession = widget.gSession;
            var param = widget.qryParams;
            var totals = "";
            param.glbSessionId = gsession.glbSessionId;
            widget.refreshPart("certNumFive", JSON.stringify(param), "#certNumTbody", function (res) {
//                var data =  $(res).find("#certNumFiveList").data("data");
//                if (data.resultValue != "" && data.resultValue != null){
//                    widget.popup("权限不足")
//                    return;
//                }
                var data =  $(res).find("#certNumFiveList").data("data");
                var certNumFive = data.certNumFiveList;
                ////// 写入查询日志
                widget._insertLogAndData(param, data.totalNumber);
                widget._insertLogData(certNumFive);
                if(data.certNumFiveList != null && data.certNumFiveList != ""){
                    var accNum =data.certNumFiveList[0].valueAccNum.accNum;
                    var certTypeName = data.certNumFiveList[0].valueAccNum.certTypeName;
                    var certNum = data.certNumFiveList[0].valueAccNum.certNum;
                    widget.model.set("certNumFive", certNumFive);
                    widget.model.set("accNum", accNum);
                    widget.model.set("certTypeName", certTypeName);
                    widget.model.set("certNum", certNum);
                    widget._logLog(certNumFive.length);
                }else{
                    widget._logLog(0);
                }
                var r = $(res), paging = this.require("paging");
                totals = r.find("#showPageInfo").data("totalNumber");
                var e = paging.new({
                    recordNumber: widget.global.pageSize,
                    total: r.find("#showPageInfo").data("totalNumber"),
                    pageIndex: widget.global.pageIndex,
                    callback: function (pageIndex, pageSize) {
                        var param = {
                            regionId: gsession.staffRegionId,//地区id
                            certTypeId: $("#qryType").val(),//证件类型id
                            certTypeName:$("#qryType").find("option:selected").attr("certTypeName"),
                            custName: $("#custName").val(),//客户名
                            certNum: $("#certNum").val(),//证件号码
                            staffId: gsession.staffId,//员工id
                            pageInfo: {
                                pageIndex: pageIndex,
                                pageSize: widget.global.pageSize,
                            }
                        };
                        if (widget.getMDA("CERT_NUM_FIVE_SENSITIVE_ON")) {
                        	param.isScanCertQry = global.isScanCertQry;
                        	param.passPriv = global.passPriv;
                        }
                        param.glbSessionId = gsession.glbSessionId;
                        widget.refreshPart("certNumFive", JSON.stringify(param), "#certNumFiveListR", function (res) {
                            var data =  $(res).find("#certNumFiveList").data("data");
                            ////// 写入查询日志
                            widget._insertLogData(data.certNumFiveList);
                        });
                    }
                });
                r.find("#showPageInfo").append(e.getElement());
                if (widget.getMDA("AUTH_POINTS")[widget.widgetName] != "Y") {
                	if (certNumFive != null && certNumFive.length > 0 ){
                        $("#certNumTable").hide();
                        if(data.isHave=='0'){
                            widget._toCustAuth();
                        }else {
                            $("#certNumTable").hide();
                            $("#certNumTbody").show();
                        }
                    }else {
                        $("#certNumTable").hide();
                        $("#certNumTbody").show();
                    }
                }
            }, {
                mask:true,
                async: true
            });
        },
        _logLog : function (totals){
        	 var widget = this, element = $(widget.el);
             var gsession = widget.gSession;
             var totalNumber =  totals;
             var params =  widget.model.get("certNumFiveData");
             params.totalNumber = totalNumber;
             widget.callService("logLog", JSON.stringify(params), function(res) {
            	 var ret = res;
             })
        },
        _toCustAuth: function (cust, callBack) {
            var widget = this, element = $(widget.el);
            var gsession = widget.gSession;
            $("#certNumTbody").hide();
            $("#certNumTable").show();
            var certNumFives = widget.model.get("certNumFive");
            var uri = widget.global.custAuthUrl;
            var params = {
                certNum: $("#certNum").val(),
                certAccNum: JSON.stringify(certNumFives),
                contactTele: "11111",
                widgetName: widget.widgetName,
                certType: element.find("#qryType").val()
            };
            var option = {
                url: uri,
                id: "custAuthDialog",
                params: params,
                onOpen: function (res) {
                    var custAuth = $(res).closest("[data-widget=custAuth]");
                    custAuth.find("#tab1").addClass("active");
                    custAuth.find("#tab1").click();
                    custAuth.find("#tab2").hide();
                    custAuth.find("#content2").hide();

                },
                onClose: function (res) {
                    var custAuth = $(res).closest("[data-widget=custAuth]");
                    if (custAuth.length) {
                        var data = custAuth.custAuth("getValue");
                        //validate attributes   inline
                        if (data.validate == true) {
                            $("#certNumTable").hide();
                            $("#certNumTbody").show();
                        	if (callBack && _.isFunction(callBack)) {
                        		callBack();
                        	}
                        }
                    }
                }
            };
            widget.dialog(option);
            return false;
        },
        _qryCertNumFiveQuery: function () {
            var widget = this;
            var gsession = widget.gSession;
            var param = widget._getConds();
            param.glbSessionId = gsession.glbSessionId;
            widget.refreshPart("certNumFive", JSON.stringify(param), "#certNumFiveListR", function (res) {
                widget.global.op=res;
                widget.global.retCertFive=res;
                var r = $(res), paging = this.require("paging");
                var e = paging.new({
                    recordNumber: widget.global.pageSize,
                    total: r.find("#showPageInfo").data("totalNumber"),
                    pageIndex: widget.global.pageIndex,
                    callback: function (pageIndex, pageSize) {
                        var param = {
                            regionId: gsession.staffRegionId,//地区id
                            certTypeId: $("#qryType").val(),//证件类型id
                            certTypeName:$("#qryType").find("option:selected").attr("certTypeName"),
                            custName: $("#custName").val(),//客户名
                            certNum: $("#certNum").val(),//证件号码
                            staffId: gsession.staffId,//员工id
                            glbSessionId: gsession.glbSessionId,
                            pageInfo: {
                                pageIndex: pageIndex, 
                                pageSize: widget.global.pageSize,
                            }
                        };
                        widget.refreshPart("certNumFive", JSON.stringify(param), "#certNumFiveListR");
                    }
                });
                r.find("#showPageInfo").append(e.getElement());
            }, {
                mask:true,
                async: true
            });
        },
        //认领
        _claim:function () {
            var widget = this,
            element = $(widget.el);
            var gsession = widget.gSession;
            var data = element.find("div[id='certNumFiveList']").data("data");
            var certNumFive = data.certNumFiveList;
            if(data.certNumFiveList != null && data.certNumFiveList != ""){
                var accNum =data.certNumFiveList[0].valueAccNum.accNum;
                var certTypeName = data.certNumFiveList[0].valueAccNum.certTypeName;
                var certNum = data.certNumFiveList[0].valueAccNum.certNum;
                widget.model.set("certNumFive", certNumFive);
                widget.model.set("accNum", accNum);
                widget.model.set("certTypeName", certTypeName);
                widget.model.set("certNum", certNum);
            }
            
            var selectChks = element.find('input[type=radio][name=checkCertFive]:checked');

            if(selectChks.length==0){
                widget.popup("请选择待认领号码",2000);
                return;
            }
            var certNumFive = widget.model.get("certNumFive");
            var custNames = certNumFive[0].custName;
            $.each(certNumFive, function(i, refund) {
            	if (null != refund.custName && "" != refund.custName) {
            		custNames = refund.custName;
            		return;
				}
            })
            var accNum = widget.model.get("accNum");
            var param ={
                "operId" : gsession.staffId,
                "company" : gsession.areaName,
                "areaId" : gsession.areaId,
                "departmentId":gsession.areaId,
                "customerPhone" : accNum,
                "advice" : "客户对上述号码登记本人信息有异议，要求电信公司核查后进行更改登记到其他真实用户名下等处理，或根据核查情况再进一步向客户进行反馈沟通",
                "customerName" : custNames,
                "customerCardId" :widget.model.get("certNum") ,//证件号码
                "customerCardType" : widget.model.get("certTypeName") ,//证件类型
                "routeParam" : {"areaId" : gsession.areaId}
            };
            var phones = [];
            var seletPhoneNum;
            var isSelect;
            var accNums;
            var regionNames;
            //组装phones的数据
            $.each(certNumFive, function(i, refund) {
                isSelect=false;
                seletPhoneNum = refund.valueAccNum.accNum;
                regionNames = refund.regionName;
                $.each(selectChks, function(i, selectChk) {
                	 var $tr = selectChk.parentElement.parentElement;
                     var datas = $($tr).find("[link=certNumFive]").data("certNumFive");
                     accNums = datas.valueAccNum.accNum;
                    if(seletPhoneNum==accNums){
                        isSelect=true;//判断是否已领
                    }
                });
                if(isSelect){
                    phones.push({"phoneNum":seletPhoneNum,"status":"已领",phoneStatus:"在用","phoneArea":regionNames});
                }
                else{
                    phones.push({"phoneNum":seletPhoneNum,"status":"未认领",phoneStatus:"在用","phoneArea":regionNames});
                }
            });
            param.phones=phones;
            widget._button_post(param);//调用函数

        },
        _certNumRepair:function () {
          
        		var widget = this,element = $(widget.el);
            	var gsession = widget.gSession;
                var selectChks = element.find('input[type=radio][name=checkCertFive]:checked');
                if(!selectChks||!selectChks.length){
                    widget.popup("请选择需要修复号码");
                    return;
                }
                var params = {};
                var serviceType = 1800;
                var actionType = 12;
                var lanId = gsession.staffLanId;
                var channelNbr = gsession.curChannelNbr;
                var staffCode = gsession.staffCode;
                var staffId = gsession.staffId;//员工id
                $.each(selectChks, function(i, selectChk) {
                	var $tr = selectChk.parentElement.parentElement;
                	var datas = $($tr).find("[link=certNumFive]").data("certNumFive");
                    var phoneNum = datas.valueAccNum.accNum;
                    var certNum = datas.valueAccNum.certNum;
                    params.certNum = certNum;
                    params.phoneNum = phoneNum;
                    params.actionType = actionType;
                    params.serviceType = serviceType;
                    params.lanId = lanId;
                    params.channelNbr = channelNbr;
                    params.staffCode = staffCode;
                    params.staffId = staffId;
                });
               
                widget.callService("exeRepairRelation", params, function(res) {
                	widget.popup(res.retDesc)
                	widget._qryCertNumFiveQuery();
					return;
                }, {
                    mask:true,
                	async: true
                });
              
           },
       
        _button_post:function (param){
        //用form表单形式post json
        var form =$("<form target='_blank' method='post'>" +
            "</form> ");
        var url = "http://133.40.117.32:8094"+"/ecpweb/json/home";
        form.attr("style", "display:none");
        form.attr("action", url);
        var input1 = $("<input>");
        input1.attr("type", "hidden");
        input1.attr("name", "form");
        input1.attr("value", JSON.stringify(param));
        form.append(input1);
        $("#iframe_id" ).remove();//如果已存在iframe则将其移除
        $( "body").append("<iframe id='iframe_id' name='SMAL' style='display: none'></iframe>");//载入iframe
        (function(){
            $( "#iframe_id" ).contents().find('body').html(form);//将form表单塞入iframe;
            $( "#iframe_id" ).contents().find('form').submit();//提交数据
        }());
    }
    });
    vita.widget.register("certNumFive", certNumFive, true);
})(window.vita);