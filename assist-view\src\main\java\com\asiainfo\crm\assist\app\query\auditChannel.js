(function(vita) {
    var auditChannel = vita.Backbone.BizView.extend({
        events : {
            "click #closeAreaBtn" : "_closePage",
        },
        _closePage : function() {
            var widget = this,
                element = $(widget.el);
            var dialog = element.closest("[id=" + widget.widgetName + "Dialog]");
            if (dialog.length) {
                dialog.dialog("close");
            }
        },
        getValue : function() {
            var widget = this;
            var channel = widget.model.toJSON();
            return channel;
        },
    });
    vita.widget.register("auditChannel", auditChannel, true);
})(window.vita);