(function(vita) {
    var orderDetail = vita.Backbone.BizView.extend({
        events : {
            "click .okbutt" : "_closeBtn",
            "click #sruleTreeOrderInfo" : "_sruleTreeOrderInfo"
        },
        global: {
            pageSize: 4,
            "sruleTreeOrderInfo" : "../../crm/config/sruleTreeOrderInfo"
        },
        _initialize : function() {
            var widget = this,
                el = $(widget.el);
        },
        _sruleTreeOrderInfo : function(e){
            var widget = this,
                el = $(widget.el);
            var soUtil = widget.require("soUtil");
            var button = $(e.target).closest("a");
            var id = button.attr("id") || "";
            var datas = el.data("data");
            var csNbr = datas.csNbr;
            var tar = el.find("#orderDetail");
            var atomActionId =$(e.currentTarget).data("atomActionId") ;
            var iUId = datas.iUId;

            var param = {
                "csNbr" : csNbr,
                "iUId" : iUId,
                "atomActionId" : atomActionId
            };
            if (id && widget.global[id]) {
                var dialogId = id + 'Dialog';
                var option = {
                    url : widget.global[id],
                    params : param,
                    id : dialogId,
                    onClose : function(res) {
                        //widget._traceHisQuery();
                    }
                };
                widget.dialog(option);
            }
        },

    });
    vita.widget.register("orderDetail", orderDetail, true);
})(window.vita);

