package com.asiainfo.crm.assist.app.logistics;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.ISoQueryMultiSMO;
import com.asiainfo.crm.service.intf.ISoQuerySMO;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("vita.logisticsQuery")
public class LogisticsQuery extends AbstractComponent {
    @Autowired
    private IOrderQuerySMO orderQuerySMO;
    @Override
    public Map achieveData(Object... objects) throws Exception {
        return null;
    }
    public Map qryLogisticsList(String inStr) throws Exception{
        Map<String, Object> options = new HashMap<>();
        Map req = jsonConverter.toBean(inStr,Map.class);
        String res =  orderQuerySMO.qryLogisticsInfoList(jsonConverter.toJson(req));
        Map<String,Object> resultList = (Map<String,Object>)resolveResult(res);
        List logisticsList = (List) MapUtils.getObject(resultList,"logisticsList");
        Map pageInfo = (Map)MapUtils.getObject(resultList,"pageInfo");
        int totalNumber = pageInfo.get("rowCount")==null ? 0:(int)pageInfo.get("rowCount");
        //查询物流状态
        Map logisticsStatusMap = MDA.LOGISTICS_STATUS_ATTR_ID;
        List<Map<String,Object>> logisticsStatusList = new ArrayList<>();
        if(logisticsStatusMap != null){
            Iterator iterator = logisticsStatusMap.entrySet().iterator();
            while(iterator.hasNext()){
                Map status = new HashMap();
                Map.Entry<String, Object> entry = (Map.Entry<String, Object>)iterator.next();
                status.put("name",entry.getKey());
                status.put("value",entry.getValue());
                logisticsStatusList.add(status);
            }
        }
        options.put("logisticsList",logisticsList);
        options.put("totalNumber",totalNumber);
        options.put("logisticsStatusList",logisticsStatusList);
        return options;
    }
}
