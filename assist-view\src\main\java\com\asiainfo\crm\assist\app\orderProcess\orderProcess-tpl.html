#import("../bundles/vita/plugs/echarts-5.2.2.min.js")
<div data-widget="orderProcess" class="parent" style="overflow: auto">
    <p class="vita-data">{"data":$options}</p>


    <div>
        <div class="col-lg-12"  class="top" >
            <div class="box-item">
                <div class="container-fluid row">
                    <div class="form_title">
                        <div><i class="bot"></i>订单全流程</div>
                    </div>

                    <div class="wmin_content row" style="text-align: center">

                            <div class="form-group col-md-5 " >
                                <label class="col-md-3 control-label lablep">查询条件</label>
                                <select  class="col-md-3"  id="sourceType"
                                        class="btn btn-default dropdown-toggle">
                                    <option value="1" selected="selected"
                                    >订单id</option>
                                    <option value="0" selected="selected"
                                    >订单Nbr</option>
                                </select>
                                <input  class="col-md-6" name="text" id="text" type="text" class="form-control" placeholder="">
                            </div>

                            <div class="form-group col-md-2 "   >
                                <div class="col-md-4 searchbutt_r" align="left">
                                    <button id="btn-query"   type="button" class="btn btn-primary">信息查询</button>
                                </div>
                            </div>

                    </div>


                </div>


            </div>

        </div>
    </div>

        <!--填单end-->
         <div  class="bottom" id="line" ></div>

    <button id="btn-showInfo" style="visibility:hidden"  type="button" class="btn btn-primary"></button>
</div>

<style type="text/css">
    html,body {height: 100%;padding: 0; margin: 0; }
    .parent{
        width:100%;
        height: 100%;
        position: relative;
    }
    .top{
        background-color: #BBE8F2;
        height:200px;
    }
    .bottom{
        position: absolute;
        top: 110px;
        width:100%;
        height: 600px;
        bottom:0;
    }
</style>