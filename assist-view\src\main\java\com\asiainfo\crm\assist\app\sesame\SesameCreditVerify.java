package com.asiainfo.crm.assist.app.sesame;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ISesameCreditSmo;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by wenhy on 2017/7/31.
 * 芝麻信用购机第二步：校验验证
 */
@Component("vita.sesameCreditVerify")
public class SesameCreditVerify extends AbstractComponent {

    private static final Logger LOGGER = LoggerFactory.getLogger(SesameCreditVerify.class);

    @Autowired
    private ISesameCreditSmo sesameCreditSmo;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map sesameCreditAction(String jsonStr) throws Exception {
        String sesameCreditStr = sesameCreditSmo.sesameCreditAction(jsonStr);
        String resultStr = (String)resolveResult(sesameCreditStr);
        Map resultObjectMap = jsonConverter.toBean(resultStr, Map.class);
        return resultObjectMap;
    }

}

