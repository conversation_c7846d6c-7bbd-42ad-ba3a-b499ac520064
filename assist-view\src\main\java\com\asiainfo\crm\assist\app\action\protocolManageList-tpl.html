<div data-widget="protocolManageList" style="height: 100%">
<p class="vita-data"> {"data": $options} </p>
	<div class="pace-done wrapper mini-rightmax no-minright">
		<!--控制测边栏mini-rightmax-->
		<!--控制底部按钮nobuttnfood-->
		<div class="box-maincont">
			<div class="homenofood">
				<div class="page_main notopnav">
					<div class="col-lg-12">
						<div class="box-item">
							<div class="container-fluid row">
								<div class="form_title">
									<div>
										<button type="button" id="addAgreement" class="btn btn-primary">新建协议</button>
									</div>
								
									

									<div class="wmin_content" id="">
									  <div class="col-lg-12 mart10" id="">
										   #if($options != "null" && $options.reglist && $options.reglist != "null" &&
	                                            $options.reglist.size() > 0)
										  <div class="form-group col-md-4">
	                                        <label class="col-md-5 control-label lablep">
	                                        <label class="wu-radio full absolute" data-scope="">
	                                              		  地区:
	                                              	 </label> 
	                                             </label>
	                                        <div class="col-md-7">
	                                            <select id="rengSe" class="form-control">
	                                            #foreach($list in $options.reglist)
		                                          			#if($!{list.regionLevel} == 20 || $!{list.regionLevel} == 10)
					                                        <option value="$!{list.commonRegionId}">$!{list.regionName}</option>
					                                 	#end
	                                              #end
	                                            </select>
	                                        </div>
	                                     </div>
	                                     #end
										<div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custOrderNbr" type="checkbox" name="payment">
                                                </label> 协议名称
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custOrderNbr" type="text" class="form-control" placeholder="">
                                            </div>
                                     </div>
                                      <div class="form-group col-md-4">
                                        <label class="col-md-5 control-label lablep">
                                        <label class="wu-radio full absolute" data-scope="">
                                              		  状 态:
                                              	 </label> 
                                             </label>
                                        <div class="col-md-7">
                                            <select id="status" class="form-control">
                                                <option value="9999">所有状态</option>
                                                <option value="1000">上线</option>
                                                <option value="1200">下线</option>
                                            </select>
                                        </div>
                                     </div>
                                     <div align="right">
										<button type="button" id="qryButton" class="btn btn-primary" >查询</button>
									</div >
											<table class="table table-hover">
												<thead>
													<tr>
														<th >协议编号</th>
														<th >协议名称</th>
														<th >适用地区</th>
														<th >状态</th>
														<th >创建人</th> 
														<th >创建日期</th>
														<!--  
														<th >审核人</th>
														<th >审核日期</th>
														-->
														<th >普通操作</th>
														<th >权限操作</th>
													</tr>
												</thead>
												<tbody id="qryprotool">
												 #if($options != "null" && $options.protocolManageLists && $options.protocolManageLists != "null" &&
	                                            $options.protocolManageLists.size() > 0)
	                                            <tr align="right"><td>共 $options.count 条</td></tr>
	                                            #foreach($list in $options.protocolManageLists)
	                                            <tr>
	                                            	<td style="display: none;" name="tds"> $!{list.protocolTempletId}</td>
		                                          	<td>$!{list.protocolTempletId}</td>
		                                          	<td name="tdName">$!{list.name}</td>
		                                          	<td>
		                                          		#if($!{list.privCode} == "true" || $!{list.privCode} == true )
		                                          		#if($list.regionIdList != "null" &&  $list.regionIdList != "null" &&
	                                          				  $list.regionIdList.size() > 0)
				                                          		<select id="sele">
				                                          		<option value="$!{list.regionId}">$!{list.regionName}</option>
	                                          					  #foreach($regionIdLists in $list.regionIdList)
	                                          					  #if($!{regionIdLists.regionLevel} == 20 || $!{regionIdLists.regionLevel} == 10)
				                                          			<option value="$!{regionIdLists.commonRegionId}">$!{regionIdLists.regionName}</option>
				                                          		  #end
				                                          		  #end
				                                          		</select>
				                                          		<button id="saveBtn" type="button" class="btn btn-primary">保存</button>
		                                          		#end
		                                          		#else
		                                          		$!{list.regionName}
		                                          		#end
		                                          	</td>
		                                          	<td>$!{list.statusCds}</td>
		                                          	<td>$!{list.staffName}</td>
		                                          	<td>$!{list.createDate}</td>
		                                          	<td> 
		                                          	<button id="reviewBtn" type="button" class="btn btn-primary">预览</button>
		                                          	#if($!{list.regionNamePrivCode} && $!{list.regionNamePrivCode} == "true" || $!{list.regionNamePrivCode} == true)
		                                          	#if( $!{list.privCode} == "true" && $!{list.privCode} == true )

														#if( ($!{list.disable} == "true" && $!{list.disable} == true) || ($!{list.relTypeDisable} == "true" && $!{list.relTypeDisable} == true))
		                                          			<button id="editBtn" type="button" class="btn btn-primary" disabled>编辑 </button>
														#else
															<button id="editBtn" type="button" class="btn btn-primary">编辑 </button>
														#end
		                                          	#end
		                                          	#else
														#if( ($!{list.disable} == "true" && $!{list.disable} == true) || ($!{list.relTypeDisable} == "true" && $!{list.relTypeDisable} == true))
															<button id="editBtn" type="button" class="btn btn-primary" disabled>编辑 </button>
														#else
														<button id="editBtn" type="button" class="btn btn-primary">编辑 </button>
														#end
		                                          	#end
		                                          	<button id="channelIdBtn" type="button" class="btn btn-primary" style="display:none">渠道关联 </button>
														#if($!{list.relTypeDisable} == true)
		                                          		<button id="offerIdBtn" type="button" class="btn btn-primary" disabled>销售品关联 </button>
														#else
														<button id="offerIdBtn" type="button" class="btn btn-primary">销售品关联 </button>
														#end
		                                          	</td>
		                                          	<td>
														#if($!{list.relTypeDisable} == true)
		                                          	<button id="activeBtn" type="button" class="btn btn-primary" disabled>上线</button>
		                                          	<button id="invalidBtn" type="button" class="btn btn-primary" disabled>下线 </button>
														#else
														<button id="activeBtn" type="button" class="btn btn-primary">上线</button>
														<button id="invalidBtn" type="button" class="btn btn-primary">下线 </button>
														#end
		                                          	</td>
	                                          	</tr>
	                                            #end 
	                                            #elseif(($options != "null" && $options.protocolManageLists && $options.protocolManageLists != "null" &&
		                                        $options.protocolManageLists.size() == 0) || !$options ||$options.protocolManageLists =="null")
		                                        <tr><td align='center' colspan='7'>未查询到数据！<td></tr>
		                                        #end
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
