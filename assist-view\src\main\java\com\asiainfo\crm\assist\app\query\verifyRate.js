(function (vita) {
    var verifyRate = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_verifyRate",
            "click #regionIdBtn": "_chooseArea",
            "click #c_bearerNo": "_chooseBearerNo",
            "click #c_broadBandNo": "_chooseBroadBandNo",
        },

        _initialize: function () {
        	var widget = this, element = $(widget.el);
        	$("#broadBandNoDev").hide();
        	$("#c_bearerNo").attr("checked",true);
        	var gSession = widget.gSession;
            element.find("#acceptRegionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
        },
        global: {
            pageIndex: 1,
            pageSize: 5,
            chooseArea: "../comm/chooseArea"
        },

        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#acceptRegionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _chooseBearerNo: function(){
        	$("#bearerNoDev").show();
        	$("#broadBandNoDev").hide();
        	$("#bearerNo").val("");
        	$("#broadBandNo").val("");
        	
        },
        _chooseBroadBandNo: function(){
        	$("#bearerNoDev").hide();
        	$("#broadBandNoDev").show();
        	$("#bearerNo").val("");
        	$("#broadBandNo").val("");
        },
        _verifyRate: function () {

            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("verifyRate", JSON.stringify(params), "#verifyRateResult", function (res) {
                    var r = $(res);
                    var resultInfo = r.find("#resultInfo").data("data");
                    if(0!= resultInfo.ResultCode){
                    	widget.popup(resultInfo.ResultDesc);
                    }
                  

                }, {
                    async: false
                });
            };
        },
        
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this;
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };

            var acceptRegionId = $("#acceptRegionId").attr("value");
            if (widget._isNullStr(acceptRegionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.localArea = acceptRegionId;
            }

            if ($("#c_bearerNo").is(":checked")) {

                var bearerNo = $("#bearerNo").val();
                if(widget._isNullStr(bearerNo)) {
                    widget.popup("请输入承装号码！");
                    return false;
                } else {
                    params.searchType = $("#c_bearerNo").attr("value");
                    params.content = bearerNo;
                }
            }

            if ($("#c_broadBandNo").is(":checked")) {

                var broadBandNo = $("#broadBandNo").val();
                if(widget._isNullStr(broadBandNo)) {
                    widget.popup("请输入宽带账号！");
                    return false;
                } else {
                	 params.searchType = $("#c_broadBandNo").attr("value");
                     params.content = broadBandNo;
                }
            }

            return params;
        }
    });
    vita.widget.register("verifyRate", verifyRate, true);
})(window.vita);