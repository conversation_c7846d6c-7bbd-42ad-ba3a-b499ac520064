package com.asiainfo.crm.assist.app.action;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.asiainfo.crm.assist.AssistMDA;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.bcomm.utils.LogHelper;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.asiainfo.crm.service.intf.IResourceSMO;
import com.asiainfo.crm.service.intf.ISoQuerySMO;

/**
 * Created by wenhy on 2017/7/17.
 */
@Component("vita.releaseRs")
public class releaseRs extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(releaseRs.class);
    
    @Autowired
    private IProdInstSMO prodInstSMO;
    
    @Autowired
	protected IOrderQuerySMO orderQuerySMO;
  
    @Autowired
    private ISoQuerySMO  iSoQuerySMO;
    
    @Autowired
    private IResourceSMO resourceSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }
    /**
     * 释放资源
     * @param jsonStr
     * @return
     * @throws Exception
     */
    public Map releaseRs(String jsonStr) throws Exception {
    	
    	Map<String, Object> retMap = new HashMap<String, Object>();
    	Map<String, Object> paramMap = jsonConverter.toBean(jsonStr, Map.class);
    	

        String regionId = String.valueOf(paramMap.get("regionId"));//地市编码
        String accNumType = String.valueOf(paramMap.get("accNumType"));//资源类型 1、接入号码  2、账号 3、串号 4、UIM卡
        String rsNum = String.valueOf(paramMap.get("rsNum"));//需要释放的资源号码
        String channelId = String.valueOf(paramMap.get("channelId"));//渠道编码
        String staffId = String.valueOf(paramMap.get("staffId"));//操作工号
    	
    	String lanId = "";
    	
    	//根据输入的地区编码查询LAN_ID
    	Map<String, Object> regionIdMap = new HashMap();
        regionIdMap.put("regionId", regionId);
        String regionIdStr = iSoQuerySMO.qryCommonRegionByIds(regionIdMap);
        String regionIdStrs =  (String) resolveResult(regionIdStr);
        Map<String, Object> regionIdMaps = jsonConverter.toBean(regionIdStrs, Map.class);
        if (null !=  regionIdMaps.get("lanId")) {
        	lanId = String.valueOf(regionIdMaps.get("lanId"));
        }else{
        	retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "根据地址编码:"+regionId+"查询本地网编码异常！");
    		return retMap;
        }
        
        //接入号码，目前支持固话和手机号码
        if("1".equals(accNumType)){
        	
        	//1、查询号码信息
        	Map<String,Object> resourceParam = new HashMap<String, Object>();
			resourceParam.put("phoneNumber", rsNum);
			resourceParam.put("regionId", lanId);
			Map<String,Object> routeParam = new HashMap<String, Object>();
			routeParam.put("regionId", lanId);
			resourceParam.put("routeParam", routeParam);
			String retRsStr = resourceSMO.qryPhoneNumInfoByCond(jsonConverter.toJson(resourceParam));
			Map<String, Object> retRsMap = (Map<String, Object>) resolveResult(retRsStr);
			String retCode = String.valueOf(retRsMap.get("handleResultCode"));
			String retMsg = String.valueOf(retRsMap.get("handleResultMsg"));
			if(!"0".equals(retCode)){
				retMap.put("retCode", "-1");
            	retMap.put("retDesc", retMsg);
        		return retMap;
			}
            List<Map<String, Object>> numberList = (List<Map<String, Object>>) MapUtils.getObject(retRsMap, "phoneNumInfoList");
            if(null != numberList && numberList.size() < 1){
            	retMap.put("retCode", "-1");	
            	retMap.put("retDesc", "未查询到该号码的信息！");
        		return retMap;
            }
            
            if(null != numberList && numberList.size() > 1){
            	retMap.put("retCode", "-1");	
            	retMap.put("retDesc", "查询该号码信息记录数异常，请联系运维人员！");
        		return retMap;
            }
            
            Map<String, Object> numberInfo = numberList.get(0);
            String rscStatusCd = String.valueOf(numberInfo.get("rscStatusCd"));
            String phoneNumberId = String.valueOf(numberInfo.get("phoneNumberId"));
            String anTypeCd = String.valueOf(numberInfo.get("anTypeCd"));
			if(!AssistMDA.RES_STATUS_CD.get("chooseOf").equals(rscStatusCd) && !AssistMDA.RES_STATUS_CD.get("campON").equals(rscStatusCd) && !AssistMDA.RES_STATUS_CD.get("inUse").equals(rscStatusCd)){
            	retMap.put("retCode", "-1");
            	retMap.put("retDesc", "号码状态非临时预占、预占、在用，不能释放！");
        		return retMap;
            }
        	
        	//2、判断号码是否存在实例，如果存在实例，不允许释放
        	Map<String, Object> reqMap = new HashMap<String, Object>();
        	reqMap.put("accNum", rsNum);
        	reqMap.put("regionId", lanId);
        	reqMap.put("busiRegionId", lanId);
        	String retStr = prodInstSMO.qryAccProdInstListLocal(jsonConverter.toJson(reqMap));
            Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(retStr);
            List<Map<String, Object>> prodInsts = (List<Map<String, Object>>) MapUtils.getObject(resultObjectMap, "accProdInsts");
            if(null != prodInsts && prodInsts.size() > 0){
            	retMap.put("retCode", "-1");	
            	retMap.put("retDesc", "该号码存在资产实例，不允许释放！");
        		return retMap;
            }
        	
        	//3、判断号码是否存在在途订单，如果存在在途订单，不允许释放
            Map<String, Object> onlineMap = new HashMap<String, Object>();
            List<String> orderItemStatuses = new ArrayList<String>();
            orderItemStatuses.add("101400");//客户需求确认
            orderItemStatuses.add("201300");//开通中
            orderItemStatuses.add("401200");//后端错误
            orderItemStatuses.add("101200");//待收费
            orderItemStatuses.add("401302");//修改中
            orderItemStatuses.add("401400");//已退单
            orderItemStatuses.add("301100");//竣工中
            orderItemStatuses.add("401301");//撤销中
            onlineMap.put("orderItemStatuses", orderItemStatuses);
            Map<String, Object> data = new HashMap<String, Object>();
            data.put("lanId", lanId);
            data.put("accNum", rsNum);
            onlineMap.put("data", data);
            String retProdInfoStr = orderQuerySMO.qryUnarchivedOrdProdInsts(jsonConverter.toJson(onlineMap));
            List retList = (List) resolveResult(retProdInfoStr);
            if(retList != null && retList.size() > 0){
        		retMap.put("retCode", "-1");	
        		retMap.put("retDesc", "该号码存在在途单未竣工！不能释放！");
        		return retMap;
            }
        	
        	
        	//4、如果号码状态为2（预占），但是没有在途订单，没有实例，判断网厅预约号码表precontract_resource是否存在号码预约记录，如果存在，则不允许释放
			if(AssistMDA.RES_STATUS_CD.get("campON").equals(rscStatusCd)){
            	Map<String,Object> preRsParam = new HashMap<String, Object>();
            	preRsParam.put("accNum", rsNum);
            	preRsParam.put("state", "M");
            	preRsParam.put("regionId", lanId);
    			Map<String,Object> routePreParam = new HashMap<String, Object>();
    			routePreParam.put("regionId", lanId);
    			preRsParam.put("routeParam", routePreParam);
    			String retPreStr = resourceSMO.qryPreResCount(jsonConverter.toJson(preRsParam));
    			Map<String, Object> retPreMap = (Map<String, Object>) resolveResult(retPreStr);
    			String retCount = String.valueOf(retPreMap.get("retCount"));
    			if(StringUtils.isNotEmpty(retCount) && Integer.valueOf(retCount).intValue() > 0){
    				retMap.put("retCode", "-1");	
            		retMap.put("retDesc", "该号码存在预约记录！不能释放！");
            		return retMap;
    			}
    			
            }
            
            /**
             * 5、号码状态为14（临时预占），2（预占）没有在途单，没有资产实例数据，则允许释放
             *	如果是14（临时预占），修改号码表phone_number状态为空闲
             *	如果是2（预占），修改号码表phone_number状态为8（预留），由本地号码管	理员后续释放为空闲
             */
			if(AssistMDA.RES_STATUS_CD.get("chooseOf").equals(rscStatusCd) || AssistMDA.RES_STATUS_CD.get("campON").equals(rscStatusCd)){
            	Map<String,Object> releaseRsParam = new HashMap<String, Object>();
            	releaseRsParam.put("rsId", phoneNumberId);
            	releaseRsParam.put("rsCode", rsNum);
            	releaseRsParam.put("rsType", anTypeCd);
            	releaseRsParam.put("curStstus", rscStatusCd);
            	releaseRsParam.put("channelId", channelId);
            	releaseRsParam.put("staffId", staffId);
            	releaseRsParam.put("regionId", lanId);
            	releaseRsParam.put("lanId", regionId);
            	Map<String,Object> routePreParam = new HashMap<String, Object>();
    			routePreParam.put("regionId", lanId);
    			releaseRsParam.put("routeParam", routePreParam);
    			String releaseRsRetStr = resourceSMO.releaseRs(jsonConverter.toJson(releaseRsParam));
    			Map<String, Object> releaseRsRetMap = (Map<String, Object>) resolveResult(releaseRsRetStr);
    			
    			//记录日志
    			Map<String,Object> logData = new HashMap<String,Object>();
	   			logData.put("method","releaseRs");
	   			logData.put("paramJson",jsonConverter.toJson(releaseRsParam));
	   			logData.put("retStr",releaseRsRetStr);
	   		    LogHelper.writeLog("releaseRs",logData);
   			 
    			String handleResultCode = String.valueOf(releaseRsRetMap.get("handleResultCode"));
    			if(!"0".equals(handleResultCode)){
    				retMap.put("retCode", "-1");	
            		retMap.put("retDesc", "释放号码失败！"+String.valueOf(releaseRsRetMap.get("handleResultMsg")));
            		return retMap;
    			}
    			
    			
            }
            
            /**
             * 6、号码状态为3（在用），判断没有在途订单，资产当前表没有数据，资产历史表有数据，则允许释放。修改号码表phone_number状态为8（预留），由本地号码管理员后续释放为空闲。
             */
			if(AssistMDA.RES_STATUS_CD.get("inUse").equals(rscStatusCd)){
            	Map<String,Object> hisParam = new HashMap<String, Object>();
            	hisParam.put("accNum", rsNum);
            	hisParam.put("regionId", lanId);
            	String retHisStr = prodInstSMO.qryProdInstHisList(jsonConverter.toJson(hisParam));
            	List<Map<String, Object>> retHisList = (List<Map<String, Object>>) resolveResult(retHisStr);
            	if(null != retHisList && retHisList.size() > 0){
            		Map<String,Object> releaseRsParam = new HashMap<String, Object>();
                	releaseRsParam.put("rsId", phoneNumberId);
                	releaseRsParam.put("rsCode", rsNum);
                	releaseRsParam.put("rsType", anTypeCd);
                	releaseRsParam.put("curStstus", rscStatusCd);
                	releaseRsParam.put("channelId", channelId);
                	releaseRsParam.put("staffId", staffId);
                	releaseRsParam.put("lanId", regionId);
                	releaseRsParam.put("regionId", lanId);
                	Map<String,Object> routePreParam = new HashMap<String, Object>();
        			routePreParam.put("regionId", lanId);
        			releaseRsParam.put("routeParam", routePreParam);
        			String releaseRsRetStr = resourceSMO.releaseRs(jsonConverter.toJson(releaseRsParam));
        			Map<String, Object> releaseRsRetMap = (Map<String, Object>) resolveResult(releaseRsRetStr);
        			
        			//记录日志
        			Map<String,Object> logData = new HashMap<String,Object>();
    	   			logData.put("method","releaseRs");
    	   			logData.put("paramJson",jsonConverter.toJson(releaseRsParam));
    	   			logData.put("retStr",releaseRsRetStr);
    	   		    LogHelper.writeLog("releaseRs",logData);
    	   		    
        			String handleResultCode = String.valueOf(releaseRsRetMap.get("handleResultCode"));
        			if(!"0".equals(handleResultCode)){
        				retMap.put("retCode", "-1");	
                		retMap.put("retDesc", "释放号码失败！"+String.valueOf(releaseRsRetMap.get("handleResultMsg")));
                		return retMap;
        			}

            	}else{
            		retMap.put("retCode", "-1");	
            		retMap.put("retDesc", "该号码无在途订单，资产无在用数据，资产历史无记录，请联系运维人员！");
            		return retMap;
            	}
            	
            }
        }
        //账号，宽带账号、ITV账号，福彩、体彩账号等
        else if("2".equals(accNumType)){
        	
        	//1、查询账号信息
        	Map<String,Object> accountParam = new HashMap<String, Object>();
        	accountParam.put("loginName", rsNum);
        	accountParam.put("regionId", lanId);
			Map<String,Object> routeParam = new HashMap<String, Object>();
			routeParam.put("regionId", lanId);
			accountParam.put("routeParam", routeParam);
			String retRsStr = resourceSMO.qryAccessAccountByCond(jsonConverter.toJson(accountParam));
			Map<String, Object> retRsMap = (Map<String, Object>) resolveResult(retRsStr);
			String retCode = String.valueOf(retRsMap.get("handleResultCode"));
			String retMsg = String.valueOf(retRsMap.get("handleResultMsg"));
			if(!"0".equals(retCode)){
				retMap.put("retCode", "-1");
            	retMap.put("retDesc", retMsg);
        		return retMap;
			}
            List<Map<String, Object>> accountList = (List<Map<String, Object>>) MapUtils.getObject(retRsMap, "accountInfoList");
            if(null != accountList && accountList.size() < 1){
            	retMap.put("retCode", "-1");	
            	retMap.put("retDesc", "未查询到该账号的信息！");
        		return retMap;
            }
            
            if(null != accountList && accountList.size() > 1){
            	retMap.put("retCode", "-1");	
            	retMap.put("retDesc", "查询该账号信息记录数异常，请联系运维人员！");
        		return retMap;
            }
            Map<String, Object> accountInfo = accountList.get(0);
            String accessAccountId = String.valueOf(accountInfo.get("accessAccountId"));
            String anTypeCd = String.valueOf(accountInfo.get("anTypeCd"));
            String loginName = String.valueOf(accountInfo.get("loginName"));
            //只释放307类型账号
            if(!"307".equals(anTypeCd)){
            	retMap.put("retCode", "-1");	
            	retMap.put("retDesc", "不能释放该类型账号！账号类型编码："+anTypeCd);
        		return retMap;
            }
            
            //2、判断账号是否存在实例，如果存在实例，不允许释放
        	Map<String, Object> reqMap = new HashMap<String, Object>();
        	reqMap.put("account", loginName);
        	reqMap.put("regionId", lanId);
        	reqMap.put("busiRegionId", lanId);
        	String retStr = prodInstSMO.qryAccProdInstListLocal(jsonConverter.toJson(reqMap));
            Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(retStr);
            List<Map<String, Object>> prodInsts = (List<Map<String, Object>>) MapUtils.getObject(resultObjectMap, "accProdInsts");
            if(null != prodInsts && prodInsts.size() > 0){
            	retMap.put("retCode", "-1");	
            	retMap.put("retDesc", "该账号存在资产实例，不允许释放！");
        		return retMap;
            }
        	
        	//3、判断账号是否存在在途订单，如果存在在途订单，不允许释放
            Map<String, Object> onlineMap = new HashMap<String, Object>();
            List<String> orderItemStatuses = new ArrayList<String>();
            orderItemStatuses.add("101400");//客户需求确认
            orderItemStatuses.add("201300");//开通中
            orderItemStatuses.add("401200");//后端错误
            orderItemStatuses.add("101200");//待收费
            orderItemStatuses.add("401302");//修改中
            orderItemStatuses.add("401400");//已退单
            orderItemStatuses.add("301100");//竣工中
            orderItemStatuses.add("401301");//撤销中
            onlineMap.put("orderItemStatuses", orderItemStatuses);
            Map<String, Object> data = new HashMap<String, Object>();
            data.put("lanId", lanId);
            data.put("account", loginName);
            onlineMap.put("data", data);
            String retProdInfoStr = orderQuerySMO.qryUnarchivedOrdProdInsts(jsonConverter.toJson(onlineMap));
            List retList = (List) resolveResult(retProdInfoStr);
            if(retList != null && retList.size() > 0){
        		retMap.put("retCode", "-1");	
        		retMap.put("retDesc", "该账号存在在途单未竣工！不能释放！");
        		return retMap;
            }
            
            //释放账号
            Map<String,Object> releaseRsParam = new HashMap<String, Object>();
        	releaseRsParam.put("rsId", accessAccountId);
        	releaseRsParam.put("rsCode", loginName);
        	releaseRsParam.put("rsType", anTypeCd);
        	releaseRsParam.put("channelId", channelId);
        	releaseRsParam.put("staffId", staffId);
        	releaseRsParam.put("lanId", regionId);
        	releaseRsParam.put("regionId", lanId);
        	Map<String,Object> routePreParam = new HashMap<String, Object>();
			routePreParam.put("regionId", lanId);
			releaseRsParam.put("routeParam", routePreParam);
			String releaseRsRetStr = resourceSMO.releaseRs(jsonConverter.toJson(releaseRsParam));
			Map<String, Object> releaseRsRetMap = (Map<String, Object>) resolveResult(releaseRsRetStr);
			
			//记录日志
			Map<String,Object> logData = new HashMap<String,Object>();
   			logData.put("method","releaseRs");
   			logData.put("paramJson",jsonConverter.toJson(releaseRsParam));
   			logData.put("retStr",releaseRsRetStr);
   		    LogHelper.writeLog("releaseRs",logData);
   		    
			String handleResultCode = String.valueOf(releaseRsRetMap.get("handleResultCode"));
			if(!"0".equals(handleResultCode)){
				retMap.put("retCode", "-1");	
        		retMap.put("retDesc", "释放账号失败！"+String.valueOf(releaseRsRetMap.get("handleResultMsg")));
        		return retMap;
			}
        	
        }
        //串号
        else if("3".equals(accNumType)){
        	
        	//1、查询号码信息
        	Map<String,Object> resourceParam = new HashMap<String, Object>();
			resourceParam.put("mktResInstNbr", rsNum);
			resourceParam.put("regionId", lanId);
			Map<String,Object> routeParam = new HashMap<String, Object>();
			routeParam.put("regionId", lanId);
			resourceParam.put("routeParam", routeParam);
			String retRsStr = resourceSMO.queryMaterialInfo(jsonConverter.toJson(resourceParam));
			Map<String, Object> retRsMap = (Map<String, Object>) resolveResult(retRsStr);
			String retCode = String.valueOf(retRsMap.get("handleResultCode"));
			String retMsg = String.valueOf(retRsMap.get("handleResultMsg"));
			if(!"0".equals(retCode)){
				retMap.put("retCode", "-1");
            	retMap.put("retDesc", retMsg);
        		return retMap;
			}
			String state = String.valueOf(retRsMap.get("statusCd"));
			String mktResInstId = String.valueOf(retRsMap.get("mktResInstId"));
			if(!AssistMDA.INST_STATUS_T.equals(state) && !AssistMDA.INST_STATUS_P.equals(state) && !AssistMDA.INST_STATUS_C.equals(state)){
            	retMap.put("retCode", "-1");
            	retMap.put("retDesc", "串号状态非临时预占、预占、在用，不能释放！");
        		return retMap;
            }
			
            //2、判断串号是否存在实例，如果存在实例，不允许释放
        	Map<String, Object> reqMap = new HashMap<String, Object>();
        	reqMap.put("mktResInstNbr", rsNum);
        	String retStr = prodInstSMO.qryOfferProdResInstByMktResInstNbr(jsonConverter.toJson(reqMap));
            Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(retStr);
            List<Map<String, Object>> prodResInstRels = (List<Map<String, Object>>) MapUtils.getObject(resultObjectMap, "prodResInstRels");
            List<Map<String, Object>> offerResInstRels = (List<Map<String, Object>>) MapUtils.getObject(resultObjectMap, "offerResInstRels");
            if(null != prodResInstRels && prodResInstRels.size() > 0){
            	retMap.put("retCode", "-1");	
            	retMap.put("retDesc", "该串号存在产品与营销资源实例，不允许释放！");
        		return retMap;
            }
            if(null != offerResInstRels && offerResInstRels.size() > 0){
            	retMap.put("retCode", "-1");	
            	retMap.put("retDesc", "该串号存在销售品与营销资源实例，不允许释放！");
        		return retMap;
            }
            
            
        	//3、判断串号是否存在在途订单，如果存在在途订单，不允许释放
            Map<String, Object> onlineMap = new HashMap<String, Object>();
            List<String> orderItemStatuses = new ArrayList<String>();
            orderItemStatuses.add("101400");//客户需求确认
            orderItemStatuses.add("201300");//开通中
            orderItemStatuses.add("401200");//后端错误
            orderItemStatuses.add("101200");//待收费
            orderItemStatuses.add("401302");//修改中
            orderItemStatuses.add("401400");//已退单
            orderItemStatuses.add("301100");//竣工中
            orderItemStatuses.add("401301");//撤销中
            onlineMap.put("orderItemStatuses", orderItemStatuses);
            Map<String, Object> data = new HashMap<String, Object>();
            data.put("mktResInstNbr", rsNum);
            onlineMap.put("data", data);
            String retProdResInfoStr = orderQuerySMO.qryUnarchivedOrdProdResInstRels(jsonConverter.toJson(onlineMap));
            List retProdList = (List) resolveResult(retProdResInfoStr);
            if(retProdList != null && retProdList.size() > 0){
        		retMap.put("retCode", "-1");	
        		retMap.put("retDesc", "该串号存在产品与营销资源未竣工订单，不允许释放！");
        		return retMap;
            }
            
            String retOfferResInfoStr = orderQuerySMO.qryUnarchivedOrdOfferResInstRels(jsonConverter.toJson(onlineMap));
            List retOfferList = (List) resolveResult(retOfferResInfoStr);
            if(retOfferList != null && retOfferList.size() > 0){
        		retMap.put("retCode", "-1");	
        		retMap.put("retDesc", "该串号存在销售品与营销资源未竣工订单，不允许释放！");
        		return retMap;
            }
            
            //释放串号
            Map<String,Object> releaseRsParam = new HashMap<String, Object>();
        	releaseRsParam.put("rsId", mktResInstId);
        	releaseRsParam.put("rsCode", rsNum);
        	releaseRsParam.put("rsType", "100");
        	releaseRsParam.put("channelId", channelId);
        	releaseRsParam.put("curStstus", state);
        	releaseRsParam.put("staffId", staffId);
        	releaseRsParam.put("lanId", regionId);
        	releaseRsParam.put("regionId", lanId);
        	Map<String,Object> routePreParam = new HashMap<String, Object>();
			routePreParam.put("regionId", lanId);
			releaseRsParam.put("routeParam", routePreParam);
			String releaseRsRetStr = resourceSMO.releaseRs(jsonConverter.toJson(releaseRsParam));
			Map<String, Object> releaseRsRetMap = (Map<String, Object>) resolveResult(releaseRsRetStr);
			
			//记录日志
			Map<String,Object> logData = new HashMap<String,Object>();
   			logData.put("method","releaseRs");
   			logData.put("paramJson",jsonConverter.toJson(releaseRsParam));
   			logData.put("retStr",releaseRsRetStr);
   		    LogHelper.writeLog("releaseRs",logData);
   		    
			String handleResultCode = String.valueOf(releaseRsRetMap.get("handleResultCode"));
			if(!"0".equals(handleResultCode)){
				retMap.put("retCode", "-1");	
        		retMap.put("retDesc", "释放串号失败！"+String.valueOf(releaseRsRetMap.get("handleResultMsg")));
        		return retMap;
			}
            
        }
        //UIM卡
        else if("4".equals(accNumType)){
        	//1、查询UIM卡信息
        	Map<String,Object> resourceParam = new HashMap<String, Object>();
			resourceParam.put("uimCode", rsNum);
			resourceParam.put("regionId", lanId);
			Map<String,Object> routeParam = new HashMap<String, Object>();
			routeParam.put("regionId", lanId);
			resourceParam.put("routeParam", routeParam);
			String retRsStr = resourceSMO.qryUimCardInfoByCond(jsonConverter.toJson(resourceParam));
			Map<String, Object> retRsMap = (Map<String, Object>) resolveResult(retRsStr);
			String retCode = String.valueOf(retRsMap.get("handleResultCode"));
			String retMsg = String.valueOf(retRsMap.get("handleResultMsg"));
			if(!"0".equals(retCode)){
				retMap.put("retCode", "-1");
            	retMap.put("retDesc", retMsg);
        		return retMap;
			}
			String rscStatusCd = String.valueOf(retRsMap.get("rscStatusCd"));
			String anId = String.valueOf(retRsMap.get("anId"));
			if(!"14".equals(rscStatusCd) && !"2".equals(rscStatusCd) && !"3".equals(rscStatusCd)){
            	retMap.put("retCode", "-1");
            	retMap.put("retDesc", "UIM卡状态非临时预占、预占、在用，不能释放！");
        		return retMap;
            }
			
            //2、判断UIM卡是否存在实例，如果存在实例，不允许释放
        	Map<String, Object> reqMap = new HashMap<String, Object>();
        	reqMap.put("mktResInstNbr", rsNum);
        	String retStr = prodInstSMO.qryOfferProdResInstByMktResInstNbr(jsonConverter.toJson(reqMap));
            Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(retStr);
            List<Map<String, Object>> prodResInstRels = (List<Map<String, Object>>) MapUtils.getObject(resultObjectMap, "prodResInstRels");
            if(null != prodResInstRels && prodResInstRels.size() > 0){
            	retMap.put("retCode", "-1");	
            	retMap.put("retDesc", "该UIM卡存在实例，不允许释放！");
        		return retMap;
            }
			
        	//3、判断UIM卡是否存在在途订单，如果存在在途订单，不允许释放
            Map<String, Object> onlineMap = new HashMap<String, Object>();
            List<String> orderItemStatuses = new ArrayList<String>();
            orderItemStatuses.add("101400");//客户需求确认
            orderItemStatuses.add("201300");//开通中
            orderItemStatuses.add("401200");//后端错误
            orderItemStatuses.add("101200");//待收费
            orderItemStatuses.add("401302");//修改中
            orderItemStatuses.add("401400");//已退单
            orderItemStatuses.add("301100");//竣工中
            orderItemStatuses.add("401301");//撤销中
            onlineMap.put("orderItemStatuses", orderItemStatuses);
            Map<String, Object> data = new HashMap<String, Object>();
            data.put("mktResInstNbr", rsNum);
            onlineMap.put("data", data);
            String retProdResInfoStr = orderQuerySMO.qryUnarchivedOrdProdResInstRels(jsonConverter.toJson(onlineMap));
            List retProdList = (List) resolveResult(retProdResInfoStr);
            if(retProdList != null && retProdList.size() > 0){
        		retMap.put("retCode", "-1");	
        		retMap.put("retDesc", "该UIM卡存在未竣工订单，不允许释放！");
        		return retMap;
            }
            
            //释放UIM卡
            Map<String,Object> releaseRsParam = new HashMap<String, Object>();
        	releaseRsParam.put("rsId", anId);
        	releaseRsParam.put("rsCode", rsNum);
        	releaseRsParam.put("rsType", "509");
        	releaseRsParam.put("channelId", channelId);
        	releaseRsParam.put("curStstus", rscStatusCd);
        	releaseRsParam.put("staffId", staffId);
        	releaseRsParam.put("lanId", regionId);
        	releaseRsParam.put("regionId", lanId);
        	Map<String,Object> routePreParam = new HashMap<String, Object>();
			routePreParam.put("regionId", lanId);
			releaseRsParam.put("routeParam", routePreParam);
			String releaseRsRetStr = resourceSMO.releaseRs(jsonConverter.toJson(releaseRsParam));
			Map<String, Object> releaseRsRetMap = (Map<String, Object>) resolveResult(releaseRsRetStr);
			
			//记录日志
			Map<String,Object> logData = new HashMap<String,Object>();
   			logData.put("method","releaseRs");
   			logData.put("paramJson",jsonConverter.toJson(releaseRsParam));
   			logData.put("retStr",releaseRsRetStr);
   		    LogHelper.writeLog("releaseRs",logData);
   		    
			String handleResultCode = String.valueOf(releaseRsRetMap.get("handleResultCode"));
			if(!"0".equals(handleResultCode)){
				retMap.put("retCode", "-1");	
        		retMap.put("retDesc", "释放UIM卡失败！"+String.valueOf(releaseRsRetMap.get("handleResultMsg")));
        		return retMap;
			}
			
        }else{
        	retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "无法释放该资源类型！");
    		return retMap;
        }

        retMap.put("retCode", "0");	
        retMap.put("retDesc", "操作成功！");

        return retMap;
    }

  
}
