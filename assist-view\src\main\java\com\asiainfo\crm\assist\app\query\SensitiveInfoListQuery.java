package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IStaffInfoQuerySMO;
import com.asiainfo.crm.service.intf.IStaffOrgAreaSMO;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.service.intf.ISensitiveInfoSmo;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.Collections;
import java.util.ArrayList;

/**
 * create by liugd 20190415
 */
@Component("vita.sensitiveInfoListQuery")
public class SensitiveInfoListQuery extends AbstractComponent {

    @Autowired
    private IStaffInfoQuerySMO staffInfoQuerySMO;
    @Autowired
    private ICustMultiSMO custMultiSMO;
    @Autowired
    private IStaffOrgAreaSMO staffOrgAreaSMO;
    @Autowired
    private ISensitiveInfoSmo sensitiveInfoSmo;
    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map querySensitiveInfoList(String jsonString) throws Exception {

        Map reqParams = jsonConverter.toBean(jsonString, Map.class);
        Object staffCode = reqParams.get("staffCode");
        if(null!=staffCode){
            Map map = new HashMap();
            map.put("staffCode",staffCode);
            Map resultMap = qryStaffInfo(map);
            if(null!=resultMap.get("staffId")){
                reqParams.put("staffId",resultMap.get("staffId")+"");
            }else{
                reqParams.put("staffId","0000");
            }
        }
        Map<String, Object> options = Maps.newHashMap();

        String sensitiveInfoListInfo = sensitiveInfoSmo.qrySensitiveInfoLog(jsonConverter.toJson(reqParams));
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(sensitiveInfoListInfo);
        List resultList = this.handleResultList(resultObjectMap);

        int total = MapUtils.getIntValue(resultObjectMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(resultObjectMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(resultObjectMap, "pageSize");
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", total);
        options.put("sensitiveInfoList",resultList);
        return options;
    }

    private List handleResultList( Map<String, Object> resultObjectMap) throws Exception {

        List<Map<String,Object>> list = (List<Map<String,Object>>) resultObjectMap.get("rows");
        List<Map<String,String>> resultList = new ArrayList<Map<String,String>>();
        for(Map<String,Object> sensitiveInfo:list){
            Map<String,String> resultMap = new HashMap<String,String>();

            String staffId = sensitiveInfo.get("staffId")+"";
            if(null!=staffId){
                Map map = new HashMap();
                map.put("staffId",staffId);
                Map paraMap = qryStaffInfo(map);
                resultMap.put("staffName",paraMap.get("staffName")+"");
            }

            String orgId = sensitiveInfo.get("orgId")+"";
            resultMap.put("orgName",this.queryChannelDetail(orgId));
            String custId = sensitiveInfo.get("custId")+"";
            resultMap.put("custName",this.qryCustomerDetail(custId));
            String qryPoint = sensitiveInfo.get("qryPoint")+"";
            resultMap.put("qryPoint",MDA.SENSITIVE_INFO_QRY_POINT.get(qryPoint));
            String qryObjType = sensitiveInfo.get("qryObjType")+"";
            resultMap.put("qryObjType",MDA.SENSITIVE_INFO_QRY_OBJ_TYPE.get(qryObjType));
            String ip = sensitiveInfo.get("ip")+"";
            resultMap.put("ip",ip);
            String mac = sensitiveInfo.get("mac")+"";
            resultMap.put("mac",mac);
            String createDt = sensitiveInfo.get("createDt")+"";
            resultMap.put("createDt",createDt);
            Object agentStaffId = sensitiveInfo.get("agentStaffId")+"";
            if(null!=agentStaffId){
                Map map = new HashMap();
                map.put("staffId",staffId);
                Map paraMap = qryStaffInfo(map);
                resultMap.put("agentStaffName",paraMap.get("staffName")+"");
            }
            resultList.add(resultMap);
        }
        return resultList;
    }

    /**
     * 获取员工staffcode
     * @param param
     * @return
     * @throws Exception
     */
    public Map qryStaffInfo(Map param) throws Exception {
        Map staffInfoMap = Collections.EMPTY_MAP;
        String sensitiveInfoListInfo = staffInfoQuerySMO.qryStaffInfo(jsonConverter.toJson(param));
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(sensitiveInfoListInfo);
        if(resultObjectMap.size()>0){
              staffInfoMap = (Map) resultObjectMap.get("staffInfo");
            return staffInfoMap;
        }
        return staffInfoMap;
    }

    /**
     * 获取客户名称
     * @param custId
     * @return
     * @throws Exception
     */
    public String qryCustomerDetail(String custId) throws Exception {
        Map<String,String> map = new HashMap<String,String>();
        map.put("custId",custId);
        String customerDetail = custMultiSMO.qryCustomerDetail(jsonConverter.toJson(map));
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(customerDetail);
        if(resultObjectMap.size()>0){
            Map customerDetailMap = (Map) resultObjectMap.get("customerDetail");
            String custName = customerDetailMap.get("custName")+"";
            return custName;
        }
        return custId;
    }
    /**
     * 获取渠道信息
     * @param orgId
     * @return
     * @throws Exception
     */
    public String queryChannelDetail(String orgId) throws Exception {
        Map<String,String> map = new HashMap<String,String>();
        map.put("channelId",orgId);
        String channelDetails = staffOrgAreaSMO.queryChannelDetail(jsonConverter.toJson(map));
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(channelDetails);
        if(resultObjectMap.size()>0){
            List channelDetailList = (List) resultObjectMap.get("channelDetails");
            String channelName = ((Map)channelDetailList.get(0)).get("channelName")+"";
            return channelName;
        }

        return "";

    }
}
