<div data-widget="orderAuditDetail" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="calcw_rightbox noneleft">
        <div class="calctitle">
            <div class="titlefont">订单稽核审核</div>
            <div class="toolr">
                <button id="closeBtn" type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">×</span></button>
            </div>
        </div>
        <div class="calcw_rightcont">
            <div class="container-fluid padt15">
                #if($options != "null" && $options.photoList && $options.photoList != "null")
                <div class="row">
                    #foreach($photo in $options.photoList)
                        #if($photo.busiType == "A")
                        #set($picType = "fa-photo-ico pic-d")
                        #elseif($photo.busiType == "B")
                        #set($picType = "fa-id-ico pic-d")
                        #else
                        #set($picType = "")
                        #end
                        #if($photo.busiTypeName && $photo.busiTypeName != "null")
                        #set($busiTypeName = "照片类型:" + $photo.busiTypeName + " ")
                        #else
                        #set($busiTypeName = "")
                        #end
                        #if($photo.accNbr && $photo.accNbr != "null")
                        #set($accNbr = $busiTypeName + "归属号码:" + $photo.accNbr)
                        #else
                        #set($accNbr = $busiTypeName)
                        #end
                    <div class="col-md-3 marb10" title="$accNbr">
                        <div class="title-pic"><i class="$picType"></i></div>
                        <img src="data:image/jpg;base64,$photo.result" style="width:100%;height: 270px;">
                    </div>
                    #end
                </div>
                #end
            </div>
            <div class="container-fluid padt15">
                #if($options != "null" && $options.auditOrderInfo && $options.auditOrderInfo != "null")
                    <div class="form-group col-md-12">
                        <label class="col-md-4 control-label lablep">客户姓名：</label>
                        <div class="col-md-4">
                            <span class="form-control">$!{options.auditOrderInfo.custNoEncodeName}</span>
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label class="col-md-4 control-label lablep">受理时间：</label>
                        <div class="col-md-4">
                            <span class="form-control">$!{options.auditOrderInfo.statusDate}</span>
                        </div>
                    </div>
                #if($options.auditOrderInfo.compareValue && $options.auditOrderInfo.compareValue != "null")
                <div class="form-group col-md-12">
                    <label class="col-md-4 control-label lablep">认证相符度：</label>
                    <div class="col-md-4">
                        <span class="form-control">
                            <span class="textcolorgreen">$!{options.auditOrderInfo.compareValue}</span>
                            <span class="textcolorgreen">%</span>
                        </span>

                    </div>
                </div>
                #end
                <div class="form-group col-md-12">
                    <label class="col-md-4 control-label lablep">审核结果：</label>
                    <div class="col-md-4">
                        #if($options.auditOrderInfo.auditResult && $options.auditOrderInfo.auditResult != "null" &&
                        $options.auditOrderInfo.auditResult == "3")
                        <span class="form-control">$!{options.auditOrderInfo.auditResultName}</span>
                        #elseif($options.auditOrderInfo.auditResult && $options.auditOrderInfo.auditResult != "null" &&
                        $options.auditOrderInfo.auditResult == "2")
                        <span class="form-control">审核通过</span>
                        #else
                        <select name="auditResult" class="form-control">
                            #if($options.auditResultList && $options.auditResultList != "null" &&
                            $options.auditResultList.size() > 0)
                                #foreach($auditOrderResult in $options.auditResultList)
                                #if($auditOrderResult.selected && $auditOrderResult.selected == "selected")
                                <option value="$auditOrderResult.key" selected="selected">
                                    $auditOrderResult.value
                                </option>
                                #else
                                <option value="$auditOrderResult.key">
                                $auditOrderResult.value
                                </option>
                                #end
                                #end
                            #end
                        </select>
                        #end
                    </div>
                    <div class="col-md-4" style="padding-top: 8px;"><span id="auditFailPrompt" style="color: red;font-size: 15px;"></span></div>
                </div>
                #if($options.auditOrderInfo.auditResult && $options.auditOrderInfo.auditResult != "null" &&
                $options.auditOrderInfo.auditResult == "3")
                <div class="form-group col-md-12">
                    <label class="col-md-4 control-label lablep">审核不通过原因：</label>
                    <div class="col-md-4">
                        <span class="form-control">$!{options.auditOrderInfo.remark}</span>
                    </div>
                </div>
                #end
                <div class="form-group col-md-12" name="auditResultReasonDiv" hidden="hidden">
                    <label class="col-md-4 control-label lablep">审核不通过原因：</label>
                    <div class="col-md-4">
                        <select name="auditResultReason" class="form-control">
                            #if($options != "null" && $options.auditOrderReason && $options.auditOrderReason != "null" &&
                            $options.auditOrderReason.size() > 0)
                                #foreach($reason in $options.auditOrderReason)
                                <option>$!{reason}</option>
                                #end
                            #end
                        </select>
                    </div>
                </div>
                #end
                <div name="otherReasonDiv" class="form-group col-md-12" hidden="hidden">
                    <label class="col-md-4 control-label lablep">请输入其他原因：</label>
                    <div class="col-md-4">
                        <input name="otherReason" type="text" class="form-control" placeholder="" maxlength="100">
                    </div>
                </div>
                #if($options.auditOrderInfo.auditResult && $options.auditOrderInfo.auditResult != "null" &&
                $options.auditOrderInfo.auditResult == "1")
                <div class="form-group col-md-12">
                    <div class="col-md-4">
                        <label class="col-md-4 control-label lablep"></label>
                        <button id="audit-btn" type="button" class="btn btn-primary">&nbsp;提&nbsp;交&nbsp;</button>
                        <p class="vita-data">{"custOrderId" : "$!options.auditOrderInfo.custOrderId", "custOrderNbr":
                            "$!options.auditOrderInfo.custOrderNbr"}</p>
                    </div>
                </div>
                #end
            </div>
        </div>
    </div>
</div>
