(function(vita) {
	var custQuery = vita.Backbone.BizView.extend({
		events : {
			"click .icon-search" : "_custQuery",
			"keydown #condValue" : "_keydownCustQuery",
			"click .nousercont_list li" : "_setCertType",
			"click #addCust" : "_showAddCustPage",
			"click #scanCert" : "_scanCertPage"	
		},
		_initialize : function() {
			var widget = this;
			widget.addListener("cust.addCust", function () {
				widget._showAddCustPage();
            });
			widget.addListener("cust.scanCert", function () {
				widget._scanCertPage();
            });
		},
		global : {
			addCustUrl : "../so/addCust",
			scanCertUrl: "../so/scanCert"
		},
		
		_scanCertPage : function() {
			var widget = this,
			element = $(widget.el);
			var option = {
				url : widget.global.scanCertUrl,
				onClose : function(res) {
					var scanCert = $(res).closest("[data-widget=scanCert]");
					if (scanCert.length) {
						/*var cust = scanCert.scanCert("getValue");
						if (!cust || !cust.validate) {
							return false;
						}*/
						
					}
				}
			};
			widget.dialog(option);
			return false;
		},
		_showAddCustPage : function() {
			
			var widget = this,
				element = $(widget.el);
			var option = {
				url : widget.global.addCustUrl,
				onClose : function(res) {
					var addCust = $(res).closest("[data-widget=addCust]");
					if (addCust && addCust.length) {
						var cust = addCust.addCust("getValue");						
						if (!cust || !cust.custId ) {							
							return false;
						}
						//新增客户之后
						cust.custAuthSwitch = "N";
						widget.dispatchEvent("refreshCustView", cust);	
					}
				}
			};
			widget.dialog(option);
			return false;
		},
		_getConds : function(condValue) {
			var widget = this,
				gSession = widget.gSession;
			var param = {
				value : condValue,
				glbSessionId : gSession.glbSessionId
			};
			var certType = $.trim(widget.model.get("certType"));
			var soUtil = widget.require("soUtil");
			var certTypeNullFlag = soUtil.isNullOrEmpty(certType);
			if (!certTypeNullFlag) {
				param.certType = certType;
			}
			
			var queryType = $.trim(widget.model.get("queryType"));
			var queryTypeNullFlag = soUtil.isNullOrEmpty(queryType);
			if (!queryTypeNullFlag) {
				param.queryType = queryType;
			}
			return param;
		},
		_keydownCustQuery : function(e) {
			var widget = this;
			if (e.keyCode == 13) {
				widget._custQuery();
				return false;
			}
		},
		_custQuery : function(e) {
			var widget = this,
				element = $(widget.el);
			var condValue = $.trim(element.find("#condValue").val());
			if (!widget._checkValue(condValue)) {
				widget.popup("输入客户证件或名称搜索！");
				return false;
			}
			var params = widget._getConds(condValue);
			var usercont = element.find(".search_usercont");
			widget.refreshPart("qryCustomerList", params, ".search_usercont", "partyList", function(res) {
				var $res = $(res);
				if ($res.length) {
					usercont.show();
					var length = $res.find("li").length;
					// 只有1条记录时处理
					if (length == 1) {
						var customer = $res.find("li").data("customer");
						widget.dispatchEvent("refreshCustView", customer);
					} else if (!length) {
						widget._queryAuthenticDataRange();
					}
				}
			}, {
				mask : true
			});
			widget._initQueryConds();
		},
		_setCertType : function(e) {
			var widget = this,
				element = $(widget.el);
			var li = $(e.target).closest("li");
			var data = li.data("data");
			widget.model.set({
				certType : data.dimensionValue,
				certTypeName : data.dataDimensionName,
				queryType : data.dataDimensionCode
			});
			widget._custQuery();
		},
		//查询类型
		_queryAuthenticDataRange : function() {
			var widget = this,
				gSession = widget.gSession,
				element = $(widget.el);
			var params = {
				glbSessionId : gSession.glbSessionId,
				operatSpecCd : "CML_QUERY_IDENTITY_TYPE",
				dataDimensionCd : "CMD_QUERY_IDENTITY_TYPE"
			};
			var usercont = element.find(".search_usercont");
			widget.refreshPart("queryAuthenticDataRange", params, ".search_usercont", "dataRanges", function(res) {
				var $res = $(res);
				var condValue = $.trim(element.find("#condValue").val());
				$(res).find(".nouser_if").html("没有搜索到”" + condValue + "”相关信息!");
				if ($res.length) {
					usercont.show();
				}
			}, {
				mask : true
			});
		},
		_checkValue : function(condValue) {
			var widget = this;
			var soUtil = widget.require("soUtil");
			var isNullOrEmpty = soUtil.isNullOrEmpty(condValue);
			return isNullOrEmpty ? false : true;
		},
		_initQueryConds : function(){
			var widget = this;
			widget.model.set({
				certType : "",
				certTypeName : "",
				queryType : ""
			});
		}
	});
	vita.widget.register("custQuery", custQuery, true);
})(window.vita);