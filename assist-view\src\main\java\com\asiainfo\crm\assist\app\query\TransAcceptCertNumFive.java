package com.asiainfo.crm.assist.app.query;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.util.SmLogUtil;
import com.google.common.collect.Maps;

/**
 * Created by niewq on 2018/6/20.
 */
@Component("vita.transAcceptCertNumFive")
public class TransAcceptCertNumFive extends AbstractComponent {

	private static final Logger logger = LoggerFactory
			.getLogger(TransAcceptCertNumFive.class);

	@Autowired
	private ICustMultiSMO custMultiSMO;

	@Override
	public Map achieveData(Object... params) throws Exception {
		return null;
	}

	@SuppressWarnings("unchecked")
	public Map<String, Object> queryTransCertNumFive(String jsonStr)
			throws Exception {
		Map<String, Object> options = Maps.newHashMap();
		logger.debug(jsonStr);
		jsonStr = SmLogUtil.supplyOneFivePatams(jsonStr);
		String res = custMultiSMO.queryTransOneCertNumFive(jsonStr);
		Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(res);
		if (resultObjectMap == null) {
			options.put("transCertNumFiveList", null);
			return options;
		}
		List<Map<String, Object>> transCertNumFiveList = new ArrayList<Map<String, Object>>();
		if (resultObjectMap.containsKey("resultObject")) {
			Map<String, Object> resultObjects = (Map<String, Object>) resultObjectMap
					.get("resultObject");
			transCertNumFiveList = (List<Map<String, Object>>) MapUtils
					.getObject(resultObjects, "transCertNumFiveList");
		} else {
			transCertNumFiveList = (List<Map<String, Object>>) MapUtils
					.getObject(resultObjectMap, "transCertNumFiveList");
		}
		Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils
				.getObject(resultObjectMap, "pageInfo");
		int totalCount = MapUtils.getIntValue(pageInfoMap, "totalCount");
		int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
		int pageSize = MapUtils.getIntValue(pageInfoMap, "pageSize");
		options.put("pageCount", pageIndex);
		options.put("pageIndex", pageSize);
		options.put("totalNumber", totalCount);
		options.put("transCertNumFiveList", transCertNumFiveList);
		return options;

	}

}
