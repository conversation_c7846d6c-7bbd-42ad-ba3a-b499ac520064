package com.asiainfo.crm.assist.app.cashier;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IOrderSMO;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 新增费用类
 */
@Component("vita.addChargeItem")
public class AddChargeItem extends AbstractComponent {

	@Autowired
	private IOrderSMO orderSMO;

	@Override
	public Map achieveData(Object... params) throws Exception {
		String jsonString = params[0].toString();
		Map options = new HashMap();
		List busiObjs = new ArrayList();
		List chargeItems = new ArrayList();
		List payMethods = new ArrayList();
		// String resString = orderSMO.queryBusiObjs(jsonString);
		String busiObjString = "{\n" + " \"resultCode\":\"0\",\n"
				+ " \"resultMsg\":\"处理成功!\",\n" + " \"resultObject\":{\n"
				+ "  \"busiObjs\":[\n" + "   {\n" + "    \"accNbr\":\"111\",\n"
				+ "    \"actionTypeName\":\"a\",\n" + "    \"boId\":\"111\",\n"
				+ "    \"busiObjName\":\"手机\",\n"
				+ "    \"busiSpecId\":\"378\",\n"
				+ "    \"serviceOfferName\":\"新装1\"\n" + "   },\n" + "   {\n"
				+ "    \"accNbr\":\"222\",\n"
				+ "    \"actionTypeName\":\"aa\",\n" + "    \"boId\":\"222\",\n"
				+ "    \"busiObjName\":\"固话\",\n"
				+ "    \"busiSpecId\":\"2\",\n"
				+ "    \"serviceOfferName\":\"新装2\"\n" + "   },\n" + "   {\n"
				+ "    \"accNbr\":\"333\",\n"
				+ "    \"actionTypeName\":\"aaa\",\n"
				+ "    \"boId\":\"333\",\n" + "    \"busiObjName\":\"宽带\",\n"
				+ "    \"busiSpecId\":\"9\",\n"
				+ "    \"serviceOfferName\":\"新装3\"\n" + "   }\n" + "  ]\n"
				+ " }\n" + "}";
		Map busiObjMap = jsonConverter.toBean(busiObjString, Map.class);
		String busiObjCode = MapUtils.getString(busiObjMap, "resultCode");
		if (!StringUtil.isEmpty(busiObjCode) && busiObjCode.equals(MDA.RESULT_SUCCESS.toString())) {
			Map resultObject = (Map) busiObjMap.get("resultObject");
			busiObjs = (List) resultObject.get("busiObjs");
		}
		
		if (busiObjs != null && !busiObjs.isEmpty()) {
			Map busiObj = (Map) busiObjs.get(0);
			String busiSpecId = MapUtils.getString(busiObj, "busiSpecId");
			Map paramMap = new HashMap();
			paramMap.put("objId", busiSpecId);
			// String chargeItemString =
			// orderSMO.queryChargeItems(jsonConverter.toJson(paramMap));
			String chargeItemString = "{\n" + " \"resultCode\":\"0\",\n"
					+ " \"resultMsg\":\"处理成功!\",\n"
					+ " \"resultObject\":{\n" + "  \"chargeItems\":[\n"
					+ "   {\n" + "    \"chargeItemCd\":\"43\",\n"
					+ "    \"name\":\"工料费\",\n"
					+ "    \"taxItemId\":10030101,\n"
					+ "    \"taxRate\":17\n" + "   },\n" + "   {\n"
					+ "    \"chargeItemCd\":\"63\",\n"
					+ "    \"name\":\"预存款\",\n"
					+ "    \"taxItemId\":10500101,\n"
					+ "    \"taxRate\":0\n" + "   },\n" + "   {\n"
					+ "    \"chargeItemCd\":\"1315\",\n"
					+ "    \"name\":\"ADSL违约金\",\n"
					+ "    \"taxItemId\":10010201,\n"
					+ "    \"taxRate\":6\n" + "   }\n" + "  ]\n" + " }\n"
					+ "}";
			Map chargeItemMap = jsonConverter.toBean(chargeItemString, Map.class);
			String chargeItemCode = MapUtils.getString(chargeItemMap, "resultCode");
			if (!StringUtil.isEmpty(chargeItemCode) && chargeItemCode .equals(MDA.RESULT_SUCCESS.toString())) {
				Map chargeObject = (Map) chargeItemMap.get("resultObject");
				chargeItems = (List) chargeObject.get("chargeItems");
				if (chargeItems != null && !chargeItems.isEmpty()) {
					Map chargeItem = (Map) chargeItems.get(0);
					String chargeItemCd = MapUtils.getString(chargeItem,
							"chargeItemCd");
					Map payParamMap = new HashMap();
					payParamMap.put("chargeItemCd", chargeItemCd);
					// String resString =
					// orderSMO.queryPayMethods(jsonConverter.toJson(payParamMap));
					String payString = "{\n" + " \"resultCode\":\"0\",\n"
							+ " \"resultMsg\":\"处理成功!\",\n"
							+ " \"resultObject\":{\n"
							+ "  \"payMethods\":[\n" + "   {\n"
							+ "    \"description\":\"现金支付\",\n"
							+ "    \"name\":\"现金支付\",\n"
							+ "    \"payMethodCd\":1\n" + "   },\n"
							+ "   {\n" + "    \"description\":\"支票支付\",\n"
							+ "    \"name\":\"支票支付\",\n"
							+ "    \"payMethodCd\":2\n" + "   },\n"
							+ "   {\n" + "    \"description\":\"汇票支付\",\n"
							+ "    \"name\":\"汇票支付\",\n"
							+ "    \"payMethodCd\":3\n" + "   },\n"
							+ "   {\n" + "    \"description\":\"转帐务系统\",\n"
							+ "    \"name\":\"转帐务系统\",\n"
							+ "    \"payMethodCd\":4\n" + "   }\n" + "  ]\n"
							+ " }\n" + "}";
					Map payMap = jsonConverter.toBean(payString, Map.class);
					String payCode = MapUtils.getString(payMap,"resultCode");
					if (!StringUtil.isEmpty(payCode) && payCode.equals(MDA.RESULT_SUCCESS.toString())) {
						Map payObject = (Map) payMap.get("resultObject");
						payMethods = (List) payObject.get("payMethods");
					}
				}
			}
		}
		options.put("busiObjs", busiObjs);
		options.put("chargeItems", chargeItems);
		options.put("payMethods", payMethods);
		return options;
	}

	public String queryChargeItems(String jsonString) throws Exception {
		// String resString = orderSMO.queryChargeItems(jsonString);
		String resString = "{\n" + " \"resultCode\":\"0\",\n"
				+ " \"resultMsg\":\"处理成功!\",\n" + " \"resultObject\":{\n"
				+ "  \"chargeItems\":[\n" + "   {\n"
				+ "    \"chargeItemCd\":\"43\",\n" + "    \"name\":\"工料费\",\n"
				+ "    \"taxItemId\":10030101,\n" + "    \"taxRate\":17\n"
				+ "   },\n" + "   {\n" + "    \"chargeItemCd\":\"63\",\n"
				+ "    \"name\":\"预存款\",\n" + "    \"taxItemId\":10500101,\n"
				+ "    \"taxRate\":0\n" + "   },\n" + "   {\n"
				+ "    \"chargeItemCd\":\"1315\",\n"
				+ "    \"name\":\"ADSL违约金\",\n"
				+ "    \"taxItemId\":10010201,\n" + "    \"taxRate\":6\n"
				+ "   }\n" + "  ]\n" + " }\n" + "}";
		return resString;
	}

	public String queryPayMethods(String jsonString) throws Exception {
		// String resString = orderSMO.queryPayMethods(jsonString);
		String resString = "{\n" + " \"resultCode\":\"0\",\n"
				+ " \"resultMsg\":\"处理成功!\",\n" + " \"resultObject\":{\n"
				+ "  \"payMethods\":[\n" + "   {\n"
				+ "    \"description\":\"现金支付\",\n" + "    \"name\":\"现金支付\",\n"
				+ "    \"payMethodCd\":1\n" + "   },\n" + "   {\n"
				+ "    \"description\":\"支票支付\",\n" + "    \"name\":\"支票支付\",\n"
				+ "    \"payMethodCd\":2\n" + "   },\n" + "   {\n"
				+ "    \"description\":\"汇票支付\",\n" + "    \"name\":\"汇票支付\",\n"
				+ "    \"payMethodCd\":3\n" + "   },\n" + "   {\n"
				+ "    \"description\":\"转帐务系统\",\n"
				+ "    \"name\":\"转帐务系统\",\n" + "    \"payMethodCd\":4\n"
				+ "   }\n" + "  ]\n" + " }\n" + "}";
		return resString;
	}

}
