<div data-widget="agentAuditInfoQuery" style="height: 100%">

    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>稽核厅信用额度明细</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                地 区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_channelId" type="checkbox" name="payment">
                                                </label> 渠道
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="channelId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="channelIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_auditName" type="checkbox" name="payment">
                                                </label> 稽核厅名称
                                            </label>
                                            <div class="col-md-7">
                                                <input id="auditName" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_date" type="checkbox" name="payment">
                                                </label>起始日期</label>
                                            <div class="col-md-7">
                                                <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">结束日期</label>
                                            <div class="col-md-7">
                                                <input name="endDate" id="endDate" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <div class="col-md-12 searchbutt_r" align="center">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <!--<a class="btn btn-gray btn-outline" id="out_excel_pc">-->
                                                <button type="button" id="out_excel" class="btn btn-primary" >导出</button>
                                                <!--</a>-->
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div class="form-group col-md-4">
                                <p style="color: red" id="countSize"></p>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <!--<div><i class="bot"></i>查询结果</div>-->
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12">
                                    </div>
                                    <div class="col-lg-12 mart10" id="auditAdjustInfoListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>稽核厅名称</th>
                                                <th>扣费时间</th>
                                                <th>受理工号</th>
                                                <th>受理人员</th>
                                                <th>业务类型</th>
                                                <th>订单编号</th>
                                                <th>订单来源</th>
                                                <th>扣费金额</th>
                                            </tr>
                                            </thead>
                                            <tbody id="auditAdjustInfoListTable">
                                            #if($options != "null" && $options.auditAdjustInfoList && $options.auditAdjustInfoList != "null" &&
                                            $options.auditAdjustInfoList.size() > 0)
                                            #foreach($auditAdjust in $options.auditAdjustInfoList)
                                            <tr>
                                                <td>$!{auditAdjust.name}</td>
                                                <td>$!{auditAdjust.updateDate}</td>
                                                <td>$!{auditAdjust.staffNumber}</td>
                                                <td>$!{auditAdjust.staffName}</td>
                                                <td>$!{auditAdjust.actionName}</td>
                                                <td>$!{auditAdjust.olNbr}</td>
                                                <td>$!{auditAdjust.olType}</td>
                                                <td>$!{auditAdjust.amount}</td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.auditAdjustInfoList && $options.auditAdjustInfoList != "null" &&
                                            $options.auditAdjustInfoList.size() == 0)
                                            <tr>
                                                <td align='center' colspan='8'>未查到相关数据</td>
                                            </tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber,"exportNum":$options.exportNum}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
