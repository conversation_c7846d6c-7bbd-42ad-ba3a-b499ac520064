<div data-widget="chargeRuleFormula">
    <div class="box-maincont">
        <div class="page_main notopnav">
            <div class="col-lg-12">
                <div class="box-item">
                    <div class="container-fluid row">
                        <div class="form_title">
                            <div><i class="bot"></i>查询条件</div>
                        </div>
                        <div class="wmin_content row">
                            <form class=" form-bordered">
                                <div class="form-group col-md-4">
                                <label class="col-md-4 control-label lablep">表达式公式</label>
                                <div class="col-md-7">
                                    <input type="text" class="form-control" value=""
                                           id="expression">
                                </div>
                                </div>
                                <div class="form-group col-md-4">
                                    <label class="col-md-4 control-label lablep">费用类型</label>
                                    <div class="col-md-7">
                                        <input type="text" class="form-control" value=""
                                               id="acctItemTypeId">
                                    </div>
                                </div>
                                <div class="form-group col-md-4">
                                    <label class="col-md-4 control-label lablep">业务算费Id</label>
                                    <div class="col-md-7">
                                        <input type="text" class="form-control" value=""
                                               id="busiChargeId">
                                    </div>
                                </div>
                                <div class="form-group col-md-4">
                                    <label class="col-md-4 control-label lablep">条件表达式</label>
                                    <div class="col-md-7">
                                        <input type="text" class="form-control" value=""
                                               id="condExpr">
                                    </div>
                                </div>
                                <div class="form-group col-md-4">
                                    <label class="col-md-4 control-label lablep">状态</label>
                                    <div class="col-md-4">
                                        <select id="statusCd" class="form-control">
                                            <option value="1000">有效</option>
                                            <option value="1100">失效</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group col-md-4 ">
                                    <label class="col-md-2 control-label lablep"></label>
                                    <div class="col-md-9 text-right">
                                        <button type="button" class="btn btn-white" id="resetBtn">清除</button>
                                        <button type="button" class="btn btn-primary" id="queryBtn">查询</button>
                                        <button type="button" class="btn btn-primary" id="addBtn">添加</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="container-fluid row">
                        <div class="form_title">
                            <div><i class="bot"></i>查询结果</div>
                        </div>
                        <div class="wmin_content" id="loadData">
                            <div class="col-lg-12">
                                <a class="btn btn-gray btn-outline" id="editBtn">
                                    <i class="glyphicon icon-progress text18"> </i> 编辑
                                </a>
<!--                                <a style="pointer-events: none" class="btn btn-gray btn-outline"  id="deleteBtn">-->
<!--                                    <i class="glyphicon icon-batch text18" > </i> 删除-->
<!--                                </a>-->
                            </div>
                            <div class="col-lg-12 mart10" name="tableDiv">
                                <table class="table table-hover">
                                    <thead>
                                    <tr>
                                        <th>选择</th>
                                        <th>规则ID</th>
                                        <th>表达式</th>
                                        <th>费用类型</th>
                                        <th>备注</th>
                                        <th>付费方式</th>
                                        <th>条件表达式</th>
                                        <th>业务算费Id</th>
                                        <th>状态</th>
                                        <th>余额类型标识</th>
<!--                                        <th>操作</th>-->
                                    </tr>
                                    </thead>
                                    <tbody>
                                    #if($options.reqList!="null" && $options.reqList.size()>0)
                                    #foreach($fc in $options.reqList)
                                    <tr>
                                        <td>
                                            <label class="wu-radio full absolute" data-scope="">
                                                <input type="radio" name="payment">
                                                <p class="vita-data">{"Id":"$!fc.formulaId"}</p>
                                            </label>
                                        </td>
                                        <td>$!fc.formulaId</td>
                                        <td>$!fc.expression</td>
                                        <td>$!fc.acctItemTypeId</td>
                                        <td>$!fc.remakrs</td>
                                        <td>$!fc.payMethodCd</td>
                                        <td>$!fc.condExpr</td>
                                        <td>$!fc.busiChargeId</td>
                                        <td>$!fc.statusCd</td>
                                        <td>$!fc.balanceTypeId</td>
<!--                                        <td><button type="button" class="btn btn-primary" value= $!fc.id id="checkZkValue">查看</button></td>-->
                                    </tr>
                                    #end
                                    #end
                                    </tbody>
                                </table>
                                #if($options.totalNumber)
                                <p class="vita-data" id = "_totalNumber">{"totalNumber":$options.totalNumber}</p>
                                #end
                            </div>
                            <div id="pageInfo">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--弹窗 start-->
    <div class="popup" style="display:none;">
        <div class="popup-mask active"></div>
        <div class="agile-popup right active" style="width: 75%">
            <div class="popup-container">
                <!--内容-->
                <div class="calctitle">
                    <div class="titlefont">添加</div>
                    <div class="toolr">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                    </div>
                </div>

                <div class="wmin_content row" style="margin-top: 10px">
                    <form class=" form-bordered" name="_popupForm">

                        <div class="hideAndShow form-group col-md-6" >
                            <label class="col-md-4 control-label lablep">规则ID</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_formulaId" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">表达式</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_expression"   required>
                            </div>
                        </div>

                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">费用类型Id</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_acctItemTypeId" placeholder="必填"  required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">备注说明</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_remakrs"  required>
                            </div>
                        </div>

                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">付费方式</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_payMethodCd" placeholder="选填-数字" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">条件表达式</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_condExpr"  required>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">业务算费Id</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_busiChargeId" placeholder="必填"   required>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label class="col-md-4 control-label lablep">状态</label>
                            <div class="col-md-8">
                                <select id="_statusCd" class="form-control">
                                    <option value="1000">有效</option>
                                    <option value="1100">失效</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group col-md-6 ">
                            <label class="col-md-4 control-label lablep">余额类型标识</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" value=""
                                       id="_balanceTypeId"   required>
                            </div>
                        </div>
                        <div class="form-group col-md-12">
                            <div class="col-md-12 text-center">
                                <button type="button" class="btn btn-primary" id="addSubmitBtn">添加</button>
                                <button type="button" class="btn btn-primary" id="editSubmitBtn">修改</button>
                                <button type="button" class="btn btn-primary" id="closeBtn">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!--end-->


</div>
</div>
</div>
<style>
    .modal{z-index: 9910;}
    .agile-popup{z-index: 9000;}
</style>
