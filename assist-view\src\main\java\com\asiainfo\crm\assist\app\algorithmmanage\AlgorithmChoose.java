package com.asiainfo.crm.assist.app.algorithmmanage;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * @Date 2022/1/20 16:06
 *
 */
@Component("vita.algorithmChoose")
public class AlgorithmChoose extends AbstractComponent {

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map<String, Object> data = new HashMap<String, Object>();
        data.put("dataSecurityStatusCds", AssistMDA.DATA_SECURITY_STATUS_CDS);
        return data;
    }

    public Map<String, Object> queryAlgorithms(String params) throws Exception {
        Map<String, Object> invokeResult = (Map<String, Object>) invokeMethod("algorithmList", "queryAlgorithms", new Object[]{params});
        return invokeResult;
    }
}
