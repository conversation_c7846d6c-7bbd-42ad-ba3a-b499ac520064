package com.asiainfo.crm.assist.app.query;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICommQuerySMO;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.IOrderSaveSmo;
import com.google.common.collect.Maps;

/**
 * Created on 2017/7/7.
 */
@Component("vita.backOrderListQuery")
public class BackOrderListQuery extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(BackOrderListQuery.class);

    @Autowired
    private IOrderQuerySMO orderQuerySMO;

    @Autowired
    private ICommQuerySMO commQuerySMO;
    
    @Autowired
    private IOrderSaveSmo orderSaveSmo;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    /**
     * 查询订单列表
     *
     * @param json
     * @return
     * @throws IOException
     */
    public Map queryCustomOrderList(String json) throws Exception {
        String customerOrderItemList = orderQuerySMO.qryBackOrderItemListbyCond(json);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(customerOrderItemList);
        Map<String, Object> options = Maps.newHashMap();
        List backOrders = (List) MapUtils.getObject(resultObjectMap, "backOrders");
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", total);
        options.put("backOrders", backOrders);
        return options;
    }

    public Map backOrderCommit(String json)throws Exception {
    	String retJson = orderSaveSmo.saveBackOrder(json);
    	String retCode = (String) resolveResult(retJson);
        Map ret = new HashMap();
        ret.put("retCode",retCode);
        return ret;
    }
}
