<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.asiainfo.crm</groupId>
        <artifactId>crmstan-web-parent</artifactId>
        <version>1.1.0-prod-SNAPSHOT</version>
    </parent>
    <artifactId>assist-parent</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>assist-parent</name>
    <organization>
        <name>Asiainfo</name>
        <url>http://www.asiainfo.com.cn</url>
    </organization>

    <properties>
        <project.module.version>1.0.0</project.module.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <jdk.version>1.7</jdk.version>
    </properties>

    <dependencies>
        <!--  架构部相关包  start 
        <dependency>
            <groupId>com.asiainfo.crm</groupId>
            <artifactId>al-bcomm</artifactId>
            <version>0.1.6-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.asiainfo.angel</groupId>
            <artifactId>angel-invoke</artifactId>
            <version>2.0.2-SNAPSHOT</version>
        </dependency>
         -->
        <!--  架构部相关包  end  -->

        
        <!-- spring  start  -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>4.1.4.RELEASE</version>
        </dependency>
        <!-- spring  end  -->

        <!--  日志输出  start 
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.6.4</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.0.0</version>
        </dependency>
         -->
        <!--  日志输出  end  -->

        <!-- servlet start -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <version>2.5</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.2.2</version>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.11.0</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.5</version>
        </dependency>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.0</version>
        </dependency>
        <!-- servlet end -->

        <!--  velocity相关包  start  -->
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity</artifactId>
            <version>1.7</version>
        </dependency>

        <!--  velocity相关包相关包  end  -->

        <!-- 单元测试相关包  start  -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.9</version>
        </dependency>
        <!-- 单元测试相关包  end  -->

        <!--  集中会话引入系管包  begin  -->
        <dependency>
            <groupId>com.asiainfo.crm</groupId>
            <artifactId>sm-auth-busi</artifactId>
            <version>0.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.asiainfo.angel</groupId>
                    <artifactId>angel-persistence</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--  集中会话引入系管包  end  -->

        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>2.4.4</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!-- compiler插件, 设定JDK版本 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>${jdk.version}</source>
                    <target>${jdk.version}</target>
                    <showWarnings>true</showWarnings>
                </configuration>
            </plugin>

            <!-- test插件, 增大内存且仅测试名称为*Test的类 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.12</version>
                <configuration>
                    <includes>
                        <include>**/*Test.java</include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>2.12</version>
                <executions>
                    <execution>
                        <id>integration-test</id>
                        <goals>
                            <goal>integration-test</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>verify</id>
                        <goals>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- cobertura插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.5.1</version>
            </plugin>

            <!-- war插件, 设定war名称不带版本号 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.2</version>
                <configuration>
                    <warName>${project.artifactId}</warName>
                </configuration>
            </plugin>

            <!-- resource插件, 设定编码 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.5</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>

            <!-- jar相关插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <archive>
                        <addMavenDescriptor>false</addMavenDescriptor>
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                            <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1.2</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- eclipse插件,设定下载Source并屏蔽.svn文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-eclipse-plugin</artifactId>
                <version>2.8</version>
                <configuration>
                    <sourceExcludes>
                        <sourceExclude>**/.svn/</sourceExclude>
                    </sourceExcludes>
                    <downloadSources>true</downloadSources>
                    <downloadJavadocs>false</downloadJavadocs>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.1</version>
            </plugin>

            <!-- assembly插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.3</version>
            </plugin>
        </plugins>
    </build>
   
    <!-- 设置工程与仓库的关系,快照与正式版本分开 -->
    <distributionManagement>
    	<!--
        <repository>
            <id>AL-Release</id>
            <url>http://192.168.1.22:8081/nexus/content/repositories/AL-Release</url>
        </repository>
        <snapshotRepository>
            <id>AL-Snapshot</id>
            <url>http://192.168.1.22:8081/nexus/content/repositories/AL-Snapshot</url>
        </snapshotRepository>
        -->
    </distributionManagement>
    
</project>