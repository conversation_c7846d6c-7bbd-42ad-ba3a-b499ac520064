package com.asiainfo.crm.assist.app.channel;

import com.asiainfo.crm.common.AbstractSoComponent;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 渠道列表
 *
 * <AUTHOR>
 */
@Component("vita.chooseChannel")
public class ChooseChannel extends AbstractSoComponent {

    @Override
    public Map achieveData(Object... param) throws Exception {
        String inStr = param[0].toString();
        Map<String, Object> inMap = jsonConverter.toBean(inStr, Map.class);
        Map options = new HashMap();
        List channelList = null;
        Object channels = inMap.get("channels");
        Object curChannelId = inMap.get("channelId");
        String hasCurChannel = "Y";
        if(null == curChannelId || "".equals(curChannelId.toString())){
            hasCurChannel = "N";
        }

        if(null != channels && ((List)channels).size() > 0){
            channelList = (List)channels;
            options.put("channelList", channelList);
            options.put("totalNumber", channelList.size());
        }else{
            channelList = new ArrayList();
            options.put("channelList", channelList);
            options.put("totalNumber", channelList.size());
        }
        options.put("hasCurChannel", hasCurChannel);
        options.put("hasOrder", inMap.get("hasOrder"));
        return options;
    }

    /**
     * 渠道列表查询
     *
     * @param param,size
     * @return List
     * @throws Exception
     */
    public Map queryChannelList(String param, String from, String size) throws Exception {
        Map<String, Object> retMap = new HashMap<>();
        ArrayList channelList = jsonConverter.toBean(param,ArrayList.class);
        int totalCount = channelList.size();
        int startPos = Integer.valueOf(from);
        int step = Integer.valueOf(size);
        ArrayList channels = new ArrayList() ;
        if(step > totalCount){
            step = totalCount;
        }
        for (int i = startPos - 1; i < step; i++) {
            channels.add(channelList.get(i)) ;
        }
        retMap.put("totalNumber", totalCount);
        retMap.put("channelList", channels);
        return retMap;
    }

}