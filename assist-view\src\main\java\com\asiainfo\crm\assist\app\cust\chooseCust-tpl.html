<div data-widget="chooseCust" class="popup-container" style="height:100%">
	<p class="vita-data">{"data":$options}</p>
	#macro(nullNotShow $val)
    	#if($val && $val != "null")
            $!val
        #end
    #end
		<div class="calcw_leftbox">
			<div class="calc-sidetitle">
				<i class="icon-search glyphicon text18 mar15"></i>客户查询
			</div>
			<div class="sidetcont">
				<div class="btn-group bootstrap-select form-control">
					<input type="text" class="form-control" placeholder="查询内容">
					<p class="vita-bind" model="queryValue"></p>
				</div>
				<div class="btn-group bootstrap-select form-control" name="dropdownDiv" >
					<button type="button" class="btn dropdown-toggle selectpicker btn-default" >
						<span class="filter-option pull-left">
							#if($options.dataRanges)
	                        	$options.dataRanges[0].dataDimensionName
	                        #end
						</span>&nbsp;
						<span class="caret"></span>
					</button>
                    <ul class="dropdown-menu" role="menu">
						#if($options.dataRanges && $options.dataRanges != "null" && $options.dataRanges.size() > 0)
							#foreach($dataRange in $options.dataRanges)
                              <li>
                                  <a href="javascript:void(0);">
                                      $dataRange.dataDimensionName
                                  </a>
                              </li>
                              <p class="vita-data">{"dataRange":$dataRange}</p>
                            #end
						#end
					</ul>
				</div>
			</div>
			<div class="col-lg-12">
				<button type="button" class="btn btn-block btn-white" id="custSearchBtn">立即查询</button>
			</div>
			<div class="col-lg-12">
				<div class="scanbox">
					<i class="icon-scan glyphicon"></i>
				</div>
				<p class="scanfont">
					扫描身份证
				</p>
			</div>
		</div>
		<div class="calcw_rightbox">
			<div class="calctitle">
				<div class="titlefont">
					<i class="fa-lightn glyphicon textcolorgreen text18 mar15"></i>查询结果
				</div>
				<div class="toolr">
					<button id="submitCustBtn" type="button" class="btn btn-primary btn-sm okbutt">确认</button>
					<button id="closeCustBtn" type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
				</div>
			</div>
			<div id="searchList" class="calcw_rightcont" style="display: none">
				<div id="resultList" class="account_box paddlrb15">
					#if($options != "null" && $options.customers && $options.customers != "null" && $options.customers.size() > 0)
						#foreach($customer in $options.customers)
						<div class="media bordersolid">
							<div class="media-left relative">
								<label class="wu-radio full absolute" data-scope="">
									<input type="radio" name="cust">
								</label>
							</div>
							<div class="media-body">
								<div class="col-sm-3">
									<span class="text16 textcolorgreen">$!customer.custNameValue</span>
								</div>
								<div class="col-sm-3">
									<span class="textcolor9">默认证件类型：</span><span>$!customer.certTypeName</span>
								</div>
								<div class="col-sm-3">
									<span class="textcolor9">证件号码：</span><span>$!customer.certNumValue</span>
								</div>
								<div class="col-sm-3">
									<span class="textcolor9">客户地区：</span><span>$!customer.regionName</span>
								</div>
							</div>
						</div>
						<p class="vita-data">{"customer":$customer,"totalNumber":$options.totalNumber}</p>
						#end
					#end
				</div>
				<!--翻页start -->
				<div class="page-box" id="showPageInfo"></div>
				<!--翻页end -->
			</div>
		</div>
	
</div>
