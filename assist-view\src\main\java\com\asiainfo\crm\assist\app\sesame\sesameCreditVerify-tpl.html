<div data-widget="sesameCreditVerify" class="pace-done wrapper mini-rightmax" style="height: 100%">

    <div class="pace-done wrapper mini-rightmax no-minright"><!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="page-nav">
                <div class="row">
                    <div class="pagenav_box">
                        <div class="titlefont paddt8">
                            <div class="wmin_content row">
                                <form class=" form-bordered">
                                    <div class="form-group col-md-12">
                                        <label class="col-md-12" align="center">
                                            2、征信结果校验
                                        </label>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="page_main">
                <div class="container-fluid row">
                    <div class="wmin_content row">
                        <form class=" form-bordered">
                            <div class="form-group col-md-6">
                                <label class="col-md-5 control-label lablep">
                                    芝麻征信数字工单ID：</label>
                                <div class="col-md-7">
                                    <input type="text" id="digitalOrderId" class="form-control" placeholder="">
                                </div>
                            </div>
                            <div class="form-group col-md-2">
                                <div class="col-md-2 text-left">
                                    <button type="button" id="btn-verify" class="btn btn-primary">校&nbsp;&nbsp;验</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="container-fluid row">
                    <div class="form_title">
                        <!--<div><i class="bot"></i>查询结果</div>-->
                    </div>
                    <div class="wmin_content" id="sesameCreditVerifyResult">
                        <div class="col-lg-12 mart10" >
                            <table class="table table-hover">
                                <thead>
                                <tr>
                                    <th>芝麻征信数字工单ID</th>
                                    <th>支付宝USER_ID</th>
                                    <th>支付宝客户姓名</th>
                                    <th>客户身份证号码</th>
                                    <th>手机号码</th>
                                    <th>芝麻分</th>
                                    <th>校验结果</th>
                                </tr>
                                </thead>
                                <tbody>
                                <div id="sesameVerifyResult">
                                </div>
                                <p class="vita-data">{"result":"$options.handleResultCode"}</p>

                                #if($options != "null" && $options.handleResultCode == "0" && $options.sesameInfos && $options.sesameInfos != "null" &&
                                $options.sesameInfos.size() > 0)
                                #foreach($sesameInfo in $options.sesameInfos)
                                <tr>
                                    <td>$!{sesameInfo.transactionId}</td>
                                    <td>$!{sesameInfo.alipayUserId}</td>
                                    <td>$!{sesameInfo.custName}</td>
                                    <td>$!{sesameInfo.certNo}</td>
                                    <td>$!{sesameInfo.mobile}</td>
                                    <td>$!{sesameInfo.zmScore}</td>
                                    <td>$!{options.handleResultMsg}</td>
                                </tr>
                                #end
                                #elseif($options != "null"  && $options.handleResultCode == "0" && $options.sesameInfos && $options.sesameInfos != "null" &&
                                $options.sesameInfos.size() == 0)
                                <tr><td align='center' colspan='7'>未查询到数据！<td></tr>
                                #elseif($options != "null" && $options.handleResultCode != "0")
                                <tr><td align='center' colspan='7'>$!{options.handleResultMsg}<td></tr>
                                #end
                                </tbody>
                            </table>
                        </div>
                        <!--翻页start -->
                        <div id="showPageInfo">
                        </div>
                        <!--翻页end -->
                    </div>
                </div>

            </div>

            <div class="page_food">
                <div class="floatr">
                    <a class="nextbutt">&nbsp;&nbsp;&nbsp;下&nbsp;一&nbsp;步&nbsp;&nbsp;&nbsp;</a>
                </div>
            </div>
        </div>
    </div>
</div>


