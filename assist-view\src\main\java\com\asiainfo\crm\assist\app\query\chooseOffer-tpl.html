<div data-widget="chooseOffer" style="height:100%">
<p class="vita-data">{"data":$options}</p>
	<div class="calcw_leftbox">
    <div class="calc-sidetitle">
        <i class="fa-sift glyphicon text18 mar15"></i>筛选条件
    </div>
    <div class="sidetcont">
        <div class="btn-group bootstrap-select form-control ">
            <input type="text" class="form-control" placeholder="销售品名称">
            <p class="vita-bind" model="offerName"></p>
        </div>
    </div>
    <div class="col-lg-12">
        <button type="button" class="btn btn-block  btn-white" id="searchBtn">立即查询</button>
    </div>
    <div class="col-lg-12">
        <p class="whitefont" id="searchDel">清除条件</p>
    </div>

</div>
<div class="calcw_rightbox">
    <div class="calctitle">
        <div class="titlefont">销售品列表</div>
        <div class="toolr">
            <button type="button" class="btn btn-primary btn-sm okbutt" id="submit">确认</button>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
        </div>
    </div>
    <div class="calcw_rightcont">
     <div class="col-lg-12 mart10" id="itemTable">
            <table class="table table-hover" id="items">
                <thead>
                <tr>
                    <th>选择</th>
                    <th>销售品标识</th>
                    <th>销售品名称</th>
                </tr>
                </tr>
                </thead>
                <tbody >
               	#if($options != "null" && $options.items && $options.items != "null" && $options.items.size() > 0)
				
					#foreach($item in $options.items)
	                <tr >
	                    <td><label class="wu-checkbox full absolute" data-scope="">
	                    <input type="checkbox" name="selectRadio" />
	                    <p class="vita-data">{"data":$item,"totalNumber":$options.totalNumber}</p>
	                    </label></td>
	                    <td>$!item.offerId</td>
	                    <td>$!item.offerName</td>
	                </tr>
                	#end
                #end
                </tbody>
            </table>
            <!--翻页start -->
            <div class="page-box" id="showPageInfo">
            </div>
         <p class="vita-data">{"totalNumber":"$!options.totalNumber"}</p>
            <!--翻页end -->
        </div>
    </div>
</div>
	
</div>

