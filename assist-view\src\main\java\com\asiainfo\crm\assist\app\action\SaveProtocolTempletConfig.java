package com.asiainfo.crm.assist.app.action;

import java.util.HashMap;
import java.util.Map;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ai.vita.AbstractComponent;
import com.asiainfo.crm.bcomm.serv.Result;
import com.asiainfo.crm.service.intf.IReceiptQuerySMO;

/** 
* <AUTHOR> 
* @version 1.0 
* @return  */
@Component("vita.saveProtocolTempletConfig")
public class SaveProtocolTempletConfig extends AbstractComponent{
	
	@Autowired
    private IReceiptQuerySMO receiptQuerySMO;
	
	@Override
	public Map achieveData(Object... params) throws Exception {
        Map<String, Object> options = new HashMap<String, Object>();
        String jsonString = params[0].toString();
        JSONObject jsonObj = JSONObject.fromObject(jsonString);
        String protocolTempletConfigId = jsonObj.getString("protocolTempletConfigId");
        if(StringUtils.isNotBlank(protocolTempletConfigId) && !"null".equals(protocolTempletConfigId)){
        	String retJson = receiptQuerySMO.qryProtocolTempletConfig(jsonString);
            JSONObject root = JSONObject.fromObject(retJson);
            JSONObject resultObject = root.getJSONObject("resultObject");
            if(resultObject != null && resultObject.get("rows") != null){
            	JSONArray rows = resultObject.getJSONArray("rows");
            	options.put("protocolTempletConfig", rows.getJSONObject(0));
            }
        }
        
		return options;
	}
 
	public String qryOneProtocolTempletConfig(String jsonString) throws Exception{
		return receiptQuerySMO.qryProtocolTempletConfig(jsonString);
	}
	public String save(String jsonString) throws Exception {
		JSONObject root = JSONObject.fromObject(jsonString);
		JSONObject inParams = new JSONObject();
		Long id = root.getLong("protocolTempletConfigId");
		//新增时，要判断此协议模板的配置是否已存在
		if(id == null || id < 0){
			String serviceOfferId = root.getString("serviceOfferId");
			String objId = root.getString("objId");
			Long protocolTempletId = root.getLong("protocolTempletId");
			inParams.put("serviceOfferId", serviceOfferId);
			inParams.put("objId", objId);
			inParams.put("protocolTempletId", protocolTempletId);
			String retString = receiptQuerySMO.qryProtocolTempletConfig(inParams.toString());
	        JSONObject retJson = JSONObject.fromObject(retString);
	        JSONObject resultObject = retJson.getJSONObject("resultObject");
	        if(resultObject != null && resultObject.get("rows") != null){
	        	JSONArray rows = resultObject.getJSONArray("rows");
	        	if(!rows.isEmpty()){
	        		StringBuffer sb = new StringBuffer();
	        		sb.append("此协议模板已配置了销售品：")
	        		.append(root.getString("objName"))
	        		.append("及业务动作：")
	        		.append(root.getString("serviceOfferName"))
	        		.append("的控制，请勿重复配置！");
	        		Result result = new Result("ORDER_10000", sb.toString(), sb);
	        		return JSONObject.fromObject(result).toString();
	        	}
	        }
		}
		
        return receiptQuerySMO.saveProtocolTempletConfig(jsonString);
	}
}
