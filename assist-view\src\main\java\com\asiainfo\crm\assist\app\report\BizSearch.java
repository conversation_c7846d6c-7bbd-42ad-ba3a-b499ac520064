package com.asiainfo.crm.assist.app.report;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IReportSMO;
import com.asiainfo.crm.service.intf.IStaffDataPrvQuerySMO;
import org.apache.commons.collections.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component("vita.bizSearch")
public class BizSearch extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(BizSearch.class);

    @Autowired
    private IReportSMO reportSMO;
    @Autowired
    private IStaffDataPrvQuerySMO staffDataPrvQuerySMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    /**
     * 查询清单列表
     * @param json
     * @return
     * @throws Exception
     */
    public Map queryDealList(String json) throws Exception {
        Map options = new HashedMap();
        //调用接口查询清单
        Map<String,Object> resultObjectMap = reportSMO.queryDealList(json);
        List dealLists = (List) resultObjectMap.get("dealLists");
        //计算总计
        double totalAppCharge = 0D;//应收总额
        double totalRealCharge = 0D;//实收总额
        int countNum =  dealLists.size();//总条数
        for (int i=0; i<dealLists.size(); i++){
            Map msg = (Map) dealLists.get(i);
            double  realAmount = (double) msg.get("realCharge");
            double  appAmount = (double) msg.get("appCharge");
            totalAppCharge+=appAmount;
            totalRealCharge+=realAmount;
        }
        options=resultObjectMap;
        options.put("totalAppCharge",totalAppCharge);
        options.put("totalRealCharge",totalRealCharge);
        options.put("exportNum", MDA.EXPORT_NUM);
        options.put("totalNumber", countNum);

        return options;
    }
    //根据渠道ID查询渠道下的员工测试数据
    public Map<String,Object> queryStaffByChannel(String jsonString) throws Exception{
        Map result = reportSMO.queryStaffInfoByChannelId(jsonString);
        //测试数据
      /*  Map<String, Object> retMap = Maps.newHashMap();
        List<StaffTest> list=new ArrayList<>();
        StaffTest test=new StaffTest();
        test.setStaffId(331000000952L);
        test.setStaffName("赵云");
        list.add(test);
        StaffTest test1=new StaffTest();
        test1.setStaffId(386022224604L);
        test1.setStaffName("zhanght");
        list.add(test1);
        retMap.put("channelList", list);*/
        return result;
    }
    /**
     * 查询按钮权限
     * @param json
     * @return
     */
    public Map checkSysUserPriv(String json) throws IOException {
        Map resMap = new HashMap();
        Map jsonMap =jsonConverter.toBean(json, Map.class);
        Map reqMap = new HashMap();
        reqMap.put("sysUserId",jsonMap.get("sysUserId"));
        reqMap.put("privCode", AssistMDA.BIZ_SEARCH_MODIFY_CODE);
        String  reqJson = jsonConverter.toJson(reqMap);
        //获取员工按钮权限
        String resultString  =  staffDataPrvQuerySMO.checkSysUserPriv(reqJson);
        boolean isHave = true;
        Map<String, Object> resultMap = jsonConverter.toBean(resultString, Map.class);
        Map<String, Object> resultObject = (Map)resultMap.get("resultObject");
        isHave = (Boolean) resultObject.get("isHave");
        String resultCode = (String)resultMap.get("resultCode");
        resMap.put("isHave",isHave);
        resMap.put("resultCode",resultCode);
        return resMap;
    }
}
