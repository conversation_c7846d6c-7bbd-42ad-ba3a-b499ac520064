package com.asiainfo.crm.assist.app.query;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IResourceSMO;
import com.google.common.collect.Maps;


@Component("vita.commProductQuery29")
public class CommProductQuery29 extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(CommProductQuery29.class);

    @Autowired
    private IResourceSMO resourceSMO;
    
    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    /**
     * 查询产品信息
     *
     * @param json
     * @return
     * @throws IOException
     */
    public Map queryProdList(String json) throws Exception {
    	
        String retInfoStr = resourceSMO.commProductQuery29(json);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(retInfoStr);
        Map<String, Object> options = Maps.newHashMap();
        List prodInfos = (List) MapUtils.getObject(resultObjectMap, "commonProdList");
        if(null == prodInfos || prodInfos.size() < 1){
        	return options;
        }
        
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", total);
        options.put("prodInfos", prodInfos);
        return options;
    }

}
