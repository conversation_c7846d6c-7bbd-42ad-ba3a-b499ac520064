(function (vita) {
    var epayBonusSendOperate = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_epayBonusSendQuery",
            "click #regionIdBtn": "_chooseArea",
            "click #channelIdBtn": "_chooseChannel",
            "click #staffIdBtn": "_chooseStaff",
            "change #state": "_changeState",
            "click #c_allResult": "_checkAllResult",
            "click #btnSendAgain": "_reqBonusAgain",
            "click #btnSetStatus": "_resetBonusStatus"

        },
        _checkAllResult: function (e) {
            var widget = this,element = $(widget.el);
            var $target = e.target;
            var $allBounsInputs = element.find('table input[name="payment"]');
            $.each($allBounsInputs, function (i,$check) {
                if(!$target.checked && $check.checked){
                    $check.click();
                } else if($target.checked && !$check.checked) {
                    $check.click();
                }
            });
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
            var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd"
                });
            }
            ;
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd"
                });
            }
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
            var channelName = "";
            for(var channels=gSession.channels,i=0; channels.length && i<channels.length; i++) {
                if(gSession.curChannelId == channels[i].orgId) {
                    channelName = channels[i].channelName;
                    break;
                }
            }
            element.find("#channelId").val(channelName).attr("value", gSession.curChannelId);
            element.find("#staffId").val(gSession.staffName).attr("value", gSession.staffId);
        },
        global: {
            pageIndex: 1,
            pageSize: 10,
            preset: "date",
            chooseArea: "../comm/chooseArea",
            chooseChannel: "../comm/chooseChannel",
            chooseCust: "../comm/chooseCust"
        },
        _changeState : function() {
            var widget = this, element = $(widget.el);
            var status = $('#state').val();
            if(status==1){
                $('#btnSendAgain').hide();
                $('#btnSetStatus').hide();
            }else{
                $('#btnSendAgain').show();
                $('#btnSetStatus').show();
            }
            widget._epayBonusSendQuery();
        },
        _clearCond: function () {
            var $checked = $(".form-group").find("input[type=checkbox]:checked");
            $.each($checked, function (i,chkInput) {
                chkInput.click();
                var $input = $(chkInput).closest(".form-group").find("input.form-control");
                $input.val("");
                $input.attr("value", null);
            })
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _chooseChannel: function () {
            var widget = this,element = $(widget.el);
            var compCode = "chooseChannel";
            var dialogId = "chooseChannelDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseChannel,
                params : {
                },
                onClose : function(res) {
                    var comp = $(res).closest("[data-widget=" + compCode + "]");
                    var data = comp[compCode]("getValue");
                    if (!data) {
                        return false;
                    }
                    element.find("#channelId").val(data.channelName).attr("value", data.orgId);
                }
            });
        },
        _reqBonusAgain: function() {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var params = {};
            var channelId = $("#channelId").attr("value");
            if(widget._isNullStr(channelId)) {
                widget.popup("请选择渠道！");
                return false;
            } else {
                params.channelId = channelId;
            }
            var $selectChks = element.find('table input[name="payment"]:checked');
            if($selectChks.length == 0) {
                widget.popup("请选择状态为【失败】的翼支付赠送红包数据！");
                return false;
            }
            var bonusList = [];
            $.each($selectChks, function(i, $selectChk) {
                var custOrderId = $($selectChk).data("custOrderId");
                var offerId = $($selectChk).data("offerId");
                var accNum = $($selectChk).data("accNum");
                var seq = $($selectChk).data("seq");
                var status =  $($selectChk).data("status");
                var bonusObject={};
                if(status == 'D'){
                    bonusObject.custOrderId = custOrderId;
                    bonusObject.offerId = offerId;
                    bonusObject.accNum = accNum;
                    bonusObject.seq = seq;
                    bonusObject.staffId = gSession.staffId;
                    bonusObject.channelId = channelId;
                    bonusList.push(bonusObject);
                }
            });
            if(bonusList.length==0){
                widget.popup("请选择状态为【失败】的翼支付赠送红包数据！");
                return false;
            }
            params.bonusList = bonusList;
            widget.callService("reqBonusAgain", params, function (ret) {
                if (ret.handleResultCode == "0") {
                    widget.popup(ret.handleResultMsg);
                } else {
                    widget.popup(ret.handleResultMsg);
                }
            }, { async : false });
            widget._epayBonusSendQuery();
        },
        _resetBonusStatus: function() {
            var widget = this,element = $(widget.el);
            var gSession = widget.gSession;
            var params = {};
            var channelId = $("#channelId").attr("value");
            if(widget._isNullStr(channelId)) {
                widget.popup("请选择渠道！");
                return false;
            } else {
                params.channelId = channelId;
            }
            var $selectChks = element.find('table input[name="payment"]:checked');
            if($selectChks.length == 0) {
                widget.popup("请选择初始状态为【撤销失败】的翼支付赠送红包数据！");
                return false;
            }
            var bonusList = [];
            $.each($selectChks, function(i, $selectChk) {
                var changeToStatus = $($selectChk).closest("tr").attr("changeToStatus");
                var custOrderId = $($selectChk).data("custOrderId");
                var offerId = $($selectChk).data("offerId");
                var accNum = $($selectChk).data("accNum");
                var seq = $($selectChk).data("seq");
                var status =  $($selectChk).data("status");
                var bonusObject={};
                if(status == 'RD' && typeof changeToStatus!=undefined && changeToStatus!=null && changeToStatus != 'RD'){
                    bonusObject.custOrderId = custOrderId;
                    bonusObject.offerId = offerId;
                    bonusObject.accNum = accNum;
                    bonusObject.seq = seq;
                    bonusObject.staffId = gSession.staffId;
                    bonusObject.channelId = channelId;
                    bonusObject.dealStatus = changeToStatus;
                    bonusList.push(bonusObject);
                }
            });
            if(bonusList.length==0){
                widget.popup("请选择初始状态为【撤销失败】,并修改其状态为【失败】或者【成功】的翼支付赠送红包数据做【重置状态】的操作！");
                return false;
            }
            widget.popup("重置对应数据的状态前，已确认去翼支付平台上查询相关号码对应充值是否充值到账或是否已撤销，已到账的状态请修改为【成功】；已撤销的请修改为【失败】，再做【重新充值】操作?"
                ,function() {
                        params.bonusList = bonusList;
                        widget.callService("resetBonusStatus", params, function (ret) {
                            if (ret.handleResultCode == "0") {
                                widget.popup(ret.handleResultMsg);
                            } else {
                                widget.popup(ret.handleResultMsg);
                            }
                        }, { async : false });
                        widget._epayBonusSendQuery();
                    }
                ,function() { }
            );
        },
        _changeBonusStatus: function(e) {
            $(e.target.closest("tr")).attr("changeToStatus", $(e.target).val())
        },
        _epayBonusSendQuery: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("queryBestpayBonusData", JSON.stringify(params), "#epayBonusResult", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    var $statusSelect = r.find(".changeBonusStatus");
                    $.each($statusSelect, function(i, $select){
                        $($select).change(widget._changeBonusStatus);
                    });
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if (widget._getConds()) {
                                    widget.refreshPart("queryBestpayBonusData", JSON.stringify(params), "#epayBonusList");
                                }
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }
                }, {
                    async: false
                });
            }
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this;
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            var paramCnt = 0;
            var regionId = $("#regionId").attr("value");
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.regionId = regionId;
            }

            if ($("#c_channelId").is(":checked")) {
                paramCnt++;
                var channelId = $("#channelId").attr("value");
                if(widget._isNullStr(channelId)) {
                    widget.popup("请选择渠道！");
                    return false;
                } else {
                    params.channelId = channelId;
                }
            }

            if ($("#c_staffId").is(":checked")) {
                paramCnt++;
                var staffId = $("#staffId").attr("value");
                if(widget._isNullStr(staffId)) {
                    widget.popup("请选择营业员！");
                    return false;
                } else {
                    params.staffId = staffId;
                }
            }

            var accNum = $("#accNum").val();
            if(!widget._isNullStr(accNum)) {
                params.accNum = accNum;
            }

            var beginDate = $("#beginDate").val();
            if(!widget._isNullStr(beginDate)) {
                params.beginDate = beginDate;
            }

            var endDate = $("#endDate").val();
            if(!widget._isNullStr(endDate)) {
                params.endDate = endDate;
            }

            var state = $("#state").val();
            if (widget._isNullStr(state)) {
                widget.popup("请选择查询范围！");
                return false;
            } else {
                params.state = state;
            }
            return params;
        }
    });
    vita.widget.register("epayBonusSendOperate", epayBonusSendOperate, true);
})(window.vita);