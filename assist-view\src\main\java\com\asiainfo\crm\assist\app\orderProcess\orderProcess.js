(function(vita) {
    var orderProcess = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_echart",
            "click #btn-showInfo": "_showInfo",

        },
        global:{
            ShowParam: {}

        },
        _initialize: function () {
        },
        _hasArray: (arr) => {
            return arr!=null && arr.length > 0 ? true : false;
        },

        nodeSpec: {
            "OrderSaved-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/DDLK.png"},
            "OrderSaved-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/DDLK1.png"},
            "OrderSavedMsg-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/LKXX.png"},
            "OrderSavedMsg-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/LKXX1.png"},
            "ConstructAndTriger-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/XFLK.png"},
            "ConstructAndTriger-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/XFLK1.png"},
            "SendCustomer-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/SKH.png"},
            "SendCustomer-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/SKH1.png"},
            "CustomerCenterArchived-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/KHZXGD.png"},
            "CustomerCenterArchived-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/KHZXGD1.png"},
            "CustomerOrderItemCompleted-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/KHDDJG.png"},
            "CustomerOrderItemCompleted-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/KHDDJG1.png"},
            "CustomerSendArchivedGroup-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/KHGDZSend1.png"},
            "CustomerSendArchivedGroup-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/KHGDZSend.png"},
            "SendAccount-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/SZH.png"},
            "SendAccount-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/SZH1.png"},
            "AccountCenterArchived-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/ZHZXGD.png"},
            "AccountCenterArchived-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/ZHZXGD1.png"},
            "AccountOrderItemCompleted-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/ZHDDJG.png"},
            "AccountOrderItemCompleted-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/ZHZDJG1.png"},
            "AccountSendArchivedGroup-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/ZHGDZSend.png"},
            "AccountSendArchivedGroup-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/ZHGDZSend1.png"},
            "SendInst-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/SZC.png"},
            "SendInst-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/SZC1.png"},
            "InstCenterArchived-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/ZCZXGD.png"},
            "InstCenterArchived-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/ZCZXGD1.png"},
            "InstOrderItemCompleted-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/DDJG.png"},
            "InstOrderItemCompleted-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/DDJG1.png"},
            "InstArchivedGroupSend-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/GDZSend.png"},
            "InstArchivedGroupSend-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/GDZSend1.png"},
            "SendOss-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/SSG.png"},
            "SendOss-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/SSG1.png"},
            "Construction-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/FWXG.png"},
            "Construction-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/FWSG1.png"},
            "OssBack-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/FKHD.png"},
            "OssBack-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/FKHD1.png"},
            "OssBack-2": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/FKTD.png"},
            "OrderComplete-1": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/DDJGong.png"},
            "OrderComplete-0": {"symbol":"image:///crm_assist//bundles/vita/plugs/orderProcessImg/DDJGong1.png"}
        },

        _getData: function(res) {
            var x  =100
            var y =300
            let obj = {nodes:[], links:[]};
                if(this._hasArray(res)){
                    //存在订单环节数据
                    for(let node of res){
                        //处理node
                        //服开回单根据msgType区别展示
                        let flag;
                        if(node.nodeCode=="OssBack"&&(node.msgType=="1100"||node.msgType=="1300")){
                            flag = "-2";
                        }else{
                            flag = node.nodeStatus == "1" ? "-1" : "-0";
                        }
                        // let spec = this.nodeSpec[node.nodeCode + flag], inst = {...node, key:node.nodeInstId, name:node.nodeName};
                        let spec = this.nodeSpec[node.nodeCode + flag], inst = Object.assign({}, node, {key:node.nodeInstId, name:node.nodeName});
                        if(spec){
                            // inst = {...spec, ...inst};
                            inst = Object.assign({}, spec, inst);
                        }

                        inst.id = node.nodeInstId
                        obj.nodes.push(inst);
                        //处理link
                        if(this._hasArray(node.pNodeInsts)){
                            for(let pnode of node.pNodeInsts){
                                obj.links.push({"source":pnode.toString(), "target":node.nodeInstId.toString()});
                            }
                        }
                    }
                }

                return obj;
        },


        _getCoordinate: function (nodes) {
            let res= JSON.parse(JSON.stringify(nodes))
            let endX = 100;
            let oneStart = true;
            for (let j = 0; j < res.length; j++) {
                let minIndex = 0;
                let minValue = res[0].nodeInstId;
                for (let i = 0; i < res.length; i++) {
                    let next = res[i].nodeInstId;
                    if (minValue > next) {
                        minValue = next;
                        minIndex = i;
                    }
                }
                if (oneStart){
                    nodes[minIndex].x = 100;
                    nodes[minIndex].y = 200;
                    oneStart =false;
                }
                let repeatComut = [];
                for (let k = 0; k < res.length; k++) {
                   if (nodes[minIndex].nodeInstId == res[k].pNodeInstId){
                       repeatComut.push(k);
                   }
                }
                //设置为最大
                res[minIndex].nodeInstId = 999999;
                if (repeatComut.length>1){
                    let i = nodes[minIndex].y/repeatComut.length
                    let y = nodes[minIndex].y ;
                    for (let k = 0; k < repeatComut.length; k++) {
                        nodes[repeatComut[k]].x =  nodes[minIndex].x+100;
                        nodes[repeatComut[k]].y = y ;
                         y = y+i ;
                        //计算结束位置
                        if (nodes[minIndex].x>endX){
                            endX=nodes[minIndex].x
                        }

                    }
                }else if (repeatComut.length==1){
                    nodes[repeatComut[0]].x =  nodes[minIndex].x+100;
                    nodes[repeatComut[0]].y = nodes[minIndex].y;
                    //计算结束位置
                    if (nodes[repeatComut[0]].x>endX){
                        endX=nodes[repeatComut[0]].x
                    }
                }
            }
           //处理结束
            for (let v = 0; v < nodes.length; v++) {
                if (nodes[v].pNodeInstId == null){
                    endX = endX +100
                    nodes[v].x = endX;
                    nodes[v].y = 200;
                }
            }

        },

        _showInfo: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "showInfoOrderProcess";
            var url = './showInfoOrderProcess';
            widget.dialog({
                id : dialogId,
                url : url,
                left : "-200px",
                params : {
                    data:JSON.stringify(widget.global.ShowParam.data),
                },
            });
        },

        _echart: function (e) {

            var data;
            var links;
            var widget = this, element = $(widget.el);
            // 时间控件类型初始化
            var text = element.find("#text").val();
            var sourceType = element.find("#sourceType").val();
            var params = {
            };
            params.queryType = sourceType;
            params.queryValue = text;
            widget.callService("buildOrderNode", JSON.stringify(params), function (res) {
                if (res.messageList==null || res.messageList.length == 0 ){
                    widget.popup("查询不到该订单信息");
                    return
                }
                var obj=this._getData(res.messageList)
                this._getCoordinate(obj.nodes);
                data = obj.nodes
                links = obj.links
            }, {async: false});

            // 折线图
            // 基于准备好的dom，初始化echarts实例
            var line = echarts.init(document.getElementById('line'));
            // 指定图表的配置项和数据
            let option = {
                title: {
                },
                tooltip: {},
                symbolSize:[110,110],//图形大小
                animationDurationUpdate: 1500,
                animationEasingUpdate: 'quinticInOut',
                series: [
                    {
                        type: 'graph',
                        layout: 'none',
                        symbolSize: 85,
                        roam: true,
                        label: {
                            show: false
                        },
                        edgeSymbol: ['circle', 'arrow'],
                        edgeSymbolSize: [4, 10],
                        edgeLabel: {
                            fontSize: 20
                        },
                        data: data,
                        // links: [],
                        links: links,
                        lineStyle: {
                            opacity: 0.9,
                            width: 2,
                            curveness: 0
                        }
                    }
                ]
            };
            line.clear();
            line.off('click').on('click', function (param) {
                widget.global.ShowParam = param;
                document.getElementById("btn-showInfo").click()
            });
            line.setOption(option);
        },




    });



    vita.widget.register("orderProcess", orderProcess, true);
})(window.vita);
