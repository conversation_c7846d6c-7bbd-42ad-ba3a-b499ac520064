<div data-widget="buildOrderNode" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-14 mart10" id="orderListResult">
                                        #if($options != "null" && $options.messageList && $options.messageList != "null" &&
                                        $options.messageList.size() > 0)
                                        <table class="table table-hover">
                                            <h4 style="text-align: center">流程信息</h4>
                                            <thead>
                                            <tr>
                                                <th class="col-md-1">订单ID</th>
                                                <th class="col-md-1">消息表主键</th>
                                                <th class="col-md-1">消息流水</th>
                                                <th class="col-md-1">消息主题</th>
                                                <th class="col-md-1">节点归属中心</th>
                                                <th class="col-md-1">节点CODE</th>
                                                <th class="col-md-1">节点实例ID标识</th>
                                                <th class="col-md-1">节点名称</th>
                                                <th class="col-md-1">节点状态</th>
                                                <th class="col-md-1">状态说明</th>
                                                <th class="col-md-1">订单项ID列表</th>
                                                <th class="col-md-1">父级实例ID</th>
                                                <th class="col-md-1">父节点列表</th>
                                                <th class="col-md-1">组合判断</th>
                                            </tr>
                                            </thead>
                                            <tbody id="orderList">

                                            #foreach($messageList in $options.messageList)
                                            <tr>

                                                <td class="col-md-1">
                                                    $!{messageList.coId}
                                                </td>
                                                <td class="col-md-1">
                                                    $!{messageList.msgId}

                                                </td >
                                                <td class="col-md-1">

                                                    $!{messageList.msgNbr}
                                                </td>
                                                <td class="col-md-1">

                                                    $!{messageList.msgTopic}
                                                </td>
                                                <td class="col-md-1">
                                                    $!{messageList.nodeCenter}
                                                </td>
                                                <td class="col-md-1">
                                                    $!{messageList.nodeCode}


                                                </td>
                                                <td class="col-md-1">

                                                    $!{messageList.nodeInstId}

                                                </td>
                                                <td class="col-md-1">

                                                    $!{messageList.nodeName}
                                                </td>
                                                <td class="col-md-1">

                                                    $!{messageList.nodeStatus}
                                                </td>
                                                <td class="col-md-1">

                                                    $!{messageList.nodeStatusDec}
                                                </td>
                                                <td class="col-md-1">
                                                    #if( $messageList.orderItems!="null")
                                                        #foreach($orderItem in $messageList.orderItems)
                                                                $orderItem <br/>
                                                        #end

                                                    #end

                                                </td>
                                                <td class="col-md-1">
                                                    $!{messageList.pNodeInstId}

                                                </td>
                                                <td class="col-md-1">

                                                    $!{messageList.pNodeInsts}
                                                </td>
                                                <td class="col-md-1">
                                                    #if($messageList.relaMsgs!="null")
                                                    #foreach($relaMsg in $messageList.relaMsgs)
                                                                $relaMsg <br/>
                                                    #end

                                                    #end

                                                </td>
                                                #if($options == "null"  || $options.messageList == "null" ||
                                                $options.messageList.size() == 0)
                                                <td>
                                                    找不到该流程信息
                                                </td>
                                                #end
                                            </tr>
                                            #end
                                            </tbody>
                                        </table>
                                        #end
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
