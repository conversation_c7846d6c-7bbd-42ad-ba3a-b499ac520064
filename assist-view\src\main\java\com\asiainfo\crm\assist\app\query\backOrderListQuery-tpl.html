<div data-widget="backOrderListQuery" style="height:100%">
    <!--<div class="sypage-nav">-->
        <!--<div class="titlefont">-->
            <!--<ul id="myTab" class="nav nav-tabs">-->
                <!--<li class="active">-->
                    <!--<a href="#1" data-toggle="tab">受理单</a>-->
                <!--</li>-->
                <!--<li>-->
                    <!--<a href="#2" data-toggle="tab">销售单</a>-->
                <!--</li>-->
                <!--</li>-->
            <!--</ul>-->
        <!--</div>-->
    <!--</div>-->
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                地 区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="acceptRegionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_channelId" type="checkbox" name="payment">
                                                </label> 渠道
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="channelId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="channelIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custOrderNbr" type="checkbox" name="payment">
                                                </label> 购物车号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custOrderNbr" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custOrderId" type="checkbox" name="payment">
                                                </label> 购物车ID
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custOrderId" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        
                                        
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">起始日期</label>
                                            <div class="col-md-7">
                                                <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">结束日期</label>
                                            <div class="col-md-7">
                                                <input name="endDate" id="endDate" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">是否已缓存</label>
                                            <div class="col-md-7">
                                                <select id="ifDeal" class="form-control">
                                                    <option value="0" selected="selected">否</option>
                                                    <option value="1">是</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <div class="col-md-12 searchbutt_r" align="center">
                                                <button id="btn-query" type="button" class="btn btn-primary">查询</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <!--<div><i class="bot"></i>查询结果</div>-->
                                </div>
                                <div class="wmin_content">
                                    <div class="form-group col-md-12" id="delayResonDiv">
   
			                        <label class="col-md-2 control-label lablep">暂缓受理原因</label>
			                        <div class="col-md-6">
			                            <textarea class="form-control mart10" rows="3" id="reasonRemark"></textarea>
			                        </div>
		
									<div class="col-md-4" align="right">
			                            <a id="backOrderCommit" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 暂缓受理确认
			                            </a>
			                        </div>
			                        </div>
                                    <div class="col-lg-12 mart10" id="orderListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th><label class="wu-checkbox full absolute" data-scope="">
                                                    <input id="c_all" type="checkbox" >
                                                </label></th>
                                                <th>地区</th>
                                                <th>客户</th>
                                                <th>购物车号</th>
                                                <th>业务动作ID</th>
                                                <th>业务办理</th>
                                                <th>业务动作状态</th>
                                                <th>业务时间</th>
                                                <th>原因说明</th>
                                            </tr>
                                            </thead>
                                            <tbody id="orderList">
                                            #if($options != "null" && $options.backOrders && $options.backOrders != "null" &&
                                            $options.backOrders.size() > 0)
                                            #foreach($customerOrder in $options.backOrders)
                                            <tr>
                                                <td><label class="wu-checkbox full absolute" data-scope=""><input
                                                        type="checkbox" name="payment">
                                                        <p class="vita-data">{"data":$customerOrder}</p> 
                                                    </label></td>

                                                <td>$!{customerOrder.regionName}</td>
                           						<td>$!{customerOrder.custName}</td>
                                                <td>$!{customerOrder.custOrderNbr}</td>
                                                <td>$!{customerOrder.orderItemId}</td>
                                                <td>$!{customerOrder.serviceOfferName}</td>
                                                <td>$!{customerOrder.statusCdName}</td>
                                                <td>$!{customerOrder.statusDate}</td>

                                                #if($!{customerOrder.reason} != "null")
                                                <td>$!{customerOrder.reason}</td>
                                                #else
                                                <td></td>
                                                #end
     
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.backOrders && $options.backOrders != "null" &&
                                            $options.backOrders.size() == 0)
                                                <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
