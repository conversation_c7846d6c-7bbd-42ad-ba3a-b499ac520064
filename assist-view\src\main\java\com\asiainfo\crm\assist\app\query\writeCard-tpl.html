<div data-widget="writeCard" style="overflow: auto; height: 100%;">
	<p class="vita-data">$!options</p>
	<div class="box-maincont">
		<div class="homenofood">
			<div class="page_main">
				<!--填单start-->
				<div class="col-lg-12">
					<div class="box-item">
						<div class="wmin_content row ">
							<div class="form_title" style="border-bottom: none">
									手机号码：$!options.replPhoneNum
							</div>
						</div>
						<!--<div class="container-fluid row">
							<div class="form_title" style="border-bottom: none">
									手机号码：
							</div>
						</div>-->
					</div>
				</div>
				<div class="col-lg-12">
					<div class="box-item">
						<div class="container-fluid row">
							<div class="wmin_content row ">
								<!--<form class=" form-bordered">
									<div class="form-group col-md-6">-->
										<label class="col-md-2 control-label lablep">卡序列号：</label>
										<div class="col-md-5">
											<input id="serialNum" type="text" class="form-control"  placeholder="">
										</div>
										<div class="col-md-5">
											<button id="btnReadCard" type="button" class="btn btn-primary">读卡</button>
											<button id="btnWriteCard" type="button" class="btn btn-primary">写卡</button>
										</div>
									<!--</div>
								</form>-->
							</div>
						</div>
					</div>
				</div>

				<div class="col-md-12" style="margin-top: 5rem;text-align: center">
					<button id="btn_close" type="button" class="btn btn-primary ">返回</button>
					<button id="btn_confirm" type="button" class="btn btn-primary ">确认</button>
				</div>
				<!--填单end-->
				<!--ActiveX 声明-->
				<OBJECT id="ocx" style="height: 0px;width: 0px;" Classid="clsid:5e497bde-0e29-4ac0-bfb1-4af7b7940277" codeBase="/crm_assist/bundles/vita/plugs/common.ocx#version=1.0.0.1"></OBJECT>
			</div>
		</div>
	</div>
</div>

