package com.asiainfo.crm.assist.app.orderDdn;


import com.al.crm.nosql.cache.IRedisFix;

import com.alibaba.fastjson.JSON;
import com.asiainfo.crm.assist.model.SecTransferBoardLog;
import com.asiainfo.crm.common.AbstractSoComponent;

import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IOrderSaveSmo;
import com.asiainfo.crm.service.intf.IOrderTransferLogSmo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import java.text.SimpleDateFormat;
import java.util.*;



/**
 * 转储完成情况视图
 *
 *
 *
 */
@Component("vita.serverRecommend")
public class ServerRecommend extends AbstractSoComponent {


	@Autowired
	@Qualifier("transRedis")
	private IRedisFix cache;
	@Autowired
	private IOrderTransferLogSmo iOrderTransferLogSmo;
	@Autowired
	private IOrderSaveSmo iOrderSaveSmo;


	@Override
	public Map achieveData(Object... param) throws Exception {
		return null;
	}

	public Map queryCount(String jsonString) throws Exception {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Map req = jsonConverter.toBean(jsonString,Map.class);
		String beginTime=req.get("beginDate").toString();
		String endTime=req.get("endDate").toString();
		String sourceType=req.get("sourceType").toString();
		List<Long> counts = new ArrayList<>();
		List<Long> exceptionalCounts = new ArrayList<>();
		List<String> times = new ArrayList<>();
		Map options = new HashMap();
		if ("0".equals(sourceType)){
			Long size = cache.llen("secTransferCounterRecordData");
			List<String> dateTimes =cache.lrange("secTransferCounterRecordData",0,size);
			for (int i = dateTimes.size() -1; i >=0; i--) {
				String dateTime=dateTimes.get(i);
				//判断是否在时间内
				if (dateTime.compareTo(beginTime) >= 0 && dateTime.compareTo(endTime) <= 0) {
					Long count=cache.getKeyCount("secTransferCounterRecord", dateTime);
					Long exceptionalCount=cache.getKeyCount("secTransferCounterExceptional", dateTime);
					exceptionalCounts.add(exceptionalCount);
					counts.add(count);
					times.add(dateTime);
				}
			}
		}else {
			String re = "";
			if ("Y".equals(MDA.TRANSFER_BOARD_CHOOSE)){
				 re = iOrderTransferLogSmo.querySecTransferBoardLog(beginTime,endTime);
			}else{
				 re = iOrderSaveSmo.queryTransferBoardLog(beginTime,endTime);
			}
			List<SecTransferBoardLog> secTransferBoardLogs = (List<SecTransferBoardLog>) resolveResult(re);
			List<SecTransferBoardLog> successList = new ArrayList<>();
			Map failMap = new HashMap();
			String s = JSON.toJSONString(secTransferBoardLogs);
			secTransferBoardLogs = JSON.parseArray(s, SecTransferBoardLog.class);
			for(SecTransferBoardLog secTransferBoardLog:secTransferBoardLogs){
				String time = sdf.format(secTransferBoardLog.getCreateDate());
				if ("1000".equals(secTransferBoardLog.getStatusCd())){
					successList.add(secTransferBoardLog);
				}
				if ("2000".equals(secTransferBoardLog.getStatusCd())){
					failMap.put(time,secTransferBoardLog.getSecTransferCount());
				}
			}
			for(SecTransferBoardLog secTransferBoardLog:successList){
				String time = sdf.format(secTransferBoardLog.getCreateDate());
				times.add(time);
				counts.add(Long.parseLong(secTransferBoardLog.getSecTransferCount()));
				if(null==failMap.get(time)){
					exceptionalCounts.add(0L);
				}else {
					exceptionalCounts.add(Long.parseLong(failMap.get(time).toString()));
				}
			}
		}
		options.put("exceptionalCounts",exceptionalCounts);
		options.put("counts",counts);
		options.put("times",times);
		return options;
	}
}
