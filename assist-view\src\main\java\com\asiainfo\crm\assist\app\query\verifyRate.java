package com.asiainfo.crm.assist.app.query;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.al.common.Constant;
import com.al.common.json.JsonConverter;
import com.asiainfo.crm.assist.proxy.IEopServiceProxy;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IProdInstSMO;

/**
 * Created on 2017/8/12.
 */
@Component("vita.verifyRate")
public class verifyRate extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(verifyRate.class);

    @Autowired
    private IProdInstSMO prodInstSMO;
    
    @Autowired
    private IEopServiceProxy eopServiceProxy;
    



    /**
     * 宽带测速
     * @param json
     * @return
     * @throws IOException
     */
    public Map verifyRate(String json) throws Exception {
    	
    	Map<String, Object> retMap = new HashMap<String, Object>();
    	
    	try{
    		JsonConverter jsonConverter = JsonConverter.buildNormalConverter(Constant.DateFormat.DATETIME);
            Map<String, Object> paramMap = jsonConverter.toBean(json, Map.class);
            
            String an = String.valueOf(paramMap.get("content"));
            String localArea = String.valueOf(paramMap.get("localArea"));
            String searchType = String.valueOf(paramMap.get("searchType"));
        	
        	//调用资产接口查询宽带信息
            Map<String, Object> qryReqMap = new HashMap<String, Object>();
            qryReqMap.put("accessNumber", an);
            qryReqMap.put("regionId", localArea);
            qryReqMap.put("searchType", searchType);

            String qryReqJson = jsonConverter.toJson(qryReqMap);
            String retStr = prodInstSMO.qrySpeedCheckRes(qryReqJson);
            Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(retStr);

            String userName = String.valueOf(resultObjectMap.get("userName"));
            String accessNumber = String.valueOf(resultObjectMap.get("accessNumber"));
            String prodAddr = String.valueOf(resultObjectMap.get("prodAddr"));
            String typeName = String.valueOf(resultObjectMap.get("typeName"));
            String fan = String.valueOf(resultObjectMap.get("fan"));
            String loId = String.valueOf(resultObjectMap.get("loId"));
            if(StringUtils.isEmpty(accessNumber) || "null".equals(accessNumber)){
            	if("1".equals(searchType)){
            		retMap.put("ResultCode", "-1");						
        			retMap.put("ResultDesc", "根据该号码没查询到对应的数据:未查找到电话上捆绑的宽带信息！");
        			return retMap;
            	}else{
            		retMap.put("ResultCode", "-1");						
        			retMap.put("ResultDesc", "根据该号码没查询到对应的数据:未查找到宽带信息！");
        			return retMap;
            	}
            	
            }
        	//通过eop调用外围查询宽带测速信息
            paramMap.put("LOID", loId);
            paramMap.put("ACCESSNUMBER", accessNumber);
            paramMap.put("FAN", fan);
            paramMap.put("USERNAME", userName);
            paramMap.put("PRODADDR", prodAddr);
            paramMap.put("TYPENAME", typeName);
            retMap = eopServiceProxy.getUserBroadbandCapacity(paramMap);
    	}catch(Exception e){
    		retMap.put("ResultCode", "-1");						
			retMap.put("ResultDesc", "带宽测速查询异常！");
			logger.error("带宽测速查询异常 : {}", e);
			return retMap;
    	}
    	
        return retMap;
    }


	@Override
	public Map achieveData(Object... params) throws Exception {
		return null;
	}

}
