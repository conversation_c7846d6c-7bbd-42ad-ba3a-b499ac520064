(function (vita) {
    var boProdDetailInfo = vita.Backbone.BizView.extend({
        events: {
            "click #closeBtn": "_closePage",
            "click #olNbr": "_qryCustomerDetail",
            "click #mainOfferLi,#attachOfferLi a,a[name=custOrderId]": function (e) {
                this._qryOfferDetail(e)
            },
            "click a[name=acctDetail]": "_qryAcctDetail",
            "click #prodTab > li": "_tabChange",
            "click a[name=custDetail]": "_custDetailClk",
            "click i[name=paramA]":"_paramShowOrHide"
        },
        global:{
          compUrl:{
              olDetailInfo : "../query/olDetailInfo",      // 订单详情
              boProdDetailInfo : "../query/boProdDetailInfo",      // 订单项(产品)详情
              boOfferDetailInfo : "../query/boOfferDetailInfo",    // 订单项(销售品)详情
              custDetailInfo : "../query/custDetailInfo",  // 客户详情
              acctDetailInfo : "../query/acctDetailInfo"   // 账户详情
          }
        },
        _initialize: function () {
            var widget = this,
                el = $(widget.el);
            var data = el.data("data");
            if (data) {
                widget.model.set(data);
            }
        },
        _closePage : function() {
            var widget = this,
                element = $(widget.el);
            element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
        },
        _custDetailClk: function (e) {
            var widget = this;
            var custId = $(e.currentTarget).data("custId");
            if (!custId) {
                widget.popup("客户ID未返回,请重新查询...");
                return;
            }
            var param = {
                "custId": custId
            };
            widget._openDialog("custDetailInfo",param);
        },
        _paramShowOrHide: function (e) {
            var el = $(e.currentTarget);
            var trEl = el.closest("tr").next("tr[name=paramTr]");
            trEl.slideToggle("fast");
            if(el.hasClass("ivu-noselect-arrow")){
                el.removeClass("ivu-noselect-arrow").addClass("ivu-select-arrow");
            }else{
                el.removeClass("ivu-select-arrow").addClass("ivu-noselect-arrow");
            }
        },
        /**
         * 查看购物车详情
         * @private
         */
        _qryCustomerDetail: function (e) {
            var widget = this;
            var aEl = $(e.target).closest("a");
            var param = {
                custOrderId: aEl.data("olNbr")
            };
            widget._openDialog("olDetailInfo",param);
        },
        /**
         * 查询销售品详情
         * @private
         */
        _qryOfferDetail: function (e) {
            var widget = this;

            var aEl = $(e.target).closest("a");
            var param ={"custOrderId":aEl.data("custOrderId")};
            var dialogId = 'olDetailInfo' + 'Dialog';
            var option = {
                url : '../query/olDetailInfo',
                id : dialogId,
                params : param,
                onClose : function(res) {},
                headers : {
                    "regionId" : widget.gSession.installArea
                }
            }
            widget.dialog(option);
        },
        /**
         * 查询账户详情
         * @private
         */
        _qryAcctDetail: function (e) {
            var widget = this;
            var acctCd = $(e.currentTarget).data("acctCd");
            if (!acctCd) {
                widget.popup("账户合同不存在,请重新查询...");
                return;
            }
            var param = {
                acctCd: acctCd
            };
            widget._openDialog("acctDetailInfo",param);
        },
        _tabChange: function (e) {
            var widget = this,
                el = $(widget.el),
                liEl = $(e.currentTarget);
            if (!liEl.hasClass("active")) {
                liEl.addClass("active").siblings().removeClass("active");
                var arr = liEl.find("a").attr("id");
                var index = arr.split('#')[1];
                el.find("#myTabContent .tab-pane[id=" + index + "]").addClass("active in").siblings().removeClass("active in");
            }
        },
        _openDialog:function(compName,data){
            var widget = this;
            var option = {
                url: widget.global.compUrl[compName],
                params: data,
                headers : {
                    "regionId" : widget.gSession.installArea
                }
            };
            widget.dialog(option);
        }
    });
    vita.widget.register("boProdDetailInfo", boProdDetailInfo, true);
})(window.vita);