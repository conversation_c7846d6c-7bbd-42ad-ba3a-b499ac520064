<div data-widget="fixPrice">
    <vita-data>{"data":$options}</vita-data>
    <p class="vita-data">$options</p>
    <div class="sypage-nav">
        <div class="nav_alink"><strong class="text16">动态定价</strong></div>
    </div>
    <div class="sypage-min">
        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">查询条件</strong></div>
        </div>
        <div class="sypagemin_content ">
            <form>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">动态定价编码</label>
                    <div class="col-md-8">
                        <input type="text" class="form-control" placeholder="" id="fixPriceCode">
                        <p class="vita-bind" model="fixPriceCode"></p>
                    </div>
                </div>

                <div class="form-group col-md-12">
                    <div class="col-md-12 searchbutt_r" align="right">
                        <button type="button" class="btn btn-default" id="searchDel">清除</button>
                        <button type="button" class="btn btn-primary" id="qryCommonLogSearchBtn">搜索</button>
                    </div>
                </div>

            </form>
        </div>

        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">动态定价列表</strong></div>
        </div>
        <div class="sypagemin_content ">
            <div class="col-lg-12 mart10">
                <table class="table table-hover" id="systemRoleList">
                    <thead>
                    <tr>
                        <th>选择</th>
                        <th>动态定价编码</th>
                        <th>定价别名</th>
                        <th>价格</th>
                        <th>基础价格</th>
                        <th>生效时间</th>
                        <th>失效时间</th>
                    </tr>
                    </thead>
                    <tbody>
                    #if($options != "null" && $options.fixPrices && $options.fixPrices != "null" && $options.fixPrices.size() > 0)
                    #foreach($fixPrice in $options.fixPrices)
                    <tr >
                        <td>
                            <label class="wu-radio full absolute" data-scope="">
                                <input type="radio" name="moduleRadio" />
                                <p class="vita-data">{"data":$fixPrice,"totalNumber":$options.totalNumber}</p>
                            </label></td>
                        <td>$!fixPrice.fixPriceCode</td>
                        <td>$!fixPrice.fixPriceAliasName</td>
                        <td>$!fixPrice.priceValue</td>
                        <td>$!fixPrice.basePrice</td>
                        <td>$!fixPrice.effDate</td>
                        <td>$!fixPrice.expDate</td>
                       <!-- <td>#if($!sevDaysNoReasionLog.logStep == 1000)规则校验
                            #elseif($!sevDaysNoReasionLog.logStep == 2000)订单竣工
                            #end
                        </td>
                        <td>$!sevDaysNoReasionLog.createDate</td>-->
                    </tr>
                    #end
                    #end
                    </tbody>
                </table>
                <!--翻页start -->
                <div class="page-box" id="showPageInfo">
                </div>
                <!--翻页end -->
            </div>
        </div>
    </div>
</div>