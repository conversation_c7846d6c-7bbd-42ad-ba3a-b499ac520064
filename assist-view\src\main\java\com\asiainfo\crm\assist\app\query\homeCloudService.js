
(function (vita) {
    var homeCloudService = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_homeCloudService",

        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        global: {
        },
        _homeCloudService:function () {
            var widget = this;
            var gsession = widget.gSession;
            var accNum = $("#accNum").val();
            var params = {};
            if (widget._isNullStr(accNum)) {
                widget.popup("请输入接入号！");
                return false;
            } else {
                params.accNum = accNum;
            }
            debugger;
            widget.refreshPart("homeCloudService", JSON.stringify(params), "#homeCloudCodeResult", function (re) {
            }, { async: false });
        }

    });
    vita.widget.register("homeCloudService", homeCloudService, true);
})(window.vita);
