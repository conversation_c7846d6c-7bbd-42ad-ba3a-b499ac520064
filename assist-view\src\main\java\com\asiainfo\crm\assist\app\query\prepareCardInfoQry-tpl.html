<div data-widget="prepareCardInfoQry" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <!--<label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_region" type="checkbox">
                                                </label> -->
                                                地 区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="form-group col-md-4" hidden>
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_cardStatus" type="checkbox">
                                                </label> 卡状态
                                            </label>
                                            <div class="col-md-7">
                                                <select id="cardStatus" class="form-control">
                                                    <!--动态查询卡状态主数据 -->
                                                    #foreach($cardStatus in $options.cardStatusList)
                                                    <option value="$cardStatus.attrValueId">$cardStatus.attrValueName</option>
                                                    #end
                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4" hidden>
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_isCardHis" type="checkbox" name="payment">
                                                </label> 是否查询卡历史
                                            </label>
                                            <div class="col-md-7">
                                                <select id="isCardHis" class="form-control">
                                                    <option value="10">否</option>
                                                    <option value="11">是</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_accNum" type="checkbox" name="payment">
                                                </label> 接入号码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="accNum" type="text" class="form-control" placeholder="" disabled>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4" hidden>
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_accNumScope" type="checkbox" name="payment">
                                                </label>
                                                起始接入号码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="beginAccNum" type="text" class="form-control" placeholder="" disabled>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4" hidden>
                                            <label class="col-md-5 control-label lablep">
                                                终止接入号码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="endAccNum" type="text" class="form-control" placeholder="" disabled>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_cardNum" type="checkbox" name="payment">
                                                </label> 卡号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="cardNum" type="text" class="form-control" placeholder="" disabled>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4" hidden>
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_cardNumScope" type="checkbox" name="payment">
                                                </label>
                                                起始卡号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="beginCardNum" type="text" class="form-control" placeholder="" disabled>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4" hidden>
                                            <label class="col-md-5 control-label lablep">
                                                终止卡号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="endCardNum" type="text" class="form-control" placeholder="" disabled>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-11">
                                            <div class="col-md-12 searchbutt_r" align="right">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">查询</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12 mart10" id="prepareCardInfoListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>地区</th>
                                                <th>接入号码</th>
                                                <th>卡号</th>
                                                <th>卡状态</th>
                                                <th>库存状态</th>
                                                <th>C网IMSI</th>
                                                <th>G网IMSI</th>
                                                <th>LTE-IMSI</th>
                                                <th>PUK1</th>
                                                <th>PUK2</th>
                                                <th>PIN1</th>
                                                <th>PIN2</th>
                                                <th>ESN码</th>
                                                <th>卡容量</th>
                                            </tr>
                                            </thead>
                                            <tbody id="prepareCardInfoList">
                                            #if($options != "null" && $options.cardInfos && $options.cardInfos != "null" &&
                                            $options.cardInfos.size() > 0)
                                            #foreach($cardInfo in $options.cardInfos)
                                            <tr>
                                                <td>
                                                    $!{cardInfo.regionName}
                                                </td>
                                                <td>
                                                    $!{cardInfo.accNum}
                                                </td>
                                                <td>
                                                    $!{cardInfo.mktResInstNbr}
                                                </td>
                                                <td>
                                                    $!{cardInfo.statusName}
                                                </td>
                                                <td>
                                                    $!{cardInfo.cardStatusName}
                                                </td>
                                                <td>
                                                    $!{cardInfo.cImsi}
                                                </td>
                                                <td>
                                                    $!{cardInfo.gImsi}
                                                </td>
                                                <td>
                                                    $!{cardInfo.lteImsi}
                                                </td>
                                                <td>
                                                    $!{cardInfo.pukOne}
                                                </td>
                                                <td>
                                                    $!{cardInfo.pukTwo}
                                                </td>
                                                <td>
                                                    $!{cardInfo.pinOne}
                                                </td>
                                                <td>
                                                    $!{cardInfo.pinTwo}
                                                </td>
                                                <td>
                                                    $!{cardInfo.esnCode}
                                                </td>
                                                <td>
                                                    $!{cardInfo.mktResName}
                                                </td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.cardInfos && $options.cardInfos != "null" &&
                                            $options.cardInfos.size() == 0)
                                                <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
