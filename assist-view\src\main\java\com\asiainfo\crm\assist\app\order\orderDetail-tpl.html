<link rel="stylesheet" type="text/css" href="../bundles/assist/app/order/ActionChain.css">
<div data-widget="orderDetail" class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
    <p class="vita-data">{"data":$options}</p>
    <div class="box-maincont">
        <div class="homenofood">
            <div class="page_main notopnav">
                <!--填单start-->
                <div class="col-lg-12">
                    <div class="box-item dzlaction">
                        <div class="container-fluid row">
                            <div class="wmin_content overflowh padt15">
                                <ol class="breadcrumb">
                                    <li><span class="glyphicon glyphicon-home mar15"></span>订单项信息</li>
                                    <li class="active">订单详情</li>
                                </ol>
                                <div class="col-md-12">
                                    <div class="show-grid">
                                        <ul>
                                            <li class="col-xs-6 col-sm-2 labellcont">SEQ</li>
                                            <li class="col-xs-6 col-sm-4">$!{options.traceOrderItem.seq}</li>
                                            <li class="col-xs-6 col-sm-2 labellcont">销售品规格</li>
                                            <li class="col-xs-6 col-sm-4">$!{options.traceOrderItem.applyObjSpec}</li>
                                            <li class="col-xs-6 col-sm-2 labellcont">名称</li>
                                            <li class="col-xs-6 col-sm-4">$!{options.traceOrderItem.applyObjSpecName}</li>
                                            <li class="col-xs-6 col-sm-2 labellcont">来源</li>
                                            <li class="col-xs-6 col-sm-4">#if($options.traceOrderItem.chainCreate)动作链#else前端#end</li>
                                            <li class="col-xs-12 col-sm-12 labellcont padd0 sell">
                                                <div class="col-xs-12 col-sm-2 pagelabe">服务提供</div>
                                                <div class="col-xs-12 col-sm-10 pageright">
                                                    $!{options.traceOrderItem.serviceOfferName}
                                                </div>
                                            </li>
                                            <li class="col-xs-12 col-sm-12 labellcont padd0 sell">
                                                <div class="col-xs-12 col-sm-2 pagelabe">描述</div>
                                                <div class="col-xs-12 col-sm-10 pageright">
                                                    $!{options.traceOrderItem.remark}
                                                </div>
                                            </li>
                                        </ul>
                                    </div>

                                </div>
                            </div>
                            <div class="wmin_content overflowh martb-tb10">
                                <div class="col-md-12">
                                    <table class="table table-bordered">
                                        <thead>
                                        <tr>
                                            <th>动作</th>
                                            #if($options != "null" && $options.isTree && $options.isTree != "null" &&
                                            $options.isTree)
                                            <th>规则树</th>
                                            #end
                                            <th>订单项数据</th>
                                            <th>操作</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        #if($options != "null" && $options.traceOrderItem.traceAtomActions && $options.traceOrderItem.traceAtomActions != "null" &&
                                        $options.traceOrderItem.traceAtomActions.size() > 0)
                                        #foreach($traceAtomAction in $options.traceOrderItem.traceAtomActions)
                                        <tr>
                                            <td>$!{traceAtomAction.actionTypeCdName}</td>
                                            #if($options != "null" && $options.isTree && $options.isTree != "null" &&
                                            $options.isTree)
                                            <td>
                                                <a id="sruleTreeOrderInfo">查看规则树</a>
                                                <p class="vita-data">{"atomActionId":$traceAtomAction.atomActionId}</p>
                                            </td>
                                            #end
                                            <td><textarea class="form-control mart10" rows="3" >$!{traceAtomAction.instInfo}</textarea></td>
                                            <td >#if($traceAtomAction.instInfo.operType=="1000")新增
                                                #elseif($traceAtomAction.instInfo.operType=="1100")删除
                                                #elseif($traceAtomAction.instInfo.operType=="1200")修改
                                                #elseif($traceAtomAction.instInfo.operType=="1300")保持
                                                #elseif($traceAtomAction.instInfo.operType=="9999")老数据
                                                #end
                                            </td>
                                        </tr>
                                        #end
                                        #elseif($options != "null" && $options.traceOrderItem.traceAtomActions && $options.traceOrderItem.traceAtomActions != "null" &&
                                        $options.traceOrderItem.traceAtomActions.size() == 0)
                                        <tr>
                                            <td align='center' colspan='8'>未查到相关数据</td>
                                        </tr>
                                        #end
                                        </tbody>
                                    </table>

                                </div>
                            </div>

                        </div>
                    </div>
                    <!--填单end-->


                </div>

            </div>
        </div>
    </div>


