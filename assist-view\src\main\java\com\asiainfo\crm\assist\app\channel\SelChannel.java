package com.asiainfo.crm.assist.app.channel;

import java.util.HashMap;
import java.util.Map;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ai.vita.AbstractComponent;
import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IStaffOrgAreaSMO;

/**
 * 选择渠道组件，根据地区筛选
 * Copyright: Copyright (c) 2017 Asiainfo
 * 
 * @ClassName: selChannel.java
 * @Description: 该类的功能描述
 *
 * @version: v1.0.0
 * @author:  zhoujun
 * @date: 2018年02月06日 下午2:33:31 
 *
 * Modification History:
 * Date         Author          Version            Description
 *------------------------------------------------------------
 */
@Component("vita.selChannel")
public class SelChannel extends AbstractComponent {

    @Autowired
    private IStaffOrgAreaSMO staffOrgAreaSMO;
	
	@Override
    public Map achieveData(Object... params) throws Exception {
        Map options = new HashMap();
        return options;
    }

	public Map qryList(String jsonString) throws Exception {
        String  items = staffOrgAreaSMO.queryChannelListByRegion(jsonString);
        Map retMap =  new HashMap();
        JSONObject root = JSONObject.fromObject(items);
        JSONObject resultObject = root.getJSONObject("resultObject");
        String resultCode = root.getString("resultCode");
        if (!StringUtil.isEmpty(resultCode) && resultCode.equals(MDA.RESULT_SUCCESS.toString())) {
        	JSONObject pageInfo = resultObject.getJSONObject("pageInfo");
        	JSONArray orgs = new JSONArray();
        	if(resultObject.get("channels") != null){
        		orgs = resultObject.getJSONArray("channels");
        	}
            
            //Map pageInfo = jsonConverter.toBean(rolsMap.get("pageInfo").toString(), Map.class);
            retMap.put("totalNumber",  pageInfo.getString("rowCount"));
            retMap.put("items", orgs);
        }       
        return retMap;
    }
}
