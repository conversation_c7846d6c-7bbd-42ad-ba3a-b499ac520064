<link rel="stylesheet" type="text/css" href="../bundles/assist/app/order/ActionChain.css">
<div data-widget="orderHisDetail" class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
    <p class="vita-data">{"data":$options}</p>
    <div class="box-maincont">
        <div class="homenofood">
            <div class="page_main notopnav">
                <!--填单start-->
                <div class="col-lg-12">
                    <div class="box-item dzlaction">
                        <div class="container-fluid row">

                            <Div class="Order-number">订单流水：$options.csNbr</Div>
                            <div class="tab-content">
                                <div role="tabpanel" class="tab-pane active" id="11">
                                    <div class="form_title">
                                        <div><i class="bot"></i>查询条件</div>
                                    </div>
                                    <div class="form-bordered floatl" style="margin-bottom:30px;">

                                        <div class="col-md-12">
                                            <div class="form-group col-md-4">
                                                <label class="col-md-4 control-label lablep">
                                                    来源
                                                </label>
                                                <div class="col-md-8">
                                                    <select class="form-control" id="chainCreate">
                                                        <option value="">-请选择-</option>
                                                        <option value="false">前端</option>
                                                        <option value="true">动作链</option>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="form-group col-md-4">
                                                <label class="col-md-4 control-label lablep">
                                                    服务提供</label>
                                                <div class="col-md-8">
                                                    <select class="form-control " id="serviceOfferId">
                                                        <option value="">-请选择-</option>
                                                        #if($options != "null" && $options.serviceOfferTypes && $options.serviceOfferTypes != "null" && $options.serviceOfferTypes.size() > 0)
                                                        #foreach($serviceOffer in $options.serviceOfferTypes)
                                                        <option value="$serviceOffer.serviceOfferId">$serviceOffer.serviceOfferName</option>
                                                        #end
                                                        #end
                                                    </select>

                                                </div>
                                            </div>
                                            <div class="form-group col-md-4">

                                                <div class="col-xs-10"><input id="applyObjSpecName" type="text" class="form-control" placeholder="请输入订单项名称"></div>

                                                <div class="col-xs-2">
                                                    <button id="orderSearch"  type="button" class="btn btn-primary">查询</button>
                                                </div>



                                            </div>

                                        </div>
                                        <div class="col-md-12">
                                            <div class="col-md-8">
                                                <label class="col-md-4 control-label lablep textcolorgreen">
                                                    订单流水</label>
                                                <div class="col-md-8">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" placeholder="" value="$options.csNbr">
                                               <span class="input-group-btn">
                                                <button class="btn btn-primary" type="button">确定</button>
                                              </span>
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="col-md-4 ">


                                            </div>
                                        </div>

                                    </div>
                                    <div class="form_title">
                                        <div><i class="bot"></i>查询结果
                                            <div class="floatr  textcolorred"><span class="glyphicon glyphicon-list"></span>
                                                <span class="text14">层级展示</span>
                                            </div></div>
                                    </div>
                                    <div class="container-fluid row" id="orderTable">
                                        <div class="wmin_content">
                                            <div class="col-lg-12 " id="orderDiv">
                                                <p class="vita-data">{"data":$options}</p>
                                                <table class="table table-hover">
                                                    <thead>
                                                    <tr>
                                                        <th>序号</th>
                                                        <th>订单项名称</th>
                                                        <th>服务提供</th>
                                                        <th>来源</th>
                                                        <th>动作描述</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody >
                                                    #if($options != "null" && $options.offerOrderItems && $options.offerOrderItems != "null" &&
                                                    $options.offerOrderItems.size() > 0)
                                                    #foreach($offerOrderItem in $options.offerOrderItems)
                                                    #if( !($velocityCount >= 11) )
                                                    <tr>
                                                        <td>$foreach.count</td>
                                                        <td>
                                                            <a id="orderDetail" >$!{offerOrderItem.applyObjSpecName}</a>
                                                            <p class="vita-data">{"offerOrderItem":$offerOrderItem}</p>
                                                        </td>
                                                        <td>$!{offerOrderItem.serviceOfferName}</td>
                                                        <td>#if($offerOrderItem.chainCreate)动作链#else前端#end</td>
                                                        <td>$!{offerOrderItem.remark}</td>
                                                    </tr>
                                                    #end
                                                    #end
                                                    #elseif($options != "null" && $options.offerOrderItems && $options.offerOrderItems != "null" &&
                                                    $options.offerOrderItems.size() == 0)
                                                    <tr>
                                                        <td align='center' colspan='8'>未查到相关数据</td>
                                                    </tr>
                                                    #end
                                                    </tbody>
                                                </table>
                                            </div>
                                            #if($options.totalNumber && $options.totalNumber != "null")
                                            <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                            #end
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null" && $options.totalNumber >10)
                                        <!--翻页start -->
                                        <div id="showPageInfo"></div>
                                        <!--翻页end -->
                                        #end
                                    </div>

                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane " id="13"></div>
                        </div>

                    </div>
                </div>
                <!--填单end-->


            </div>

        </div>
    </div>
</div>
</div>
