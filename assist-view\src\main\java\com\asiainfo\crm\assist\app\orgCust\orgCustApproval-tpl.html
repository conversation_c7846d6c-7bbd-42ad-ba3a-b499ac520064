<div data-widget="orgCustApproval" style="overflow: auto; height: 100%;">

    <div class="box-maincont">
        <div class="homenofood">
            <div class="page_main">
                <!--填单start-->
                <div class="col-lg-12">
                    <div class="box-item">
                        <div class="container-fluid row">
                            <div class="form_title">
                                <div>
                                    <i class="bot"></i>查询条件
                                </div>
                            </div>
                            <div class="wmin_content row ">
                                <form class=" form-bordered">
                                    <div class="form-group col-md-6">
                                        <label class="col-md-2 control-label lablep"> <label
                                                class="wu-radio full absolute" data-scope=""> </label> 批次号
                                        </label>
                                        <div class="col-md-4">
                                            <input class="form-control" id="batchId" placeholder=""
                                                   type="text">
                                        </div>

                                    </div>

                                    <div class="form-group col-md-5">
                                        <label class="col-md-2 control-label lablep"> <label
                                                class="wu-radio full absolute" data-scope=""> </label> 工号
                                        </label>
                                        <div class="col-md-5">
                                            <input class="form-control" id="staffCode" placeholder=""
                                                   type="text">
                                        </div>

                                    </div>

                                    <div class="form-group col-md-6" id="qryTimeQuantum">
                                        <label class="col-md-2 control-label lablep">
                                            受理时间</label>
                                        <div class="col-md-6 form-inline">

                                            <div class="form-group">
                                                <input class="form-control" id="beginDate" name="beginDate" placeholder="开始时间"
                                                       type="text">
                                            </div>
                                            <div class="form-group">
                                                <input class="form-control" id="endDate" name="endDate" placeholder="结束时间"
                                                       type="text">
                                            </div>

                                        </div>
                                    </div>

                                    <div class="form-group col-md-5">
                                        <label class="col-md-2 control-label lablep"> <label
                                                class="wu-radio full absolute" data-scope=""> </label> 审批状态
                                        </label>
                                        <div class="col-md-5">
                                            <select id="auditStatus" class="form-control" >
                                                <option value="-1" selected>未审批</option>
                                                <option value="1">未通过</option>
                                                <option value="0">已通过</option>
                                            </select>
                                        </div>

                                    </div>


                                    <div class="form-group col-md-12">
                                        <label class="col-md-2 control-label lablep"></label>
                                        <div class="col-md-10" align='right'>
                                            <button class="btn btn-primary" id="btn-qryBatchOrg"
                                                    type="button">查询
                                            </button>
                                        </div>
                                    </div>

                                </form>
                            </div>
                        </div>


                        <div class="container-fluid row">
                            <div class="form_title">
                                <div>
                                    <i class="bot"></i>查询结果
                                </div>
                            </div>

                            <div class="container-fluid row" id="batctOrgTbody">
                                <div class="wmin_content" id="batctOrgListR">
                                    <div class="col-lg-12">
                                        <a class="btn btn-gray btn-outline" id="approvalDo"> <i class="glyphicon fa-details text18"> </i> 审批通过
                                        </a>
                                        <a class="btn btn-gray btn-outline" id="approvalFl"> <i class="glyphicon fa-details text18"> </i> 审批不通过
                                        </a>

                                    </div>
                                    <div class="col-lg-12 mart10" id="batchOrgList">
##                                        <p class="vita-data">{"data": $options}</p>
                                        <table class="table table-hover">
                                            <thead>
                                            <tr id="batchOrgTable" style="display: none;">
                                                <th>选择</th>
                                                <th>批次号</th>
                                                <th>导入人工号</th>
                                                <th>导入人组织名称</th>
                                                <th>新合同主体名称</th>
                                                <th>导入时间</th>
                                                <th>审批状态</th>
                                                <th>文件目录</th>
                                            </tr>
                                            <tr>
                                                <th>选择</th>
                                                <th>批次号</th>
                                                <th>导入人工号</th>
                                                <th>导入人组织名称</th>
                                                <th>新合同主体名称</th>
                                                <th>导入时间</th>
                                                <th>审批状态</th>
                                                <th>文件目录</th>
                                            </tr>
                                            </thead>

                                            <tbody>
                                            #if($options != "null" && $options.orgVoList && $options.orgVoList != "null" && $options.orgVoList.size() > 0)
                                            #foreach($orgVo in $options.orgVoList)
                                            <tr>
                                                <td><label class="wu-radio full absolute" data-scope="">
                                                    #if($orgVo.auditStatus != 0)
                                                        <input name="payment" type="checkbox"></label>
                                                    #end
                                                </td>
                                                <td>
                                                    <a class="textcolorgreen" link="batchId">$!{orgVo.importBatchId}</a>
                                                    <p class="vita-data">{"batchId" : "$!orgVo.importBatchId", "statusCd": "$!orgVo.auditStatus", "importSatffCode":"$!orgVo.importSatffCode"}</p>
                                                </td>
                                                <td>$!{orgVo.importSatffCode}</td>
                                                <td>$!{orgVo.importStaffOrgName}</td>
                                                <td>$!{orgVo.newDevStaffName}</td>
                                                <td>$!{orgVo.createDate}</td>
                                                <td>
                                                    #if($orgVo.auditStatus == '' || $orgVo.auditStatus == "null")
                                                    未审批
                                                    #elseif($orgVo.auditStatus=='0')
                                                    通过
                                                    #elseif($orgVo.auditStatus=='1')
                                                    不通过
                                                    #else
                                                    未审批
                                                    #end
                                                </td>

                                                <td>
                                                    <button class="btn btn-green mainSearch" id="fileDetail" type="button">
                                                        <i class="glyphicon main-search">文件查看</i>
                                                    </button>
                                                    <p class="vita-data">{"batchId" : $!orgVo.importBatchId}</p>
                                                </td>


                                            </tr>
                                            #end
                                            #elseif( $options.orgVoList == "null" || $options.orgVoList.size() == 0)
                                            <tr>
                                                <td align='center' colspan='7'>未查询到数据！
                                                <td>
                                            </tr>
                                            #end
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <!--翻页start-->
                                <div class="page-box" id="showPageInfo"></div>
                                #if($options.totalNumber && $options.totalNumber != "null")
                                    <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                #end
                                <!--翻页end -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--填单end-->
        </div>
    </div>
</div>

