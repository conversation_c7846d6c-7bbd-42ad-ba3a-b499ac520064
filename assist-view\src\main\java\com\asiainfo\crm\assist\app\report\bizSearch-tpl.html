<div data-widget="bizSearch" style="height: 100%">

	<div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->

		<div class="box-maincont">
			<div class="homenofood">
				<div class="page-nav">
					<div class="row">
						<div class="pagenav_box">
							<div class="page_title">营业清单</div>
						</div>
					</div>
				</div>
				<div class="page_main ">
					<!--填单start-->
					<div class="col-lg-12">
						<div class="box-item">
							<div class="container-fluid row">
								<div class="form_title">
									<div><i class="bot"></i>查询条件</div>
								</div>
								<div class="wmin_content row">
									<form class=" form-bordered">
										<div class="form-group col-md-6 ">
											<label class="col-md-4 control-label lablep">
												登录员工工号</label>
											<div class="col-md-8">
												<input id="staffId" type="text" class="form-control" placeholder="" readonly="readonly">
											</div>
										</div>
										<div class="form-group col-md-6">
											<label class="col-md-4 control-label lablep">
												登录员工名称</label>
											<div class="col-md-8">
												<input id="staff_Name" type="text" class="form-control" placeholder="" readonly="readonly">
											</div>
										</div>
										<div class="form-group col-md-6 ">
											<label class="col-md-4 control-label lablep">
												员工所在渠道</label>
											<div class="col-md-8">
												<!--<select id="channelId" class="form-control ">
                                                </select>-->
												<input id="channelId" type="text" class="form-control" placeholder="" readonly="readonly">
											</div>
										</div>
										<div class="form-group col-md-6">
											<label class="col-md-4 control-label lablep">
												要统计的员工工号</label>
											<div class="col-md-8" id="channelList">
												<select id="staffIdSelect" class="form-control ">
													<option createStaff="" staffAccount=""> 请选择</option>
													#if($options != "null" && $options.channelList &&
													$options.channelList != "null"
													&& $options.channelList.size() > 0)
													#foreach($itme in $options.channelList)
													<option createStaff="${itme.staffId}" staffAccount="${itme.staffAccount}">
														$!{itme.staffName}
													</option>
													#end
													#end
												</select>
											</div>
										</div>
										<div class="form-group col-md-6 ">
											<label class="col-md-4 control-label lablep">
												起始时间</label>
											<div class="col-md-8">
												<!--<div class="input-group">-->
												<input type="text" name="beginDate" id="beginDate" class="form-control" placeholder="">
												<!-- <div class="input-group-btn">
                                                     <button class="btn btn-green" type="button">选择</button>
                                                 </div>-->
												<!--</div>-->
											</div>
										</div>
										<div class="form-group col-md-6">
											<label class="col-md-4 control-label lablep">
												截止时间</label>
											<div class="col-md-8">
												<!--<div class="input-group">-->
												<input type="text" name="endDate" id="endDate" class="form-control" placeholder="">
												<!--<div class="input-group-btn">
                                                    <button class="btn btn-green" id="endDateBtn" type="button">选择</button>
                                                </div>-->
												<!--</div>-->
											</div>
										</div>
										<div class="form-group col-md-6">
											<label class="col-md-4 control-label lablep">
												查询范围</label>
											<div class="col-md-8" id="oldDabase">
												<select id="qryScope" class="form-control ">
													<option value="1">当前库</option>
													<option value="2">历史库</option>
												</select>
											</div>
										</div>

										<div class="form-group col-md-12">
											<label class="col-md-2 control-label lablep"></label>
											<div class="col-md-10 text-right">
												<!--<button type="button" class="btn btn-white">清除</button>-->
												<button type="button" id="btn_queryList" class="btn btn-primary">查询</button>
											</div>
										</div>
									</form>
								</div>
							</div>

							<div class="container-fluid row" id="row" style="display: none">
								<!--<div class="form_title">
									<div><i class="bot"></i>营业清单</div>
								</div>-->
								<div class="wmin_content">
									<div class="col-lg-12">
									<!--	<a class="btn btn-gray btn-outline"id="print">
											<i class="glyphicon fa-print text18"> </i> 打印
										</a>-->
										<button type="button" id="print" class="btn btn-primary" >打印</button>
										<!--<a class="btn btn-gray btn-outline"  >-->
											<!--<i class="glyphicon fa-derive text18"> </i>-->
										<button type="button" id="out_excel" class="btn btn-primary" >导出</button>
										<!--</a>-->
									</div>
									<div class="form-group col-md-4">
										<p style="color: red" id="countSize"></p>
									</div>

									<div class="col-lg-12 "id="divTable">
										<div class="mart10">
											<table class="table table-bordered conttd-w">
												<tr>
													<td >制表人</td>
													<td>
														<div id="madeMan"></div>
													</td>
													<td >制表时间</td>
													<td>
														<div id="nowDate"></div>
													</td>
												</tr>
												<tr>
													<td >营业厅</td>
													<td>
														<div id="bizHallName"></div>
													</td>
													<td >营业员</td>
													<td>
														<div id="staffName"></div>
													</td>
												</tr>
												<tr>
													<td >营业员工号</td>
													<td>
														<div id="staffNu"></div>
													</td>
													<td >受理时间</td>
													<td>
														<div id="dateDistance" ></div>
													</td>
												</tr>
											</table>
										</div>
										<div class="mart10" id="bizDealList" >
											<div id="bizDealListR">
												<p class="vita-data" >
													{"data": $options}
												</p>

												<table class="table table-bordered" >
													<thead>
													<tr>
														<th>购物车号</th>
														<th>订单类型</th>
														<th>产品类型/规格</th>
														<th>销售品</th>
														<th>客户名称</th>
														<th>接入号码</th>
														<th>收费员工</th>
														<th>付费方式</th>
														<th>应收金额</th>
														<th>实收金额</th>
													</tr>
													</thead>
													<tbody  >
													#if($options != "null" && $options.dealLists && $options.dealLists != "null" &&
													$options.dealLists.size() > 0)
													#foreach($dealList  in $options.dealLists)
													<tr>
														<td>$!{dealList.cartId}</td>
														<td>$!{dealList.orderType}</td>
														<td>$!{dealList.preductSpec}</td>

														<td>$!{dealList.offer}</td>
														<td>$!{dealList.customer}</td>
														<td>$!{dealList.accessNumber}</td>
														<td>$!{dealList.chargeStaff}</td>
														<td>$!{dealList.chargeMethod}</td>
														<td>$!{dealList.appCharge}</td>
														<td>$!{dealList.realCharge}</td>

													</tr>
													#end
													<tr>
														<th></th>
														<th></th>
														<th></th>
														<th></th>
														<th></th>
														<th> </th>
														<th></th>
														<th>总计： </th>
														<th>$!{options.totalAppCharge} </th>
														<th>$!{options.totalRealCharge} </th>
													</tr>
													#elseif( $options != "null" && $options.dealLists && $options.dealLists != "null" &&
													$options.dealLists.size() == 0)
													<tr >
														<td align='center' colspan='9'>未查询到数据！ <td>
													</tr>
													#end
													</tbody>
												</table>
											</div>
											<div id="showPageInfo">
											</div>
											#if($options.totalNumber && $options.totalNumber != "null")
											<p class="vita-data">{"totalNumber":$options.totalNumber,"exportNum":$options.exportNum}</p>
											#end
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!--填单end-->


				</div>

			</div>
		</div>
	</div>

</div>

