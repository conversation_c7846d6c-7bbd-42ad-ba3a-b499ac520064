package com.asiainfo.crm.assist.app.blacklist;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.aspect.annotations.SmLogAnnotation;
import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.service.intf.ICustSMO;

@Component("vita.blackListManage")
public class BlackListManage extends AbstractSoComponent {

	@Override
	public Map achieveData(Object... params) throws Exception {
		return null;
	}

	@Autowired
	private ICustSMO custSMO;
	
	@SmLogAnnotation(logType="1000", qryPoint="blackListManage", funName="qryBlackList", paramKeys = {"idNum"})
	public Map qryBlacklist(String jsonString) throws Exception {
		Map options = new HashMap<>();
		Map inParam = jsonConverter.toBean(jsonString, Map.class);
		String res = custSMO.qryBlackList(jsonString);
		Map blackListMap = (Map) resolveResult(res);
		List custList = (List) blackListMap.get("blacklistVos");
		Long totalNumber = 0L;
		for (int i = 0; i < custList.size(); i++) {
			totalNumber = Long.valueOf(i+1);
		}
		options.put("customers", custList);
		options.put("totalNumber", totalNumber);
		System.out.println();
		return options;
	}

}
