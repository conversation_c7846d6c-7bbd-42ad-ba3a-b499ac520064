(function (vita) {

    var orderCheck = vita.Backbone.BizView.extend({

        events: {
            "click #btn-query": "_throwOrderListQuery",
            "click #btn-buildOrderNode": "_buildOrderNode",
            "click button[name=repairName]": "_repairName",
        },
        _repairName: function (e) {
            var widget = this,
                aEl = $(e.currentTarget);
            var widget = this,element = $(widget.el);
            var dialogId = "repair";
            var url = './chooseTool';
            widget.dialog({
                id : dialogId,
                url : url,
                params : {
                    typeCode: aEl.val(),
                    premaryKey : e.currentTarget.parentElement.children[2].value,
                    orderItemIds :e.currentTarget.parentElement.children[3].value,
                    errType:e.currentTarget.parentElement.children[4].value,
                    objIdType:e.currentTarget.parentElement.children[5].value,
                    orderItemId:e.currentTarget.nextElementSibling.value,
                    accNum : $.trim(element.find("#str").val()),
                    func : $.trim(element.find("#func").val())
                },
                onClose : function(res) {
                    widget._throwOrderListQuery()
                }
            });
        },
        _buildOrderNode: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "buildOrderNode";
            var params = widget._getConds();
            if (params == false){
                return
            }
            if (params.func==1){
                params.func=0
            }else if (params.func==0){
                params.func=1
            }
            var url = './buildOrderNode'
            widget.dialog({
                id : dialogId,
                url : url,
                params : params,
                onClose : function(res) {

                }
            });
        },

        _throwOrderListQuery: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("byCoId", JSON.stringify(params), "#orderListResult", function (res) {

                });
            }
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
        _getConds: function () {
            var widget = this,element = $(widget.el);
            var params = {

            };
            var accNum = $.trim(element.find("#accNum").val());
            var func = $.trim(element.find("#isQryHis").val());
                if(widget._isNullStr(accNum)) {
                    widget.popup("请输入数据！");
                    return false;
                } else {
                    params.str = accNum;
                    params.func = func;
                }
            return params;
        },

    });
    vita.widget.register("orderCheck", orderCheck, true);
})(window.vita);