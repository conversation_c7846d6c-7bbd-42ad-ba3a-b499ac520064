(function(vita) {
	var commOperInfo = vita.Backbone.BizView.extend({
		events : {
			"click .btn-white" : "_clearConds",
			"click .btn-primary" : "_queryCommOperInfoLogPage",
			"click #regionIdBtn": "_chooseArea",
			"click #channelIdBtn": "_chooseChannelClick",
			"click #staffIdBtn": "_chooseStaff",
			"click #custIdBtn": "_chooseCust",
			"click #qryTimeQuantum button" : "_qryTimeQuantum",
			"click [link=toCommOperInfoDetail]": "_showCommOperInfoDetail"
		},
		preset : "date",
		currentDayCount: 0,
		lastMonthDayCount:-29,
		ninthDayCount:-89,
		sevenDayCount: -6,
		defaultBeginTime:"",
		defaultEndTime:"",
		global: {
			chooseArea: "../comm/chooseArea",
			chooseChannel: "../comm/chooseChannel",
			chooseStaff: "../comm/chooseStaff",
			chooseCust: "../comm/chooseCust",
			showCommOperInfoDetail: "../query/commOperInfoDetail"
		},
		_initialize: function () {
			var widget = this, el = $(widget.el), datetime = widget.require("datetime"), gSession = widget.gSession;
			var beginDate = el.find("input[name='beginDate']");
			var endDate = el.find("input[name='endDate']");
			datetime.register(beginDate, {
				preset: widget.preset,
				dateFormat: "yy-mm-dd"
			});
			datetime.register(endDate, {
				preset: widget.preset,
				dateFormat: "yy-mm-dd"
			});
			widget.defaultBeginTime = widget._getFormatDate(widget.sevenDayCount); //默认是七天
			widget.defaultEndTime = widget._currentDate();
			widget._setDefaultDateTime();
			el.find("#staffId").val(gSession.staffName).data("value", gSession.staffId);
			if (+gSession.curChannelId > 0) {
				el.find("#channelId").val(gSession.curChannelName).data("value", gSession.curChannelId);
			} else if (+gSession.orgId > 0){
				$.each(gSession.channels, function(i, channel) {
					if (channel.orgId == gSession.orgId) {
						el.find("#channelId").val(channel.channelName).data("value", channel.orgId);
						return false;
					}
				});
			}
			el.find("#regionId").val(gSession.staffRegionName).data("value", gSession.staffRegionId);
		},
		_chooseArea: function () {
			var widget = this, element = $(widget.el);
			var dialogId = "chooseAreaDialog";
			widget.dialog({
				id: dialogId,
				url: widget.global.chooseArea,
				params: {},
				onClose: function (res) {
					var chooseArea = $(res).closest("[data-widget=chooseArea]");
					if (chooseArea.length) {
						var data = chooseArea.chooseArea("getValue");
						if (!data || !data.validate) {
							return false;
						}
						element.find("#regionId").val(data.regionName).data("value", data.commonRegionId);
					}
				}
			});
		},
		_chooseChannelClick: function () {
			var widget = this, element = $(widget.el);
			widget._chooseChannel();
		},
		_chooseChannel: function () {
			var widget = this, element = $(widget.el);
			var gSession = widget.gSession;
			var compCode = "chooseChannel";
			var dialogId = "chooseChannelDialog";
			widget.dialog({
				id: dialogId,
				url: widget.global.chooseChannel,
				params: {},
				onClose: function (res) {
					var comp = $(res).closest("[data-widget=" + compCode + "]");
					var data = comp[compCode]("getValue");
					if (!data) {
						return false;
					}
					element.find("#channelId").val(data.channelName).data("value", data.orgId);
				}
			});
		},
		_chooseStaff: function () {
			var widget = this, element = $(widget.el);
			var compCode = "chooseStaff";
			var dialogId = "chooseStaffDialog";
			widget.dialog({
				id: dialogId,
				url: widget.global.chooseStaff,
				params: {},
				onClose: function (res) {
					var comp = $(res).closest("[data-widget=" + compCode + "]");
					var data = comp[compCode]("getValue");
					if (!data || data == "") {
						return false;
					}
					element.find("#staffId").val(data.staffName).data("value", data.staffId);
				}
			});
		},
		_chooseCust: function () {
			var widget = this, element = $(widget.el);
			var compCode = "chooseCust";
			var dialogId = "chooseCustDialog";
			widget.dialog({
				id: dialogId,
				url: widget.global.chooseCust,
				params: {},
				onClose: function (res) {
					var comp = $(res).closest("[data-widget=" + compCode + "]");
					var data = comp[compCode]("getValue");
					if (!data || !data.validate) {
						return false;
					}
					element.find("#custId").val(data.custName).data("value", data.custId);
				}
			});
		},
		_showCommOperInfoDetail: function (e) {
			var widget = this;
			var commOperInfoLogId = $(e.currentTarget).data("commOperInfoLogId");
			if (!commOperInfoLogId) {
				return;
			}
			var params = {
				"commOperInfoLogId": commOperInfoLogId
			};
			var option = {
				url: widget.global.showCommOperInfoDetail,
				params: params,
				headers: {
					"regionId": widget.gSession.installArea
				}
			};
			widget.dialog(option);
		},
		//当天从0点开始
		_currentDate:function(){
			var widget = this, el = $(widget.el);
			var nowDate = widget._getFormatDate(widget.currentDayCount);
			return nowDate;
		},
		//获取上个月时间
		_lastMonthDate:function (){
			var Nowdate = new Date();
			var vYear = Nowdate.getFullYear();
			var vMon = Nowdate.getMonth() + 1;
			var vDay = Nowdate.getDate();
			//每个月的最后一天日期（为了使用月份便于查找，数组第一位设为0）
			 var daysInMonth = new Array(0,31,28,31,30,31,30,31,31,30,31,30,31);
			if(vMon==1){
				vYear = Nowdate.getFullYear()-1;
				vMon = 12;
				}else{
				 vMon = vMon -1;
				}
			//若是闰年，二月最后一天是29号
			if(vYear%4 == 0 && vYear%100 != 0  || vYear%400 == 0 ){
				  daysInMonth[2]= 29;
			}
			if(daysInMonth[vMon] < vDay){
					 vDay = daysInMonth[vMon];
			}
			if(vDay<10){
					vDay="0"+vDay;
			}
			if(vMon<10){
					vMon="0"+vMon;
			}
			var date =vYear+"-"+ vMon +"-"+vDay;
			return date;
		},
		_qryTimeQuantum:function(e){
			var widget = this,element = $(widget.el),
				timeQuantum = $(e.currentTarget).attr("name"),
				dayCount;
			timeQuantum = timeQuantum.substring(timeQuantum.indexOf("_") + 1);
			switch(timeQuantum){
				case "1":
					dayCount = widget.currentDayCount;
					break;
				case "7":
					dayCount = widget.sevenDayCount;
					break;
				case "30":
					dayCount = widget.lastMonthDayCount;
					break
				case "90":
					dayCount = widget.ninthDayCount;
					break
				default:
					dayCount = widget.currentDayCount;
			}
			widget._changeTimeQuanTum(dayCount);
		},
		//设置默认时间
		_setDefaultDateTime : function(){
			var widget = this, element = $(widget.el),
				beginDate = element.find("#beginDate"),
				endDate = element.find("#endDate");
			beginDate.val(widget.defaultBeginTime);
			endDate.val(widget.defaultEndTime);
		},
		_changeTimeQuanTum:function(dayCount){
			var widget = this,element = $(widget.el),beginDate = element.find('#beginDate'),endDate = element.find('#endDate');
			if(dayCount==widget.currentDayCount){
				beginDate.val(widget._currentDate());
			}else if(dayCount==widget.lastMonthDayCount){
				beginDate.val(widget._lastMonthDate());
			}else{
				beginDate.val(widget._getFormatDate(dayCount));
			}
			endDate.val(widget._getFormatDate(widget.currentDayCount));
		},
		_getFormatDate: function(days) {
			var date = new Date();
			var yesterday_milliseconds = date.getTime()+days*1000*60*60*24;
			var yesterday = new Date();
			yesterday.setTime(yesterday_milliseconds);
			var strYear = yesterday.getFullYear();
			var strDay = yesterday.getDate();
			var strMonth = yesterday.getMonth()+1;
			if(strMonth<10)
			{
				strMonth="0"+strMonth;
			}
			if(strDay<10)
			{
				strDay="0"+strDay;
			}

			/*var hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
			var minute = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
			var second = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
			var time = hour+":"+minute+":"+second;*/
			var datastr = strYear+"-"+strMonth+"-"+strDay;
			return datastr;
		},
		_clearConds : function() {
			var widget = this, element = $(widget.el);
			var $checked = element.find(".form-group").find("input[type=checkbox]:checked");
			$.each($checked, function (i,chkInput) {
				chkInput.click();
				var $input = $(chkInput).closest(".form-group").find("input.form-control");
				$input.val("");
				$input.removeData();
			});
			//时间设置为7天
			this._changeTimeQuanTum(this.sevenDayCount);
		},
		_queryCommOperInfoLogPage : function() {
			var widget = this, el = $(widget.el), recordNumber = 10, params = widget._getConds(1, recordNumber);
			if (!params) {
				return false;
			}
			widget.refreshPart("queryCommOperInfoLogPage", params, "#searchList", function(re) {
				var r = $(re), total = r.data("totalNumber"), paging = widget.require("paging");
				var e = paging.new({
					recordNumber : recordNumber,
					total : total,
					pageIndex : 1,
					callback : function(pageIndex) {
						params.pageInfo.pageIndex = pageIndex;
						params.pageInfo.rowCount = total;
						widget.refreshPart("queryCommOperInfoLogPage", params, "#searchList4Tbody", $.noop, {mask: true});
					}
				});
				r.find("#showPageInfo").append(e.getElement());
			}, {mask: true});
		},
		_getConds : function(pageIndex, pageSize) {
			var widget = this, el = $(this.el), params = {};
			if (el.find("#c_staffId").is(":checked")) {
				var staffId = el.find("#staffId").data("value");
				if(this._isNullStr(staffId)) {
					this.popup("请选择员工...");
					return false;
				} else {
					params.staffId = staffId;
				}
			}
			if (el.find("#c_channelId").is(":checked")) {
				var channelId = el.find("#channelId").data("value");
				if(this._isNullStr(channelId)) {
					this.popup("请选择渠道...");
					return false;
				} else {
					params.channelId = channelId;
				}
			}
			if (el.find("#c_regionId").is(":checked")) {
				var regionId = el.find("#regionId").data("value");
				if(this._isNullStr(regionId)) {
					this.popup("请选择地区...");
					return false;
				} else {
					params.regionId = regionId;
				}
			}
			if (el.find("#c_custId").is(":checked")) {
				var custId = el.find("#custId").data("value");
				if(this._isNullStr(custId)) {
					this.popup("请选择客户...");
					return false;
				} else {
					params.custId = custId;
				}
			}
			if (el.find("#c_dealPoint").is(":checked")) {
				var dealPoint = el.find("#dealPoint").val();
				if(this._isNullStr(dealPoint)) {
					this.popup("请选择操作类型...");
					return false;
				} else {
					params.dealPoint = dealPoint;
				}
			}
			var startTime = el.find("#beginDate").val();
			var endTime = el.find("#endDate").val();
			startTime = startTime ? startTime + " 00:00:00" : startTime;
			endTime = endTime ? endTime + " 23:59:59" : endTime;
			if (!widget._isNullStr(startTime) && !widget._isNullStr(endTime)) {
				if(startTime>=endTime){
					widget.popup("结束时间应大于开始时间...");
					return false;
				}
				var start  = new Date(startTime.replace(/-/g,"/")).getTime();
				var end = new Date(endTime.replace(/-/g,"/")).getTime();

				var flag = end - start  > 90*24*60*60*1000;
				if (flag) {
					widget.popup("开始、结束时间跨度不能超出90天...");
					return false;
				}
			}
			if(!widget._isNullStr(startTime)||!widget._isNullStr(endTime)) {
				if (!widget._isNullStr(startTime)) {
					params.beginDate = startTime;
				}
				if (!widget._isNullStr(endTime)) {
					params.endDate = endTime;
				}
			}
			params.pageInfo = {
				pageIndex : pageIndex,
				pageSize : pageSize
			};
			return params;
		},
		_isNullStr: function (str) {
			if(str == null || str == "") {
				return true;
			}
			return false;
		}
	});
	vita.widget.register("commOperInfo", commOperInfo, true);
})(window.vita);