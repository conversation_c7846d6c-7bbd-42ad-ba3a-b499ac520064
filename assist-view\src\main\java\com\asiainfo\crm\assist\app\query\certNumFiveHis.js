(function (vita) {
    var Model = vita.Backbone.BizModel.extend({
        staffCode: "",
        channelNbr: "",
        channelName: "",
        certNum: "",
        startDate: "",
        endDate: "",
        pageIndex: 1,
        pageSize: 10
    });

    var certNumFiveHis = vita.Backbone.BizView.extend({
        _newModel : function(){
            return new Model();
        },
        events: {
            "click #queryHis": "_queryHis",
            "click [data-seq]": "_showData"
        },
        _initialize: function () {
            var $el = this.$el, datetime = this.require("datetime");
            var createDate = $el.find("#startDate,#endDate");
            if (createDate.length) {
                datetime.register(createDate, {
                    preset : "date"
                });
            }
        },
        _queryHis: function () {
            var widget = this, $el = widget.$el, model = widget.model;
            var conditions = {
                staffCode: $el.find("#staffCode").val(),
                channelNbr: $el.find("#channelNbr").val(),
                channelName: $el.find("#channelName").val(),
                certNum: $el.find("#certNum").val(),
                startDate: $el.find("#startDate").val(),
                endDate: $el.find("#endDate").val()
            }, pageInfo = {pageIndex: 1, pageSize: 10};
            model.set(conditions);
            widget.refreshPart("queryHis", [conditions, pageInfo], "#certNumTbody", function (res) {
                var r = $(res), paging = widget.require("paging");
                var pageInfo = r.find("#showPageInfo").data("pageInfo");
                var e = paging.new({
                    recordNumber: pageInfo.pageSize,
                    total: pageInfo.rowCount,
                    pageIndex: pageInfo.pageIndex,
                    callback: function (pageIndex, pageSize) {
                        var pageInfo = {pageIndex: pageIndex, pageSize: pageSize};
                        widget.refreshPart("queryHis", [conditions, pageInfo], "#certNumFiveListR");
                    }
                });
                r.find("#showPageInfo").append(e.getElement());
            });
        },
        _showData: function (e) {
            var widget = this, $target = $(e.target);
            widget.callService("queryHisData", $target.attr("data-seq"), function (res) {
                var content = $("<div style='width: 100%; padding: 30px'/>");
                content.append($("<div style='height: 50px; font-size: 20px; margin-bottom: 20px; border-bottom: 1px solid;'>查看的号码</div>")
                    .append($("<button id='certNumFiveHisShowDataClose' class='btn btn-primary btn-sm okbutt' style='float:right;'>关闭</button>")));
                if(res && res.length > 0){
                    var list = $("<div/>");
                    for(var r of res){
                        var row = $("<div style='float: left; margin: 0px 50px 30px 0px'/>");
                        row.append(
                            $("<div style='font-size: 18px; font-weight: bold'/>").append(r.resultCode),
                            $("<div style='font-size: 12px'/>").append(r.createDate)
                        );
                        list.append(row);
                    }
                    content.append(list);
                }else {
                    content.append("<div style='height: 50px; text-align: center'>没有查看任何号码</div>");
                }
                this.dialog({
                    id: "certNumFiveHisShowData",
                    content : content[0].outerHTML,
                    destroy : true,
                    autoClose : true
                });
                setTimeout(function () {
                    $("#certNumFiveHisShowDataClose").click(function () {
                        $("#certNumFiveHisShowData").dialog("close");
                    });
                });
            });
        }
    });
    vita.widget.register("certNumFiveHis", certNumFiveHis, true);
})(window.vita);