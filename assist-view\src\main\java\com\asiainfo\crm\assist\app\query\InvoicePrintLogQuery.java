package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.ISoReceiptSMO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("vita.invoicePrintLogQuery")
public class InvoicePrintLogQuery extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(InvoicePrintLogQuery.class);

    @Autowired
    private IOrderQuerySMO orderQuerySMO;

    @Autowired
    private ISoReceiptSMO soReceiptSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map retMap = new HashMap();
        retMap.put("logTypeMap", AssistMDA.INVOICE_LOG_TYPES);
        retMap.put("statusCdMap", AssistMDA.INVOICE_LOG_STATUS_CDS);
        return retMap;
    }

    /**
     * 查询电子发票日志列表
     */
    public Map queryInvoicePrintLogList(String json) throws Exception {
        String invoicePrintLogList = soReceiptSMO.qryInvoicePrintLogListByCond(json);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(invoicePrintLogList);
        Map<String, Object> options = Maps.newHashMap();
        List logEReceiptVoList = (List) MapUtils.getObject(resultObjectMap, "logEReceiptVoList");
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", total);
        options.put("invoicePrintLogs", logEReceiptVoList);

        options.put("logTypeMap",AssistMDA.INVOICE_LOG_TYPES);
        options.put("statusCdMap",AssistMDA.INVOICE_LOG_STATUS_CDS);
        return options;
    }

    /**
     * 查询订单详情
     */
    public Map queryCustomOrderDetail(String json) throws Exception {
        String customOrderDetail = orderQuerySMO.queryCustomerOrderDetailsByCodId(json);
        return (Map) resolveResult(customOrderDetail);
    }
}
