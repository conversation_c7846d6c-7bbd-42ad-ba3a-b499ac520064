package com.asiainfo.crm.assist.app.algorithmmanage;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ISecuritySmo;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * @Date 2022/1/20 16:06
 */
@Component("vita.tenantUpdate")
public class TenantUpdate extends AbstractComponent {

    @Autowired
    private ISecuritySmo securitySmo;

    @Override
    public Map achieveData(Object... params) throws Exception {
        String json = params[0].toString();
        Map options = jsonConverter.toBean(json, Map.class);
        String tenantId = MapUtils.getString(options, "tenantId");
        Map<String, Object> argMap = new HashMap<String, Object>();
        argMap.put("tenantId", tenantId);
        Map<String, Object> invokeResult = (Map<String, Object>) invokeMethod("tenantList", "queryTenants", new Object[]{jsonConverter.toJson(argMap)});
        List<Map> tenants = (List<Map>) MapUtils.getObject(invokeResult, "tenants");
        Map<String, Object> data = new HashMap<String, Object>();
        Map<String, Object> tenantInfo = null;
        if (tenants != null) {
            tenantInfo = tenants.get(0);
        }
        data.put("tenantInfo", tenantInfo);
        data.put("dataSecurityStatusCds", AssistMDA.DATA_SECURITY_STATUS_CDS);
        return data;
    }

    public String updateTenant(String params) {
        String result = securitySmo.updateTenant(params);
        return result;
    }
}
