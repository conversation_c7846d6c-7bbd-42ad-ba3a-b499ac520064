<div data-widget="qryPrepareOrderInfo" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                地 区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_channelId" type="checkbox" name="payment">
                                                </label> 渠道
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="channelId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="channelIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_staffId" type="checkbox" name="payment">
                                                </label> 营业员
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="staffId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="staffIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custId" type="checkbox" name="payment">
                                                </label> 所属客户
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="custId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="custIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_prepareOrderNbr" type="checkbox" name="payment">
                                                </label> 预受理单流水
                                            </label>
                                            <div class="col-md-7">
                                                <input id="prepareOrderNbr" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_statusCd" type="checkbox" name="payment">
                                                </label> 甩单类型
                                            </label>
                                            <div class="col-md-7">
                                                <select id="statusCd" class="form-control">
                                                    #if($options != "null" && $options.statusList != "null" &&
                                                    $options.statusList.size() > 0)
                                                    #foreach($status in $options.statusList)
                                                    <option value=$status.key>$status.value</option>
                                                    #end
                                                    #end
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custOrderId" type="checkbox" name="payment">
                                                </label> 购物车号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custOrderId" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
										<div id="busiNumberDiv" class="form-group col-md-4" style="display:none;">
											<label class="col-md-5 control-label lablep">
												<label class="wu-radio full absolute" data-scope="">
													<input id="c_busiNumber" type="checkbox" name="payment">
												</label> 业务号码
											</label>
											<div class="col-md-7">
												<input id="busiNumber" type="text" class="form-control" placeholder="">
											</div>
										</div>
                                        <div class="form-group col-md-12" id="qryTimeQuantum">
                                            <label class="col-md-2 control-label lablep">
                                                起止时间</label>
                                            <div class="col-md-10 form-inline">

                                                <div class="form-group">
                                                    <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="开始时间">
                                                </div>
                                                <div class="form-group">
                                                    <input name="endDate" id="endDate" type="text" class="form-control" placeholder="结束时间">
                                                </div>
                                                <button type="button" id="timeQuantum_1" name="timeQuantum_1" class="btn btn-gray btn-outline btn-sm">当天</button>
                                                <button type="button" id="timeQuantum_7" name="timeQuantum_7" class="btn btn-gray btn-outline btn-sm">七天</button>
                                                <button type="button" id="timeQuantum_30" name="timeQuantum_30" class="btn btn-gray btn-outline btn-sm">一个月</button>
                                                <button type="button" id="timeQuantum_90" name="timeQuantum_90" class="btn btn-gray btn-outline btn-sm">三个月</button>

                                            </div>
                                        </div>
                                        <div class="form-group col-md-11">
                                            <div class="col-md-12 searchbutt_r" align="right">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-clear" type="button" class="btn btn-white">清除</button>
                                            </div>
                                        </div>


                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询结果</div>
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12 mart10" id="orderListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>预受理单号</th>
                                                <th>预受理单流水</th>
                                                <th>正式单流水</th>
                                                <th>客户名称</th>
                                                <th>客户ID</th>
                                                <th>甩单类型</th>
                                                <th>甩单状态</th>
                                                <th>受理渠道</th>
                                                <th>受理地区</th>
                                                <th>受理员工</th>
                                                <th>创建时间</th>
                                            </tr>
                                            </thead>
                                            <tbody id="orderList">
                                            #if($options != "null" && $options.prepareOrders && $options.prepareOrders != "null" &&
                                            $options.prepareOrders.size() > 0)
                                            #foreach($prepareOrder in $options.prepareOrders)
                                            <tr>
                                            	<td>
                                            		$!{prepareOrder.prepareOrderId}
                                                </td>
                                                <td>
                                                	#if($prepareOrder.isInfo == "Y")
                                            			<a name="info">
	                                                	$!{prepareOrder.prepareOrderNbr}
	                                                	</a>
	                                                	<p class="vita-data">{
	                                                		"prepareOrderId": "$!prepareOrder.prepareOrderId"
	                                                	}</p>
	                                                #else
	                                                	$!{prepareOrder.prepareOrderNbr}
                                            		#end
                                                </td>
                                                <td>$!{prepareOrder.custOrderNbr}</td>
                                                <td>$!{prepareOrder.custName}</td>
                                                <td>$!{prepareOrder.custId}</td>
                                                <td>$!{prepareOrder.orderTypeName}</td>
                                                <td>
                                                	#if($prepareOrder.statusCd == "D")
                                                	<span class="textcolorred mar15" data-toggle="tooltip" data-placement="top" data-original-title="$!prepareOrder.remark">$!{prepareOrder.statusCdName}</span>
                                                	#else
                                                	$!{prepareOrder.statusCdName}
                                                	#end
                                                </td>
                                                <td>$!{prepareOrder.createOrgName}</td>
                                                <td>$!{prepareOrder.regionName}</td>
                                                <td>$!{prepareOrder.staffName}</td>
                                                <td>$!{prepareOrder.createDate}</td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.prepareOrders && $options.prepareOrders != "null" &&
                                            $options.prepareOrders.size() == 0)
                                                <tr><td align='center' colspan='7'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
