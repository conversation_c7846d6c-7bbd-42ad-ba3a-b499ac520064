package com.asiainfo.crm.assist.app.action;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IAcctQuerySMO;
import com.asiainfo.crm.service.intf.IBindAcctQuerySMO;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.google.common.collect.Maps;

/**
 * Created by Administrator on 2018-01-27.
 */
@Component("vita.addMemberList")
public class AddMemberList  extends AbstractComponent{
	
	private static final Logger logger = LoggerFactory.getLogger(AddMemberList.class);
	
	  
	@Autowired
	private IProdInstSMO prodInstSmo;
	
	@Autowired
	private IAcctQuerySMO acctQurySmo;
	
	@Autowired 
	private IBindAcctQuerySMO acctBindQuerySmo;
	
    @Override
    public Map achieveData(Object... params) throws Exception {
    	 String jsonString = params[0].toString();
         Map dataRangeMap = jsonConverter.toBean(jsonString, Map.class);
         Map retMap = new HashMap();
         retMap.put("data", dataRangeMap);
         return retMap;
    }
    
    public Map queryAddInfo(String jsonStr) throws Exception {
    	Map<String, Object> resultObjectMap = jsonConverter.toBean(jsonStr, Map.class);
        Map<String, Object> options = Maps.newHashMap();
        java.util.ArrayList accNumsList  =  (ArrayList) resultObjectMap.get("accNums");
        if(accNumsList.size()>0&&accNumsList!=null){
        	String str  = (String) accNumsList.get(0);
        	if(str.contains(",")){
        		String[]  arraySplit =  str.split(",");
        		resultObjectMap.remove("accNums");
        		resultObjectMap.put("accNums", arraySplit);
        		jsonStr = jsonConverter.toJson(resultObjectMap);
        	}
        }
		String acctInfo = prodInstSmo.qryAccProdInstListLocal(jsonStr);
		Map<String, Object> acctInfoMap = (Map<String, Object>) resolveResult(acctInfo);
		List accounts = (List) MapUtils.getObject(acctInfoMap, "accProdInsts");
		if(!MapUtils.isEmpty(acctInfoMap)&&accounts!=null){
			List<Map<String, Object>> orderInfos = new ArrayList<Map<String, Object>>();
			for (int i=0;i<accounts.size();i++) {
				    Map<String, Object> orderInfo = Maps.newHashMap();
				    Map<String, Object> accountsDetail = (Map<String, Object>) accounts.get(i);
				    orderInfo.put("bindNumber", accountsDetail.get("accNum"));
				    orderInfo.put("acctId", accountsDetail.get("acctId"));
				    orderInfo.put("acctCd", accountsDetail.get("acctCd"));
				    orderInfo.put("regionName", accountsDetail.get("regionName"));
					orderInfo.put("currentSate",accountsDetail.get("statusName"));
					orderInfo.put("acctName", accountsDetail.get("paymentMan"));
//					orderInfo.put("regionId", accountsDetail.get("regionId"));
					orderInfo.put("bindRegionId", accountsDetail.get("regionId"));
					orderInfo.put("acctNumber", resultObjectMap.get("manageNum"));
					orderInfo.put("statusCd", "1000");
					orderInfos.add(orderInfo);
			 }
		    Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(acctInfoMap,"pageInfo");
  	        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
  	        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
  	        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
  	        options.put("pageCount",pageCount);
	        options.put("pageIndex",pageIndex);
	        options.put("totalNumber", total);
	        options.put("orderInfos", orderInfos);
			options.put("resultCode", "0");	
		    options.put("resultDesc", "查询成功！");
		 }else{
			options.put("resultCode", "-1");	
		    options.put("resultDesc", "无账户绑定信息！");
		 }
        return options;
    }
    
    //绑定账户
    public Map bindNumberMsg(String jsonStr) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        Map<String, Object> reqMap = jsonConverter.toBean(jsonStr, Map.class);
        String statusCd =  (String) MapUtils.getObject(reqMap,"statusCd");
        String acctCd =  (String) MapUtils.getObject(reqMap,"acctCd");
        String bindNumber =  (String) MapUtils.getObject(reqMap,"bindNumber");
        String acctNumber =  (String) MapUtils.getObject(reqMap,"acctNumber");
        Integer bindRegionId =  (Integer) MapUtils.getObject(reqMap,"bindRegionId");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("bindRegionId", bindRegionId);
        map.put("acctCd", acctCd);
        map.put("bindNumber", bindNumber);
        map.put("acctNumber", acctNumber);
        map.put("statusCd", statusCd);
		String acctReqStr = jsonConverter.toJson(map);
		String bindInfo = acctBindQuerySmo.qryBindAcctScLocal(acctReqStr);
		Map<String, Object> qryAcctInfoMap = (Map<String, Object>) resolveResult(bindInfo);
 		List bindAcctScLocalList = (List) MapUtils.getObject(qryAcctInfoMap, "bindAcctScLocalList");
 		String acctInfo = "";
 		if(bindAcctScLocalList!=null&&bindAcctScLocalList.size()>0){
 			Map<String, Object> qryAcctInfoDetail = (Map<String, Object>) bindAcctScLocalList.get(0);
 			String qryStatusCd = (String) qryAcctInfoDetail.get("statusCd");
 			if(bindAcctScLocalList!=null&&bindAcctScLocalList.size()>0&&qryStatusCd.equals("1000")){
 				options.put("resultCode", "-1");
 				options.put("resultDesc", "账户已经绑定");
 				return options;
 			}
 			if(qryStatusCd.equals("1300")){
 				qryAcctInfoDetail.remove("statusCd");
 				qryAcctInfoDetail.put("statusCd", "1000");
 				jsonStr = jsonConverter.toJson(qryAcctInfoDetail);
 				acctInfo = acctBindQuerySmo.updateBindAcct(jsonStr);
 			}else{
 				acctInfo = acctBindQuerySmo.saveBindAcct(jsonStr);
 			}
 		}else{
 			acctInfo = acctBindQuerySmo.saveBindAcct(jsonStr);
 		}
        //测试报文
//        String acctInfo = "{\"resultObject\":710000000201,\"resultCode\":\"0\",\"resultType\":\"0\"}";
		Map<String, Object> acctInfoMap = jsonConverter.toBean(acctInfo, Map.class);
		if(!MapUtils.isEmpty(acctInfoMap)){
			String resultCode =  (String) MapUtils.getObject(acctInfoMap,"resultCode");
			if("0".equals(resultCode.trim())){
				options.put("resultCode", "0");	
				options.put("resultDesc", "账户绑定成功");
			}else{
				options.put("resultCode", "-1");	
			    options.put("resultDesc", "账户绑定失败");
			}
		 }else{
			options.put("resultCode", "-1");	
		    options.put("resultDesc", "账户绑定失败");
		 }
        return options;
    }
    
}
