(function (vita) {
    var materialCodeQuery = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_materialCodeQuery"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
        },
        global: {
        },
        _materialCodeQuery: function () {
            var widget = this;
            var gsession = widget.gSession;
            var params = {
                mktResInstNbr: $("#mktResNbr").val(),
                staffId: gsession.staffId,
                regionId: gsession.staffRegionId,
                routeParam : {
                    "regionId": gsession.staffRegionId
                }
            };
            debugger;
            widget.refreshPart("materialCodeQuery", JSON.stringify(params), "#materialCodeResult", function (re) {
            }, { async: false, headers:{} });
        }
    });
    vita.widget.register("materialCodeQuery", materialCodeQuery, true);
})(window.vita);