
(function (vita) {
    var speedtest = vita.Backbone.BizView.extend({
        events: {

        },
        _initialize: function () {
            // var st = new Date();
            var widget = this, element = $(widget.el);
            widget._showspeed();

        },
        //选择类型
        _showspeed:function () {
            var filesize =2790;    //measured in KB
            var et = new Date();
            var speed = Math.round(filesize*1000)/(et - st);
            document.getElementById("showtxt").innerHTML = ("您的下载速度为：" + speed+ " (K/秒)")
        },

    });
    vita.widget.register("speedtest", speedtest, true);
})(window.vita);