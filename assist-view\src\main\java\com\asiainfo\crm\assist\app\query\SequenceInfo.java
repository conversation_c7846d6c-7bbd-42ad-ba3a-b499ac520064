package com.asiainfo.crm.assist.app.query;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import com.asiainfo.crm.service.intf.IStaffInfoQuerySMO;
import com.asiainfo.crm.util.ListUtil;
import net.sf.json.JSONObject;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by daixw on 2017/7/18 0018.
 */
@Component("vita.sequenceInfo")
public class SequenceInfo extends AbstractComponent {

    @Autowired
    private IOrderQuerySMO iOrderQuerySMO;

    @Autowired
    private ISoQuerySMO soQuerySMO;

    @Autowired
    private IStaffInfoQuerySMO staffInfoQuerySMO;

    @Override
    public Map achieveData(Object... objects) throws Exception {
        return null;
    }

    public Map qryPageInfoSequenceInfo(String params) throws Exception {
        Map<String, Object> options = new HashMap();
        String queryInfo = iOrderQuerySMO.qryPageInfoSequenceInfo(params);
        JSONObject queryInfoJson = JSONObject.fromObject(queryInfo);
        Map<String,Object> resultObjectMap = (Map<String,Object>) queryInfoJson;
        Map<String,Object> resultObject = (Map<String, Object>) MapUtils.getObject(resultObjectMap, "resultObject");
        Map<String,Object> pageInfo = (Map<String, Object>) MapUtils.getObject(resultObject, "pageInfo");
        List<Map> sequenceInfos = (List) MapUtils.getObject(resultObject, "sequenceInfos");
        for (Map map : ListUtil.nvlList(sequenceInfos)) {
            String regionId = MapUtils.getString(map, "regionId");
            if (!StringUtil.isEmpty(regionId)) {
                map.put("regionName", qryRegionName(regionId));
            }
            String staffId = MapUtils.getString(map, "createStaff");
            if (!StringUtil.isEmpty(staffId)) {
                map.put("staffName", qryStaffName(staffId));
            }
            map.put("statusCd", AssistMDA.SEQUENCE_INFO_STATUS_CD_MAP.get(MapUtils.getString(map,"statusCd")));

        }
        resultObjectMap.clear();
        resultObjectMap.put("reqList", sequenceInfos);
        resultObjectMap.put("totalNumber", pageInfo.get("rowCount"));
        return resultObjectMap;
    }

    public Map createSequenceInfo(String params) throws Exception {
        String insertResult = iOrderQuerySMO.createSequenceInfo(params);
        Map<String, Object> result = jsonConverter.toBean(insertResult, Map.class);
        Map<String, Object> resultObjectMap = new HashMap<>();
        resultObjectMap.put("resultCode",MapUtils.getString(result, "resultCode"));
        resultObjectMap.put("resultType",MapUtils.getString(result, "resultType"));
        return resultObjectMap;
    }

    public Map deleteByIdSequenceInfo(String params) throws Exception {
        String deleteResult = iOrderQuerySMO.deleteByIdSequenceInfo(params);
        Map<String, Object> result = jsonConverter.toBean(deleteResult, Map.class);
        Map<String, Object> resultObjectMap = new HashMap<>();
        resultObjectMap.put("resultCode",MapUtils.getString(result, "resultCode"));
        resultObjectMap.put("resultType",MapUtils.getString(result, "resultType"));
        return resultObjectMap;
    }


    public Map updateSequenceInfo(String params) throws Exception {
        String updateResult = iOrderQuerySMO.updateSequenceInfo(params);
        Map<String, Object> result = jsonConverter.toBean(updateResult, Map.class);
        Map<String, Object> resultObjectMap = new HashMap<>();
        resultObjectMap.put("resultCode",MapUtils.getString(result, "resultCode"));
        resultObjectMap.put("resultType",MapUtils.getString(result, "resultType"));
        return resultObjectMap;
    }
    /**
     * 查询地区名称
     *
     * @param regionId
     * @return
     * @throws Exception
     */
    private String qryRegionName(String regionId) throws Exception {
        Map<String, Object> regionIdMap = new HashMap(1);
        regionIdMap.put("regionId", regionId);
        String ret = soQuerySMO.qryCommonRegionByIds(regionIdMap);
        String regionStr = (String) resolveResult(ret);
        Map<String, Object> regionMap = jsonConverter.toBean(regionStr, Map.class);
        String regionName = MapUtils.getString(regionMap, "regionName", "");
        return regionName;
    }

    /**
     * 查询员工名称
     *
     * @param createStaff
     * @return
     * @throws Exception
     */
    private String qryStaffName(String createStaff) throws Exception {
        Map param = new HashMap(1);
        param.put("staffId", createStaff);
        String staffInfoRet = staffInfoQuerySMO.checkCosponsor(jsonConverter.toJson(param));
        Map staffInfoMap = (Map) resolveResult(staffInfoRet);
        Map staffMap = MapUtils.getMap(staffInfoMap, "staffInfo");
        String staffName = MapUtils.getString(staffMap, "staffName", "");
        return staffName;
    }
    public Map getCurrentSeq(String params) throws Exception {
        String currentSeq = iOrderQuerySMO.getCurrentSeq(params);
        Map<String, Object> result = jsonConverter.toBean(currentSeq, Map.class);
        Map<String, Object> resultObjectMap = new HashMap<>();
        resultObjectMap.put("resultCode",MapUtils.getString(result, "resultCode"));
        resultObjectMap.put("resultObject",MapUtils.getString(result, "resultObject"));
        return resultObjectMap;
    }

}
