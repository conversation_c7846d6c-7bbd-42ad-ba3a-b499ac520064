(function (vita) {
    var transDealCertNumFive = vita.Backbone.BizView.extend({
        events: {
            "click #btn-qryTransDealCertNumFive": "_qryTransDealCertNumFive",
            "click #regionIdBtn": "_chooseArea",
            "click #showDetail": "_qryTransCertNumFiveDetail",
            "click #dealOrder": "_dealTransCertNumFive"
        },
        _initialize: function () {
            var widget = this,
                element = $(widget.el);
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var nowDate = new Date();
            var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
            var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
            var startYear = nowDate.getFullYear() - 5, endYear = nowDate.getFullYear();
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear:endYear
                });
            }
            ;
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear:endYear
                });
            }
            //日期控件设置默认时间
            widget.global.defaultBeginTime = widget._getFormatDate(widget.global.sevenDayCount); //默认是七天
            widget.global.defaultEndTime = widget._currentDate();
            widget._setDefaultDateTime();

        },
        _getFormatDate: function(days) {
            var date = new Date();
            var yesterday_milliseconds = date.getTime()+days*1000*60*60*24;
            var yesterday = new Date();
            yesterday.setTime(yesterday_milliseconds);
            var strYear = yesterday.getFullYear();
            var strDay = yesterday.getDate();
            var strMonth = yesterday.getMonth()+1;
            if(strMonth<10)
            {
                strMonth="0"+strMonth;
            }
            if(strDay<10)
            {
                strDay="0"+strDay;
            }

            /*var hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
            var minute = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
            var second = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
            var time = hour+":"+minute+":"+second;*/
            var datastr = strYear+"-"+strMonth+"-"+strDay;
            return datastr;
        },
        _currentDate:function(){
            var widget = this, el = $(widget.el);
            var nowDate = widget._getFormatDate(widget.global.currentDayCount);
            return nowDate;
        },
        //设置默认时间
        _setDefaultDateTime : function(){
            var widget = this, element = $(widget.el),
                beginDate = element.find("#beginDate"),
                endDate = element.find("#endDate");
            beginDate.val(widget.global.defaultBeginTime);
            endDate.val(widget.global.defaultEndTime);
        },
        global: {
        	transCertNumFiveDetailUrl: "../query/transCertNumFiveDetail",
        	chooseArea: "../comm/chooseArea",
            pageIndex: 1,
            pageSize: 15,
            preset: "date",
            defaultBeginTime:"",
            defaultEndTime:"",
            currentDayCount:0,
            sevenDayCount:-6,
            lastMonthDayCount:-29,
            ninthDayCount:-89,
            op:"",
            retCertFive:"",//一证5号查询结果
        },
        _getConds: function () {
        	debugger;
            var widget = this;
            var gsession = widget.gSession;
            var regionId = $("#regionId").attr("value");
            var beginDate = $("#beginDate").val();//开始时间
            var endDate = $("#endDate").val();//结束时间
            beginDate = beginDate.replace(/-/g, "");
            endDate = endDate.replace(/-/g, "");
            var param = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize,
                },
                regionId: regionId,//地区id
                beginDate: beginDate,//开始时间
                endDate: endDate,//结束时间
                phoneNum: $("#phoneNum").val(),//手机号
                custIdentity:$("#custIdentity").val(),//证件号码
                custType:1,//默认1证件号码
                isOwner: $("#isOwner").val(),//是否本人
                status: $("#status").val(),//处理状态
                order: $("#order").val(),//证件号码
                handleStaffCode: gsession.staffCode,//员工code
            };
            return param;
        },
        _isNullStr: function (str) {
            if (str == null || str == "") {
                return true;
            }
            return false;
        },
        _clear: function () {
            $("#value").val("");
        },
        _qryTransDealCertNumFive: function () {
            var widget = this, element = $(widget.el);
            var gsession = widget.gSession;
            var param = widget._getConds();
            if (widget._isNullStr(param.regionId)) {
                widget.popup("请选择地区！");
                return false;
            } 
            widget.refreshPart("queryTransDealCertNumFive", JSON.stringify(param), "#certNumTbody", function (res) {
              debugger;
              var data =  $(res).find("#transDealCertNumFiveList").data("data");
              var transDealCertNumFive = data.transDealCertNumFiveList;
              var r = $(res), paging = this.require("paging");
              var beginDate = $("#beginDate").val();//开始时间
              var endDate = $("#endDate").val();//结束时间
              beginDate = beginDate.replace(/-/g, "");
              endDate = endDate.replace(/-/g, "");
              var e = paging.new({
                  recordNumber: widget.global.pageSize,
                  total: r.find("#showPageInfo").data("totalNumber"),
                  pageIndex: widget.global.pageIndex,
                  callback: function (pageIndex, pageSize) {
                      debugger;
                      var param = {
                    		  regionId: $("#regionId").attr("value"),//地区id
                              beginDate: beginDate,//开始时间
                              endDate: endDate,//结束时间
                              phoneNum: $("#phoneNum").val(),//手机号
                              custIdentity:$("#custIdentity").val(),//证件号码
                              isOwner: $("#isOwner").val(),//是否本人
                              status: $("#status").val(),//处理状态
                              order: $("#order").val(),//证件号码
                          staffId: gsession.staffId,//员工id
                          pageInfo: {
                              pageIndex: pageIndex,
                              pageSize: widget.global.pageSize,
                          }
                      };
                      widget.refreshPart("queryTransDealCertNumFive", JSON.stringify(param), "#certNumFiveListR");
                  }
              });
              r.find("#showPageInfo").append(e.getElement());

          }, {
              async: false
          });
  
        
        },
        _qryTransCertNumFiveDetail: function (e) {
        	var widget = this;
        	aEl = $(e.target).closest("button");
            var orderNbr = aEl.data("orderNbr");
            var accNbr = aEl.data("telNbr");
            var areaId = $("#regionId").attr("value");
            if (widget._isNullStr(areaId)) {
                widget.popup("请选择地区！");
                return false;
            } 
            var params = {};
            params.orderId = orderNbr;
            params.accNbr = accNbr;
            params.commonRegionId = areaId;
            params.regionId = areaId;
			var option = {
				url : widget.global.transCertNumFiveDetailUrl,
				params : params,
				onClose : function(res) {
					return false;
				}
			};
			widget.dialog(option);
			return false;
            
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
       
        _dealTransCertNumFive : function(e) {
        	var widget = this,
            element = $(widget.el);
        gsession = widget.gSession;
        aEl = $(e.target).closest("button");
        var orderNbr = aEl.data("orderNbr");
        var telNbr = aEl.data("telNbr");
        var orderId = aEl.data("orderId");
        var params = {
        		orderNbr: orderNbr,//订单流水
        		//orderId: orderId,//订单id
        		areaId: gsession.staffRegionId,//受理地区id
        		regionId:gsession.staffRegionId,//受理地区id
        		accNbr: telNbr,//接入号
        		statusCd: "201300",//处理状态
        		channelNbr: gsession.curChannelNbr,//受理渠道
        		staffCode: gsession.staffId,//员工编码
                remarks: "接单处理", //备注
                glbSessionId: gsession.glbSessionId
                
            };
        widget.callService("updateTransCertNumFive", params, function(res) {
        	widget.popup(res.retDesc);
			return;
        }, {
        	async: false 
        });
        },
     
        _toCustAuth : function(cust) {}
       
    });
    vita.widget.register("transDealCertNumFive", transDealCertNumFive, true);
})(window.vita);