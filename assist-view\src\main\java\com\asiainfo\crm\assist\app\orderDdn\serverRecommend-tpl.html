
#import("../bundles/vita/plugs/echarts.simple.min.js")
<div data-widget="serverRecommend" style="height:100%">
    <p class="vita-data">{"data":$options}</p>


    <!--填单start-->
    <div class="col-lg-12">
        <div class="box-item">
            <div class="container-fluid row">
                <div class="form_title">
                    <div><i class="bot"></i>转储完成情况</div>
                </div>

                <div class="wmin_content row" style="text-align: center">
                    <form class=" form-bordered">
                        <div class="form-group col-md-3 ">
                            <label class="col-md-5 control-label lablep">
                                <label class="wu-radio full absolute" data-scope="">
                                </label>起始日期</label>
                            <div class="col-md-7">
                                <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="">
                            </div>
                        </div>
                        <div class="form-group col-md-3 ">
                            <label class="col-md-5 control-label lablep">结束日期</label>
                            <div class="col-md-7">
                                <input name="endDate" id="endDate" type="text" class="form-control" placeholder="">
                            </div>
                        </div>
                        <div class="form-group col-md-3 ">
                            <label class="col-md-5 control-label lablep">数据来源</label>
                            <select id="sourceType"
                                    class="btn btn-default dropdown-toggle">
                                <option value="1" selected="selected"
                                >数据库</option>
                                <option value="0" selected="selected"
                                       >redis缓存</option>

                            </select>
                        </div>
                        <div class="form-group col-md-2 "   >
                            <div class="col-md-4 searchbutt_r" align="left">
                                <button id="btn-query"   type="button" class="btn btn-primary">信息查询</button>
                            </div>
                        </div>
                    </form>
                </div>


            </div>


        </div>
    </div>
    <!--填单end-->
    <div id="line" style="height: 500px"></div>

    <script>

    </script>
</div>
