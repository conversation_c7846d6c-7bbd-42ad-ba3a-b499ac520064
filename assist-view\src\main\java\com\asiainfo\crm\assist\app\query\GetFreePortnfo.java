package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IEopServiceSmo;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by hello on 2017-10-08.
 *
 */
@Component("vita.getFreePortnfo")
public class GetFreePortnfo extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(GetFreePortnfo.class);

    @Autowired
    private IProdInstSMO iProdInstSMO;
    @Autowired
    private IEopServiceSmo eopServiceSmo;
    @Autowired
    private ISoQuerySMO iSoQuerySMO;
    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }
    /**
     * 光猫剩余语音口数查询
     * searchType 0:固话，1:宽带
     **/
    public Map<String, Object> getFreePortnfo(String json) throws IOException {
        Map<String, Object> returnMap = new HashMap<String, Object>();

        Map<String,Object> map = jsonConverter.toBean(json,Map.class);
        String regionId = (String)map.get("regionId");//地区
        String account = (String)map.get("account");//账号
        String searchType = (String)map.get("searchType");//类型宽带1或电话0
        //转换地区ID为2.9地区ID
        Map resMap = new HashMap();
        Map<String, Object> regionIdMap = new HashMap();
        regionIdMap.put("regionId", regionId);
        String regionIdStr = iSoQuerySMO.qryCommonRegionByIds(regionIdMap);
        String regionIdStrs =  (String) resolveResult(regionIdStr);
        Map<String, Object> regionIdMaps = jsonConverter.toBean(regionIdStrs, Map.class);
        if (null !=  regionIdMaps.get("regionNbr")) {
            regionId = (String) regionIdMaps.get("regionNbr");
        }
        String xmlResult=null;
        //得到产口实例ID
        String result=iProdInstSMO.qryAccProdInstListLocal(json);
        Map<String,Object> resultMap = jsonConverter.toBean(result,Map.class);
        Long prodInstId;
        String remark;
        Map  list= (Map)resultMap.get("resultObject");
        List accList= (List) list.get("accProdInsts");
        if(accList!=null&&accList.size()>0){
            Map mapList = (Map)accList.get(0);
            //得到产口实例ID
            prodInstId=(Long) mapList.get("prodInstId");
        }else{
            prodInstId=null;
            remark = "查询失败：系统不存在此号码，请核实号码是否在用，重新查询！";
        }
        //调用外围接口
        try {
            if(prodInstId!=null){
                StringBuilder retXml = new StringBuilder();
                retXml.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:web=\"http://webservice.app/\">")
                        .append("<soapenv:Header/><soapenv:Body> <web:getFreePortnfoByAccountId> <!--Optional:--> <arg0>")
                        .append(regionId)
                        .append("</arg0> <!--Optional:--> <arg1>")
                        .append(" <![CDATA[ <request> <areaid>")
                        .append(regionId)
                        .append("</areaid> <prodid>")
                        .append(prodInstId)
                        .append("</prodid><type>")
                        .append(searchType)
                        .append("</type></request> ]]> </arg1>")
                        .append("</web:getFreePortnfoByAccountId></soapenv:Body> </soapenv:Envelope>");
                //得到报文执行结果
                 xmlResult= eopServiceSmo.getFreePortnfoByAccountId(retXml.toString());
                /* xmlResult = xmlResult.replaceAll("</response>", "<ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc></response>");
                 Document document = DocumentHelper.parseText(xmlResult);

                String ResultCode = document.selectSingleNode("//ResultCode") == null ? "" : document.selectSingleNode(
                        "//ResultCode").getText();
                String ResultDesc = document.selectSingleNode("//ResultDesc") == null ? "" : document.selectSingleNode(
                        "//ResultDesc").getText();
                String protnum = document.selectSingleNode("//protnum") == null ? "" : document.selectSingleNode(
                        "//protnum").getText();*/
                String protnum = xmlResult.substring(xmlResult.indexOf("protnum>")+8,xmlResult.indexOf("&lt;/protnum>"));
                returnMap.put("ResultCode",0);
                returnMap.put("ResultDesc","调用成功");
                returnMap.put("protnum",protnum);

            }else{
                remark = "查询失败：系统不存在此号码，请核实号码是否在用，重新查询！";
    //            xmlResult = "{'ResultCode':1,'ResultDesc':'" + remark + "'}";
                returnMap.put("ResultCode",1);
                returnMap.put("ResultDesc",remark);
            }
        } catch (Exception e) {
            remark = "查询失败：解析资源系统返回数据失败！";
            returnMap.put("ResultCode",-1);
            returnMap.put("ResultDesc",remark);
        }
        return returnMap;
    }

}
