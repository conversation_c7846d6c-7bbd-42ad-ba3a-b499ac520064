(function (vita) {
    var custMergeInfo = vita.Backbone.BizView.extend({
        events: {
            "click #enterBtn": "_closePage",
            "click #closeBtn": "_closePage"
        },
        _initialize: function () {

        },
        //取消
        _closePage: function () {
            var widget = this,
                element = $(widget.el);
            element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
        }
    });
    vita.widget.register("custMergeInfo", custMergeInfo, true);
})(window.vita);