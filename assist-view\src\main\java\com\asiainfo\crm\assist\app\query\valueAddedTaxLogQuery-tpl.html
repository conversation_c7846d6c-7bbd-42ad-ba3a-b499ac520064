<div data-widget="valueAddedTaxLogQuery" style="height:100%">
    #macro(nullNotShowDefault $val)
        #if($val && $val != "null")
            $!val
        #else
           无
        #end
    #end
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custOrderNbr" type="checkbox">
                                                </label> 购物车流水号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custOrderNbr" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_custOrderId" type="checkbox">
                                                </label> 购物车ID
                                            </label>
                                            <div class="col-md-7">
                                                <input id="custOrderId" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_state" type="checkbox">
                                                </label>处理状态
                                            </label>
                                            <div class="col-md-7">
                                                <select id="state" class="form-control">
                                                    #if($options != "null" && $options.stateMap != "null" && $options.stateMap.size() > 0)
                                                    #foreach($item in $!options.stateMap.entrySet())
                                                    <option value="$!{item.key}">$!{item.value}</option>
                                                    #end
                                                    #end
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-12" id="qryTimeQuantum">
                                            <label class="col-md-2 control-label lablep">
                                                起止时间</label>
                                            <div class="col-md-10 form-inline">
                                                <div class="form-group">
                                                    <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="开始时间">
                                                </div>
                                                <div class="form-group">
                                                    <input name="endDate" id="endDate" type="text" class="form-control" placeholder="结束时间">
                                                </div>
                                                <button type="button" id="timeQuantum_1" name="timeQuantum_1" class="btn btn-gray btn-outline btn-sm">当天</button>
                                                <button type="button" id="timeQuantum_7" name="timeQuantum_7" class="btn btn-gray btn-outline btn-sm">七天</button>
                                                <button type="button" id="timeQuantum_30" name="timeQuantum_30" class="btn btn-gray btn-outline btn-sm">一个月</button>
                                                <button type="button" id="timeQuantum_90" name="timeQuantum_90" class="btn btn-gray btn-outline btn-sm">三个月</button>

                                            </div>
                                        </div>
                                        <div class="form-group col-md-11">
                                            <div class="col-md-12 searchbutt_r" align="right">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-clear" type="button" class="btn btn-white">清除</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title"></div>
                                <div class="wmin_content">
                                    <div class="col-lg-12 mart10" id="orderListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>购物车流水</th>
                                                <th>购物车ID</th>
                                                <th>创建时间</th>
                                                <th>处理状态</th>
                                                <th>处理时间</th>
                                                <th>备注</th>
                                                <th>业务流水号</th>
                                                <th>操作</th>
                                            </tr>
                                            </thead>
                                            <tbody id="invoicePrintLogList">
                                            #if($options != "null" && $options.valueAddedTaxLogs && $options.valueAddedTaxLogs != "null" && $options.valueAddedTaxLogs.size() > 0)
                                            #foreach($valueAddedTaxLog in $options.valueAddedTaxLogs)
                                            <tr>
                                                <td>$!{valueAddedTaxLog.custOrderNbr}</td>
                                                <td>$!{valueAddedTaxLog.custOrderId}</td>
                                                <td>$!{valueAddedTaxLog.createDate}</td>
                                                <td>
                                                    #if($options != "null" && $options.stateMap != "null" && $options.stateMap.size() > 0)
                                                        #foreach($item in $!options.stateMap.entrySet())
                                                            #if($!{item.key} == $!{valueAddedTaxLog.state})
                                                                $!{item.value}
                                                            #end
                                                        #end
                                                    #end
                                                </td>
                                                <td>$!{valueAddedTaxLog.dealDate}</td>
                                                <td>$!{valueAddedTaxLog.remarks}</td>
                                                <td>$!{valueAddedTaxLog.operationId}</td>
                                                <td>
                                                    <a class="textcolorgreen" link="valueAddedTaxLogDetail" data-toggle="tooltip" data-placement="top" title="点击查看详情">详情</a>
                                                    <p class="vita-data">{"id" : $!valueAddedTaxLog.id}</p>
                                                </td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.valueAddedTaxLogs && $options.valueAddedTaxLogs != "null" &&
                                            $options.valueAddedTaxLogs.size() == 0)
                                                <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
