package com.asiainfo.crm.assist.app.query;

import java.util.HashMap;
import java.util.Map;

import com.al.common.utils.MapUtil;
import com.asiainfo.crm.common.AbstractComponent;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.ISpecSMO;

/**
 * 选择销售品组件，根据地区筛选
 * Copyright: Copyright (c) 2017 Asiainfo
 * 
 * @ClassName: selChannel.java
 * @Description: 该类的功能描述
 *
 * @version: v1.0.0
 * @author:  zhoujun
 * @date: 2018年02月06日 下午2:33:31 
 *
 * Modification History:
 * Date         Author          Version            Description
 *------------------------------------------------------------
 */
@Component("vita.chooseOffer")
public class ChooseOffer extends AbstractComponent {

    @Autowired
    private ISpecSMO specSMO;
	
	@Override
    public Map achieveData(Object... params) throws Exception {
        String paramStr = (String) params[0];
        Map options = jsonConverter.toMap(paramStr, String.class, Object.class);
        Map pageInfo = new HashMap();
        pageInfo.put("pageIndex", 1);
        pageInfo.put("pageSize", 10);
        options.put("pageInfo", pageInfo);
        options.putAll(qryList(jsonConverter.toJson(options)));
        return options;
    }

	public Map qryList(String jsonString) throws Exception {
        Map options = new HashMap();

        String itemStr = specSMO.qryOfferList(jsonString);
        Map retMap = (Map) resolveResult(itemStr);

        options.put("items", MapUtils.getObject(retMap, "offers"));
        Map pageInfo = MapUtils.getMap(retMap, "pageInfo");
        options.put("totalNumber", MapUtils.getString(pageInfo, "rowCount"));
        return options;
    }
}
