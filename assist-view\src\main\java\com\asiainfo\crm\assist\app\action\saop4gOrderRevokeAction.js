(function(vita) {
	var saop4gOrderRevokeAction = vita.Backbone.BizView.extend({
		events : {
			"click #btn-clear" : "_clearCond",
			"click #btn-revoke" : "_revokeCond",
		},
		_clearCond : function() {
			var $checked = $(".form-group").find("input[type=radio]:checked");
			$.each($checked, function(i, chkInput) {
				chkInput.click();
				var $input = $(chkInput).closest(".form-group").find("input.form-control");
				$input.val("");
				$input.attr("value", null);
			})
		},
		_isNullStr : function(str) {
			if (str == null || str == "") {
				return true;
			}
			return false;
		},
		_getParams : function() {
			var widget = this;
			var gSession = widget.gSession;
			var params = {
				staffName : gSession.staffName,
				staffCode : gSession.staffCode,
				srvcInstId : "SAOP_4G_ORDER_ACTION",
				busyTypeCd : "401300"
			};
			var paramCnt = 0;
			if ($("#c_isale").is(":checked")) {
				var isale = $("#isale").val();
				if (widget._isNullStr(isale)) {
					widget.popup("请输入集团渠道方订单号！");
					return false;
				} else {
					paramCnt++;
					params.isale = isale;
				}
			}
			if ($("#c_custOrderId").is(":checked")) {
				var custOrderId = $("#custOrderId").val();
				if (widget._isNullStr(custOrderId)) {
					widget.popup("请输入CRM3.0客户订单号！");
					return false;
				} else {
					paramCnt++;
					params.custOrderId = custOrderId;
				}
			}
			if ($("#c_extCustOrderId").is(":checked")) {
				var extCustOrderId = $("#extCustOrderId").val();
				if (widget._isNullStr(extCustOrderId)) {
					widget.popup("请输入CRM3.0外部客户订单号！");
					return false;
				} else {
					paramCnt++;
					params.extCustOrderId = extCustOrderId;
				}
			}
			if ($("#c_extCustOrderNbr").is(":checked")) {
				var extCustOrderNbr = $("#extCustOrderNbr").val();
				if (widget._isNullStr(extCustOrderNbr)) {
					widget.popup("请输入CRM3.0外部客户订单流水！");
					return false;
				} else {
					paramCnt++;
					params.extCustOrderNbr = extCustOrderNbr;
				}
			}
			if (!paramCnt) {
				widget.popup("请选择输入至少一个参数！");
				return;
			}
			return params;
		},
		_revokeCond : function() {
			var widget = this;
			var params = widget._getParams();
			if (params) {
				widget.callService("doSaopAction", JSON.stringify(params), function(ret) {
					$("#resultDsc").text(ret.resultDsc);
					$("#resultMsg").text(ret.resultMsg);
				});
			}
		}
	});
	vita.widget.register("saop4gOrderRevokeAction", saop4gOrderRevokeAction, true);
})(window.vita);
