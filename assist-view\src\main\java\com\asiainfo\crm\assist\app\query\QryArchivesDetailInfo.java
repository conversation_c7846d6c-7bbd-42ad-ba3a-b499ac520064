package com.asiainfo.crm.assist.app.query;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.IPaperlessSysRecodSMO;
import com.asiainfo.crm.util.ListUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created on 2019/4/25.
 */
@Component("vita.qryArchivesDetailInfo")
public class QryArchivesDetailInfo extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(QryArchivesDetailInfo.class);

    /**
     * 有副卡时，接入号数量的最小值
     */
    private final int second = 2;

    @Autowired
    private IOrderQuerySMO orderQuerySMO;

    @Autowired
    private IPaperlessSysRecodSMO saveMergeSignatureService;

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map<String, Object> options = new HashMap<>(1);
        options.put("isStream", MDA.LOGIC_N);
        Map<String, Object> paramBean = jsonConverter.toBean(params[0].toString(), Map.class);
        Map<String, Object> param = Maps.newHashMap();
        String custOrderNbr = MapUtils.getString(paramBean, "custOrderNbr");
        String accNum = MapUtils.getString(paramBean, "accNum");
        String type = MapUtils.getString(paramBean, "type");
        if (!MDA.QRY_ARCHIVES_INFO_ATTR.get("receipt").equals(type)) {
            if (!StringUtil.isEmptyStr(accNum)) {
                param.put("relaValue", accNum);
                param.put("relaItem", MapUtils.getString(MDA.QRY_ARCHIVES_INFO_ATTR, "relaItem", ""));
            }
        }
        param.put("busiType", type);
        param.put("olId", custOrderNbr);
        param.put("srcFlag", MapUtils.getString(MDA.QRY_ARCHIVES_INFO_ATTR, "srcFlag", ""));
        Map ret = downLoadArchives(param);
        int result = MapUtils.getIntValue(ret, "resultCode", 1);
        if (result == 0) {
            options.put("isStream", MDA.LOGIC_Y);
            options.put("outputStream", MapUtils.getString(ret, "outputStream"));
            options.put("type", type);
        }
        return options;
    }

    /**
     * 电子档案下载文件接口(base64)
     *
     * @param param
     * @return
     * @throws Exception
     */
    public Map downLoadArchives(Map param) throws Exception {
        String type = MapUtils.getString(param, "busiType");
        Map<String, Object> mapCer = null;
        if (MDA.QRY_ARCHIVES_INFO_ATTR.get("receipt").equals(type)) {
            //下载回执
            mapCer = jsonConverter.toMap(saveMergeSignatureService.archivesDownload(jsonConverter.toJson(param)),
                    String.class, Object.class);
        } else {
            //下载经办人
            mapCer = jsonConverter.toMap(saveMergeSignatureService.base64Download(jsonConverter.toJson(param)),
                    String.class, Object.class);
        }
        mapCer.put("outputStream", MapUtils.getString(mapCer, "result"));
        return mapCer;
    }

    /**
     * 查询电子档案详情
     *
     * @param json
     * @return
     * @throws Exception
     */
    public Map qryArchivesDetailInfo(String json) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        Map qryParam = jsonConverter.toBean(json, Map.class);
        String qryType = MapUtils.getString(qryParam, "qryType");
        String custOrderNbr = MapUtils.getString(qryParam, "custOrderNbr");
        if ("oper".equals(qryType)) {
            // 查询经办人照片
            // 从经办人文档信息获取接入号列表
            List<Object> accNumList = getAccNumListFromOrderHandlerDoc(custOrderNbr);
            List<Map<String, Object>> accNumDetailList = new ArrayList<>(8);
            if (ListUtil.isListEmpty(accNumList) || accNumList.size() == 0) {
                //1.不存在接入号的情况，如修改客户
                List<Map<String, Object>> operPhotoList = this.getOperArchives(custOrderNbr, null);
                Map<String, Object> accNumDetail = new HashMap<>(1);
                if (!ListUtil.isListEmpty(operPhotoList) && operPhotoList.size() > 0) {
                    accNumDetail.put("operPhotoList", operPhotoList);
                    accNumDetailList.add(accNumDetail);
                }
            } else {
                //2.存在接入号的情况，如新装、过户等
                // 根据购物车流水查询订单数据
                List<Map<String, Object>> orderItemList = getOrderItemList(custOrderNbr);
                // 将订单数据填充到接入号详情
                accNumDetailList = getAccNumDetailList(accNumList, orderItemList);
                // 如果存在主副卡，进行主副卡区分
                if (isHasSecondaryCard(accNumDetailList)) {
                    // 处理接入号详情，主要区分主副卡
                    dealCardInfo(accNumDetailList, orderItemList);
                }
                this.getOperPhotoList(accNumDetailList, custOrderNbr);
            }
            if (!ListUtil.isListEmpty(accNumDetailList)) {
                options.put("operPhotoList", accNumDetailList);
            }
        } else {
            //查询回执
            if ("Y".equals(AssistMDA.SWITCH_EASW_PDF_IFRAME_INIT)) {
                Map<String, Object> receiptPhoto = this.judgeReceiptArchives(custOrderNbr);
                if (MapUtils.isNotEmpty(receiptPhoto)) {
                    options.put("receiptPhoto", receiptPhoto);
                }
            } else {
                Map<String, Object> receiptPhoto = this.getReceiptArchives(custOrderNbr);
                if (MapUtils.isNotEmpty(receiptPhoto)) {
                    options.put("receiptPhoto", receiptPhoto);
                }
            }
        }
        options.put("custOrderNbr", custOrderNbr);
        options.put("loadByIframe", AssistMDA.SWITCH_EASW_PDF_IFRAME_INIT);
        return options;
    }


    /**
     * 获取接入号详情
     *
     * @param accNumList
     * @param orderItemList
     * @return
     */
    public List<Map<String, Object>> getAccNumDetailList(List<Object> accNumList, List<Map<String, Object>> orderItemList) {
        if (ListUtil.isListEmpty(accNumList) || ListUtil.isListEmpty(orderItemList) || accNumList.size() == 0) {
            return null;
        }
        List<Map<String, Object>> accNumDetailList = new ArrayList<>(accNumList.size());
        for (Object accNum : ListUtil.nvlList(accNumList)) {
            for (Map orderItem : ListUtil.nvlList(orderItemList)) {
                if (accNum.equals(orderItem.get("accNum")) && MDA.ORDER_ITEM_CD_MAP.get("prod").equals(MapUtils.getString(orderItem, "orderItemCd"))) {
                    Map<String, Object> accMap = new HashMap<>(4);
                    accMap.put("accNum", accNum);
                    accMap.put("prodId", MapUtils.getString(orderItem, "applyObjSpec"));
                    accMap.put("prodName", MapUtils.getString(orderItem, "applyObjSpecName"));
                    accNumDetailList.add(accMap);
                    break;
                }
            }
        }
        return accNumDetailList;
    }

    /**
     * 判断是否存在主副卡
     *
     * @param accNumDetailList
     * @return
     */
    private boolean isHasSecondaryCard(List<Map<String, Object>> accNumDetailList) {
        if (ListUtil.isListEmpty(accNumDetailList) || accNumDetailList.size() < second) {
            return false;
        }
        int count = 0;
        for (Map accNumDetail : ListUtil.nvlList(accNumDetailList)) {
            if (MDA.PHONE_ID.equals(accNumDetail.get("prodId"))) {
                accNumDetail.put("cardType", "unKnow");
                count += 1;
            }
        }
        if (count > 1) {
            return true;
        }
        return false;
    }

    /**
     * 根据购物车流水查询订单数据
     *
     * @param custOrderNbr
     * @return
     * @throws Exception
     */
    private List<Map<String, Object>> getOrderItemList(String custOrderNbr) throws Exception {
        if (StringUtil.isEmptyStr(custOrderNbr)) {
            return null;
        }
        List<Map<String, Object>> orderItems = null;
        Map<String, Object> req = new HashMap<>(1);
        req.put("custOrderNbr", custOrderNbr);
        String customerOrderItemList = orderQuerySMO.qryCustomerOrderItemListByCond(jsonConverter.toJson(req));
        Map<String, Object> coiRetMap = (Map<String, Object>) resolveResult(customerOrderItemList);
        List<Map> customerOrders = (List<Map>) MapUtils.getObject(coiRetMap, "customerOrders");
        if (!ListUtil.isListEmpty(customerOrders) && MapUtils.isNotEmpty(customerOrders.get(0))) {
            Map customerOrder = customerOrders.get(0);
            orderItems = (List<Map<String, Object>>) customerOrder.get("orderItems");
        }
        return orderItems;
    }

    /**
     * 从经办人文档信息获取接入号列表
     *
     * @param custOrderNbr
     * @return
     * @throws Exception
     */
    public List<Object> getAccNumListFromOrderHandlerDoc(String custOrderNbr) throws Exception {
        if (StringUtil.isEmptyStr(custOrderNbr)) {
            return null;
        }
        List<Object> accNumList = ListUtil.newArrayList();
        Map qryParam = new HashMap(1);
        qryParam.put("custOrderNbr", custOrderNbr);
        String orderHandlerDocs = orderQuerySMO.qryOrderHandlerDocs(jsonConverter.toJson(qryParam));
        List<Map<String, Object>> orderHandlerDocList = (List<Map<String, Object>>) resolveResult(orderHandlerDocs);
        for (Map<String, Object> orderHandlerDoc : ListUtil.nvlList(orderHandlerDocList)) {
            String accNum = MapUtils.getString(orderHandlerDoc, "accNum");
            if (!StringUtil.isEmptyStr(accNum)) {
                accNumList.add(accNum);
            }
        }
        return accNumList;
    }

    /**
     * 对接入号标识出主副卡信息
     *
     * @param accNumDetailList
     * @param orderItemList
     * @return
     */
    private void dealCardInfo(List<Map<String, Object>> accNumDetailList, List<Map<String, Object>> orderItemList) throws Exception {
        // 手机接入号的列表
        List<Object> accNumList = ListUtil.newArrayList();
        for (Map accNumDetail : ListUtil.nvlList(accNumDetailList)) {
            if (accNumDetail.containsKey("cardType")) {
                accNumList.add(accNumDetail.get("accNum"));
            }
        }
        for (Map orderItem : ListUtil.nvlList(orderItemList)) {
            // 订单项中过滤可独立订购的主销售品
            if (MDA.ORDER_ITEM_CD_MAP.get("offer").equals(MapUtils.getString(orderItem, "orderItemCd")) && "1".equals(MapUtils.getString(orderItem, "isIndependent"))) {
                String offerOrderItemId = MapUtils.getString(orderItem, "orderItemId");
                int count = 0;
                //查找销售品构成产品，通过构成产品的角色判断主副卡
                List<Map<String, Object>> ordOfferProdInstRelList = qryOrdOfferProdInstRels(offerOrderItemId);
                for (Map ordOfferProdInstRel : ListUtil.nvlList(ordOfferProdInstRelList)) {
                    if (ListUtil.in(MapUtils.getString(ordOfferProdInstRel, "accNum"), accNumList) && MDA.PHONE_ID.equals(MapUtils.getString(ordOfferProdInstRel, "prodId"))) {
                        count += 1;
                    } else {
                        break;
                    }
                }
                // 手机接入号数量与销售品构成产品数量一致，说明是这个销售品的构成产品成员
                if (count == accNumList.size()) {
                    for (Map accNumMap : ListUtil.nvlList(accNumDetailList)) {
                        if (accNumMap.containsKey("cardType")) {
                            for (Map offerProdInstRel : ListUtil.nvlList(ordOfferProdInstRelList)) {
                                if (MapUtils.getString(offerProdInstRel, "accNum").equals(MapUtils.getString(accNumMap, "accNum"))) {
                                    Long roleId = MapUtils.getLongValue(offerProdInstRel, "roleId");
                                    // 角色id判断是否主副卡
                                    if (ListUtil.in(roleId, MDA.ZHUKA_ROLE_ID_LIST)) {
                                        //主卡 Y 用于前端展示判断
                                        accNumMap.put("cardType", MDA.LOGIC_Y);
                                    } else {
                                        //副卡 N 用于前端展示判断
                                        accNumMap.put("cardType", MDA.LOGIC_N);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 通过销售品订单项ID查询订单销售品产品实例关系
     *
     * @param offerOrderItemId
     * @return
     * @throws Exception
     */
    private List<Map<String, Object>> qryOrdOfferProdInstRels(String offerOrderItemId) throws Exception {
        if (StringUtil.isEmptyStr(offerOrderItemId)) {
            return null;
        }
        Map reqMap = new HashMap(1);
        reqMap.put("orderItemId", offerOrderItemId);
        String ordOfferProdInstRels = orderQuerySMO.queryOrdOfferProdInstRels(jsonConverter.toJson(reqMap));
        List<Map<String, Object>> ordOfferProdInstRelList = (List<Map<String, Object>>) resolveResult(ordOfferProdInstRels);
        return ordOfferProdInstRelList;
    }

    /**
     * 获取多个经办人照片列表(base64)
     *
     * @param accNumDetailList
     * @param custOrderNbr
     * @throws Exception
     */
    public void getOperPhotoList(List<Map<String, Object>> accNumDetailList, String custOrderNbr) throws Exception {
        for (Map accNumDetail : ListUtil.nvlList(accNumDetailList)) {
            String accNum = MapUtils.getString(accNumDetail, "accNum");
            List<Map<String, Object>> operPhotoList = getOperArchives(custOrderNbr, accNum);
            accNumDetail.put("operPhotoList", operPhotoList);
        }
    }

    /**
     * 获取单个经办人照片列表(base64)
     *
     * @param custOrderNbr
     * @param accNbr
     * @return
     * @throws Exception
     */
    public List<Map<String, Object>> getOperArchives(String custOrderNbr, String accNbr) throws Exception {
        //查找的照片类型 先全量查询照片是否存在 根据返回的照片种类进行图片base64查看
        List<String> busiTypeMap = MDA.QRY_ARCHIVES_OPER_INFO_ATTR;
        if (ListUtil.isListEmpty(busiTypeMap)) {
            busiTypeMap = new ArrayList<>(2);
        }
        Map<String, Object> paramMap = new HashMap<>(4);
        List<Map<String, Object>> returnList = new ArrayList<>();
        //附件分组查询
        List<Map<String, Object>> attachList = new ArrayList<>();
        for (String busiType : busiTypeMap) {
            Map<String, Object> t = new HashMap<>(4);
            t.put("busiType", busiType);
            if (!StringUtil.isEmptyStr(accNbr)) {
                //关联类型：接入号关联：ACC_NBR
                t.put("relaItem", MapUtils.getString(MDA.QRY_ARCHIVES_INFO_ATTR, "relaItem", ""));
                //分组标识
                t.put("relaValue", accNbr);
            }
            attachList.add(t);
        }
        paramMap.put("olId", custOrderNbr);
        paramMap.put("srcFlag", MapUtils.getString(MDA.QRY_ARCHIVES_INFO_ATTR, "srcFlag", ""));
        paramMap.put("attachList", attachList);
        //获取电子档案附件列表
        String archiveData = saveMergeSignatureService.attachsQuery(jsonConverter.toJson(paramMap));
        Map<String, Object> archiveDataMap = jsonConverter.toMap(archiveData, String.class, Object.class);
        //找到对应附件
        String resultCode = "resultCode";
        int result = MapUtils.getIntValue(archiveDataMap, resultCode, 1);
        if (result == 0) {
            List<Map<String, Object>> resultList = (List<Map<String, Object>>) archiveDataMap.get("result");
            for (Map<String, Object> resultData : resultList) {
                Map<String, Object> operQryMap = new HashMap<>(8);
                operQryMap.put("busiType", MapUtils.getString(resultData, "busiType"));
                operQryMap.put("olId", custOrderNbr);
                operQryMap.put("srcFlag", MapUtils.getString(MDA.QRY_ARCHIVES_INFO_ATTR, "srcFlag", ""));
                String relaItem = MapUtils.getString(resultData, "relaItem");
                String relaValue = MapUtils.getString(resultData, "relaValue");
                // 如果没有分组关系（既没有接入号数据的情况），则不传
                if (!StringUtil.isEmptyStr(relaItem) && !StringUtil.isEmptyStr(relaValue)) {
                    operQryMap.put("relaItem", relaItem);
                    operQryMap.put("relaValue", relaValue);
                }
                Map<String, Object> mapCer = jsonConverter.toMap(saveMergeSignatureService.base64Download(jsonConverter.toJson(operQryMap)),
                        String.class, Object.class);
                String busiType = MapUtils.getString(resultData, "busiType");
                String busiTypeName = MapUtils.getString(MDA.EASW_BUSI_TYPE_MAP, busiType, "经办人图片");
                mapCer.put("busiType", busiType);
                mapCer.put("busiTypeName", busiTypeName);
                if (!StringUtil.isEmptyStr(relaValue)) {
                    mapCer.put("accNbr", relaValue);
                }
                returnList.add(mapCer);
            }
        }
        return returnList;
    }

    /**
     * 获取回执(base64)
     *
     * @param custOrderNbr
     * @return
     * @throws Exception
     */
    public Map<String, Object> getReceiptArchives(String custOrderNbr) throws Exception {
        Map qryMap = new HashMap(4);
        qryMap.put("olId", custOrderNbr);
        qryMap.put("srcFlag", MapUtils.getString(MDA.QRY_ARCHIVES_INFO_ATTR, "srcFlag", ""));
        qryMap.put("busiType", MapUtils.getString(MDA.QRY_ARCHIVES_INFO_ATTR, "receipt", ""));
        Map<String, Object> mapCer = jsonConverter.toMap(saveMergeSignatureService.archivesDownload(jsonConverter.toJson(qryMap)),
                String.class, Object.class);
        String resultCode = "resultCode";
        int result = MapUtils.getIntValue(mapCer, resultCode, 1);
        if (result == 0) {
            mapCer.put("busiType", MapUtils.getString(MDA.QRY_ARCHIVES_INFO_ATTR, "receipt", ""));
        } else {
            mapCer.clear();
        }
        return mapCer;
    }

    /**
     * 判断回执是否存在
     *
     * @param custOrderNbr
     * @return
     * @throws Exception
     */
    public Map<String, Object> judgeReceiptArchives(String custOrderNbr) throws Exception {
        Map qryMap = new HashMap(4);
        qryMap.put("olId", custOrderNbr);
        qryMap.put("srcFlag", MapUtils.getString(MDA.QRY_ARCHIVES_INFO_ATTR, "srcFlag", "CRM-WEB"));
        List<Map<String, Object>> attachList = new ArrayList<>(1);
        Map<String, Object> attachMap = new HashMap<>(1);
        attachMap.put("busiType", MapUtils.getString(MDA.QRY_ARCHIVES_INFO_ATTR, "receipt", "R"));
        attachList.add(attachMap);
        qryMap.put("attachList", attachList);
        Map<String, Object> archiveDataMap = jsonConverter.toMap(saveMergeSignatureService.attachsQuery(jsonConverter.toJson(qryMap)),
                String.class, Object.class);
        String resultCode = "resultCode";
        int result = MapUtils.getIntValue(archiveDataMap, resultCode, 1);
        if (result == 0) {
            List<Map<String, Object>> resultList = (List<Map<String, Object>>) archiveDataMap.get("result");
            for (Map<String, Object> resultData : ListUtil.nvlList(resultList)) {
                if (MapUtils.isNotEmpty(resultData) && resultData.containsKey("busiType")) {
                    Map option = new HashMap(1);
                    option.put("result", "Y");
                    return option;
                }
            }
        }
        return null;
    }

    /**
     * 邮件发送
     *
     * @param params
     * @return
     * @throws Exception
     */
    public Map sendEmail(String params) throws Exception {
        Map options = new HashMap(2);
        Map req = jsonConverter.toBean(params, Map.class);
        req.put("srcFlag", MapUtils.getString(MDA.QRY_ARCHIVES_INFO_ATTR, "srcFlag", ""));
        String rsp = saveMergeSignatureService.mailReSend(jsonConverter.toJson(req));
        if (null != rsp) {
            options.putAll(jsonConverter.toBean(rsp, Map.class));
        }
        return options;
    }

}
