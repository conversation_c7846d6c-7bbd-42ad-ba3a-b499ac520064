(function(vita){
    var batchMarkAgentUser = vita.Backbone.BizView.extend({
        events : {
            "click #markSubbutton": "_markSubbutton",//导入EXCEL
            "click #upLoadTemplet": "_upLoadTemplet"//导出EXCEL模板
        },
        _initialize:function(){
            var widget = this,
                element = $(widget.el);

        },
        _markSubbutton:function () {
            debugger;
            var widget = this,element = $(widget.el);
            var gsession = widget.gSession;
            var m_fileVal = $('.m_file').val();
            if(m_fileVal=="") {
                alert('请选择文件！');
                return ;
            }
            if('-1' == m_fileVal.indexOf('.xls')) {
                alert('只能上传Excel文件！');
                return ;
            }
            var params = {
                updateStaff:gsession.staffId //员工编号
            };
            $(".route_input").val(JSON.stringify(params));
            var url ="../action/uploadMarkAgentUserExcels";
            $('#a_form').ajaxSubmit({
                type:"post",url:url, scriptCharset: 'utf-8',
                success:function (date) {
                    var jsonData = JSON.parse(date);
                    if(jsonData.resultObject.resultCode == 0){
                        var dataInfo = jsonData.resultObject.configDataInfoVo
                        localStorage.setItem('batchId',dataInfo.batchId);
                        // alert('操作批次号：'+dataInfo.batchId+' 成功'+
                        //     dataInfo.successSum+'个; 失败'+dataInfo.errorSum+'个，'+"号码："+dataInfo.filedAccNum);
                        if(dataInfo.errorSum==0){
                            $("#retDataWord").html('打标成功'+
                                dataInfo.successSum+'个; 失败'+dataInfo.errorSum+'个');
                        }else{
                            $("#retDataWord").html('打标成功'+
                                dataInfo.successSum+'个; 失败'+dataInfo.errorSum+'个，'+"号码："+dataInfo.filedAccNum);
                        }
                    } else {
                        alert('上传失败，请检查内容是否合法');
                    }
                }
            })
        },
        _upLoadTemplet: function () {
            var widget = this;
            var gsession = widget.gSession;
            var url = "../action/downLoadMarkAgentUserTemplet";
            location.href=url;
        }
    });
    vita.widget.register("batchMarkAgentUser", batchMarkAgentUser, true);
})(window.vita);