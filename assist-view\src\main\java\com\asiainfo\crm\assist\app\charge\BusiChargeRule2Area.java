package com.asiainfo.crm.assist.app.charge;


import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ISoQuerySMO;
import com.asiainfo.crm.service.intf.ISpecChargeSmo;
import com.asiainfo.crm.util.ListUtil;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("vita.busiChargeRule2Area")
public class BusiChargeRule2Area extends AbstractComponent {

    @Autowired
    private ISpecChargeSmo chargeSmo;

    @Autowired
    private ISoQuerySMO soQuerySMO;

    @Override
    public Map achieveData(Object... objects) throws Exception {
        return null;
    }

    public Map qryBusiChargeRule2AreaPageInfo(String params) throws Exception {
        String queryInfo = chargeSmo.queryBusiChargeRule2AreaPage(params);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(queryInfo);
        Map<String,Object> pageInfo = (Map<String, Object>) MapUtils.getObject(resultObjectMap, "pageInfo");
        List<Map> listData = (List) MapUtils.getObject(resultObjectMap, "listData");
        for (Map map : ListUtil.nvlList(listData)) {
            map.put("statusCd", AssistMDA.COMMON_STATUS_CD_MAP.get(MapUtils.getString(map,"statusCd")));
        }
        resultObjectMap.clear();
        resultObjectMap.put("reqList", listData);
        resultObjectMap.put("totalNumber", pageInfo.get("rowCount"));
        return resultObjectMap;
    }

    public Map createBusiChargeRule2Area(String params) throws Exception {
        String insertResult = chargeSmo.createBusiChargeRule2Area(params);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(insertResult);
        resultObjectMap.put("resultCode",MapUtils.getString(resultObjectMap, "resultCode"));
        resultObjectMap.put("resultMsg",MapUtils.getString(resultObjectMap, "resultMsg"));
        return resultObjectMap;
    }

    public Map updateBusiChargeRule2Area(String params) throws Exception {
        String updateResult = chargeSmo.updateBusiChargeRule2Area(params);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(updateResult);
        resultObjectMap.put("resultCode",MapUtils.getString(resultObjectMap, "resultCode"));
        resultObjectMap.put("resultMsg",MapUtils.getString(resultObjectMap, "resultMsg"));
        return resultObjectMap;
    }

    /**
     * 查询地区名称
     *
     * @param regionId
     * @return
     * @throws Exception
     */
    private String qryRegionName(String regionId) throws Exception {
        Map<String, Object> regionIdMap = new HashMap(1);
        regionIdMap.put("regionId", regionId);
        String ret = soQuerySMO.qryCommonRegionByIds(regionIdMap);
        String regionStr = (String) resolveResult(ret);
        Map<String, Object> regionMap = jsonConverter.toBean(regionStr, Map.class);
        String regionName = MapUtils.getString(regionMap, "regionName", "");
        return regionName;
    }

}
