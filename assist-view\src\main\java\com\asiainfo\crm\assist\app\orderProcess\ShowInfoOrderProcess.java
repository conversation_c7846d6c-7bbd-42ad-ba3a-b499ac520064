package com.asiainfo.crm.assist.app.orderProcess;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.asiainfo.appframe.ext.flyingserver.org.apache.commons.collections.MapUtils;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICrmTools;
import com.asiainfo.crm.service.intf.IOrderQueryMultiSMO;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.google.common.collect.Maps;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Component("vita.showInfoOrderProcess")
public class ShowInfoOrderProcess extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(ShowInfoOrderProcess.class);
    @Autowired
    private IOrderQuerySMO iOrderQuerySMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        String paramStr = (String)params[0];
        Map paramMap = jsonConverter.toBean(paramStr, Map.class);
        options = jsonConverter.toBean(MapUtils.getString(paramMap,"data"), Map.class);
        return options;
    }

    public Map queryCustomerOrderLogs(String params) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        Map req = jsonConverter.toBean(params,Map.class);
        req.put("contentType","1");
        String resultStr =iOrderQuerySMO.queryCustomerOrderLogs(JSON.toJSONString(req));
        List<Map> list = (List<Map>) resolveResult(resultStr);
        if (list != null && list.size()>0 ){
            options.put("data",list.get(0));
        }
        return  options;

    }

    public Map asynMsgInfo(String params) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        Map req = jsonConverter.toBean(params,Map.class);
        req.put("contentType","1");
        String resultStr =iOrderQuerySMO.queryCustomerOrderLogs(JSON.toJSONString(req));
        List<Map> list = (List<Map>) resolveResult(resultStr);
        if (list != null && list.size()>0 ){
            options.put("data",list.get(0));
        }
        return  options;

    }


//
//    @Override
//    public List<AsynMsgInfo> findAsynMsgInfoByCoIdAndMsgTopic(AsynInfoMsgReq req) {
//        String centerCode = req.getCenterCode();
//        String coId = req.getCoId();
//        String msgTopic = req.getMsgTopic();
//        List<Long> orderItems = req.getOrderItems();
//        Long ordGrpId = req.getOrdGrpId();
//        List<AsynMsgInfo> infos;
//        switch(centerCode){
//            case CrmToolMDA.NODE_CENTER_SO :
//                infos = orderQuerySmo.findAsynMsgInfoByCoIdAndMsgTopic(coId,msgTopic);
//                break;
//            case CrmToolMDA.NODE_CENTER_CUST :
//                infos = custQuerySmo.findAsynMsgInfoByCoIdAndMsgTopic(coId,msgTopic);
//                break;
//            case CrmToolMDA.NODE_CENTER_INST :
//                infos = instQuerySmo.findAsynMsgInfoByCoIdAndMsgTopic(coId,msgTopic);
//                break;
//            default :
//                infos = acctQuerySmo.findAsynMsgInfoByCoIdAndMsgTopic(coId,msgTopic);
//        }
//        if (msgTopic.equals(MsgTopicEnum.ACHIVE_GROUP_MSGTOPIC.getMsgTopic())){
//            for (AsynMsgInfo info : infos){
//                JSONObject obj = JSONObject.parseObject(info.getEvenInfo());
//                String businessNo = obj.getString("businessNo");
//                info.setMsgKey(businessNo);
//            }
//        }
//        infos = removeDiffRecord(infos,centerCode,orderItems,ordGrpId);
//        JsonConfig jsonConfig = new JsonConfig();
//        jsonConfig.registerJsonValueProcessor(Date.class, new JsonDateValueProcessor());
//        JSONArray itemVoArrays  = JSONArray.fromObject(infos,jsonConfig);
//        return  itemVoArrays;
//    }


}
