<div data-widget="epayBonusSendOperate" style="height:100%">

    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询条件</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                地 区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_channelId" type="checkbox" name="payment">
                                                </label> 渠道
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="channelId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="channelIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4 ">
                                            <label class="col-md-5 control-label lablep">
                                                <label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_staffId" type="checkbox" name="payment">
                                                </label> 营业员
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="staffId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="staffIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">
                                                  接入号
                                            </label>
                                            <div class="col-md-7">
                                                <input id="accNum" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">起始日期</label>
                                            <div class="col-md-7">
                                                <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">结束日期</label>
                                            <div class="col-md-7">
                                                <input name="endDate" id="endDate" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label class="col-md-5 control-label lablep">状态</label>
                                            <div class="col-md-7">
                                                <select id="state" class="form-control">
                                                    <option value="0">失败</option>
                                                    <option value="1">成功</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <div class="col-md-12 searchbutt_r" align="center">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">查&nbsp&nbsp&nbsp询</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <!--<div><i class="bot"></i>查询结果</div>-->
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12">
                                        <a id="btnSendAgain" class="btn btn-gray btn-outline"> <i class="glyphicon fa-details text18"> </i> 重新充值
                                        </a>
                                        <a id="btnSetStatus" class="btn btn-gray btn-outline"> <i class="glyphicon fa-request text18"> </i> 重置状态
                                        </a>
                                    </div>
                                    <div class="col-lg-12 mart10" id="epayBonusResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th><label class="wu-radio full absolute" data-scope="">
                                                    <input id="c_allResult" type="checkbox" name="resultList">
                                                </label></th>
                                                <th>原充值流水</th>
                                                <th>接入号码</th>
                                                <th>金额(元)</th>
                                                <th>状态</th>
                                                <th>状态时间</th>
                                                <th>最新应答码</th>
                                                <th>最新充值流水</th>
                                            </tr>
                                            </thead>
                                            <tbody id="epayBonusList">
                                            #if($options != "null" && $options.bonusList && $options.bonusList != "null" &&
                                            $options.bonusList.size() > 0)
                                            #foreach($bonus in $options.bonusList)
                                            <tr>
                                                <td><label class="wu-radio full absolute" data-scope=""><input
                                                        type="checkbox" name="payment">
                                                    <p class="vita-data">{"custOrderId":"$bonus.custOrderId","offerId":"$bonus.offerId"
                                                        ,"accNum":"$bonus.accNum","seq":"$bonus.seq","status":"$bonus.dealStatus"}</p>
                                                </label></td>
                                                <td>$!{bonus.acceptSeqNo}</td>
                                                <td>$!{bonus.accNum}</td>
                                                <td>$!{bonus.bonusAmount}</td>
                                                #if($bonus.dealStatus != "null" && $bonus.dealStatus == "D")
                                                <td>失败</td>
                                                #elseif($bonus.dealStatus != "null" && $bonus.dealStatus == "RD")
                                                <td>   <select  class="changeBonusStatus form-control">
                                                    <option value="RD">撤销失败</option>
                                                    <option value="D">失败</option>
                                                    <option value="C">成功</option>
                                                </select>  </td>
                                                #else
                                                <td>成功</td>
                                                #end
                                                <td>$!{bonus.statusDate}</td>
                                                <td>$!{bonus.respCode}</td>
                                                <td>$!{bonus.latestAcceptSeqNo}</td>
                                            </tr>
                                            #end
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
