(function (vita) {
    var transAcceptCertNumFive = vita.Backbone.BizView.extend({
        events: {
            "click #btn-qryCertNumFive": "_qryTransCertNumFive",
            "click #btn-configChoose": "_configChoose",
            "click #btn-readCert": "_readCert"
        },
        _initialize: function () {
            var widget = this,
                element = $(widget.el);
        },
        global: {
        	scanCertUrl: "../comm/scanCert",
        	configChoose: "../query/transCertNumFiveUpload",
            pageIndex: 1,
            pageSize: 15,
            preset: "date",
            op:""
        },
        _getConds: function () {
        	debugger;
            var widget = this;
            var gsession = widget.gSession;
            
            var param = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize,
                },
                regionId: gsession.staffRegionId,//地区id
                custType: $("#custType").val(),//证件类型
                custIdentity:$("#custIdentity").val(),
                custName: $("#custName").val(),//客户名
                custAddress: $("#custAddress").val(),//证件地址
                staffId: gsession.staffId,//员工id
                glbSessionId: gsession.glbSessionId
            };
            return param;
        },
        _isNullStr: function (str) {
            if (str == null || str == "") {
                return true;
            }
            return false;
        },
        _clear: function () {
            $("#value").val("");
        },
        _qryTransCertNumFive: function () {
            var widget = this, element = $(widget.el);
            var gsession = widget.gSession;
            var param = widget._getConds();
            if (param.custIdentity == null || param.custIdentity.trim() == "") {
                widget.popup("请输入证件号")
                return;
            }
            widget.refreshPart("queryTransCertNumFive", JSON.stringify(param), "#certNumTbody", function (res) {
              debugger;
              var data =  $(res).find("#transCertNumFiveList").data("data");
//              var transCertNumFive = data.transCertNumFiveList;
//              if(transCertNumFive != null && transCertNumFive != ""){
//                  var custType =transCertNumFive[0].valueAccNum.custType;
//                  var custIdentity = transCertNumFive[0].valueAccNum.custIdentity;
//                  var custName = transCertNumFive[0].valueAccNum.custName;
//                  var custAddress = transCertNumFive[0].valueAccNum.custAddress;
//                  widget.model.set("custType", custType);
//                  widget.model.set("custIdentity", custIdentity);
//                  widget.model.set("custName", custName);
//                  widget.model.set("custAddress", custAddress);
//              }
              var r = $(res), paging = this.require("paging");
              var e = paging.new({
                  recordNumber: widget.global.pageSize,
                  total: r.find("#showPageInfo").data("totalNumber"),
                  pageIndex: widget.global.pageIndex,
                  callback: function (pageIndex, pageSize) {
                      debugger;
                      var param = {
                          regionId: gsession.staffRegionId,//地区id
                          custType: $("#custType").val(),//证件类型
                          custIdentity:$("#custIdentity").val(),
                          custName: $("#custName").val(),//客户名
                          custAddress: $("#custAddress").val(),//证件地址
                          staffId: gsession.staffId,//员工id
                          glbSessionId: gsession.glbSessionId,
                          pageInfo: {
                              pageIndex: pageIndex,
                              pageSize: widget.global.pageSize,
                          }
                      };
                      widget.refreshPart("queryTransCertNumFive", JSON.stringify(param), "#certNumFiveListR");
                  }
              });
              r.find("#showPageInfo").append(e.getElement());

          }, {
              async: false
          });
            
        },
        _readCert: function () {

        	var widget = this;
			var option = {
				url : widget.global.scanCertUrl+"?certOperType=" + "add",
				onClose : function(res) {
					var scanCert = $(res).closest("[data-widget=scanCert]");
                    if (scanCert.length) {
                        
                        var cust = scanCert.scanCert("getValue");
                        widget.model.set("certInfo",cust);
                        if (!cust) {
                            return false;
                        }
                        widget._setCustomerInfo(cust);
                    }
				}
			};
			widget.dialog(option);
			return false;
        
        },
        _setCustomerInfo: function (data) {
        	var widget = this,
            global = widget.global,
            element = $(widget.el);
        	element.find("#custName").val(data.partyName);
            element.find("#custName").attr("disabled", true);
            element.find("#custIdentity").val(data.certNumber);
            element.find("#custIdentity").attr("disabled", true);
            element.find("#custAddress").val(data.certAddress);
            element.find("#custAddress").attr("disabled", true);
        },
        _configChoose: function (e) {
        	var widget = this, element = $(widget.el);
        	var gsession = widget.gSession;
        	var $selectChks = element.find('table input[name="checkCertFive"]:checked');
            if($selectChks.length == 0) {
                widget.popup("请选择数据！");
                return false;
            }
            var params = {};
            var phoneList = [];
            var phoneStr = "";
            var phoneNameStr = "";
            var areaId = gsession.staffRegionId;//受理地区
            var custType = 1;//证件类型
            var custIdentity = $("#custIdentity").val();//身份证号
            var custName = $("#custName").val();//客户名
            var custAddress = $("#custAddress").val();//证件地址
            var staffId = gsession.staffId;//员工id
            var channelNbr = gsession.curChannelNbr;//员工id
            var loginIp = gsession.loginIp;//登入ip
            var isDealData =false;
            $.each($selectChks, function(i, selectChk) {
            	var $tr = selectChk.parentElement.parentElement;
            	var datas = $($tr).find("[link=certNumFive]").data("certNumFive");
            	var regionId = datas.regionId;
            	var regionName = datas.regionName;
            	var postAddr = datas.postAddr;
            	var phoneNum = datas.phoneNum;
            	var dealStatus = datas.dealStatus;
                var phoneDetail={};
                if(dealStatus != '1000'){
                	isDealData = true;
                    return false;
                }
                phoneStr = phoneStr + phoneNum +",";
                phoneNameStr = phoneNameStr + phoneNum +"("+regionName+postAddr+"),";
                phoneDetail.accNbr = phoneNum;
                phoneDetail.lanId = regionId;
                phoneDetail.remarks = custName;
                phoneList.push(phoneDetail);
            });
            if(isDealData==true){
            	widget.popup("请选择未处理的数据！");
                return false;
            }
            if(phoneStr.length==0){
                widget.popup("请选择数据！");
                return false;
            }
            phoneStr = (phoneStr.substring(phoneStr.length - 1) == ',') ? phoneStr.substring(0, phoneStr.length - 1) : phoneStr ;
            phoneNameStr = (phoneNameStr.substring(phoneNameStr.length - 1) == ',') ? phoneNameStr.substring(0, phoneNameStr.length - 1) : phoneNameStr ;
            params.collectionItemInfos =  JSON.stringify(phoneList);
            params.phoneNameStr = phoneNameStr;
            params.phoneStr = phoneStr;
            params.areaId = areaId;
            params.custType = custType;
            params.custIdentity = custIdentity;
            params.custName = custName;
            params.custAddress = custAddress;
            params.staffId = staffId;
            params.channelNbr = channelNbr;
            params.loginIp = loginIp;
            
			var option = {
				url : widget.global.configChoose,
				params : params,
				onClose : function(res) {
					return false;
				}
			};
			widget.dialog(option);
			return false;
            
        }
    });
    vita.widget.register("transAcceptCertNumFive", transAcceptCertNumFive, true);
})(window.vita);