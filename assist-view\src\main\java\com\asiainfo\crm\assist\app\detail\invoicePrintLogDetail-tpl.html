<div data-widget="invoicePrintLogDetail" style="height: 100%">
    #macro(nullNotShow $val)#if($val && $val != "null")$!val#end#end
    <div class="calcw_rightbox noneleft">
        <div class="calctitle">
            <div class="titlefont">发票打印日志详情</div>
            <div class="toolr">
                <button id="closeBtn" type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
            </div>
        </div>
        <div class="calcw_rightcont" >
            #if($options.detailInfo && $options.detailInfo != "null")
            #set($detailInfo = $options.detailInfo)
            <div class="container-fluid">
                <h5 class="meal_htitle nobrder"><i class="bot"></i>日志信息</h5>
                <div class="row">
                    <div class="col-md-12 ">
                        <table class="table table-bordered conttd-w">
                            <tbody>
                            <tr>
                                <td class="labellcont">日志标识</td>
                                <td>#nullNotShow($detailInfo.logId)&nbsp;</td>
                                <td class="labellcont">发票代码</td>
                                <td>#nullNotShow($detailInfo.receiptCode)&nbsp;</td>
                            </tr>
                            <tr>
                                <td class="labellcont">入参(调接口参数)</td>
                                <td colspan="3">
                                    <textarea class="form-control" rows="3">#nullNotShow($detailInfo.paramIn)</textarea>
                                </td>
                            </tr>
                            <tr>
                                <td class="labellcont">出参(接口返回)</td>
                                <td colspan="3">
                                    <textarea class="form-control" rows="3">#nullNotShow($detailInfo.paramOut)</textarea>
                                </td>
                            </tr>
                            <tr>
                                <td class="labellcont">原因</td>
                                <td>#nullNotShow($detailInfo.reason)&nbsp;</td>
                                <td class="labellcont">日志记录时间</td>
                                <td>#nullNotShow($detailInfo.logDate)&nbsp;</td>
                            </tr>
                            <tr>
                                <td class="labellcont">日志类型</td>
                                <td>
                                    #if($options != "null" && $options.logTypeMap != "null" && $options.logTypeMap.size() > 0)
                                        #foreach($item in $!options.logTypeMap.entrySet())
                                            #if($!{item.key} == $!{detailInfo.logType})
                                            $!{item.value}
                                            #end
                                        #end
                                    #end
                                </td>
                                <td class="labellcont">订单流水</td>
                                <td>#nullNotShow($detailInfo.custOrderNbr)&nbsp;</td>
                            </tr>
                            <tr>
                                <td class="labellcont">状态</td>
                                <td>
                                    #if($options != "null" && $options.statusCdMap != "null" && $options.statusCdMap.size() > 0)
                                        #foreach($item in $!options.statusCdMap.entrySet())
                                            #if($!{item.key} == $!{detailInfo.statusCd})
                                            $!{item.value}
                                            #end
                                        #end
                                    #end
                                </td>
                                <td class="labellcont">营业员名称</td>
                                <td>#nullNotShow($detailInfo.staffName)&nbsp;</td>
                            </tr>
                            <tr>
                                <td class="labellcont">受理渠道</td>
                                <td>#nullNotShow($detailInfo.createOrgName)&nbsp;</td>
                                <td class="labellcont">更新时间</td>
                                <td>#nullNotShow($detailInfo.statusDate)&nbsp;</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            #end
        </div>
    </div>
</div>