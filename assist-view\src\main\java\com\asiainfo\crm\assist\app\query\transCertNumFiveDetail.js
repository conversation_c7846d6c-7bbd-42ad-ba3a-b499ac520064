(function (vita) {
    var transCertNumFiveDetail = vita.Backbone.BizView.extend({
        events: {
        	 "click #showImage": "_showImage",
        	 "click #dealOrder": "_dealOrder",
        	 "click #backOrder": "_backOrder",
        	 "click #cancelOrder": "_cancelOrder",
        	 "click #successOrder": "_successOrder"
        },
        _initialize: function () {
            var widget = this,
                element = $(widget.el);

        },
        global: {
            pageIndex: 1,
            pageSize: 15,
            preset: "date",
            op:"",
        },
        _getConds: function () {
            var widget = this;
            var gsession = widget.gSession;
            
            var param = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize,
                },
                regionId: gsession.staffRegionId,//地区id
                
                staffId: gsession.staffId,//员工id
            };
            return param;
        },
        _isNullStr: function (str) {
            if (str == null || str == "") {
                return true;
            }
            return false;
        },
        _clear: function () {
            $("#value").val("");
        },
       
        _showImage: function () {
        	var widget = this, element = $(widget.el);
            var gsession = widget.gSession;
            var data = element.data("data");
        	 var params = {
             		orderNbr: data.transCertNumFiveDetail.orderNbr,//订单流水
             		areaId: gsession.staffRegionId,//受理地区id
             		regionId: gsession.staffRegionId,//受理地区id
             		picFlags: "F,E,E1,E2,E3",//处理状态
             		staffCode: data.transCertNumFiveDetail.staffCode,//员工编码
                     
                 };
			widget.refreshPart("queryImageDetail", params, "#imageDiv", "", function(res) {
				
			},{
				async: false
			});

        },
        
        _cancelOrder: function (e) {
        	var widget = this;
        	aEl = $(e.target).closest("button");
            var orderNbr = aEl.data("order");
            var collectionItemId = aEl.data("collectionItemId");
            var accNbr = aEl.data("accNbr");
            var dealStatus="100004";
            widget._sumitOrder(orderNbr,collectionItemId,accNbr,dealStatus);
        },
        _dealOrder: function (e) {
        	var widget = this;
        	aEl = $(e.target).closest("button");
            var orderNbr = aEl.data("order");
            var collectionItemId = aEl.data("collectionItemId");
            var accNbr = aEl.data("accNbr");
            var dealStatus="201300";
            widget._sumitOrder(orderNbr,collectionItemId,accNbr,dealStatus);
        },
        _backOrder: function (e) {
        	var widget = this;
        	aEl = $(e.target).closest("button");
            var orderNbr = aEl.data("order");
            var collectionItemId = aEl.data("collectionItemId");
            var accNbr = aEl.data("accNbr");
            var dealStatus="100002";
            widget. _sumitOrder(orderNbr,collectionItemId,accNbr,dealStatus);
        },
        _successOrder: function (e) {
        	var widget = this;
        	aEl = $(e.target).closest("button");
            var orderNbr = aEl.data("order");
            var collectionItemId = aEl.data("collectionItemId");
            var accNbr = aEl.data("accNbr");
            var dealStatus="301200";
            widget._sumitOrder(orderNbr,collectionItemId,accNbr,dealStatus);
        },
        _sumitOrder : function(order,collectionItemId,accNbr,dealStatus){
        	var widget = this, element = $(widget.el);
        	var data = element.data("data");
        	var certNumber = data.transCertNumFiveDetail.certNumber;//身份证号码
            var gsession = widget.gSession;
            var params = {
            		orderNbr: order,//订单流水
            		collectionItemId: collectionItemId,//详细订单id
            		areaId: gsession.staffRegionId,//受理地区id
            		regionId:gsession.staffRegionId,//受理地区id
            		accNbr: accNbr,//接入号
            		statusCd: dealStatus,//处理状态
            		channelNbr: gsession.curChannelNbr,//受理渠道
            		staffCode: gsession.staffCode, //员工编码
            		certNumber: certNumber, //身份证号码
                    remarks: "订单修改", //备注
                    glbSessionId: gsession.glbSessionId
                    
                };
            widget.callService("updateTransCertNumFive", params, function(res) {
            	widget.popup(res.retDesc);
				return;
            }, {
            	async: false 
            });
        }
    });
    vita.widget.register("transCertNumFiveDetail", transCertNumFiveDetail, true);
})(window.vita);