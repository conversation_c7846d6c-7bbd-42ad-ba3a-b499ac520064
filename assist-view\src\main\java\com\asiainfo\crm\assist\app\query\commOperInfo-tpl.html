<div data-widget="commOperInfo" style="height:100%;">
	<div class="pace-done wrapper mini-rightmax no-minright">
		<div class="box-maincont">
			<div class="homenofood">
				<div class="page_main notopnav">
					<div class="col-lg-12">
						<div class="box-item">
						   <div class="container-fluid row">
							<div class="form_title">
								<div><i class="bot"></i>公共日志查询</div>
							</div>
							<div class="wmin_content row">
								<form class=" form-bordered">
									<div class="form-group col-md-4 ">
										<label class="col-md-5 control-label lablep">
											<label class="wu-checkbox full absolute" data-scope="">
												<input id="c_staffId" type="checkbox" name="payment">
											</label> 员工
										</label>
										<div class="col-md-7">
											<div class="input-group">
												<input id="staffId" type="text" class="form-control" placeholder="" readonly="readonly">
												<div class="input-group-btn">
													<button id="staffIdBtn" class="btn btn-green" type="button">选择</button>
												</div>
											</div>
										</div>
									</div>
									<div class="form-group col-md-4 ">
										<label class="col-md-5 control-label lablep">
											<label class="wu-checkbox full absolute" data-scope="">
												<input id="c_channelId" type="checkbox" name="payment">
											</label> 渠道
										</label>
										<div class="col-md-7">
											<div class="input-group">
												<input id="channelId" type="text" class="form-control" placeholder="" readonly="readonly">
												<div class="input-group-btn">
													<button id="channelIdBtn" class="btn btn-green" type="button">选择</button>
												</div>
											</div>
										</div>
									</div>
									<div class="form-group col-md-4 ">
										<label class="col-md-5 control-label lablep">
											<label class="wu-checkbox full absolute" data-scope="">
												<input id="c_regionId" type="checkbox" name="payment">
											</label> 地区
										</label>
										<div class="col-md-7">
											<div class="input-group">
												<input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
												<div class="input-group-btn">
													<button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
												</div>
											</div>
										</div>
									</div>
									<div class="form-group col-md-4 ">
										<label class="col-md-5 control-label lablep">
											<label class="wu-checkbox full absolute" data-scope="">
												<input id="c_custId" type="checkbox" name="payment">
											</label> 客户
										</label>
										<div class="col-md-7">
											<div class="input-group">
												<input id="custId" type="text" class="form-control" placeholder="" readonly="readonly">
												<div class="input-group-btn">
													<button id="custIdBtn" class="btn btn-green" type="button">选择</button>
												</div>
											</div>
										</div>
									</div>
									<div class="form-group col-md-4 ">
										<label class="col-md-5 control-label lablep">
											<label class="wu-checkbox full absolute" data-scope="">
												<input id="c_dealPoint" type="checkbox" name="payment">
											</label> 操作类型
										</label>
										<div class="col-md-7">
											<select id="dealPoint" class="form-control">
												<option value="">请选择</option>
												#foreach($item in $!options.commOperInfoDealPoints.entrySet())
													<option value="$!{item.key}">$!{item.value}</option>
												#end
											</select>
										</div>
									</div>
									<div class="form-group col-md-12" id="qryTimeQuantum">
										<label class="col-md-2 control-label lablep">起止时间</label>
										<div class="col-md-10 form-inline">
											<div class="form-group">
												<input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="开始时间" readonly>
											</div>
											<div class="form-group">
												<input name="endDate" id="endDate" type="text" class="form-control" placeholder="结束时间" readonly>
											</div>
											<button type="button" id="timeQuantum_1" name="timeQuantum_1" class="btn btn-gray btn-outline btn-sm">当天</button><button type="button" id="timeQuantum_7" name="timeQuantum_7" class="btn btn-gray btn-outline btn-sm">七天</button><button type="button" id="timeQuantum_30" name="timeQuantum_30" class="btn btn-gray btn-outline btn-sm">一个月</button><button type="button" id="timeQuantum_90" name="timeQuantum_90" class="btn btn-gray btn-outline btn-sm">三个月</button>
										</div>
									</div>
									<div class="form-group col-md-11">
										<div class="col-md-12 searchbutt_r" align="right">
											<label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
											<button id="btn-query" type="button" class="btn btn-primary">查询</button>
											<label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp</label>
											<button id="btn-clear" type="button" class="btn btn-white">清除</button>
										</div>
									</div>
									</form>
								</div>
						  </div>
							<div class="container-fluid row">
								<div class="form_title">
									<div><i class="bot"></i>公共日志列表</div>
								</div>
								<div  class="wmin_content" id="searchList">
									<p class="vita-data">{"totalNumber" : "$options.totalNumber"}</p>
									<div  class="col-lg-12 mart10 ">
										<table class="table table-hover">
											<thead>
											<tr>
												<th>渠道</th>
												<th>地区</th>
												<th>客户</th>
												<th>操作入口点</th>
												<th>操作MAC</th>
												<th>员工</th>
												<th>授权员工</th>
												<th>是否脱敏</th>
												<th>权限编码</th>
												<th>操作结果</th>
												<th>操作时间</th>
											</tr>
											</thead>
											<tbody id="searchList4Tbody">
											#if($options != "null" && $options.commOperInfos && $options.commOperInfos != "null" && $options.commOperInfos.size() > 0)
												#foreach($commOperInfo in $options.commOperInfos)
												<tr>
													<td>$!{commOperInfo.channelName}</td>
													<td>$!{commOperInfo.regionName}</td>
													<td>$!{commOperInfo.custName}</td>
													<td>$!{commOperInfo.dealPoint}</td>
													<td>$!{commOperInfo.mac}</td>
													<td>$!{commOperInfo.staffName}</td>
													<td>$!{commOperInfo.agentStaffName}</td>
													<td>$!{commOperInfo.isDesensName}</td>
													<td>$!{commOperInfo.authCode}</td>
													<td>$!{commOperInfo.resultTypeName}</td>
													<td>
														<a class="textcolorgreen" link="toCommOperInfoDetail">
															$!{commOperInfo.createDate}
														</a>
														<p class="vita-data">{"commOperInfoLogId" : "$!{commOperInfo.commOperInfoLogId}"}</p>
													</td>
												</tr>
												#end
											#end
											</tbody>
										</table>
									</div>
									<div class="row">
										<div class="page-box" id="showPageInfo"></div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>