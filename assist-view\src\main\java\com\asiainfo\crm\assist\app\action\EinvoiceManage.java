package com.asiainfo.crm.assist.app.action;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICustSMO;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.asiainfo.crm.service.intf.ISoReceiptSMO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by wenhy on 2017/7/19.
 */
@Component("vita.einvoiceManage")
public class EinvoiceManage extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(AllowanceManage.class);

    @Autowired
    private IProdInstSMO prodInstSMO;

    @Autowired
    private ICustSMO custSMO;

    @Autowired
    private ISoReceiptSMO soReceiptSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map einvoiceSign(String jsonStr) throws Exception {
        String ret = soReceiptSMO.saveForSignInvoiced(jsonStr);
        Map resultObject = (Map) resolveResult(ret);
        return resultObject;
    }

    public Map einvoiceSignConfirm(String jsonStr) throws Exception {
        String ret = soReceiptSMO.updateInvoicedForSign(jsonStr);
        Map resultObject = (Map) resolveResult(ret);
        return resultObject;
    }

    public Map einvoiceUnSign(String jsonStr) throws Exception {
        String ret = soReceiptSMO.updateInvoicedForCancel(jsonStr);
        Map resultObject = (Map) resolveResult(ret);
        return resultObject;
    }

    public Map queryCust(String jsonStr) throws Exception {
        Map<String, Object> paramMap = jsonConverter.toBean(jsonStr, Map.class);
        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<Long> custIds = new ArrayList<Long>();
        if(null != paramMap.get("accNum")) {
            String re = prodInstSMO.qryAccProdInstListLocal(jsonStr);
            Map resultObject = (Map) resolveResult(re);
            List<Map> prodInsts = (List<Map>) resultObject.get("accProdInsts");
            if(null != prodInsts && prodInsts.size() > 0) {
                for(Map map :prodInsts) {
                    custIds.add(Long.valueOf(map.get("ownerCustId").toString()));
                }
                paramMap.put("custIds", custIds);
            } else {
                resultMap.put("resultCode", "-1");
                resultMap.put("resultMsg", "根据接入号未查询到产品实例！");
                return resultMap;
            }
        }
        paramMap.put("attrNbr", AssistMDA.EINVOICE_SPEC_CUST_ATTR_NBR);
        String custStr = custSMO.qrySpecCustList(jsonConverter.toJson(paramMap));
        Map resultObject = (Map) resolveResult(custStr);
        return resultObject;
    }

    public Map markSpecCust(String jsonStr) throws Exception {
        Map<String, Object> paramMap = jsonConverter.toBean(jsonStr, Map.class);
        paramMap.put("attrNbr", AssistMDA.EINVOICE_SPEC_CUST_ATTR_NBR);
        String ret = custSMO.updateMarkSpecCust(jsonConverter.toJson(paramMap));
        Map resultObject = (Map) resolveResult(ret);
        return resultObject;
    }

}
