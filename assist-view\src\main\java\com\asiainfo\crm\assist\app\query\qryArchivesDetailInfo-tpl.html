<div data-widget="qryArchivesDetailInfo" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div id="myTabContent" class="tab-content">
        #if($options.operClick == "Y")
        <div class="tab-pane fade active in" id="operDiv">
            #if($options != "null" && $options.operPhotoList && $options.operPhotoList != "null" &&
            $options.operPhotoList.size() > 0)
            #foreach($operInfo in $options.operPhotoList)
            #if($operInfo.cardType && $operInfo.cardType == "Y")
            #set($type = $operInfo.prodName + "主卡")
            #elseif($operInfo.cardType && $operInfo.cardType == "N")
            #set($type = $operInfo.prodName + "副卡")
            #else
            #set($type = $operInfo.prodName)
            #end
            <div class="col-lg-12 " style="padding-top: 10px">
                #if($operInfo != "null" && $operInfo.accNum && $operInfo.accNum != "null")
                <h5 class="paddlr15">接入号：<span>$!{operInfo.accNum}</span>(<span>$!{type}</span>)</h5>
                #else
                <h5 class="paddlr15">经办人照片:</h5>
                #end
                #if($operInfo != "null" && $operInfo.operPhotoList && $operInfo.operPhotoList != "null" &&
                $operInfo.operPhotoList.size() > 0)

                #foreach($operPhoto in $operInfo.operPhotoList)
                #set($picSrc = "data:image/jpg;base64,"+$operPhoto.result)
                <div class="col-xs-2" name="operShowDiv">
                    <p><span style="display:none">$!operPhoto.busiTypeName</span><img src="$!picSrc" style="width:100%;height: 220px;"></p>
                    <button type="button" class="btn btn-primary btn-sm okbutt btn-block">下载</button>
                    <p class="vita-data">{"custOrderNbr" : "$!options.custOrderNbr", "accNum" : "$!operInfo.accNum",
                        "busiType" : "$!operPhoto.busiType"}</p>
                </div>
                #end
                #end
            </div>
            #end
            #else
            <div class="col-xs-11" style="padding-top: 10px">
                <h5 class="paddlr15">未查询到数据！</h5>
            </div>
            #end
        </div>
        #end
        #if($options.receiptClick == "Y")
        <div class="tab-pane fade active in" id="receiptDiv">
            #if($options != "null" && $options.receiptPhoto && $options.receiptPhoto != "null")
            #set($picSrc = "data:application/pdf;base64,"+$options.receiptPhoto.result)
            <div class="col-lg-12 ">
                <div class="form-group col-md-12" id="qryTimeQuantum" style="padding-top: 10px">
                    <label class="col-md-2 control-label lablep">
                        回执预览</label>
                    <div class="col-md-10 form-inline" style="padding-left: 550px">
                        <div class="form-group">
                            <input id="emailAddr" type="text" class="form-control" placeholder="请输入邮箱地址">
                        </div>
                        <button type="button" id="sendEmail" class="btn btn-green">发送邮件</button>
                        <p class="vita-data">{"custOrderNbr" : "$!options.custOrderNbr"}</p>
                    </div>
                </div>
                <div class="col-xs-11">
                    <p>
                        #if($options.loadByIframe && $options.loadByIframe == "Y")
                        <iframe name="myPdf" style="width: 100%; height: 1000px;border: none;"></iframe>
                        <a id="go" href="" target="myPdf"><span></span></a>
                        #else
                        <iframe src="$!picSrc" style="width: 100%; height: 1000px;border: none;"></iframe>
                        #end
                    </p>
                    <button id="receiptDownload" type="button" class="btn btn-primary btn-sm okbutt">下载</button>
                    <p class="vita-data">{"custOrderNbr" : "$!options.custOrderNbr", "busiType" :
                        "$!options.receiptPhoto.busiType"}</p>
                </div>
            </div>
            #else
            <div class="col-xs-11" style="padding-top: 10px">
                <h5 class="paddlr15">未查询到数据！</h5>
            </div>
            #end
        </div>
        #end
    </div>
</div>