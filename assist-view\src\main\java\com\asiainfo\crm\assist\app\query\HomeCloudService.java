package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IOdServiceSMO;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by pushuo on 2017/7/24.
 */
@Component("vita.homeCloudService")
public class HomeCloudService extends AbstractComponent {

    @Autowired
    private IOdServiceSMO odServiceSmo;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }


    public Map homeCloudService(String jsonStr) throws Exception {
        String homeCloudInfoStr = odServiceSmo.queryHomeCloud(jsonStr);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(homeCloudInfoStr);
        Map<String, Object> options = Maps.newHashMap();
        options.put("homeCloudInfo", resultObjectMap);
        return options;
    }

}
