package com.asiainfo.crm.assist.app.photodownload;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.assist.model.FileDownLoad;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.util.SftpUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpATTRS;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;

@Component("vita.showdownloadpage")
public class Showdownloadpage extends AbstractComponent {

    private final Logger logger = LoggerFactory.getLogger(Showdownloadpage.class);

    @Override
    public Map achieveData(Object... objects) throws Exception
    {
        return null;
    }

    public Map queryFiles(String json) throws Exception {
        Map<String, Object> resultObjectMap = jsonConverter.toBean(json, Map.class);
        Map<String, Object> options = getFiles();
        Map fileOptions = Maps.newHashMap();
        Map <String, Integer> resultMap = jsonConverter.toMap(jsonConverter.toJson(resultObjectMap
                        .get("pageInfo")),String.class, Integer.class);
        jsonConverter.toJson(resultObjectMap.get("pageInfo"));
        Integer pageSize = resultMap.get("pageSize");
        Integer pageIndex = resultMap.get("pageIndex");
        int totalNum = Integer.parseInt(options.get("totalNumber").toString());
        List< FileDownLoad >  fileList = (List) options.get("files");
        int stratNum = (pageIndex - 1) * pageSize;
        int forNum = pageSize * pageIndex;
        List< FileDownLoad > listNum = Lists.newArrayList();
        if (forNum > totalNum)
        {
            forNum = totalNum;
        }
        for (int i = stratNum; i < forNum; i++)
        {
            listNum.add(fileList.get(i));
        }
        fileOptions.put("totalNumber", totalNum);
        fileOptions.put("pageSize", pageSize);
        fileOptions.put("pageIndex", pageIndex);
        fileOptions.put("files", listNum);
        return fileOptions;
    }

    public Map getFiles() {
        ChannelSftp sftp = null;
        List< FileDownLoad >  fileList = Lists.newArrayList();
        Map options = Maps.newHashMap();
        int totalNumber;
        try {
            sftp = SftpUtil.getSftpConnect(AssistMDA.FTP_IPADDR, Integer.parseInt(AssistMDA.FTP_PORT)
                    ,AssistMDA.FTP_USERNAME , AssistMDA.FTP_PWD);
            Vector files = sftp.ls(AssistMDA.FTP_PATH);
            for (Iterator<ChannelSftp.LsEntry> iterator = files.iterator();
                 iterator.hasNext();) {
                ChannelSftp.LsEntry str = iterator.next();
                String filename = str.getFilename();
                if (filename.equals(".") || filename.equals("..") ||
                        str.getAttrs().isDir()) {
                    continue;
                }
                SftpATTRS attrs = str.getAttrs();
                FileDownLoad fileDownLoad = new FileDownLoad();
                String size = SftpUtil.getPrintSize(attrs.getSize());
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Long longTime = new Long(attrs.getMTime() * 1000L);
                Date date = new Date(longTime);
                String fileType= filename.substring(filename.length() - 3, filename.length());
                fileDownLoad.setFileName(filename);
                fileDownLoad.setDate(simpleDateFormat.format(date));
                fileDownLoad.setSize(size);
                fileDownLoad.setType(fileType);
                fileDownLoad.setPath(AssistMDA.FTP_PATH);
                fileList.add(fileDownLoad);
            }
            totalNumber = fileList.size();
            options.put("totalNumber",totalNumber);
            options.put("files",fileList);

        } catch (Exception e) {
            logger.error("SFTP通道初始化失败:", e);
        }
        finally {
            if (sftp != null)
            {
                try{
                    sftp.disconnect();
                }catch (Exception e)
                {
                    logger.error("SFTP通道关闭失败:", e);
                }
            }
        }
        return options;
    }
}
