package com.asiainfo.crm.assist.app.query;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.al.common.utils.StringUtil;
import com.asiainfo.angel.invoke.http.HttpInvoke;
import com.asiainfo.angel.invoke.http.HttpResponse;
import com.asiainfo.crm.bcomm.BCommMDA;
import com.asiainfo.crm.bcomm.exception.BError;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.util.XmlConverUtil;

/**
 * Created by niewq on 2018/6/20.
 */
@Component("vita.transCertNumFiveUpload")
public class TransCertNumFiveUpload extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(TransCertNumFiveUpload.class);
	@Autowired
	private ICustMultiSMO custMultiSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    @SuppressWarnings("unchecked")
	public Map<String,Object> transCertNumFiveDeal(String jsonStr) throws Exception {
    	logger.debug(jsonStr);
    	Map<String,Object> params = jsonConverter.toBean(jsonStr,Map.class);
    	Map<String, Object> retMap = new HashMap<String, Object>();
    	//(1)先调用批量上传图片
    	Map<String,Object> uploadParams = new HashMap<String, Object>();
    	uploadParams.put("areaId", params.get("areaId"));
    	uploadParams.put("regionId", params.get("areaId"));
    	uploadParams.put("clientIp", params.get("clientIp"));
    	uploadParams.put("orderNbr", params.get("orderNbr"));
    	uploadParams.put("remarks", params.get("remarks"));
    	uploadParams.put("staffCode", params.get("staffCode"));
    	List<Map<String,Object>> picturesInfo = new ArrayList<Map<String,Object>>();
    	Map<String,Object> fInfo = new HashMap<String, Object>();
    	//F表示回执图片，请求类型1;
    	fInfo.put("picFlag", "F");
    	String fImageStr = queryImageByOrder(params.get("orderNbr").toString(),"1");
    	if(StringUtils.isEmpty(fImageStr)){
			retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "没有找到回执信息");
    		return retMap;
		}
    	fInfo.put("picture",fImageStr);
    	//e1表示身份证证件 请求类型2
    	Map<String,Object> e1Info = new HashMap<String, Object>();
    	String e1ImageStr = queryImageByOrder(params.get("orderNbr").toString(),"2");
    	if(StringUtils.isEmpty(e1ImageStr)){
			retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "没有找到身份证信息");
    		return retMap;
		}
    	e1Info.put("picFlag", "E1");
    	e1Info.put("picture", e1ImageStr);
//    	Map<String,Object> e2Info = new HashMap<String, Object>();
//    //	String e2ImageStr = queryImageByOrder(params.get("orderNbr").toString(),"1");
//    	e2Info.put("picFlag", "E2");
//    	e2Info.put("picture", params.get("E2"));
    	//e3表示经办人拍照，请求类型为13
    	Map<String,Object> e3Info = new HashMap<String, Object>();
    	String e3ImageStr = queryImageByOrder(params.get("orderNbr").toString(),"51");
    	e3Info.put("picFlag", "E3");
    	e3Info.put("picture", e3ImageStr);
//    	Map<String,Object> eInfo = new HashMap<String, Object>();
//    	String eImageStr = queryImageByOrder(params.get("orderNbr").toString(),"1");
//    	eInfo.put("picFlag", "E");
//    	eInfo.put("picture", params.get("E"));
    	picturesInfo.add(fInfo);
    	picturesInfo.add(e1Info);
    	//picturesInfo.add(e2Info);
    	picturesInfo.add(e3Info);
    	//picturesInfo.add(eInfo);
    	uploadParams.put("picturesInfo", picturesInfo);

    	//(2)提交申请
    	Map<String,Object> reqParams = new HashMap<String, Object>();
    	reqParams.put("addressStr", params.get("addressStr"));
    	reqParams.put("areaId", params.get("areaId"));
    	reqParams.put("regionId", params.get("areaId"));
    	reqParams.put("certNumber", params.get("certNumber"));
    	reqParams.put("certType", params.get("certType"));
    	reqParams.put("channelNbr", params.get("channelNbr"));
    	reqParams.put("collectionItemInfos", params.get("collectionItemInfos"));
    	reqParams.put("contactNbr", params.get("contactNbr"));
    	reqParams.put("custName", params.get("custName"));
    	reqParams.put("orderNbr", params.get("orderNbr"));
    	reqParams.put("remarks", params.get("remarks"));
    	reqParams.put("staffCode", params.get("staffCode"));
    	reqParams.put("clientIp", params.get("clientIp"));
    	
    	try{
    		String resultJson = custMultiSMO.transOneCertNumFiveAccept(jsonConverter.toJson(reqParams));
        	Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(resultJson);
        	String uploadResultJson = custMultiSMO.addTransOneCertNumFiveFileUpload(jsonConverter.toJson(uploadParams));
        	resolveResult(uploadResultJson);
        	String msg ="";
        	if("0".equals(resultObjectMap.get("resultCode"))){
        		msg="操作成功";
        	}else{
        		msg=""+resultObjectMap.get("resultMsg");
        	}
        	retMap.put("retCode", resultObjectMap.get("resultCode"));
			retMap.put("retDesc", msg);
			return retMap;
    	} catch (Exception e) {
 			retMap.put("retCode", "-1");
			if (e instanceof BError){
	            BError bError = (BError)e;
	            retMap.put("retDesc", bError.getMsg());
			}else{
				retMap.put("retDesc", e.getMessage());
			}
			return retMap;
 		}
    }

 
    /**
     * 根据订单流水号，去无纸化服务查询图片
     * @param orderNbr 订单流水号
     * @param imageType 图片类型 1，回执pdf  2：身份证照片  51：经办人拍照
     * @return String base64的图片
     */
    private String queryImageByOrder(String orderNbr,String imageType){
    	String body = getBodyByOrder(orderNbr,imageType);
    	//拼装成xml的报文格式
    	String sendXml = getRequestXml(body);
    	//请求无纸化接口获取图片base64信息；
    	 Map<String, String> headerMap = new HashMap<String, String>();
         headerMap.put("Content-Type", "text/xml;charset=UTF-8");
         //无纸化服务地址
         String url = MDA.TRANS_ELE_PRINT_URL.get("serviceUrl_ES_QueryImage");
         int timeout = Integer.parseInt(BCommMDA.REMOTE_INVOKE_TIMEOUT);
         HttpResponse resp = HttpInvoke.sendRequest(url, headerMap, sendXml, timeout, null);
 		String responseSOAP = resp.getContent().trim();
 		//System.out.println(responseSOAP);
 		Map<String, Object> resultObject;
 		//解析返回报文
 		String picData = "";
		try {
			resultObject = XmlConverUtil.xmlToMap(responseSOAP);
			Map<String, Object> bodyObject=(Map<String, Object>) resultObject.get("Body");
			Map<String, Object> picResponse = (Map<String, Object>) bodyObject.get("qryPicDataForCertFiveResponse");
			String outJson =  picResponse.get("out").toString();
			Map<String,Object> outObject = jsonConverter.toBean(outJson,Map.class);
	 		String resultCode = MapUtils.getString(outObject, "code");
	 		if("1".equals(resultCode)){
	 			List<Map<String, Object>> msgObject =  (List<Map<String, Object>>)outObject.get("resMsg");
	 			picData = msgObject!=null&&msgObject.size()>0?MapUtils.getString(msgObject.get(0), "picData"):"";
	 			if(!StringUtil.isEmpty(picData)){
	 				picData=picData.replaceAll("\n", "");
	 			}
	 		}
		} catch (Exception e) {
			picData = "";
			logger.error("调用查询无纸化图片出现异常", e);
		}
   	return picData;
   }
    
    /**
     * 根据参数获取json格式字符串
     * @param orderNbr 订单编号
     * @param imageType 图片类型
     * @return
     */
    private String getBodyByOrder(String orderNbr,String imageType){
    	JSONObject json = new JSONObject();
		json.put("busiNbr", orderNbr);
		json.put("esDataTypeCd", imageType);
		return json.toString();
    }
    
    /**
     * 根据json格式封装成xml报文
     * @param body json格式参数
     * @return xml请求报文
     */
    private String getRequestXml(String body){
    	 StringBuilder sbff = new StringBuilder();
         sbff.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ifac=\"http://iface.wsutil.signature.crm.bss.linkage.com\">")
         .append("<soapenv:Header/>")
         .append("<soapenv:Body>")
         .append("<ifac:qryPicDataForCertFive>")
         .append("<ifac:in0><![CDATA[")
         .append(body)
         .append("]]></ifac:in0>")
         .append("</ifac:qryPicDataForCertFive>")
         .append("</soapenv:Body>")
         .append("</soapenv:Envelope>");
    	return sbff.toString();
    }
}
