<div data-widget="orderAuditHis" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="calcw_rightbox noneleft">
        <div class="calctitle">
            <div class="titlefont">实名制甩单审核历史</div>
            <div class="toolr">
                <button id="closeBtn" type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">×</span></button>
            </div>
        </div>
        <div class="calcw_rightcont">
            <div class="container-fluid mart10">
                <div class="wmin_content">
                    <div class="col-lg-12 mart10" id="orderListResult">
                        <table class="table table-hover">
                            <thead>
                            <tr>
                                <th>序号</th>
                                <th>购物车流水</th>
                                <th>购物车ID</th>
                                <th>工单状态</th>
                                <th>创建时间</th>
                                <th>审核人</th>
                                <th>审核人工号</th>
                                <th>审核人渠道</th>
                                <th>备注</th>
                            </tr>
                            </thead>
                            <tbody id="orderAuditHisInfos">
                            #if($options != "null" && $options.orderAuditHisInfos &&
                            $options.orderAuditHisInfos != "null" &&
                            $options.orderAuditHisInfos.size() > 0)
                            #foreach($info in $options.orderAuditHisInfos)
                            #if($velocityCount == $options.orderAuditHisInfos.size())
                            #set($mystyle = "style='color:#02c153'")
                            #else
                            #set($mystyle = "style=''")
                            #end
                            <tr $mystyle>
                                <td>$!{velocityCount}</td>
                                <td>$!{info.custOrderNbr}</td>
                                <td>$!{info.custOrderId}</td>
                                <td>$!{info.auditResultName}</td>
                                <td>$!{info.statusDate}</td>
                                <td>$!{info.auditorName}</td>
                                <td>$!{info.staffCode}</td>
                                <td>$!{info.channelName}</td>
                                #if($info.auditResult && $info.auditResult != "null" && $info.auditResult == "3")
                                #set($ps = "审核不通过原因：")
                                #else
                                #set($ps = "")
                                #end
                                <td><span>$!{ps}</span><span>$!{info.remark}</span></td>
                            </tr>
                            #end
                            #elseif($options != "null" && $options.orderAuditHisInfos &&
                            $options.orderAuditHisInfos != "null" &&
                            $options.orderAuditHisInfos.size() == 0)
                            <tr>
                                <td align='center' colspan='8'>未查询到数据！
                                </td>
                            </tr>
                            #end
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
