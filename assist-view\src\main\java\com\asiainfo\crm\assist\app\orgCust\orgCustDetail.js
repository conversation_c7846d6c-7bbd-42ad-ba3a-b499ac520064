(function (vita) {
    var orgCustDetail = vita.Backbone.BizView.extend({
        events: {
        },
        _initialize: function () {
            var widget = this;
             ele = $(widget.el);
            var params = {
                pageInfo: {
                    pageIndex: 1,
                    pageSize: 10
                },
                importBatchId: ele.data("importBatchId")
            };
            widget.refreshPart("queryOrgDetailInfos", JSON.stringify(params), "#auditAdjustInfoListResult", function (res) {
                var paging = widget.require("paging"), r = $(res);
                var totalNumber = r.find("#showPageInfo").data("totalNumber");
                if (totalNumber > 0) {
                    var e = paging.new({
                        recordNumber: widget.global.pageSize,
                        total: totalNumber,
                        pageIndex: widget.global.pageIndex,
                        callback: function (_pageIndex, _recordNumber) {
                            params.pageInfo.pageIndex = _pageIndex;
                            params.pageInfo.pageSize = _recordNumber;
                            if (widget._getConds()) {
                                widget.refreshPart("queryOrgDetailInfos", JSON.stringify(params), "#batctOrgListR");
                            }
                        }
                    });
                    r.find("#showPageInfo").append(e.getElement());
                }

            }, {
                async: false
            });
        },
        global:{
            pageIndex: 1,
            pageSize: 10
        },
        _getConds: function () {
            var widget = this;
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            return params;
        },


    });
    vita.widget.register("orgCustDetail", orgCustDetail, true);
})(window.vita);