<div data-widget="logisticsQuery">
    <p class="vita-data">{"data":$options}</p>
        <div class="box-maincont">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>更新物流信息</div>
                                </div>
                                <div class="wmin_content row ">
                                    <form class="form-bordered">
                                        <div class="form-group col-md-12">
                                            <label class="col-md-4 control-label lablep">
                                                集团订单信息查询</label>
                                            <div class="col-md-8">
                                                <input type="checkbox" id="queryType" checked/>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6">

                                            <label class="col-md-4 control-label lablep">
                                                集团订单号</label>
                                            <div class="col-md-8">
                                                <input id="groupOrderId"  type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="col-md-4 control-label lablep">
                                                宽带购物车id</label>
                                            <div class="col-md-8">
                                                <input id="broadbandCoId" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="col-md-4 control-label lablep">
                                                收货人手机号</label>
                                            <div class="col-md-8">
                                                <input id="receiverPhone" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="col-md-4 control-label lablep">
                                                物流单号</label>
                                            <div class="col-md-8">
                                                <input id="logisticsNbr" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-12">
                                            <label class="col-md-2 control-label lablep"></label>
                                            <div class="col-md-10 text-center">
                                                <button id="btn-clear" type="button" class="btn btn-white">清除</button>
                                                <button  id="btn-qryLogisitcs" type="button" class="btn btn-primary">查询</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询结果</div>
                                </div>
                                <div class="wmin_content" id="#loadData">
                                    <div class="col-lg-12 mart10" id="logisticsListId">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th width="100">收货人</th>
                                                <th width="100">收货地址</th>
                                                <th width="100">收货人联系电话</th>
                                                <th width="80">邮编</th>
                                                <th width="100">物流单号</th>
                                                <th width="60">物流公司</th>
                                                <th width="100">物流状态</th>
                                                <th width="100">创建日期</th>
                                                <th width="80">备注</th>
                                                <th width="50">操作</th>
                                            </tr>
                                            </thead>
                                            <tbody id="logisticsListTbody">
                                            #if($options.logisticsList && $options.logisticsList != "null" && $options.logisticsList.size() > 0)
                                            #foreach($logistics in $options.logisticsList)
                                            <tr>
                                                <td>$!logistics.receiver</td>
                                                <td>$!logistics.receiverAddr</td>
                                                <td>$!logistics.receiverPhone</td>
                                                <td>$!logistics.postCode</td>
                                                <td>$!logistics.logisticsNbr</td>
                                                <td>$!logistics.logisticsCompany</td>
                                                #if($options.logisticsStatusList && $options.logisticsStatusList.size() > 0)
                                                    #foreach($logisticsStatus in $options.logisticsStatusList)
                                                    #if($logisticsStatus.value == $logistics.statusCd)
                                                    <td>$logisticsStatus.name</td>
                                                    #end
                                                    #end
                                                #end
                                                <td>$!logistics.createDt</td>
                                                <td>$!logistics.remark</td>
                                                <td>
                                                    <button id="modify" type="button" class="btn btn-primary">修改</button>
                                                    <p class="vita-data">
                                                        {"data":$logistics}
                                                    </p>
                                                </td>
                                            </tr>
                                            #end
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
        </div>

</div>