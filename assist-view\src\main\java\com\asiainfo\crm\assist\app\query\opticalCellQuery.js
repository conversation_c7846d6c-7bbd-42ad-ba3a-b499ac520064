(function (vita) {
    var opticalCellQuery = vita.Backbone.BizView.extend({
        events: {
            "click #channelIdBtn": "_chooseArea",
            "click #qryBtn": "_opticalCellQry"
        },
        _initialize: function () {
            
        },
        global: {
        	 chooseArea: "../comm/chooseArea"
        },
        _chooseArea: function () {
            var widget = this,element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id : dialogId,
                url : widget.global.chooseArea,
                params : {
                },
                onClose : function(res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _opticalCellQry : function (){
        	var widget = this,element = $(widget.el);
        	var params = widget._getConds();
        	if (params) {
        		widget.refreshPart("opticalCellQuery",params,"#opticalCellQueryList");
        	}
        },
        _getConds: function () {
        	  var widget = this;
              var params = {};
              var regionId = $("#regionId").attr("value");
              if (widget._isNullStr(regionId)) {
                  widget.popup("请选择地区！");
                  return false;
              } else {
                  params.regionId = regionId;
              }
              
              var villageName = $("#villageName").val();
              if (widget._isNullStr(villageName)) {
                  widget.popup("请输入小区名称！");
                  return false;
              } else {
                  params.villageName = villageName;
              }
              return params;
        },
        _isNullStr: function (str) {
            if(str == null || str == "") {
                return true;
            }
            return false;
        },
       
    });
    vita.widget.register("opticalCellQuery", opticalCellQuery, true);
})(window.vita);