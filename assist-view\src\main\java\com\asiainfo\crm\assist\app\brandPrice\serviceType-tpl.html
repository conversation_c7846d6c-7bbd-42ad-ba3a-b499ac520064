<div data-widget="serviceType">
    <vita-data>{"data":$options}</vita-data>
    <p class="vita-data">$options</p>
    <div class="sypage-nav">
        <div class="nav_alink"><strong class="text16">服务类型</strong></div>
    </div>
    <div class="sypage-min">
        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">查询条件</strong></div>
        </div>
        <div class="sypagemin_content ">
            <form>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">服务类型编码</label>
                    <div class="col-md-8">
                        <input type="text" class="form-control" placeholder="" id="serviceTypeNbr">
                        <p class="vita-bind" model="serviceTypeNbr"></p>
                    </div>
                </div>
                <div class="form-group col-md-6 ">
                    <label class="col-md-4 control-label lablep">服务类型名称</label>
                    <div class="col-md-8">
                        <input type="text" class="form-control" placeholder="" id="serviceTypeNbr">
                        <p class="vita-bind" model="serviceTypeNbr"></p>
                    </div>
                </div>

                <div class="form-group col-md-12">
                    <div class="col-md-12 searchbutt_r" align="right">
                        <button type="button" class="btn btn-default" id="searchDel">清除</button>
                        <button type="button" class="btn btn-primary" id="qryCommonLogSearchBtn">搜索</button>
                    </div>
                </div>

            </form>
        </div>

        <div class="form_title">
            <div><i class="bot"></i><strong class="text16">服务类型列表</strong></div>
        </div>
        <div class="sypagemin_content ">
            <div class="col-lg-12 mart10">
                <table class="table table-hover" id="systemRoleList">
                    <thead>
                    <tr>
                        <th>选择</th>
                        <th>服务类型编码</th>
                        <th>服务类型名称</th>
                        <th>服务详细描述</th>
                     <!--   <th>状态</th>-->
                        <th>创建时间</th>
                    </tr>
                    </thead>
                    <tbody>
                    #if($options != "null" && $options.serviceTypes && $options.serviceTypes != "null" && $options.serviceTypes.size() > 0)
                    #foreach($serviceType in $options.serviceTypes)
                    <tr >
                        <td>
                            <label class="wu-radio full absolute" data-scope="">
                                <input type="radio" name="moduleRadio" />
                                <p class="vita-data">{"data":$serviceType,"totalNumber":$options.totalNumber}</p>
                            </label></td>
                        <td>$!serviceType.serviceTypeNbr</td>
                        <td>$!serviceType.serviceTypeName</td>
                        <td>$!serviceType.serviceTypeDesc</td>
                     <!--   <td>#if($!offerInformProg.statusCd == 1000)有效
                            #elseif($!offerInformProg.statusCd == 1100)无效
                            #end
                        </td>-->
                        <td>$!serviceType.createDate</td>
                    </tr>
                    #end
                    #end
                    </tbody>
                </table>
                <!--翻页start -->
                <div class="page-box" id="showPageInfo">
                </div>
                <!--翻页end -->
            </div>
        </div>
    </div>
</div>