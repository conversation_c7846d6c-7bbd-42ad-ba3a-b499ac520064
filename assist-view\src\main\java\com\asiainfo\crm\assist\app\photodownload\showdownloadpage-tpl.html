<div data-widget="showdownloadpage" class="pace-done wrapper mini-rightmax no-minright">
    &nbsp;文件生成时间：最近一周内的图片数据
    <!--<img id="qryDiv" src="../resources/images/indicator.gif"
         style="display: none;" />-->
    <span id="spanValidate" style="font-size: 12px; display: none"></span>
    <div id="filedata" style='position: absolute; right: 0px; top: 0px' >
        图片数据所涉及业务：新装、过户、改产品属性、用户资料修改&nbsp;
    </div>
    <div class="container-fluid row">
        <div class="wmin_content">
            <div class="col-lg-12">
            </div>
            <div class="col-lg-12 mart10" id="auditAdjustInfoListResult">
                <table class="table table-hover">
                    <thead>
                    <tr>
                        <th>文件名称</th>
                        <th>文件大小</th>
                        <th>文件类型</th>
                        <th>生成时间</th>
                        <th>下载</th>
                    </tr>
                    </thead>
                    <tbody id="fileInfoListTable">
                    #if($options != "null" && $options.files != "null" &&
                    $options.files.size() > 0)
                    #foreach($file in $options.files)
                    <tr>
                        <td>$!{file.fileName}</td>
                        <td>$!{file.size}</td>
                        <td>$!{file.type}</td>
                        <td>$!{file.date}</td>
                        <td>
                            <a id="downloadfile" href="javascript:void(0);" class="textcolorgreen" title="点击下载文件">
                            下载
                                <p class="vita-data">{"filePath":"$!file.path","fileName":"$!file.fileName"}</p>
                            </a>

                        </td>
                    </tr>
                    #end
                    #elseif($options != "null" && $options.files != "null" &&
                    $options.files.size() == 0)
                    <tr>
                        <td align='center' colspan='8'>未查到相关数据</td>
                    </tr>
                    #end
                    </tbody>
                </table>
                <!--翻页start -->
                <div id="showPageInfo">
                </div>
                #if($options.totalNumber && $options.totalNumber != "null")
                <p class="vita-data">{"totalNumber":"$!options.totalNumber"}</p>
                #end
                <!--翻页end -->
            </div>
        </div>
    </div>
</div>
</div>