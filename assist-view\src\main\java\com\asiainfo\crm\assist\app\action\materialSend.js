(function (vita) {
    var materialSend = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_materialRelaQuery"
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var beginDate = element.find("input[name='beginDate'],input[name='sale_beginDate']");
            var endDate = element.find("input[name='endDate'],input[name='sale_endDate']");
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd"
                });
            }
            ;
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd"
                });
            }
        },
        global: {
            pageIndex: 1,
            pageSize: 5,
            preset: "date"
        },
        _materialRelaQuery: function () {
            var widget = this;
            var gsession = widget.gSession;
            var params = {
                accNum: $("#accNum").val(),
                staffId: gsession.staffId,
                regionId: gsession.staffRegionId,
                // channelId: gsession.curChannelId,
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                }
            };
            widget.refreshPart("materialRelaQuery", JSON.stringify(params), "#materialResult", function (re) {
                var r = $(re), paging = this.require("paging");
                var e = paging.new({
                    recordNumber : widget.global.pageSize,
                    total : r.find("#showPageInfo").data("totalNumber"),
                    pageIndex : widget.global.pageIndex,
                    callback : function (pageIndex, pageSize) {
                        var params = {
                            accNum: $("#accNum").val(),
                            staffId: gsession.staffId,
                            areaId: gsession.staffRegionId,
                            // channelId: gsession.curChannelId,
                            pageInfo: {
                                pageIndex: pageIndex,
                                pageSize: widget.global.pageSize
                            }
                        };
                        widget.refreshPart("materialRelaQuery", JSON.stringify(params), "#materialList");
                    }
                });
                r.find("#showPageInfo").append(e.getElement());
            }, { async: false });
        }
    });
    vita.widget.register("materialSend", materialSend, true);
})(window.vita);