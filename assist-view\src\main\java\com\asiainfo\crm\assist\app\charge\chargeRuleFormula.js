(function (vita) {
    var chargeRuleFormula = vita.Backbone.BizView.extend({
        events: {
            "click #queryBtn": "_queryChargeRuleFormulaPage",
            "click #resetBtn": "_clearConditions",
            "click #addBtn": "_addPopup",
            "click #editBtn": "_editPopup",
            "click #deleteBtn": "_deleteSubmit",
            "click #addSubmitBtn": "_addSubmit",
            "click #editSubmitBtn": "_editSubmit",
            "click button.close": "_closeBtnClick",
            "click #closeBtn": "_closeBtnClick",
        },
        _initialize: function () {
            var widget = this,
                el = $(widget.el);
            // 时间控件类型初始化
            this._queryChargeRuleFormulaPage();
        },
        global: {
            totalNumber: 0,
            pageIndex: 1,
            pageSize: 10,
            datetime: 'time',
            date: 'date',
        },

        /**
         * 打开添加弹框
         */
        _addPopup: function () {
            var widget = this,
                el = $(widget.el);
            widget._cleanPopupData();
            widget._openPopup("add");
        },
        _openPopup: function (action) {
            var widget = this,
                el = $(widget.el);
            switch (action) {
                case "add":
                    // 时间控件类型初始化

                    el.find(".titlefont").html("添加");
                    el.find("#_regionIdBtn").removeAttr("disabled");
                    el.find(".hideAndShow").hide();
                    el.find("#addSubmitBtn").show();
                    el.find("#editSubmitBtn").hide();
                    el.find(".popup").show();
                    break;
                case "edit":
                    el.find(".titlefont").html("编辑");
                    el.find(".hideAndShow").show();
                    el.find("#_formulaId").attr("disabled","disabled");
                    el.find("#addSubmitBtn").hide();
                    el.find("#editSubmitBtn").show();
                    el.find(".popup").show();
                    break;
                default:
                    break;
            }
        },
        /**
         * 新增
         * @private
         */
        _addSubmit: function () {
            var widget = this;
            var param = widget._getPopupData();
            if(!this._checkParam(param)){
                return;
            }
            widget.callService("createChargeRuleFormula", JSON.stringify(param), function (res) {
                    if("0" == res.resultCode){
                        widget.popup("添加成功");
                        widget._closeBtnClick();
                        widget._queryChargeRuleFormulaPage();
                    }else{
                        widget.popup(res.resultMsg);
                    }
            }, {async: false, mask: false});
        },
        /**
         * 编辑更新
         * @private
         */
        _editSubmit: function () {
            var widget = this,el = $(widget.el);
            var param = widget._getPopupData();
            param["formulaId"] = el.find("#_formulaId").val().trim()
            if(!this._checkParam(param)){
                return;
            }
            widget.callService("updateChargeRuleFormula", JSON.stringify(param), function (res) {
                if (res) {
                    if("0" == res.resultCode){
                        widget.popup("修改成功");
                        widget._closeBtnClick();
                        widget._queryChargeRuleFormulaPage();
                    }else{
                        widget.popup(res.resultMsg);
                    }
                }
            }, {async: false, mask: false});
        },
        /**
         * 打开编辑弹框
         */
        _editPopup: function () {
            var widget = this,
                el = $(widget.el);
            var payment = el.find("table").find("input[name='payment']:checked");
            if (payment.length > 0) {
                widget._cleanPopupData();
                var tr = payment.parent().parent().parent();
                el.find("#_formulaId").val(tr.find("td:eq(1)").text());
                el.find("#_expression").val(tr.find("td:eq(2)").text());
                el.find("#_acctItemTypeId").val(tr.find("td:eq(3)").text());
                el.find("#_remakrs").val(tr.find("td:eq(4)").text());
                el.find("#_payMethodCd").val(tr.find("td:eq(5)").text());
                el.find("#_condExpr").val(tr.find("td:eq(6)").text());
                el.find("#_busiChargeId").val(tr.find("td:eq(7)").text());
                var all_options = document.getElementById("_statusCd").options;
                for (i=0;i<all_options.length;i++){
                    if (all_options[i].text == tr.find("td:eq(8)").text()){
                        all_options[i].selected = true;
                    }
                }
                el.find("#_balanceTypeId").val(tr.find("td:eq(9)").text());
                widget._openPopup("edit");
            } else {
                widget.popup("请选择要编辑的一行!");
                return;
            }
        },
        /**
         * 弹出框关闭事件
         */
        _closeBtnClick: function () {
            var widget = this,
                el = $(widget.el);
            el.find(".popup").hide();
            el.find(".popup1").hide();
        },

        /**
         * 清除弹框数据
         */
        _cleanPopupData: function () {
            var widget = this,
                el = $(widget.el);
            el.find("form[name=_popupForm]").resetForm();
        },


        /**
         * 重置查询条件
         */
        _clearConditions: function () {
            var widget = this,
                el = $(widget.el);
            el.find("#expression").val('');
            el.find("#acctItemTypeId").val('');
            el.find("#busiChargeId").val('');
            el.find("#condExpr").val('');
            el.find("#statusCd").val('');
        },
        /**
         * 获取弹出框数据
         * @private
         */
        _getPopupData: function () {
            var widget = this,
                el = $(widget.el);
            var params = {
                "formulaId": el.find("#_formulaId").val().trim(),
                "expression": el.find("#_expression").val().trim(),
                "acctItemTypeId": el.find("#_acctItemTypeId").val().trim(),
                "remakrs": el.find("#_remakrs").val().trim(),
                "payMethodCd": el.find("#_payMethodCd").val().trim(),
                "condExpr": el.find("#_condExpr").val().trim(),
                "busiChargeId": el.find("#_busiChargeId").val().trim(),
                "statusCd":el.find("#_statusCd").val().trim(),
                "balanceTypeId":el.find("#_statusCd").val().trim(),
            };
            return params;
        },
        _checkParam: function (param){
            var widget = this,
                el = $(widget.el);
            var soUtil = widget.require("soUtil");
            if(soUtil.isNullOrEmpty(param.acctItemTypeId)||soUtil.isNullOrEmpty(param.statusCd)){
                widget.popup("参数必填，请检查后重新提交!");
                return false;
            }
            return true;
        },
        /**
         * 查询
         */
        _queryChargeRuleFormulaPage: function () {
            var widget = this,
                el = $(widget.el);
            var param = widget._getConds(widget.global.pageIndex);
            widget.refreshPart("queryChargeRuleFormulaPage", param, "#loadData",
                "reqList", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = $(res).find("table").data("totalNumber");
                    if (parseInt(totalNumber) > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                param.pageInfo.pageIndex = _pageIndex;
                                param.pageInfo.pageSize = _recordNumber;
                                widget.refreshPart("queryChargeRuleFormulaPage", param, "div[name=tableDiv]",null,null,{mask: true});

                                ;
                            }
                        });
                        r.find("#pageInfo").append(e.getElement());
                    }
                }, {mask: true});
        },
        _getConds: function (pageIndex) {
            var widget = this, el = $(widget.el);
            var param = {
                "pageInfo": {
                    "pageIndex": pageIndex,
                    "pageSize": widget.global.pageSize,
                }
            };
            param.expression = $("#expression").val();
            param.acctItemTypeId = $("#acctItemTypeId").val();
            param.busiChargeId = $("#busiChargeId").val();
            param.condExpr = $("#condExpr").val();
            param.statusCd = $("#statusCd").val();
            return param;
        },
    });
    vita.widget.register("chargeRuleFormula", chargeRuleFormula, true);})(window.vita);
