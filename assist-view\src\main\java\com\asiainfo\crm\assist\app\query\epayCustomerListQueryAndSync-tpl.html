<div data-widget="epayCustomerListQueryAndSync" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>用户接入号码</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-4">
                                            <div class="col-md-7">
                                                <input id="accNum" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-6">
                                            <div class="col-md-11 searchbutt_r" align="">
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-query" type="button" class="btn btn-primary">搜索</button>
                                                <label>&nbsp&nbsp&nbsp&nbsp&nbsp</label>
                                                <button id="btn-Sync" type="button" class="btn btn-primary">同步</button>

                                            </div>
                                        </div>


                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>查询结果</div>
                                </div>
                                <div class="wmin_content">

                                    <div class="col-lg-12 mart10" id="customerInfoResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>选择</th>
                                                <th>新姓名</th>
                                                <th>新证件类型</th>
                                                <th>新证件号码</th>
                                                <th>新电子邮件</th>
                                            </tr>
                                            </thead>
                                            <tbody id="">
                                            #if($options != "null" && $options.customerInfoResult && $options.customerInfoResult != "null")

                                            <tr>
                                                <td><label class="wu-radio full absolute" data-scope=""><input
                                                        type="radio" name="payment"></label></td>
                                                <td>$!{customerInfoResult.custName}</td>
                                                <td>$!{customerInfoResult.certTypeName}</td>
                                                <td>$!{customerInfoResult.certNum}</td>
                                                <td>$!{accountList.email}</td>

                                            </tr>
                                            #elseif($options != "null" && $options.customerInfoResult && $options.customerInfoResult == "null")
                                                <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
