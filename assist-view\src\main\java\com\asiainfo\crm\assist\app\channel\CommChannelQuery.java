package com.asiainfo.crm.assist.app.channel;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.AbstractSoComponent;
import org.springframework.stereotype.Component;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by wenhy on 2017/7/11.
 */
@Component("vita.commChannelQuery")
public class CommChannelQuery extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(CommChannelQuery.class);

    @Autowired
    private IStaffOrgAreaSMO staffOrgAreaSMO;



    @Override
    public Map achieveData(Object... param) throws Exception {
        Map options = new HashMap();
        return options;
    }
    public Map qryChannelList(String params) throws Exception {
        String respStr = staffOrgAreaSMO.queryChannelDetail(params);
        return (Map) resolveResult(respStr);
    }
}
