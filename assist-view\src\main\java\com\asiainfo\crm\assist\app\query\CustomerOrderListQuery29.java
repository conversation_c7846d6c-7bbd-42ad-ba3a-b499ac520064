package com.asiainfo.crm.assist.app.query;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.angel.json.JsonConverter;
import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICommQuerySMO;
import com.asiainfo.crm.service.intf.IResourceSMO;
import com.google.common.collect.Maps;


@Component("vita.customerOrderListQuery29")
public class CustomerOrderListQuery29 extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(CustomerOrderListQuery29.class);
    
    @Autowired
    private IResourceSMO resourceSMO;

    @Autowired
    private ICommQuerySMO commQuerySMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
    	Map<String,Object> param = new HashMap<String,Object>();
    	param.put("queryType", "orderQquery");
    	param.put("regionId", "8510101");
    	JsonConverter jsonConverter = JsonConverter.buildNormalConverter();
		String paramStr = jsonConverter.toJson(param);
        String retStr = resourceSMO.getOrderStatus(paramStr);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(retStr);
        List list = (List) MapUtils.getObject(resultObjectMap, "list");
        Map retMap = new HashMap();
        retMap.put("statusList", list);
        retMap.put("orderStatusNC", AssistMDA.ORDER_STATUS_CD_NC);
        retMap.put("orderStatusTD", AssistMDA.ORDER_STATUS_CD_TD);
        retMap.put("specialChannel", AssistMDA.SPECIAL_CHANNEL);
        retMap.put("specialStaffB", AssistMDA.SPECIAL_STAFF_B);
        return retMap;
    }

    /**
     * 查询订单列表
     */
    public Map queryCustomOrderList(String json) throws Exception {
    	JsonConverter jsonConverter = JsonConverter.buildNormalConverter();
    	Map reqMap = jsonConverter.toBean(json, Map.class);
    	String regionId = String.valueOf(reqMap.get("regionId"));
        String customerOrderItemList = resourceSMO.queryOrderListInfo(json);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(customerOrderItemList);
        Map<String, Object> options = Maps.newHashMap();
        List orderLists = (List) MapUtils.getObject(resultObjectMap, "orderLists");
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", total);
        List customerOrders = transfOrderInfo(orderLists,regionId);
        options.put("customerOrders", customerOrders);
        //System.out.println(jsonConverter.toJson(options));
        
        return options;
    }
    
    private List transfOrderInfo(List orderLists,String regionId) throws Exception{
    	List<Map<String,Object>> customerOrders = new ArrayList<Map<String,Object>>();
    	if(null!=orderLists && orderLists.size()>0){
    		for (int i = 0; i < orderLists.size(); i++) {
    			Map<String,Object> customerOrder = new HashMap<String, Object>();
    			Map<String,Object> orderListMap = (Map<String, Object>) orderLists.get(i);
    			customerOrder.put("partyId", orderListMap.get("partyId"));
    			List<Map<String, Object>> list = (List<Map<String, Object>>) orderListMap.get("list");
    			List<Map<String,Object>> orderItems = new ArrayList<Map<String,Object>>();
    			if(null!=list && list.size()>0){
    				customerOrder.put("olNbr", list.get(0).get("olNbr"));
    				customerOrder.put("olId", list.get(0).get("olId"));
    				customerOrder.put("channelName", list.get(0).get("channelName"));
    				customerOrder.put("soDate", list.get(0).get("soDate"));
    				customerOrder.put("staffName", list.get(0).get("staffName"));
    				customerOrder.put("olStatusCd", list.get(0).get("olStatusCd"));
    				customerOrder.put("regionId", regionId);
    				
    				Map<String,Object> param = new HashMap<String,Object>();
    		    	param.put("olId", list.get(0).get("olId"));
    		    	param.put("isBillMode", -1);
    		    	param.put("ifInvoice", "true");
    		    	param.put("queryFlag", 1);
    		    	param.put("regionId", regionId);
    				String paramStr = jsonConverter.toJson(param);
    		        String retStr = resourceSMO.checkPrintOrderList(paramStr);
    		        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(retStr);
    		        List chargeList = (List) MapUtils.getObject(resultObjectMap, "list");
    		        if(null == chargeList || chargeList.size() < 1){
    		        	customerOrder.put("isNeedPrint", "1");//没有费用项。不需要打印
    		        }else{
    		        	customerOrder.put("isNeedPrint", "2");
    		        }
    		        String retStrPrint = resourceSMO.getPrintClass(paramStr);
    		        Map<String, Object> retObjectMap = (Map<String, Object>) resolveResult(retStrPrint);
    		        String printClassDom = String.valueOf(retObjectMap.get("printClassDom"));
    		        if(StringUtils.isNotEmpty(printClassDom)){
    		        	customerOrder.put("printClassDom", printClassDom);
    		        }else{
    		        	customerOrder.put("printClassDom", "null");
    		        }
    		        
    				for (int j = 0; j < list.size(); j++) {
    					Map<String,Object> orderItem = new HashMap<String, Object>();
    					Map<String, Object> listMap = list.get(j);
        				String olId = String.valueOf(listMap.get("olId"));
        				String olNbr = String.valueOf(listMap.get("olNbr"));
        				String boId = String.valueOf(listMap.get("boId"));
        				String offerId = String.valueOf(listMap.get("offerId"));
        				String offerSpecName = String.valueOf(listMap.get("offerSpecName"));
        				String prodId = String.valueOf(listMap.get("prodId"));
        				String prodSpecId = String.valueOf(listMap.get("prodSpecId"));
        				String prodSpecName = String.valueOf(listMap.get("prodSpecName"));
        				String accessNumber = String.valueOf(listMap.get("accessNumber"));
        				String actionClassCd = String.valueOf(listMap.get("actionClassCd"));
        				String boActionTypeCd = String.valueOf(listMap.get("boActionTypeCd"));
        				String boActionTypeName = String.valueOf(listMap.get("boActionTypeName"));
        				String statusName = String.valueOf(listMap.get("statusName"));
        				
        				orderItem.put("olId", olId);
        				orderItem.put("olNbr", olNbr);
        				orderItem.put("boId", boId);
        				orderItem.put("statusName", statusName);
        				if((StringUtils.isEmpty(offerId)|| "null".equals(offerId)) && (StringUtils.isEmpty(prodId)||"null".equals(prodId))){
        					if("1".equals(actionClassCd) && "C2".equals(boActionTypeCd)){
        						orderItem.put("boActionTypeName", boActionTypeName);
        						orderItem.put("flag", "cust");
        					}else{
        						orderItem.put("boActionTypeName", boActionTypeName);
        						orderItem.put("flag", "ohter");
        					}
        				}else{
        					if("3".equals(actionClassCd) || ("5".equals(actionClassCd) && (StringUtils.isNotEmpty(offerId) && !"null".equals(offerId)))){
        						orderItem.put("boActionTypeName", boActionTypeName);
        						orderItem.put("offerId", offerId);
        						orderItem.put("offerSpecName", "null".equals(offerSpecName)?"":offerSpecName);
        						orderItem.put("flag", "offer");
        					}else{
        						orderItem.put("boActionTypeName", boActionTypeName);
        						orderItem.put("prodSpecName", "null".equals(prodSpecName)?"":prodSpecName);
        						orderItem.put("prodId", prodId);
        						orderItem.put("prodSpecId", prodSpecId);
        						orderItem.put("offerId", offerId);
        						orderItem.put("accessNumber", accessNumber);
        						orderItem.put("flag", "prod");
        					}
        				}
        				
        				orderItems.add(orderItem);
        				
        				List<Map<String, Object>> subBusiOrdersList =  (List<Map<String, Object>>) listMap.get("subBusiOrders");
        				if(null!=subBusiOrdersList && subBusiOrdersList.size()>0){
        					for (int z = 0; z < subBusiOrdersList.size(); z++) {
        						Map<String,Object> subOrderItem = new HashMap<String, Object>();
        						Map<String, Object> subBusiOrdersMap = subBusiOrdersList.get(z);
        						String subOfferId = String.valueOf(subBusiOrdersMap.get("offerId"));
                				String subBoActionTypeName = String.valueOf(subBusiOrdersMap.get("boActionTypeName"));
                				String subOfferSpecName = String.valueOf(subBusiOrdersMap.get("offerSpecName"));
                				String subProdSpecName = String.valueOf(subBusiOrdersMap.get("prodSpecName"));
                				String subProdId = String.valueOf(subBusiOrdersMap.get("prodId"));
                				String subBoId = String.valueOf(subBusiOrdersMap.get("boId"));
                				String subProdSpecId = String.valueOf(subBusiOrdersMap.get("prodSpecId"));
                				String subStatusName = String.valueOf(subBusiOrdersMap.get("statusName"));
                				String subAccessNumber = String.valueOf(subBusiOrdersMap.get("accessNumber"));
                				subOrderItem.put("olId", olId);
                				subOrderItem.put("olNbr", olNbr);
        						subOrderItem.put("statusName", subStatusName);
                				if((StringUtils.isEmpty(subOfferId)||"null".equals(offerId)) && (StringUtils.isEmpty(subProdId)||"null".equals(subProdId))){
                					subOrderItem.put("boActionTypeName", subBoActionTypeName);
                					subOrderItem.put("flag", "ohter");
                				}else{
                					if(StringUtils.isNotEmpty(subOfferId) && !"null".equals(subOfferId)){
                						subOrderItem.put("boActionTypeName", subBoActionTypeName);
                						subOrderItem.put("offerSpecName",  "null".equals(subOfferSpecName)?"":subOfferSpecName);
                						subOrderItem.put("offerId", offerId);
                						subOrderItem.put("boId", boId);
                    					subOrderItem.put("flag", "offer");
                					}else{
                						subOrderItem.put("boActionTypeName", subBoActionTypeName);
                						subOrderItem.put("prodSpecName",  "null".equals(subProdSpecName)?"":subProdSpecName);
                						subOrderItem.put("prodId", subProdId);
                						subOrderItem.put("boId", subBoId);
                						subOrderItem.put("offerId", subOfferId);
                						subOrderItem.put("accessNumber", subAccessNumber);
                						subOrderItem.put("prodSpecId", subProdSpecId);
                						subOrderItem.put("flag", "prod");
                					}
                				}
                				
                				orderItems.add(subOrderItem);
        					}
        				}
    				}
    				
    				customerOrder.put("orderItems", orderItems);
    				
    			}
    			
    			customerOrders.add(customerOrder);
			}
    	}

    	
    	
    	return customerOrders;
    }








}
