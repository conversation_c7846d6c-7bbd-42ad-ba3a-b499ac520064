package com.asiainfo.crm.assist.app.sesame;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ISesameCreditSmo;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by wenhy on 2017/7/26.
 * 芝麻信用购机申请查询
 */
@Component("vita.sesameCreditQuery")
public class SesameCreditQuery extends AbstractComponent {

    private static final Logger LOGGER = LoggerFactory.getLogger(SesameCreditQuery.class);

    @Autowired
    private ISesameCreditSmo sesameCreditSmo;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map querySesameCredit(String jsonStr) throws Exception {
        String sesameCreditStr = sesameCreditSmo.querySesameCredit(jsonStr);
        Map<String, Object> options = Maps.newHashMap();
        List<Map<String, Object>> sesameCreditInfos = (List<Map<String, Object>>) resolveResult(sesameCreditStr);
        for(Map map : sesameCreditInfos){
            Iterator iter = map.keySet().iterator();
            while (iter.hasNext()) {
                String key = (String)iter.next();
                if(null == map.get(key)) {
                    map.put(key, "");
                }
            }
        }
        options.put("sesameCreditInfos", sesameCreditInfos);
        return options;
    }
}
