(function (vita) {
    var olDetailInfo = vita.Backbone.BizView.extend({
        events: {
            "click #closeBtn": "_closePage",
            "click #custDetail": "_custDetailClk",
            "click a[name=orderUrl]": "_redictDetail"
        },
        global: {
            compUrl:{
                boProdDetailInfo : "../query/boProdDetailInfo",      // 订单项(产品)详情
                boOfferDetailInfo : "../query/boOfferDetailInfo",    // 订单项(销售品)详情
                custDetailInfo : "../query/custDetailInfo",  // 客户详情
                acctDetailInfo : "../query/acctDetailInfo"   // 账户详情
            }
        },
        _initialize: function () {
            var widget = this,
                el = $(widget.el);
           /* var busiOrders = el.data("data");
            var custOrderId = el.data("custOrderId");*/
        },
        _closePage : function() {
            var widget = this,
                element = $(widget.el);
            element.closest("[data-widgetfullname=vita-dialog]").dialog("close");
        },
        _custDetailClk: function (e) {
            var widget = this;
            var custId = $(e.currentTarget).data("custId");
            if (!custId) {
                widget.popup("客户ID未返回,请重新查询...");
                return;
            }
            var param = {
                "custId": custId
            };
            widget._openDialog("custDetailInfo",param);
        },
        _redictDetail:function(e){
            
            var widget = this,
                aEl = $(e.currentTarget);

            var flag = aEl.data("flag");
            var custOrderId = aEl.data("custOrderId");
            var orderItemId = aEl.data("orderItemId");
            var compCode = "";
            if(flag == "prod"){
                compCode = "boProdDetailInfo"
            }else if(flag == "offer"){
                compCode = "boOfferDetailInfo"
            }
            var data = {
                custOrderId : aEl.data("custOrderId"),
                orderItemId : aEl.data("orderItemId")
            };
            widget._openDialog(compCode,data);

        },
        _openDialog:function(compName,data){
            var widget = this;
            var option = {
                url: widget.global.compUrl[compName],
                params: data,
                headers : {
                    "regionId" : widget.gSession.installArea
                }
            };
            widget.dialog(option);
        }
    });
    vita.widget.register("olDetailInfo", olDetailInfo, true);
})(window.vita);