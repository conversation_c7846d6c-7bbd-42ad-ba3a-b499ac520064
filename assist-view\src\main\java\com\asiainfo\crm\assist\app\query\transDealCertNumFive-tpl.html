<div data-widget="transDealCertNumFive" style="overflow: auto; height: 100%;">

	<div class="box-maincont">
		<div class="homenofood">
			<div class="page-nav">
				<div class="row">
					<div class="pagenav_box">
						<div class="page_title">跨省一证五号处理</div>
					</div>
				</div>
			</div>
			<div class="page_main">
				<!--填单start-->
				<div class="col-lg-12">
					<div class="box-item">
						<div class="container-fluid row">
							<div class="form_title">
								<div>
									<i class="bot"></i>查询条件
								</div>
							</div>
							<div class="wmin_content row ">
								<form class=" form-bordered">

									<div class="form-group col-md-5 ">
                                            <label class="col-md-5 control-label lablep">
                                               	选择地区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group col-md-7" id="qryTimeQuantum">
                                            <label class="col-md-2 control-label lablep">
                                                	受理时间</label>
                                            <div class="col-md-7 form-inline">

                                                <div class="form-group">
                                                    <input name="beginDate" id="beginDate" type="text" class="form-control" placeholder="开始时间">
                                                </div>
                                                <div class="form-group">
                                                    <input name="endDate" id="endDate" type="text" class="form-control" placeholder="结束时间">
                                                </div>
                             
                                            </div>
                                        </div>
                                      
                                    <div class="form-group col-md-5">
										<label class="col-md-2 control-label lablep"> <label
											class="wu-radio full absolute" data-scope=""> </label> 手机号码
										</label>
										<div class="col-md-7">
											<input id="phoneNum" type="text" class="form-control"
												placeholder="">
										</div>
										
									</div>
									    
									<div class="form-group col-md-5">
										<label class="col-md-2 control-label lablep"> <label
											class="wu-radio full absolute" data-scope=""> </label> 证件号码
										</label>
										<div class="col-md-7">
											<input id="custIdentity" type="text" class="form-control"
												placeholder="">
										</div>
										
									</div>
									
									<div class="form-group col-md-5">
										<label class="col-md-2 control-label lablep"> 只显示本人</label>
										<div class="col-md-7">
											<div class="input-group">
												<div class="input-group-btn">
													<select id="isOwner"
														class="btn btn-default dropdown-toggle"
														data-toggle="dropdown" aria-haspopup="true"
														aria-expanded="false">
														
														<option value="N" selected="selected" certTypeName="否">否</option>
														<option value="Y" selected="selected" certTypeName="是">是</option>
					
													</select>
												</div>
											</div>
										</div>
									</div>
									
									<div class="form-group col-md-5">
										<label class="col-md-2 control-label lablep">处理情况</label>
										<div class="col-md-7">
											<div class="input-group">
												<div class="input-group-btn">
													<select id="status"
														class="btn btn-default dropdown-toggle"
														data-toggle="dropdown" aria-haspopup="true"
														aria-expanded="false">
														
														<option value="100002" selected="selected" certTypeName="初始化">初始化</option>
														<option value="201300" selected="selected" certTypeName="已接单">已接单</option>
														<option value="100004" selected="selected" certTypeName="已取消">已取消</option>
														<option value="301200" selected="selected" certTypeName="成功">成功</option>
													</select>
												</div>
											</div>
										</div>
									</div>
									
									<div class="form-group col-md-5">
										<label class="col-md-2 control-label lablep"> <label
											class="wu-radio full absolute" data-scope=""> </label>流水号
										</label>
										<div class="col-md-7">
											<input id="order" type="text" class="form-control"
												placeholder="">
										</div>
									</div>
									
									
									<div class="form-group col-md-5">
										<label class="col-md-2 control-label lablep"></label>
										<div class="col-md-5">
											<button id="btn-qryTransDealCertNumFive" type="button"
												class="btn btn-primary">查询</button>
										</div>
									</div>
									
								</form>
							</div>
						</div>



						<div class="container-fluid row">
							<div class="form_title">
								<div>
									<i class="bot"></i>查询结果
								</div>
							</div>
	
							<div class="container-fluid row" id="certNumTbody"
								>
								<div class="wmin_content" id="certNumFiveListR">
									<div class="col-lg-12 mart10" id="transDealCertNumFiveList">
										<p class="vita-data">{"data": $options}</p>
										<table class="table table-hover">
											<thead>
											<tr id="certNumTable" style="display: none;">
												<th></th>
												<th>订单流水号</th>
												<th>受理渠道</th>
												<th>受理工号</th>
												<th>受理时间</th>
												<th>受理时限</th>
												<th>联系方式</th>
												<th>操作</th>
											</tr>
												<tr>
													<th></th>
												<th>订单流水号</th>
												<th>受理渠道</th>
												<th>受理工号</th>
												<th>受理时间</th>
												<th>受理时限</th>
												<th>联系方式</th>
												<th>受理情况</th>
												<th>操作</th>
												</tr>
											</thead>

											<tbody>
												#if($options != "null" && $options.transDealCertNumFiveList && 
												$options.transDealCertNumFiveList != "null" && $options.transDealCertNumFiveList.size() > 0) 
												#foreach($transDealCertNumFive in $options.transDealCertNumFiveList)
												<tr>
													<td><input type="radio" name="checkCertFive">
														<a class="textcolorgreen" link="certNumFive"></a>
													<td>$!{transDealCertNumFive.orderNbr}</td>
													<td>$!{transDealCertNumFive.channelName}</td>
													<td>$!{transDealCertNumFive.staffCode}</td>
													<td>$!{transDealCertNumFive.soDate}</td>
													<td>$!{transDealCertNumFive.limitDate}</td>
													<td>$!{transDealCertNumFive.telNbr}</td>
													<td>
													#if($transDealCertNumFive.statusCd=='100002') 
														未接单
													#elseif($transDealCertNumFive.statusCd=='201300')
														已接单
													#elseif($transDealCertNumFive.statusCd=='301200')
														成功
													#elseif($transDealCertNumFive.statusCd=='100004')
														已取消
													#end
													</td>
													
                                           			
                                            		<td>
                                                		<button id="showDetail" type="button" class="btn btn-primary">详情</button>
                                                		<p class="vita-data">{"orderNbr":"$!transDealCertNumFive.orderId","telNbr":"$!transDealCertNumFive.telNbr"}</p>
                                                		<!--  
                                                		#if($transDealCertNumFive.statusCd=='100002')
                                                		<button id="dealOrder" type="button" class="btn btn-primary">接单</button>
                                                		<p class="vita-data">{"orderNbr":"$!transDealCertNumFive.orderNbr","telNbr":"$!transDealCertNumFive.telNbr","orderId":"$!transDealCertNumFive.orderId"}</p>
                                                		#else  #end
                                                		<p class="vita-data">
                                                    	-->
                                                		</p>
                                            		</td>
                                            		
													
												</tr>
												#end
												#elseif( $options.transDealCertNumFiveList == "null")
												<tr>
													<td align='center' colspan='7'>未查询到数据！
													<td>
												</tr>
												#end
											</tbody>
										</table>
									</div>
								</div>
									<!--翻页start-->
									<div id="showPageInfo" class="page-box"></div>
									#if($options.totalNumber && $options.totalNumber != "null")
									<p class="vita-data">{"totalNumber":$options.totalNumber}</p>
									#end
									<!--翻页end -->
							</div>
						</div>
					</div>
				</div>
			</div>
			<!--填单end-->
		</div>
	</div>
</div>

