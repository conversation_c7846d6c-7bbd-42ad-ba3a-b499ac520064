package com.asiainfo.crm.assist.app.query;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.common.result.Result;
import com.asiainfo.crm.service.intf.*;
import com.asiainfo.crm.util.ListUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.Predicate;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.aspect.annotations.SmLogAnnotation;
import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.Constant;
import com.google.common.collect.Maps;

import com.asiainfo.crm.util.SmLogUtil;
import com.al.common.utils.StringUtil;

/**
 * Created on 2017/7/7.
 */
@Component("vita.customerOrderListQuery")
public class CustomerOrderListQuery extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(CustomerOrderListQuery.class);

    @Autowired
    private IOrderQuerySMO orderQuerySMO;
    
    @Autowired
    private IOrderTransferLogSmo orderTransferLogSmo;

    @Autowired
    private ICommQuerySMO commQuerySMO;

    @Autowired
    private ISoQuerySMO soQuerySMO;

    @Autowired
    private IStaffDataPrvQuerySMO staffDataPrvQuerySMO;
    @Autowired
    private IAgencyCapitalSMO agencyCapitalSMO;

    @Autowired
    private IBillingSmo billingSmo;

    @Autowired
    private IOrderSaveSmo orderSaveSmo;

    @Override
    public Map achieveData(Object... params) throws Exception {
    	String paramsStr = (String) params[0];
        String retStr = commQuerySMO.qryAttrValuesById(AssistMDA.ORDER_ITEM_STATUS_NBR);
        List<Map<String, Object>> resultObjectMap = (List<Map<String, Object>>) resolveResult(retStr);
        Map retMap = new HashMap();
        retMap.put("statusList", resultObjectMap);
        retMap.put("orderStatusNC", AssistMDA.ORDER_STATUS_CD_NC);
        retMap.put("orderStatusTD", AssistMDA.ORDER_STATUS_CD_TD);
        retMap.put("orderStatusNAEM", AssistMDA.ORDER_STATUS_CD_NAEM);
        retMap.put("orderStatusFEE", AssistMDA.ORDER_STATUS_CD_FEE);
        retMap.put("orderStatusCO", AssistMDA.ORDER_STATUS_CD_CO);
        retMap.put("cmSysSource", AssistMDA.CHANNEL_MIDDLE_SYS_SOURCE);
        retMap.put("specialChannel", AssistMDA.SPECIAL_CHANNEL);
        retMap.put("specialStaffB", AssistMDA.SPECIAL_STAFF_B);
        retMap.put("viewOrderOn", AssistMDA.CHAIN_ORDER_INFO_ON);
        Map paramsMap = jsonConverter.toBean(paramsStr, Map.class);
        if (Constant.LOGIC_STR_Y.equals(MDA.EMERGENCY_ON) || matchStaffUseAdapter(paramsMap)) {
        	retMap.put("emergOn", Constant.LOGIC_STR_Y);
        } else{
        	retMap.put("emergOn", Constant.LOGIC_STR_N);
        }
        return retMap;
    }

    /**
     * 查询订单列表
     */
	@SmLogAnnotation(logType = "5000", qryPoint = "queryCustomOrderList", funName = "qryCustomerOrderItemListByCond", paramKeys = {
			"channelId,staffId,custOrderNbr,orderItemNbr,orderItemId,statusCds,extCustOrderId,custId,accNum,mktResInstNbr,isProdAcctRel,certNum,account,custOrderId" })
    public Map queryCustomOrderList(String json) throws Exception {
        String customerOrderItemList = orderQuerySMO.qryCustomerOrderItemListByCond(json);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(customerOrderItemList);
        Map<String, Object> options = Maps.newHashMap();
        List<Map> customerOrders = (List) MapUtils.getObject(resultObjectMap, "customerOrders");
        Double num100 = 100.00;
        if("Y".equals(AssistMDA.SWITCH_QUERY_ORDER_SHOW_AMOUNT) && customerOrders!=null && customerOrders.size()>0){
            for(Map orderItem:customerOrders){
                Long custOrderId = MapUtils.getLong(orderItem,"custOrderId");
                if(custOrderId!=null){
                    Map params = new HashMap();
                    params.put("custOrderId",custOrderId);
                    Map payInfo = (Map) resolveResult(billingSmo.qryTotalAmountForCommercialAgent(jsonConverter.toJson(params)));
                    //金额除以100
                    payInfo.put("amountTotal",Double.valueOf((Integer)payInfo.get("amountTotal"))/num100);
                    payInfo.put("realAmountTotal",Double.valueOf((Integer)payInfo.get("realAmountTotal"))/num100);
                    orderItem.put("payInfo",payInfo);
                }
            }
        }
        for (Map customerOrder : ListUtil.nvlList(customerOrders)) {
            List<Map<String, Object>> orderItems = (List<Map<String, Object>>) MapUtils.getObject(customerOrder, "orderItems");
            final boolean isPkgOrder = isPkgOrder(orderItems);
            CollectionUtils.filter(ListUtil.nvlList(orderItems), new Predicate() {
                @Override
                public boolean evaluate(Object object) {
                    boolean isProdOrderItem = "1300".equals(MapUtils.getString((Map) object, "orderItemCd"));
                    boolean isExistInNotShowProdIds = MDA.NOT_SHOW_ON_PAGE_RROD_IDS.contains(MapUtils.getLong((Map) object, "applyObjSpec"));
                    boolean returnVlaue = !(isProdOrderItem && isExistInNotShowProdIds);
                    if (returnVlaue && isPkgOrder) {
                    	return isShowOrderItem(object);
                    }
                    return returnVlaue;
                }
            });
        }
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", total);
        options.put("customerOrders", customerOrders);
        return options;
    }
    
    protected boolean isShowOrderItem(Object object) {
    	boolean isShowOrderItem = Constant.LOGIC_BOOLEAN_TRUE;
    	Map orderItem = (Map) object;
		String orderItemCd = MapUtils.getString(orderItem, "orderItemCd");
		if (Constant.ORDER_ITEM_CD_OFFER.equals(orderItemCd) && MDA.PKG_FILTER_OFFER_ITEM_OFFER_TYPES.contains(MapUtils.getString(orderItem, "offerType"))) {
			isShowOrderItem = Constant.LOGIC_BOOLEAN_FALSE;
		}
    	return isShowOrderItem;
    }
    
    protected boolean isPkgOrder(List<Map<String, Object>> orderItems) {
    	boolean isPkgOrder = Constant.LOGIC_BOOLEAN_FALSE;
    	if (Constant.LOGIC_STR_N.equals(MDA.PKG_FILTER_OFFER_ITEM_SWITCH)) {
    		return isPkgOrder;
    	}
    	for (Map<String, Object> orderItem: ListUtil.nvlList(orderItems)) {
    		String orderItemCd = MapUtils.getString(orderItem, "orderItemCd");
    		if (Constant.ORDER_ITEM_CD_OFFER_PKG.equals(orderItemCd)) {
    			isPkgOrder = Constant.LOGIC_BOOLEAN_TRUE;
    			break;
    		}
    	}
    	return isPkgOrder;
    }

    /**
     * 查询订单详情
     */
    public Map queryCustomOrderDetail(String json) throws Exception {
        String customOrderDetail = orderQuerySMO.queryCustomerOrderDetailsByCodId(json);
        return (Map) resolveResult(customOrderDetail);
    }

    public Map isOrderCommAttrItem(String jsonStr) throws Exception {
        Map paramMap = jsonConverter.toBean(jsonStr, Map.class);
        String customerOrderItemList = soQuerySMO.isOrderCommAttrItem(paramMap.get("coNbr").toString());
        Boolean isAfterPayOrder = (Boolean) resolveResult(customerOrderItemList);
        Map options = new HashMap();
        options.put("isAfterPayOrder", "N");
        if(isAfterPayOrder) {
            options.put("isAfterPayOrder", "Y");
        }
        return options;
    }

    /**
     *  查询是否有补退费的权限
     */
    public Map chenckFeePriv(String jsonStr) throws Exception {
        String retStr = staffDataPrvQuerySMO.checkSysUserPriv(jsonStr);
        Map retMap = (Map)resolveResult(retStr);
        return retMap;
    }

    /**
        1. 如果工号具备该权限，在做已竣工退补费的时候，只能对90天内的购物车做退补费操作
        2. 如果工号具备该权限，在做已竣工退补费的时候，只能对工号归属渠道的购物车做退补费操作
        3. 如果工号具备上述两个权限，在做已竣工退补费的时候，只能对工号归属渠道90天内的购物车做退补费操作
     */
    public Map qryReturnFeePriv(String jsonStr) throws Exception {
        Map privParams = jsonConverter.toBean(jsonStr, Map.class);
        Map retMap = new HashMap();
        privParams.put("privCode", AssistMDA.COMPLETED_AND_RETURN);
        String privRetStr = staffDataPrvQuerySMO.querySysUserPrivs(jsonConverter.toJson(privParams));
        Map privRetMap = (Map)resolveResult(privRetStr);
        List<Map> privsCList = (List)privRetMap.get("privs");
        String completedReturn = "0";
        String isLimit90Day = "0";
        String isLimitChannel = "0";
        if(privsCList != null){
            for(Map privMap : privsCList){
                if(privMap.get("privCode").equals(AssistMDA.COMPLETED_AND_RETURN)){
                    if(null!=privMap.get("busiObjNbr") && privMap.get("busiObjNbr").equals(AssistMDA.COMPLETED_AND_RETURN_DIMENSION) &&
                            null!=privMap.get("attrValue") && !privMap.get("attrValue").equals("")){
                        if(privMap.get("attrValue").equals("1")){
                            completedReturn ="1";
                        }
                        if(privMap.get("attrValue").equals("2")){
                            isLimit90Day ="1";
                        }
                        if(privMap.get("attrValue").equals("3")){
                            isLimitChannel ="1";
                        }
                    }
                }
            }
        }
        retMap.put("completedReturn",completedReturn);
        retMap.put("isLimit90Day", isLimit90Day);
        retMap.put("isLimitChannel",isLimitChannel);
        return retMap;
    }
    public Map commitCoManager(String inStr) throws IOException {
        Map<String, Object> jsonMap =jsonConverter.toBean(inStr, Map.class);
        Map<String, Object>  retString = agencyCapitalSMO.commitAgencyOrderDeal(jsonMap);
        return retString;
    }
    public  Map queryOneItemResults(String jsonStr) throws IOException {
        Map params = jsonConverter.toBean(jsonStr, Map.class);
        Long  custOrderId =(Long) params.get("custOrderId");
        Map<String, Object> retMap = Maps.newHashMap();
        String restult = billingSmo.queryOneItemResults(custOrderId);
        List oneItemResults =( List) resolveResult(restult);
        boolean isTrue=false;
        for(int i=0; i<oneItemResults.size(); i++){
            Map mag = (Map) oneItemResults.get(i);
            Integer orgId=(Integer) mag.get("orgId");
//            Long staffId=(Long) mag.get("staffId");
            String staffid =mag.get("staffId")==null ? "": mag.get("staffId").toString();
            Long staffId=Long.parseLong(staffid);
            //是特殊工号与渠道
            if(AssistMDA.SPECIAL_CHANNEL.equals(orgId.longValue())&& AssistMDA.SPECIAL_STAFF_B.equals(staffId)){
                isTrue=true;
                break;
            }
        }
        retMap.put("isTrue",isTrue);
        return retMap;

    }
    //查询是否是押金来源
    public  Map queryOneItemResultsSource(String jsonStr) throws IOException {
        Map params = jsonConverter.toBean(jsonStr, Map.class);
        Long  custOrderId =Long.parseLong(String.valueOf(params.get("custOrderId")));
        Map<String, Object> retMap = Maps.newHashMap();
        String restult = billingSmo.queryOneItemResults(custOrderId);
        List oneItemResults =( List) resolveResult(restult);
        boolean isTrue=false;
        for(int i=0; i<oneItemResults.size(); i++){
            Map mag = (Map) oneItemResults.get(i);
            Integer sourceType=(Integer) mag.get("sourceType");
            //是押金来源
            if(AssistMDA.DEP_SOURCE_TYPE.equals(sourceType)){
                isTrue =true;
            }
        }
        retMap.put("isTrue",isTrue);
        return retMap;

    }
	
	/**
     * 受理地区和安装地区切换
     */
    public Map changeRegion(String jsonStr) throws IOException {
        Map options = new HashMap();
        Map jsMap = jsonConverter.toBean(jsonStr, Map.class);
        Object isCheck = jsMap.get("isCheck");
        if (isCheck != null) {
            String check = (String) isCheck;
            if (StringUtils.equals(check,"1")) {
                options.put("regionName",AssistMDA.REGION_DATAS.get("acceptRegion"));
            }else {
                options.put("regionName", AssistMDA.REGION_DATAS.get("region"));
            }
        }
        return options;
    }

    /**
     * 查询订单无纸化信息
     * @param jsonStr
     * @return
     * @throws IOException
     */
    public Map queryDocGrpNbr(String jsonStr) throws IOException {
        Map param = jsonConverter.toMap(jsonStr, String.class, Object.class);
        Map staffParam = (Map) param.remove("staffParam");
        Map privParam = (Map) param.remove("privParam");
        String createStaff = MapUtils.getString(staffParam, "createStaff");
        String staffId = MapUtils.getString(staffParam, "staffId");
        if(!StringUtils.equals(createStaff, staffId)){
            privParam.put("privCode", MDA.USER_PRIV_RIGHTS.get("paperlessQryRight"));
            String res = staffDataPrvQuerySMO.checkSysUserPriv(jsonConverter.toJson(privParam));
            privParam = (Map) resolveResult(res);
            if (!MapUtils.getBoolean(privParam, "isHave")) {
                param.put("resultCode", 1);
                param.put("resultMsg", "无无纸化查询权限");
                return param;
            }
        }

        param.put("statusCd", "QZWC");
        // pdf下载签字后的文档
        param.put("pdfItems", Arrays.asList(MDA.CUST_ORDER_DOC_ITEMS.get("signedPdf").substring(0, 1)));
        String res = orderSaveSmo.queryPaperLessReceiptByCoNbr(jsonConverter.toJson(param));
        List<Map> docItems = (List<Map>) resolveResult(res);

        param.clear();
        if (docItems == null || docItems.size() == 0) {
            param.put("resultCode", 1);
            param.put("resultMsg", "该订单未做无纸化");
        }else{
            param.put("resultCode", 0);
            param.put("docGrpNbr", MapUtils.getString(docItems.get(0), "docGrpNbr"));
        }
        return param;
    }

    /**
     * 查询订单中的出库单号
     * @param params
     * @return
     */
    public Result queryDeliveryCodesFromOrder(String params) throws IOException {
        String res = orderQuerySMO.queryDeliveryCodesFromOrder(params);
        List<String> deliveryCodes = (List<String>) resolveResult(res);
        return  Result.success(deliveryCodes);
    }

    /**
     *  针对“客户确认中”状态的订单，可以进行作废操作
     */
    public Map revokeOrder(String jsonStr) throws Exception {
        String retStr = orderSaveSmo.dealOrderAfterSendMsg(jsonStr);
        Map retMap = (Map) resolveResult(retStr);
        return retMap;
    }
    
	/**
	 * 查询订单属性_购物车流水号
	 * 
	 * @param custOrderId
	 * @return
	 */
	public String viewOrder(String custOrderId) throws IOException {
		Map params = new HashMap();
		params.put("custOrderId", custOrderId);
		params.put("attrId", AssistMDA.SAVE_CS_NBR_ATTR_ID);
		String re = orderQuerySMO.qryOrderAttrsByCond(jsonConverter.toJson(params));
		List<Map> orderAttrVos = (List) resolveResult(re);
		String csNbr = "";
		if (!ListUtil.isListEmpty(orderAttrVos)) {
			csNbr = MapUtils.getString(orderAttrVos.get(0), "attrValue");
		}
		return AssistMDA.CHAIN_ORDER_INFO_URL + "?csNbr=" + csNbr;
	}
	
	/**
	 * 查询订单属性
	 * 
	 * @param custOrderId
	 * @param qryScope
	 * @return
	 * @throws Exception
	 */
	public String isEmergOrder(String custOrderId, String qryScope) throws Exception {
		Map params = new HashMap();
		if (StringUtils.isBlank(custOrderId)) {
			return Constant.LOGIC_STR_N;
		}
		params.put("attrId", MDA.EMERGENCY_ORDER_ATTR_ID);
		params.put("custOrderId", custOrderId);
		List orderAttrs = null;
		if ("11".equals(qryScope)) {
			String re = orderTransferLogSmo.qryOrderAttrsByCond(jsonConverter.toJson(params));
			Map resultObject = (Map) resolveResult(re);
			orderAttrs = (List) MapUtils.getObject(resultObject, "orderAttrs");
		} else {
			String re = orderQuerySMO.qryOrderAttrsByCond(jsonConverter.toJson(params));
			orderAttrs = (List) resolveResult(re);
		}
		return !ListUtil.isListEmpty(orderAttrs) ? Constant.LOGIC_STR_Y : Constant.LOGIC_STR_N;
	}

    /**
     * 埋点方法
     *
     * @param jsonString
     * @return
     * @throws Exception
     */
    public Map logRecorads(String jsonString) throws Exception {
        Map paramMap = jsonConverter.toBean(jsonString, Map.class);
        if("Y".equals(MDA.WATCH_QUERY_SENSITIVE_INFO_SWITCH.get("customerOrderOperate"))) {
            String elId = MapUtils.getString(paramMap, "elId");
            String point = "";
            if(!StringUtil.isEmpty(elId)){
                point = "customerOrderLabel"+MapUtils.getString(paramMap, "elId");
            }
            if(!StringUtil.isEmpty(MapUtils.getString(paramMap, "point"))){
                point = MapUtils.getString(paramMap, "point");
            }
            SmLogUtil.saveCommonQryCommonLog(Constant.COMMON_LOG_QYR_POINT, point, Constant.FUN_NAME_QUERY_OPERATE_ORDER, paramMap);
        }
        return new HashMap();
    }
    
}
