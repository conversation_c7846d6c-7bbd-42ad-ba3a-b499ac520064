package com.asiainfo.crm.assist.app.query;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.crm.bcomm.exception.BError;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.util.SmLogUtil;
import com.google.common.collect.Maps;

/**
 * Created by niewq on 2018/6/20.
 */
@Component("vita.transCertNumFiveDetail")
public class TransCertNumFiveDetail extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(TransCertNumFiveDetail.class);

	@Autowired
	private ICustMultiSMO custMultiSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
    	String jsonString = params[0].toString();
        return queryTransCertNumFive(jsonString);
    }

    @SuppressWarnings("unchecked")
	public Map<String, Object> queryTransCertNumFive(String jsonStr) throws Exception {
    	Map<String, Object> options = Maps.newHashMap();
        String res = custMultiSMO.queryTransOneCertNumFiveDetail(jsonStr);
        Map<String, Object> resultObjectMap =(Map<String, Object>) resolveResult(res);
        if (resultObjectMap == null) {
        	 options.put("transCertNumFiveDetail", null);
             return options;
		}
        Map<String, Object> transCertNumFiveDetail =  (Map<String, Object>) MapUtils.getObject(resultObjectMap,"transOneCertNumFiveDetail");
//        if (resultObjectMap.containsKey("transOneCertNumFiveDetail")) {
//        	transCertNumFiveDetail = (Map<String, Object>)resultObjectMap.get("transOneCertNumFiveDetail");
//		}
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int totalCount = MapUtils.getIntValue(pageInfoMap, "totalCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageSize = MapUtils.getIntValue(pageInfoMap, "pageSize");
        options.put("pageCount",pageIndex);
        options.put("pageIndex",pageSize);
        options.put("totalNumber", totalCount);
        options.put("transCertNumFiveDetail", transCertNumFiveDetail);
        return options;
    }
    
    @SuppressWarnings("unchecked")
	public  Map<String, Object> updateTransCertNumFive(String jsonStr) throws Exception {
    	Map<String, Object> retMap = new HashMap<String, Object>();
		String resultJson = "";
		try {
			logger.debug(jsonStr);
			jsonStr = SmLogUtil.supplyOneFivePatams(jsonStr);
			resultJson = custMultiSMO.dealTransOneCertNumFive(jsonStr);
			Map<String, Object> resultObject = (Map<String, Object>) resolveResult(resultJson);
			retMap.put("retCode", resultObject.get("resultCode"));
			retMap.put("retDesc", resultObject.get("resultMsg"));
			return retMap;
		} catch (Exception e) {
			retMap.put("retCode", "-1");
			if (e instanceof BError){
	            BError bError = (BError)e;
	            retMap.put("retDesc", bError.getMsg());
			}else{
				retMap.put("retDesc", e.getMessage());
			}
			return retMap;
		}
    	
    }
    
    @SuppressWarnings("unchecked")
	public  Map<String, Object> queryImageDetail(String jsonStr) throws Exception {
    	List<Map<String, Object>> imageList = new ArrayList<Map<String, Object>>();
    	Map<String, Object> options = Maps.newHashMap();
    	String resultJson  = custMultiSMO.queryCertImage(jsonStr);
    	Map<String, Object> resultObject = (Map<String, Object>) resolveResult(resultJson);
    	if (resultObject == null) {
       	 	options.put("imageDetail", null);
            return options;
		}
    	imageList =  (List<Map<String, Object>>) MapUtils.getObject(resultObject,"imageList");
//    	Map<String, Object> imageDetailF = new HashMap<String, Object>();
//    	imageDetailF.put("picFlag", "F");
//    	imageDetailF.put("picture", "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");
//    	Map<String, Object> imageDetailE = new HashMap<String, Object>();
//    	imageDetailE.put("picFlag", "E");
//    	imageDetailE.put("picture", "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");
//    	imageList.add(imageDetailF);
//    	imageList.add(imageDetailF);
    	 options.put("imageDetail", imageList);
    	return options;
    }

}
