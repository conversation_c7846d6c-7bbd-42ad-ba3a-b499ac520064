package com.asiainfo.crm.assist.app.query;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.asiainfo.angel.json.JsonConverter;
import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.bcomm.utils.LogHelper;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IAllowanceSMO;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.service.intf.ICustSMO;
import com.asiainfo.crm.service.intf.IProdInstSMO;
import com.asiainfo.crm.service.intf.IStaffDataPrvQuerySMO;
import com.google.common.collect.Maps;

/**
 * Created on 2017/8/24.
 */
@Component("vita.intfWhole")
public class IntfWhole extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(IntfWhole.class);

    @Autowired
    private IProdInstSMO prodInstSMO;
    
    @Autowired
    private ICustSMO custSMO;
    
    @Autowired
    private ICustMultiSMO custMultiSMO;
    
    @Autowired
	private IStaffDataPrvQuerySMO staffDataPrvQuerySMO;
    
    @Autowired
    private IAllowanceSMO allowanceSMO;
    


    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    /**
     * 查询停机用户列表
     *
     * @param json
     * @return
     * @throws IOException
     */
    public Map queryUserList(String json) throws Exception {
    	//调用资产中心获取停机记录
        String userList = prodInstSMO.qryProdInstStateListAtom(json);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(userList);
        Map<String, Object> options = Maps.newHashMap();
        List users = (List) MapUtils.getObject(resultObjectMap, "prodInstStates");
        if(null == users || users.size() < 1){
        	return options;
        }
        for (int i=0;i<users.size();i++) {
        	Map userMap = (Map) users.get(i);
        	Long prodInstId = (Long)userMap.get("prodInstId");//产品实例编码
        	Integer regionId = (Integer)userMap.get("regionId");//地区编码
        	String stopType = String.valueOf(userMap.get("stopType"));//停机类型
        	
        	//通过产品实例编码再查询资产中心获取产品规格和客户编码、号码
        	JsonConverter jsonConverter = JsonConverter.buildNormalConverter();
    		Map<String, Object> jsonMap = new HashMap<String, Object>();
    		jsonMap.put("prodInstId", prodInstId);
    		String paramInstStr = jsonConverter.toJson(jsonMap);
        	String instInfoRet = prodInstSMO.qryAccProdInstDetailLocal(paramInstStr);
        	Map<String, Object> instObjectMap = (Map<String, Object>) resolveResult(instInfoRet);
        	Map instInfo = (Map) MapUtils.getObject(instObjectMap, "prodInstDetail");
        	Integer prodId = (Integer)instInfo.get("prodId");//产品规格
        	Long ownerCustId = (Long)instInfo.get("ownerCustId");//客户编码
        	String accNum = String.valueOf(instInfo.get("accNum"));//号码
        	userMap.put("accNum", accNum);
        	
        	//根据客户编码查询客户名称
        	jsonMap.clear();
        	jsonMap.put("custId", ownerCustId);
        	String paramCustStr = jsonConverter.toJson(jsonMap);
        	String custInfoRet = custSMO.qryCustomerList(paramCustStr);
        	Map<String, Object> custObjectMap = (Map<String, Object>) resolveResult(custInfoRet);
        	List custInfos = (List) MapUtils.getObject(custObjectMap, "customers");
        	String custName = "";
        	if(null != custInfos && custInfos.size() > 0){
        		custName = (String) ((Map)custInfos.get(0)).get("custName");
        	}
        	userMap.put("userName", custName);
        	
        	//地区映射处理
        	String oldAreaId = AssistMDA.regionId.get(String.valueOf(regionId));
        	if(379 == prodId){
        		if("12102".equals(oldAreaId)){
        			userMap.put("credArea", "280");
        		}else{
        			userMap.put("credArea", AssistMDA.billRegionId_379.get(oldAreaId.subSequence(0, 3)));
        		}
        		
        	}else{
        		if("12102".equals(oldAreaId)){
        			userMap.put("credArea", "028");
        		}else{
        			userMap.put("credArea", AssistMDA.billRegionId.get(oldAreaId.subSequence(0, 3)));
        		}
        	}
        	//停机状态
        	if("150001".equals(stopType)){
        		userMap.put("statusName", "非实名单停");
        	}
        	if("150002".equals(stopType)){
        		userMap.put("statusName", "非实名双停");
        	}
		}
        
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", total);
        options.put("users", users);
        return options;
    }

    /**
     * 复机
     * @param json
     * @return
     * @throws Exception
     */
    public Map recoverConnect(String json) throws Exception{
    	
    	Map<String, Object> retMap = new HashMap<String, Object>();
    	Map<String, Object> paramMap = jsonConverter.toBean(json, Map.class);
    	Map<String, Object> userInfo = (Map<String, Object>) MapUtils.getObject(paramMap,"userInfo");
    	

        String prodInstId = String.valueOf(userInfo.get("prodInstId"));//用户实例编码
        String regionId = String.valueOf(userInfo.get("regionId"));
        String createStaff = String.valueOf(paramMap.get("createStaff"));
        String createOrgId = String.valueOf(paramMap.get("createOrgId"));
        
    	//复机需要实名
        Integer isRealName = custMultiSMO.qryIsRealNameUser(Long.valueOf(prodInstId));
        if(0 == isRealName){
        	retMap.put("retCode", "-1");	
            retMap.put("retDesc", "该用户实名制未完成，不允许复机！！");
            return retMap;
        }


        //组装订单信息
        Map<String, Object> customerOrder = new HashMap<String, Object>();
        Map<String, Object> ordProdItems = new HashMap<String, Object>();
        ordProdItems.put("acceptRegionId", regionId);
        ordProdItems.put("createOrgId", Long.valueOf(createOrgId));
        ordProdItems.put("orderSource", "1000");
        ordProdItems.put("createStaff", createStaff);
        ordProdItems.put("sysSource", "100005");
        
        List<Map<String, Object>> orderItemList = new ArrayList<Map<String, Object>>();
        
        //组装订单项
        Map<String, Object> orderItem = new HashMap<String, Object>();
        orderItem.put("orderItemId", -1);
        orderItem.put("serviceOfferId", 4060201002L);
        orderItem.put("createOrgId", Long.valueOf(createOrgId));
        
        //产品实例订单项
        Map<String, Object> ordProdInst = new HashMap<String, Object>();
        List<Map<String, Object>> prodInstList = new ArrayList<Map<String, Object>>();
        ordProdInst.put("prodInstId", prodInstId);
        ordProdInst.put("regionId", regionId);
        prodInstList.add(ordProdInst);
        orderItem.put("ordProdInsts", prodInstList);

        orderItemList.add(orderItem);
        
        ordProdItems.put("prodOrderItems", orderItemList);
        customerOrder.put("customerOrder", ordProdItems);
        customerOrder.put("orderFlow", new String[]{"CONFIRM"});
        String customerOrderId = "";
        try{

        	String orderInfoStr =  allowanceSMO.createCustomerOrderIntf(jsonConverter.toJson(customerOrder));
            Map<String, Object> orderInfoMap = (Map<String, Object>) resolveResult(orderInfoStr);
            //记录日志
			Map<String,Object> logData = new HashMap<String,Object>();
   			logData.put("method","recoverConnect");
   			logData.put("paramJson",jsonConverter.toJson(customerOrder));
   			logData.put("retStr",orderInfoStr);
   		    LogHelper.writeLog("recoverConnect",logData);
   		    
            String resultCode = String.valueOf(orderInfoMap.get("resultCode"));
            String resultMsg =  String.valueOf(orderInfoMap.get("resultMsg"));
            customerOrderId =  String.valueOf(orderInfoMap.get("custOrderNbr"));
            if(StringUtils.isNotEmpty(resultCode) && !"0".equals(resultCode)){
            	retMap.put("retCode", "-1");	
            	retMap.put("retDesc", resultMsg);
            	return retMap;
            }
        }catch(Exception e){
        	retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "生成订单异常:"+e.getMessage());
    		return retMap;
        }
        retMap.put("retCode", "0");	
        retMap.put("retDesc", "提交成功，订单流水编号: "+customerOrderId);
        return retMap;
    }
    
    /**
     * 临时复机
     * @param json
     * @return
     * @throws Exception
     */
    public Map temporaryRecover(String json) throws Exception{
    	
    	Map<String, Object> retMap = new HashMap<String, Object>();
    	Map<String, Object> reqMap = new HashMap<String, Object>();
    	Map<String, Object> paramMap = jsonConverter.toBean(json, Map.class);
    	Map<String, Object> userInfo = (Map<String, Object>) MapUtils.getObject(paramMap,"userInfo");
    	Long sysUserId = Long.valueOf(String.valueOf(paramMap.get("sysUserId")));
    	String prodInstId = String.valueOf(userInfo.get("prodInstId"));//用户实例编码
        String regionId = String.valueOf(userInfo.get("regionId"));
        String createStaff = String.valueOf(paramMap.get("createStaff"));
        String createOrgId = String.valueOf(paramMap.get("createOrgId"));

    	
    	//判断用户是否有操作临时复机权限
    	reqMap.put("sysUserId", sysUserId);
    	reqMap.put("privCode", AssistMDA.INTF_RIGHTS);
    	String privInfo = staffDataPrvQuerySMO.checkSysUserPriv(jsonConverter.toJson(reqMap));
    	Map<String, Object> privInfoMap = (Map<String, Object>) resolveResult(privInfo);
    	String isHave = String.valueOf(privInfoMap.get("isHave"));
    	if(StringUtils.isEmpty(isHave) || !"true".equals(isHave)){
    		retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "该用户没有临时复机操作权限！");
    		return retMap;
    	}

        //组装订单信息
        Map<String, Object> customerOrder = new HashMap<String, Object>();
        Map<String, Object> ordProdItems = new HashMap<String, Object>();
        ordProdItems.put("acceptRegionId", regionId);
        ordProdItems.put("createOrgId", Long.valueOf(createOrgId));
        ordProdItems.put("orderSource", "1000");
        ordProdItems.put("createStaff", createStaff);
        ordProdItems.put("sysSource", "100005");
        
        List<Map<String, Object>> orderItemList = new ArrayList<Map<String, Object>>();
        
        //组装订单项
        Map<String, Object> orderItem = new HashMap<String, Object>();
        orderItem.put("orderItemId", -1);
        orderItem.put("serviceOfferId", 4060201001L);
        orderItem.put("createOrgId", Long.valueOf(createOrgId));
        
        //产品实例订单项
        Map<String, Object> ordProdInst = new HashMap<String, Object>();
        List<Map<String, Object>> prodInstList = new ArrayList<Map<String, Object>>();
        ordProdInst.put("prodInstId", prodInstId);
        ordProdInst.put("regionId", regionId);
        prodInstList.add(ordProdInst);
        orderItem.put("ordProdInsts", prodInstList);

        orderItemList.add(orderItem);
        
        ordProdItems.put("prodOrderItems", orderItemList);
        customerOrder.put("customerOrder", ordProdItems);
        customerOrder.put("orderFlow", new String[]{"CONFIRM"});
        String customerOrderId = "";
        try{
        	String orderInfoStr =  allowanceSMO.createCustomerOrderIntf(jsonConverter.toJson(customerOrder));
            Map<String, Object> orderInfoMap = (Map<String, Object>) resolveResult(orderInfoStr);
            //记录日志
			Map<String,Object> logData = new HashMap<String,Object>();
   			logData.put("method","temporaryRecover");
   			logData.put("paramJson",jsonConverter.toJson(customerOrder));
   			logData.put("retStr",orderInfoStr);
   		    LogHelper.writeLog("temporaryRecover",logData);
   		    
            String resultCode = String.valueOf(orderInfoMap.get("resultCode"));
            String resultMsg =  String.valueOf(orderInfoMap.get("resultMsg"));
            customerOrderId =  String.valueOf(orderInfoMap.get("custOrderNbr"));
            if(StringUtils.isNotEmpty(resultCode) && !"0".equals(resultCode)){
            	retMap.put("retCode", "-1");	
            	retMap.put("retDesc", resultMsg);
            	return retMap;
            }
        }catch(Exception e){
        	retMap.put("retCode", "-1");	
        	retMap.put("retDesc", "生成订单异常:"+e.getMessage());
    		return retMap;
        }
        retMap.put("retCode", "0");	
        retMap.put("retDesc", "提交成功，订单流水编号: "+customerOrderId);
        return retMap;
    }
}
