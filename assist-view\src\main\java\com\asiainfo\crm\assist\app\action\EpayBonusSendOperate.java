package com.asiainfo.crm.assist.app.action;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.IFinanceSignSMO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by wenhy on 2017/7/20.
 */
@Component("vita.epayBonusSendOperate")
public class EpayBonusSendOperate extends AbstractComponent {

    private static final Logger LOGGER = LoggerFactory.getLogger(EpayBonusSendOperate.class);

    @Autowired
    private IFinanceSignSMO financeSignSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    public Map queryBestpayBonusData(String jsonStr) throws Exception {
        String epayBonusDataStr = financeSignSMO.queryBestpayBonusData(jsonStr);
//        epayBonusDataStr = "{\n" +
//                "\t\"resultCode\":\"0\",\n" +
//                "\t\"resultMsg\":\"处理成功!\",\n" +
//                "\t\"resultObject\":{\n" +
//                "\t\t\"bonusList\":[\n" +
//                "\t\t\t{\n" +
//                "\t\t\t\t\"accNum\":\"18180536447\",\n" +
//                "\t\t\t\t\"acceptSeqNo\":\"510201609302001099731502149913\",\n" +
//                "\t\t\t\t\"bonusAmount\":2,\n" +
//                "\t\t\t\t\"custOrderId\":100844903284,\n" +
//                "\t\t\t\t\"dealStatus\":\"RD\",\n" +
//                "\t\t\t\t\"offerId\":100030005040,\n" +
//                "\t\t\t\t\"respCode\":\"BR9999\",\n" +
//                "\t\t\t\t\"rowNum\":1,\n" +
//                "\t\t\t\t\"seq\":0,\n" +
//                "\t\t\t\t\"statusDate\":\"2016-09-30 20:01:16\"\n" +
//                "\t\t\t},\n" +
//                "\t\t\t{\n" +
//                "\t\t\t\t\"accNum\":\"17380617581\",\n" +
//                "\t\t\t\t\"acceptSeqNo\":\"510201609301848064521502149191\",\n" +
//                "\t\t\t\t\"bonusAmount\":2,\n" +
//                "\t\t\t\t\"custOrderId\":100844856909,\n" +
//                "\t\t\t\t\"dealStatus\":\"RD\",\n" +
//                "\t\t\t\t\"offerId\":100030005040,\n" +
//                "\t\t\t\t\"respCode\":\"PA9999\",\n" +
//                "\t\t\t\t\"rowNum\":2,\n" +
//                "\t\t\t\t\"seq\":16,\n" +
//                "\t\t\t\t\"statusDate\":\"2016-09-30 18:48:11\"\n" +
//                "\t\t\t},\n" +
//                "\t\t\t{\n" +
//                "\t\t\t\t\"ACCEPTSEQ_NO\":\"510201609301732466541502148228\",\n" +
//                "\t\t\t\t\"ACC_NBR\":\"18108220558\",\n" +
//                "\t\t\t\t\"BONUS_AMOUNT\":2,\n" +
//                "\t\t\t\t\"DEAL_STATUS\":\"D\",\n" +
//                "\t\t\t\t\"OFFER_SPEC_ID\":100030005040,\n" +
//                "\t\t\t\t\"OL_ID\":100844811423,\n" +
//                "\t\t\t\t\"RESP_CODE\":\"PA9999\",\n" +
//                "\t\t\t\t\"ROWNUM_\":3,\n" +
//                "\t\t\t\t\"SEQ\":0,\n" +
//                "\t\t\t\t\"STATUS_DATE\":\"2016-09-30 17:32:53\"\n" +
//                "\t\t\t}\n" +
//                "\t\t],\n" +
//                "\t\t\"pageInfo\":{\n" +
//                "\t\t\t\"limit\":10,\n" +
//                "\t\t\t\"offset\":0,\n" +
//                "\t\t\t\"pageCount\":2,\n" +
//                "\t\t\t\"pageIndex\":1,\n" +
//                "\t\t\t\"pageSize\":10,\n" +
//                "\t\t\t\"rowCount\":20,\n" +
//                "\t\t\t\"scope\":null,\n" +
//                "\t\t\t\"selectCount\":true\n" +
//                "\t\t}\n" +
//                "\t}\n" +
//                "}";
//

        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(epayBonusDataStr);
        Map<String, Object> options = Maps.newHashMap();
        List bonusList = (List) MapUtils.getObject(resultObjectMap, "bonusList");
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap,"pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount",pageCount);
        options.put("pageIndex",pageIndex);
        options.put("totalNumber", total);
        options.put("bonusList", bonusList);
        return options;
    }

    public Map reqBonusAgain(String jsonStr) throws Exception {
        String retStr = financeSignSMO.reqBestpayBonusAgain(jsonStr);
        Map resultObject = (Map) resolveResult(retStr);
        return resultObject;
    }

    public Map resetBonusStatus(String jsonStr) throws Exception {
        String retStr = financeSignSMO.resetBestpayBonusStatus(jsonStr);
        Map resultObject = (Map) resolveResult(retStr);
        return resultObject;
    }

}
