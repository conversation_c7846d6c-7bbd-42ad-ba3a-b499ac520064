(function (vita) {
    var sesameCreditUnsign = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_qrySesameCreditInfoByCert",
            "click #unsign": "_sesameCreditUnsign",
            "click #sesameUser_list tr" : "_selectSesameUser"
        },
        _initialize: function () {
            var widget = this;
        },
        global: {
            pageIndex: 1,
            pageSize: 10
        },
        _qrySesameCreditInfoByCert : function () {
            var widget = this,element = $(widget.el);
            var gsession = widget.gSession;
            element.find("#sesameUserIdListTips").hide();
            var certNum = $.trim($("#certNum").val());
            if(certNum == null || certNum == ""){
                widget.popup("请输入身份证号码！");
                return;
            }
            var params = {
                certNum: certNum,
                busiRegionId : gsession.staffRegionId
            };
            var retFlag = 0;
            widget.callService("qrySesameCreditInfoByCert", params, function (re) {
                if(re.sesameUserIdList && re.sesameUserIdList.length && re.sesameUserIdList.length == 1){
                    params.aliPayUserId = re.sesameUserIdList[0].aliPayUserId;
                } else if(re.sesameUserIdList && re.sesameUserIdList.length && re.sesameUserIdList.length > 1) {
                    var infoParams = {
                        sesameUserIdList: re.sesameUserIdList
                    }
                    widget.refreshPart("refreshInfoListTips", JSON.stringify(infoParams), "#sesameUserIdListTips", function (re) {
                    }, { async: false, headers:{}});
                    element.find("#sesameUserIdListTips").show();
                    retFlag = -2;
                } else {
                    retFlag = -1;
                }
            },{async: false, headers:{}});
            if(retFlag == -1) {
                widget.popup("未查询到数据！");
                return;
            } else if(retFlag == -2) {
                return;
            }
            widget._refeshSesameCreditList(params);
        },
        _refeshSesameCreditList: function (params) {
            debugger;
            var widget = this;
            widget.refreshPart("qrySesameCreditUnsignInfo", JSON.stringify(params), "#sesameCreditUnsignResult", function (re) {
            }, { async: false, headers:{}});
        },
        _selectSesameUser: function(e) {
            var widget = this,
                element = $(widget.el);
            var span = $(e.target).closest("tr").find('[name="trData"]');
            var user = span.data("user");
            element.find("#sesameUserIdListTips").hide();
            widget._refeshSesameCreditList(user);
        },
        _sesameCreditUnsign: function() {
            var widget = this,element = $(widget.el);
            var gsession = widget.gSession;
            var orders = element.find('table input[name="payment"]:checked');
            if(orders.length == 0) {
                widget.popup("请选择一条数据！");
                return false;
            }
            var $order = $(orders[0]);
            var $tr = $($order.closest("tr"));
            var info = $($tr.find('[name="trData"]')).data("info");
            if(info) {
                info.busiRegionId = gsession.staffRegionId;
            }
            widget.callService("sesameCreditUnsignAction", info, function (re) {
                if (re.handleResultCode == 0) {
                    widget.popup("解约成功！");
                    var params = {
                        alipayUserId : info.alipayUserId,
                        certNum : info.certNo,
                        busiRegionId : gsession.staffRegionId
                    }
                    widget._refeshSesameCreditList(params);
                } else {
                    widget.popup("解约失败，原因："+re.handleResultMsg);
                }
            },{async: false, headers:{}});
        }
    });
    vita.widget.register("sesameCreditUnsign", sesameCreditUnsign, true);
})(window.vita);