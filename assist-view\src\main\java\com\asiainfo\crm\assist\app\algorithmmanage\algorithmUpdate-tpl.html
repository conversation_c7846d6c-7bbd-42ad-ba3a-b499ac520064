<div data-widget="algorithmUpdate" style="height:100%">
    <p class="vita-data">{"data":$options}</p>
    <div class="calctitle">
        <div class="titlefont">
            <i class="fa-lightn glyphicon textcolorgreen text18 mar15"></i>算法修改
        </div>
        <div class="toolr">
            <button id="submitBtn" type="button" class="btn btn-primary btn-sm okbutt">确认</button>
            <button id="cancelBtn" type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">×</span>
            </button>
        </div>
    </div>
    <div class="calcw_rightcont marltb15">
        <form>
            <div class="form-group col-md-12 ">
                <label class="col-md-2 control-label lablep">算法编码</label>
                <div class="col-md-6">
                    <input id="algoNbr" name="algoNbr" type="text" class="form-control" placeholder="输入算法编码">
                    <p class="vita-bind" model="algoNbr"></p>
                </div>
            </div>
            <div class="form-group col-md-12 ">
                <label class="col-md-2 control-label lablep">算法描述</label>
                <div class="col-md-6">
                    <textarea id="algoDesc" name="algoDesc" class="form-control" rows="3" placeholder="输入算法描述"></textarea>
                    <p class="vita-bind" model="algoDesc"></p>
                </div>
            </div>
            <div class="form-group col-md-12 ">
                <label class="col-md-2 control-label lablep">状态</label>
                <div class="col-md-6">
                    <select id="statusCd" class="form-control">
                        #foreach($item in $!options.dataSecurityStatusCds.entrySet())
                        <option value="$!{item.key}">$!{item.value}</option>
                        #end
                    </select>
                    <p class="vita-bind" model="statusCd"></p>
                </div>
            </div>
        </form>
    </div>
</div>