<div data-widget="batchOrgFilesDownLoad">
    <p class="vita-data">{"importBatchId" : $options.importBatchId}</p>
    <div class="page_main nopagenav">
        <div class="wmin_content">
            <div class="col-lg-12">
            </div>
            <div class="col-lg-12 mart10" id="auditAdjustInfoListResult">
                <div class="wmin_content" id="batctOrgListR">
                    <div class="col-lg-12 mart10" id="batchOrgList">
                        <table class="table table-hover">
                            <thead>
                            <tr id="batchOrgTable" style="display: none;">
                                <th>文件名称</th>
                                <th>文件类型</th>
                                <th>下载</th>
                            </tr>
                            <tr>
                                <th>文件名称</th>
                                <th>文件类型</th>
                                <th>下载</th>
                            </tr>

                            </thead>

                            <tbody>
                            #if($options != "null" && $options.orgFileVoList != "null" && $options.orgFileVoList.size() > 0)
                            #foreach($file in $options.orgFileVoList)
                            <tr>
                                <td>$!{file.fileName}</td>
                                <td>
                                    #if($file.fileType == "0")
                                    用户信息EXCEL
                                    #elseif($file.fileType == "1")
                                    审批资料
                                    #end
                                </td>
                                <td>
                                    <a class="textcolorgreen" data-placement="top" data-toggle="tooltip" link="downloadFile" title="点击下载文件">下载</a>
                                    <p class="vita-data">{"data":$!file}</p>
                                </td>
                            </tr>
                            #end
                            #elseif($options.orgFileVoList != "null" || $options.orgFileVoList.size() == 0)
                            <tr>
                                <td align='center' colspan='7'>未查询到数据！
                                <td>
                            </tr>
                            #end
                            </tbody>
                        </table>
                    </div>
                </div>
                <!--翻页start -->
                <div id="showPageInfo">
                </div>
                #if($options.totalNumber && $options.totalNumber != "null")
                <p class="vita-data">{"totalNumber":$!options.totalNumber}</p>
                #end
                <!--翻页end -->
            </div>
        </div>
    </div>
    </div>
</div>
