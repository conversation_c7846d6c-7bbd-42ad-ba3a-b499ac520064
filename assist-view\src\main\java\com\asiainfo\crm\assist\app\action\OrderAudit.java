package com.asiainfo.crm.assist.app.action;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IMenuVisitSMO;
import com.asiainfo.crm.service.intf.IOrderAuditSMO;
import com.asiainfo.crm.service.intf.IStaffInfoQuerySMO;
import com.asiainfo.crm.util.ListUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created on 2019/3/27.
 */
@Component("vita.orderAudit")
public class OrderAudit extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(OrderAudit.class);

    @Autowired
    private IOrderAuditSMO orderAuditSMO;

    @Autowired
    private IMenuVisitSMO smAuthMngSmo;

    @Autowired
    private IStaffInfoQuerySMO staffInfoQuerySMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        String reqString = params[0].toString();
        Map reqRoleMap = jsonConverter.toBean(reqString, Map.class);
        reqRoleMap.put("sysRoleCode", MDA.ORDER_AUDIT_SMS_ROLE);
        Map rspMap = (Map) resolveResult(smAuthMngSmo.queryRolesListByUser(jsonConverter.toJson(reqRoleMap)));
        String isHaveRole = "N";
        if (rspMap.containsKey("rows")) {
            List rows = (List) rspMap.get("rows");
            if (!ListUtil.isListEmpty(rows) && rows.size() > 0) {
                isHaveRole = "Y";
            }
        }
        if ("Y".equals(AssistMDA.SWITCH_ORDER_AUDIT_STAFF_REGION_PRIV) && isHaveRole.equals("Y")) {
            //限制区级工号只能受理当前地区，全区工号可以选择地区
            Map staffReq = new HashMap(1);
            staffReq.put("staffId", MapUtils.getString(reqRoleMap, "sysUserId", "-1"));
            Map staffMap = (Map) resolveResult(staffInfoQuerySMO.getStaffBaseInfo(jsonConverter.toJson(staffReq)));
            if (MapUtils.isNotEmpty(staffMap) && staffMap.containsKey("commonRegionLevel")) {
                Integer commonRegionLevel = MapUtils.getInteger(staffMap, "commonRegionLevel", 30);
                isHaveRole = commonRegionLevel < 30 ? "Y" : "N";
            } else {
                isHaveRole = "N";
            }
        }
        String serviceOfferList = orderAuditSMO.qryAuditServiceOfferList();
        List serviceOffers = (List) resolveResult(serviceOfferList);
        List orderAuditStatusList = new ArrayList(4);
        if (MapUtils.isNotEmpty(AssistMDA.ORDER_AUDIT_STATUS_MENU)) {
            Iterator iterator = AssistMDA.ORDER_AUDIT_STATUS_MENU.keySet().iterator();
            while (iterator.hasNext()) {
                Object key = iterator.next();
                Object value = AssistMDA.ORDER_AUDIT_STATUS_MENU.get(key);
                Map statusMap = new HashMap(2);
                statusMap.put("statusNbr", key);
                statusMap.put("statusName", value);
                orderAuditStatusList.add(statusMap);
            }
        }
        Map<String, Object> options = Maps.newHashMap();
        options.put("serviceOffers", serviceOffers);
        options.put("isHaveRole", isHaveRole);
        options.put("orderAuditStatusMenu", orderAuditStatusList);
        options.put("orderAuditHisSwitch", AssistMDA.SWITCH_ORDER_AUDIT_HIS);
        return options;
    }

    /**
     * 查询订单稽核列表
     *
     * @param json
     * @return
     * @throws Exception
     */
    public Map queryOrderAuditList(String json) throws Exception {
        String auditOrderInfoList = orderAuditSMO.qryNeedAuditOrderInfo(json);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(auditOrderInfoList);
        Map<String, Object> options = Maps.newHashMap();
        List auditOrderInfos = (List) MapUtils.getObject(resultObjectMap, "auditOrderInfoVos");
        Map<String, Object> pageInfoMap = (Map<String, Object>) MapUtils.getObject(resultObjectMap, "pageInfo");
        int total = MapUtils.getIntValue(pageInfoMap, "rowCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageCount = MapUtils.getIntValue(pageInfoMap, "pageCount");
        options.put("pageCount", pageCount);
        options.put("pageIndex", pageIndex);
        options.put("totalNumber", total);
        options.put("auditOrderInfos", auditOrderInfos);
        options.put("orderAuditHisSwitch", AssistMDA.SWITCH_ORDER_AUDIT_HIS);
        return options;
    }

    /**
     * 抢单
     *
     * @param json
     * @return
     * @throws Exception
     */
    public Map grabOrder(String json) throws Exception {
        String grabOrder = orderAuditSMO.rapOrder(json);
        Map<String, String> retMap = (Map<String, String>) resolveResult(grabOrder);
        Map<String, Object> options = Maps.newHashMap();
        String auditResult = MapUtils.getString(retMap, "auditResult");
        String auditResultName = MapUtils.getString(retMap, "auditResultName");
        String statusDate = MapUtils.getString(retMap, "statusDate");
        String code = MapUtils.getString(retMap, "code");
        String desc = MapUtils.getString(retMap, "desc");
        options.put("auditResult", auditResult);
        options.put("auditResultName", auditResultName);
        options.put("statusDate", statusDate);
        options.put("code", code);
        options.put("desc", desc);
        return options;
    }
}