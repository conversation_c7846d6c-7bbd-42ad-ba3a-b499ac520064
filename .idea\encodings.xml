<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding" native2AsciiForPropertiesFiles="true" defaultCharsetForPropertiesFiles="UTF-8">
    <file url="file://$PROJECT_DIR$/../local/sd/local-view/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../local/sd/local-view/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common-view/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common-view/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/offerpackage-view/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/offerpackage-view/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/so-view/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/so-view/src/main/java/com/asiainfo/crm/so/custview/CommProductQuery.java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/so-view/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/so-web/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/so-web/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/soweb-compile/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/soweb-compile/src/main/resources" charset="UTF-8" />
  </component>
</project>