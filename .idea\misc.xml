<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/soweb-compile/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" project-jdk-name="1.8" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
  <component name="SvnBranchConfigurationManager">
    <option name="myConfigurationMap">
      <map>
        <entry key="$PROJECT_DIR$">
          <value>
            <SvnBranchConfiguration>
              <option name="trunkUrl" value="http://10.19.195.99:8001/svn/hxbss/CRM3.0/SOURCE/code/web" />
            </SvnBranchConfiguration>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>