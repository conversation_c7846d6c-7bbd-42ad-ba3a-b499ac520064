package com.asiainfo.crm.assist.app.orderProcess;

import com.alibaba.fastjson.JSONObject;
import com.asiainfo.appframe.ext.flyingserver.org.apache.commons.collections.MapUtils;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICrmTools;
import com.asiainfo.crm.service.intf.IOrderQueryMultiSMO;
import com.asiainfo.crm.service.intf.IOrderQuerySMO;
import com.asiainfo.crm.service.intf.IOrderSMO;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Component("vita.orderProcess")
public class OrderProcess extends AbstractComponent {
    private static final Logger logger = LoggerFactory.getLogger(OrderProcess.class);
    @Autowired
    private IOrderSMO iOrderSMO ;
    @Override
    public Map achieveData(Object... params) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        return options;
    }

    public Map buildOrderNode(String params) throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        String result=iOrderSMO.buildOrderNode(params);
        Map map = jsonConverter.toBean(result, Map.class);
        List messageList = (List) MapUtils.getObject(map, "resultObject");
        options.put("messageList", messageList);
        return  options;
    }




}
