package com.asiainfo.crm.assist.app.cust;

import org.apache.commons.lang.StringUtils;
import com.asiainfo.crm.assist.AssistMDA;
//import com.asiainfo.crm.assist.local.service.intf.IDishonestPersonSmo;
//import com.asiainfo.crm.assist.local.service.intf.ILocalSoQuerySmo;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.*;
import com.asiainfo.crm.util.ListUtil;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import studio.raptor.ddal.common.util.StringUtil;

import java.io.IOException;
import java.util.*;

@Component("vita.custMerge")
public class CustMerge extends AbstractComponent {

    @Autowired
    private ICustSMO custSMO;
    @Autowired
    private IOrderSMO orderSMO;
    @Autowired
    IProdInstSMO instProdQuerySmo;
    @Autowired
    private ISceneSMO sceneSMO;
    @Autowired
    private IProdInstSMO prodInstSMO;
    @Autowired
    private IOrderQuerySMO orderQuerySMO;
    @Autowired
    private ISoSaveSMO soSaveSMO;
//    @Autowired
//    private ILocalSoQuerySmo localSoQuerySmo;
    @Autowired
    private IStaffDataPrvQuerySMO staffDataPrvQuerySMO;
//    @Autowired
//    private IDishonestPersonSmo dishonestPersonSmo;
    @Autowired
    private ICustMultiSMO custMultiSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        String qryCustCompCfgRsp = custSMO.qryCustCompCfgInfo();
        Map custCompCfgMap = (Map) resolveResult(qryCustCompCfgRsp);
        custCompCfgMap.put("groupYuanSwitch", AssistMDA.CUST_MERGE_YUAN_SWITCH);
        return custCompCfgMap;
    }

    public Map qryCustomerList(String json) throws Exception {

//        String custListStr = custMultiSMO.qryCustomerListForMulti(json);
        String custListStr = custMultiSMO.qryCustomerMergeList(json);//查询客户信息
        Map resultMap = jsonConverter.toBean(custListStr, Map.class);
        List customers = (List) (((Map) resultMap.get("resultObject")).get("customers"));
        if (customers == null) {
            return resultMap;
        }
        List<Object> newCustomers = new ArrayList<Object>();
        for (Object customer : customers) {
            String useCustId = ((Map) customer).get("custId").toString();
            int pageIndex = 1;
            int pageCount = 10;
            StringBuffer accNumbuf = new StringBuffer();
            List<String> prodIds = new ArrayList<String>();
            List<String> prodInstIds = new ArrayList<String>();


            while (pageIndex <= pageCount) {
                Map pageInfo = new HashMap();
                pageInfo.put("pageIndex", pageIndex);
                pageInfo.put("pageSize", 100);
                Map qp = new HashMap();
                qp.put("ownerCustId", useCustId);
                qp.put("pageInfo", pageInfo);
                //String prodInst = instProdQuerySmo.qryAccProdInstListAtom(jsonConverter.toJson(qp));//查询产品
                String prodInst = prodInstSMO.qryAccProdInstList(jsonConverter.toJson(qp));//查询接入类产品
                pageIndex++;
                Map acctMap = jsonConverter.toBean(prodInst, Map.class);
                List acctList = (List) (((Map) acctMap.get("resultObject")).get("accProdInsts"));
                if (acctList == null || acctList.isEmpty()) {
                    break;
                }
                Map page = (Map) ((Map) acctMap.get("resultObject")).get("pageInfo");
                pageCount = MapUtils.getInteger(page, "pageCount", 0);

                if (acctList != null && acctList.size() > 0) {
                    for (Object acct : acctList) {
                        accNumbuf.append("," + ((Map) acct).get("accNum")).toString();
                        prodIds.add(((Map) acct).get("prodId").toString());
                        prodInstIds.add(((Map) acct).get("prodInstId").toString());
                    }
                }
            }
            if (ListUtil.isListEmpty(prodIds)) continue;
            String str = accNumbuf.toString().substring(1);
            ((Map) customer).put("accNum", str);
            ((Map) customer).put("prodIds", prodIds);
            ((Map) customer).put("prodInstIds", prodInstIds);
            newCustomers.add(customer);
        }


        ((Map) resultMap.get("resultObject")).put("customers", newCustomers);
        resultMap.put("success", true);

        return resultMap;
    }

    //查询客户信息对应的业务编号
    public Map qryAcctNbr(String jsonString) throws IOException {
        Map map = new HashMap<>();
        String prodInst = instProdQuerySmo.qryFuncProdInstListAtom(jsonString);
        Map resultMap = jsonConverter.toBean(prodInst, Map.class);
        List list = (List) resultMap.get("resultObject");
        List<String> strs = new ArrayList<String>();
        for (Object object : list) {
            String accNum = ((Map) object).get("accNum").toString();
            strs.add(accNum);
        }
        String str = StringUtils.join(list, ",");
        map.put("accNum", str);
        return map;
    }

    /**
     * 提交.
     *
     * @param params
     * @return
     * @throws Exception
     */
    public Map saveSceneData(String params) throws Exception {
        //保存营销订购附属场景
        String re = sceneSMO.saveSceneData(params);
        return (Map) resolveResult(re);
    }

    public Map operationVerify(String params) throws Exception {
        String res = orderQuerySMO.qryCustomerOrdersCountByCustId(params);
        Map resultMap = jsonConverter.toBean(res, Map.class);
        return resultMap;
    }

    public Map saveInitCustToken(String jsonString) throws Exception {
        String re = soSaveSMO.saveInitCustToken(jsonString);
        Map initToken = (Map) resolveResult(re);
        Map result = new HashMap();
        result.put("custTokenId", initToken.get("custTokenId"));
        return result;
    }


    /**
     * 提交订单
     *
     * @param inStr
     * @return
     * @throws IOException
     */
    public Map commitOrder(String inStr) throws IOException {
        String retString = null;
        if ("Y".equals(MDA.COMMIT_ORDER_METHOD_SWITCH)) {
            retString = orderSMO.noTransCommitCustomerOrder(inStr);
        } else {
            retString = orderSMO.commitCustomerOrder(inStr);
        }
        return (Map) resolveResult(retString);
    }

    /**
     * 获取跳过打印免填单权限
     *
     * @param jsonStr
     * @return
     */
    public Map<String, Object> getAuthSkipReceipt(String jsonStr) throws IOException {
        Map<String, Object> result = new HashMap<>();
        Map resMap = jsonConverter.toBean(jsonStr, Map.class);
        String staffId = MapUtils.getString(resMap, "staffId");
        Map<String, Object> jsonOptions = new HashMap<>();
        if (org.apache.commons.lang.StringUtils.isBlank(staffId)) {
            result.put("success", false);
            result.put("msg", "员工ID不能为空");
            return result;
        }
        jsonOptions.put("sysUserId", staffId);

        jsonOptions.put("privCode", AssistMDA.SKIP_RECEIPT);
        String privInfo = staffDataPrvQuerySMO.checkSysUserPriv(jsonConverter.toJson(jsonOptions));
        Map<String, Object> mapReslutSign = (Map<String, Object>) resolveResult(privInfo);
        int status = (boolean) mapReslutSign.get("isHave") ? 1 : 0;//true 跳过回执
        result.put("success", true);
        result.put("status", status);
        return result;
    }

    /**
     * 获取打印免填单状态
     *
     * @param jsonStr
     * @return
     */
    public Map<String, Object> getReceiptStatus(String jsonStr) throws IOException {
        Map<String, Object> result = new HashMap<>();
        Map resMap = jsonConverter.toBean(jsonStr, Map.class);
        if ("Y".equals(AssistMDA.RECEIPT_STATUS_SWITCH)) {

            String custOrderNbr = MapUtils.getString(resMap, "custOrderNbr");
            String glbSessionId = MapUtils.getString(resMap, "glbSessionId");
            if (StringUtil.isBlank(custOrderNbr)) {
                result.put("success", false);
                result.put("msg", "订单流水为空！");
                return result;
            }
            if (StringUtil.isBlank(glbSessionId)) {
                result.put("success", false);
                result.put("msg", "glbSessionId为空！");
                return result;
            }
            String resultStr = "";//localSoQuerySmo.qryReceiptStatus(jsonStr);
            Map map = (Map) resolveResult(resultStr);
            result.put("status", map.get("status"));
        } else {
            result.put("status", 1);
        }
        result.put("success", true);
        return result;
    }

    /**
     * 判断当前客户下的号码是否为失信号码
     *
     * @param jsonString
     * @return
     */
    public Map isDishonestPerson(String jsonString) throws IOException {

        // 返回值
        Map returnMap = new HashMap();

        // 根据客户ID获取客户下号码信息
        String prodInstIdStr = prodInstSMO.qryAccProdInstListLocal(jsonString);
        Map cprodInstDetailMap = jsonConverter.toBean(prodInstIdStr, Map.class);
        Map prodInstObj = (Map) cprodInstDetailMap.get("resultObject");
        if (prodInstObj != null) {
            List<Map> accProdInsts = (List<Map>) prodInstObj.get("accProdInsts");
            if (accProdInsts != null && accProdInsts.size() > 0) {
                for (Map accprodInst : accProdInsts) {
                    // 判断当前号码是否为失信号码
                    Map<String, Object> param = new HashMap<String, Object>();
                    param.put("accNum", accprodInst.get("accNum"));
                    // 增加失信人员限制业务受理
                    Map<String, Object> res = null;//dishonestPersonSmo.qryDishonestPersonS(param);
                    if (res != null && res.containsKey("dishonestPersons")) {
                        List<Map<String, Object>> dishonestPersonList = (List<Map<String, Object>>) MapUtils.getObject(res, "dishonestPersons");
                        if (!ListUtil.isListEmpty(dishonestPersonList)) {
                            returnMap.put("isDishonestPerson", "Y");
                            break;
                        }
                    }
                }
            }
        }

        return returnMap;
    }

    public Map getCustDetialInfo(String jsonString) throws Exception{
        String party = custMultiSMO.qryCustomerDetailForMulti(jsonString);
        Map result = (Map)resolveResult(party);
        return result;
    }
}
