<div data-widget="commProductQuery29" style="height:100%">
    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->
        <div class="box-maincont">
            <!--<div class="tab-pane fade in active" id="1">-->
            <div class="homenofood">
                <div class="page_main notopnav">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="form_title">
                                    <div><i class="bot"></i>产品通用查询（2.9）</div>
                                </div>
                                <div class="wmin_content row">
                                    <form class=" form-bordered">
                                        <div class="form-group col-md-6 ">
                                            <label class="col-md-5 control-label lablep">
                                                	地 区
                                            </label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder="" readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdBtn" class="btn btn-green" type="button">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                       <div class="form-group col-md-6">
                                            <label class="col-md-5 control-label lablep" id="queryLabel">
                                            	   接入号码
                                            </label>
                                            <div class="col-md-7">
                                                <input id="qryStr" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6" id="qryTypeDiv">
                                            <label class="col-md-5 control-label lablep">查询类型</label>
                                            <div class="col-md-7">
                                                <select id="qryType" class="form-control">
                                                    <option value="1">接入号码</option>
                                                    <option value="2">身份证</option>
                                                    <option value="3">账户合同号</option>
                                                    <option value="4">客户名</option>
                                                    <option value="5">客户简拼</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-5">
                                            <label class="col-md-4 control-label lablep"></label>
                                            <div class="col-md-8 text-right">
                                                <button id="qryBtn" type="button" class="btn btn-primary">查&nbsp;&nbsp;询</button>
                                            </div>
                                        </div>
                                        <input id="channelId" type="hidden" class="form-control" placeholder="" readonly="readonly">
                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12">
                                        <a id="edOrderProdDtl" class="btn btn-gray btn-outline">  详情
                                        </a>
                                        <a id="sameCustProdQuery" class="btn btn-gray btn-outline">  查询同一客户下产品
                                        </a>
                                    </div>
                                    <div class="col-lg-12 mart10" id="prodListResult">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>选择</th>
                                                <th>地区</th>
                                                <th>客户名称</th>
                                                <th>产品规格</th>
                                                <th>接入号</th>
                                                <th>状态</th>
                                            </tr>
                                            </thead>
                                            <tbody id="prodListResult">
                                            #if($options != "null" && $options.prodInfos && $options.prodInfos != "null" &&
                                            $options.prodInfos.size() > 0)
                                            #foreach($prodInfo in $options.prodInfos)
                                            <tr>
                                                <td><label class="wu-radio full absolute" data-scope=""><input
                                                        type="radio" name="prodInfo">
                                                        <p class="vita-data">{"data":$prodInfo}</p> 
                                                    </label>
                                                </td>
                                                <td>$!{prodInfo.areaName}</td>
                                                <td>$!{prodInfo.custName}</td>
                                                <td>$!{prodInfo.prodSpecName}</td>
                                                <td>$!{prodInfo.accessNumber}</td>
                                                <td>$!{prodInfo.statusStr}</td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.prodInfos && $options.prodInfos != "null" &&
                                            $options.prodInfos.size() == 0)
                                                <tr><td align='center' colspan='8'>未查询到数据！<td></tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <!--翻页start -->
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--填单end-->
                </div>
            </div>
        </div>
    </div>
</div>
