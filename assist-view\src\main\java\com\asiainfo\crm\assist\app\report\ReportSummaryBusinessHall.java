package com.asiainfo.crm.assist.app.report;

import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.IReportSMO;
import org.apache.commons.collections.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017-9-11.
 */
@Component("vita.reportSummaryBusinessHall")
public class ReportSummaryBusinessHall extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(ReportSummaryBusinessHall.class);

    @Autowired
    private IReportSMO reportFormSMO;

    @Override
    public Map achieveData(Object... objects) throws Exception {
        return null;
    }

    public Map ReportSummaryBusinessHallQuery(String json) throws IOException {
        Map options = new HashedMap();//ReportSummaryBusinessHallList
        String str = reportFormSMO.ReportSummaryBusinessHallQuery(json);
        Map<String,Object> map = jsonConverter.toBean(str,Map.class);
        Map<String,Object> mapObj = (Map<String, Object>) map.get("resultObject");
        List list = (List) mapObj.get("ReportSummaryBusinessHallList");
        options.put("ReportSummaryBusinessHallList",list);
        double countRealAmount=0D,countPaidInAmount=0D,countSun=0D;
        int countNum =  list.size();//总条数
        for (int i=0; i<list.size(); i++){
            Map msg = (Map) list.get(i);
            double  realAmount = (double ) msg.get("realAmount");
            double  paidInAmount = (double) msg.get("paidInAmount");
            double  deductionAmount = (double) msg.get("deductionAmount");
            countSun+=deductionAmount;
            countRealAmount+=realAmount;
            countPaidInAmount+=paidInAmount;
        }

        options.put("countRealAmount",countRealAmount);
        options.put("countPaidInAmount",countPaidInAmount);
        options.put("countSun",countSun);
        options.put("exportNum", MDA.EXPORT_NUM);
        options.put("totalNumber", countNum);
        return options;
    }
}
