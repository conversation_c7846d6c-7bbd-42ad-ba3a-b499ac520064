package com.asiainfo.crm.assist.app.cust;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.asiainfo.crm.service.intf.IStaffDataPrvQuerySMO;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.aspect.annotations.SmLogAnnotation;
import com.asiainfo.crm.common.AbstractSoComponent;
import com.asiainfo.crm.common.Constant;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.service.intf.IStaffInfoQuerySMO;
import com.asiainfo.crm.util.JsCommUtil;
import com.asiainfo.crm.util.ListUtil;

/**
* 选择客户
*/
@Component("vita.chooseCust")
public class ChooseCust extends AbstractSoComponent {

	@Autowired
	private ICustMultiSMO custMultiSMO;

	@Autowired
	private IStaffInfoQuerySMO staffSMO;
	@Autowired
	private IStaffDataPrvQuerySMO staffDataPrvQuerySMO;
	@Override
	public Map achieveData(Object... param) throws Exception {
		String jsonString = param[0].toString();
		// String resString = staffSMO.queryAuthenticDataRange(jsonString);
		String resString = this.getCustQryTypeMockData();
		Map dataRangeMap = jsonConverter.toBean(resString, Map.class);
		Map retMap = new HashMap();
		String resultCode = MapUtils.getString(dataRangeMap, "resultCode");
		if (!StringUtil.isEmpty(resultCode) && resultCode.equals(MDA.RESULT_SUCCESS.toString())) {
			Map resultObject = (Map) dataRangeMap.get("resultObject");
			List dataRanges = (List) resultObject.get("dataRanges");
			if (dataRanges != null && !dataRanges.isEmpty()) {
				retMap.put("dataRanges", dataRanges);
			}
		}
		return retMap;
	}

	@SmLogAnnotation(logType="1000", funName="qryCustomerListForMulti", paramKeys = {"value,queryType"})
	public Map qryCustomerList(String jsonString) throws Exception {
		JSONObject json = JSONObject.parseObject(jsonString);
		String resString = custMultiSMO.qryCustomerListForMulti(jsonString);
		Map resultMap = jsonConverter.toBean(resString, Map.class);
		Map retMap = new HashMap();
		List<Map> customers = ListUtil.newArrayList();
		long totalNumber = 0L;
		String resultCode = MapUtils.getString(resultMap, "resultCode");
		if (!StringUtil.isEmpty(resultCode) && resultCode.equals(MDA.RESULT_SUCCESS.toString())) {
			Map resultObject = (Map) resultMap.get("resultObject");
			customers = (List) resultObject.get("customers");
			Map pageInfo = (Map) resultObject.get("pageInfo");
			String rowCount = MapUtils.getString(pageInfo, "rowCount");
			if (!StringUtil.isEmpty(rowCount)) {
				totalNumber = Long.valueOf(rowCount);
			}
		}
		if (Constant.LOGIC_STR_Y.equals(MDA.NEW_PERMANENT_CERT_ON) && ListUtil.isListEmpty(customers)) {
			//根据新卡证件号码查询客户，若无客户资料，则根据旧卡号码进行客户查询
			Map map = jsonConverter.toBean(jsonString, Map.class);
			String certNumber = MapUtils.getString(map, "value");
			String oldCertNumber = MapUtils.getString(map, "oldCertNumber");
			if (JsCommUtil.isNewPermanentCert(oldCertNumber, certNumber)) {
				map.put("value", oldCertNumber);
				String oldStr = custMultiSMO.qryCustomerListForMulti(jsonConverter.toJson(map));
				Map result = (Map) resolveResult(oldStr);
				customers = (List) result.get("customers");
				Map pageInfo = (Map) result.get("pageInfo");
				String rowCount = MapUtils.getString(pageInfo, "rowCount");
				if (!StringUtil.isEmpty(rowCount)) {
					totalNumber = Long.valueOf(rowCount);
				}
				for(Map customer: ListUtil.nvlList(customers)) {
					customer.put("oldCertNumber", oldCertNumber);
					customer.put("newCertNumber", certNumber);
				}
			}
		}
		//选择客户脱敏
		if(customers!=null&&customers.size()>0){
			if(json.get("sysUserId") != null){
				String sysUserId=json.get("sysUserId").toString();
				Map addressMap = new HashMap();
				addressMap.put("sysUserId",sysUserId);
				addressMap.put("privCode",MDA.USER_PRIV_RIGHTS.get("custDetailQry"));
				String permissionReturn = staffDataPrvQuerySMO.checkSysUserPriv(jsonConverter.toJson(addressMap));
				JSONObject addressPriv = JSONObject.parseObject(permissionReturn).getJSONObject("resultObject");
				Map customerMap = new HashMap();
				String certNum="";
				String certAddr="";
				String custName="";

				if(addressPriv == null || !("true".equals(addressPriv.getString("isHave")))){
					for(int i=0;i<customers.size();i++){
						customerMap=(Map)customers.get(i);
						certNum=customerMap.get("certNum")==null?"":customerMap.get("certNum").toString();
						custName=customerMap.get("custName")==null?"":customerMap.get("custName").toString();
						certAddr=customerMap.get("certAddr")==null?"":customerMap.get("certAddr").toString();

						if(certNum.length()>6){
							certNum=certNum.substring(0,6)+"*********";
						}else{
							certNum="******";
						}
						if(certAddr.length() > 5){
							certAddr = certAddr.substring(0,5)+"********";
						}else{
							certAddr = "**";
						}
						if(custName.length()>1){
							custName=custName.substring(0,1)+"*********";
						}else{
							custName = "**";
						}
						customerMap.put("certAddrValue",certAddr);
						customerMap.put("certNumValue",certNum);
						customerMap.put("custNameValue",custName);
						customers.set(i,customerMap);
					}
				}else{
					for(int i=0;i<customers.size();i++){
						customerMap=(Map)customers.get(i);
						certNum=customerMap.get("certNum")==null?"":customerMap.get("certNum").toString();
						certAddr=customerMap.get("certAddr")==null?"":customerMap.get("certAddr").toString();
						custName=customerMap.get("custName")==null?"":customerMap.get("custName").toString();
						customerMap.put("certNumValue", certNum);
						customerMap.put("certAddrValue",certAddr);
						customerMap.put("custNameValue",custName);
						customers.set(i,customerMap);
					}

				}
			}
		}
		retMap.put("totalNumber", totalNumber);
		retMap.put("customers", customers);
		return retMap;
	}

	public String getCustQryTypeMockData() throws Exception {
		// String resString = staffSMO.queryAuthenticDataRange(jsonString);
		String resString = "{"
						+"    \"resultCode\": \"0\","
						+"    \"resultMsg\": \"处理成功!\","
						+"    \"resultObject\": {"
						+"        \"total\": 0,"
						+"        \"dataRanges\": ["
						+"            {"
						+"                \"dataDimensionCode\": \"\","
						+"                \"dataDimensionName\": \"智能匹配查询类型\","
						+"                \"dimensionValue\": \"\","
						+"                \"rangeId\": \"1\""
						+"            },"
						+"            {"
						+"                \"dataDimensionCode\": \"identity\","
						+"                \"dataDimensionName\": \"居民身份证\","
						+"                \"dimensionValue\": \"1\","
						+"                \"rangeId\": \"1\""
						+"            },"
						+"            {"
						+"                \"dataDimensionCode\": \"custName\","
						+"                \"dataDimensionName\": \"客户名称\","
						+"                \"dimensionValue\": \"\","
						+"                \"rangeId\": \"2\""
						+"            },"
						+"            {"
						+"                \"dataDimensionCode\": \"simpleSpell\","
						+"                \"dataDimensionName\": \"客户简拼\","
						+"                \"dimensionValue\": \"\","
						+"                \"rangeId\": \"3\""
						+"            },"
						+"            {"
						+"                \"dataDimensionCode\": \"coNbr\","
						+"                \"dataDimensionName\": \"订单流水号\","
						+"                \"dimensionValue\": \"\","
						+"                \"rangeId\": \"4\""
						+"            },"
						+"            {"
						+"                \"dataDimensionCode\": \"accNbr\","
						+"                \"dataDimensionName\": \"接入号码\","
						+"                \"dimensionValue\": \"\","
						+"                \"rangeId\": \"5\""
						+"            },"
						+"            {"
						+"                \"dataDimensionCode\": \"acctCd\","
						+"                \"dataDimensionName\": \"帐户合同号\","
						+"                \"dimensionValue\": \"\","
						+"                \"rangeId\": \"6\""
						+"            }"
						+"        ],"
						+"        \"resultCode\": \"0\","
						+"        \"resultMsg\": \"成功\""
						+"    }"
						+"}";
		return resString;
	}
}
