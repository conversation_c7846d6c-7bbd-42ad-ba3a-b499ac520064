<div data-widget="custQuery" style="display: none;height:100%">
	<div class="positionuser_title">
		<h5 class="textcolor6"><i class="icon-user glyphicon titledot"></i><span>客户定位</span></h5>
	</div>
	<div class="positionuser_search">
		<div class="searchbutt">
			<i class="icon-search glyphicon text16"></i>
		</div>
		<input type="text" id="condValue" class="form-control" placeholder="输入客户证件或名称搜索">
	</div>
	<div class="scan_box" id="scanCert">
		<span class="icon-scan glyphicon scanf"></span>
		<div >
			扫描身份证
		</div>
	</div>
	<div class="adduser_box paddlr15" id="addCust">
		<a class="btn btn-primary btn-block" href="javascript:void(0);">新增客户</a>
	</div>
	
	<div class="search_usercont" style="display:none;">
		#if($options != "null" && $options.partyList && $options.partyList != "null" && $options.partyList.size() > 0)
			<ul class="usercont_list">
				#foreach($party in $options.partyList)
					<li>
						<h5>$party.custName</h5>
						<p>$party.certNum</p>
						<p>$party.regionName</p>
					</li>
					<p class="vita-data">{"customer":$party}</p>
				#end
			</ul>
		#else
			#if($options != "null" && $options.dataRanges && $options.dataRanges != "null" && $options.dataRanges.size() > 0)
				<div class="nouser_if">
				</div>
				<div class="sidetcont">
					<div class="marb0">
						<input type="text" id="" readonly="readonly" class="form-control colorgrey" placeholder="请选择输入内容的类型">
						<p class="vita-bind" model="certTypeName"></p>
					</div>
				</div>
				<ul class="nousercont_list">
					#foreach($dataRange in $options.dataRanges)
						<li>$dataRange.dataDimensionName</li>
						<p class="vita-data">{"data":$dataRange}</p>
					#end
				</ul>
	    	#end
	    	<div class="adduser_box paddlr15" id="addCust">
				<a class="btn btn-primary btn-block" href="javascript:void(0);">新增客户</a>
			</div>
	    #end
    </div>
</div>
