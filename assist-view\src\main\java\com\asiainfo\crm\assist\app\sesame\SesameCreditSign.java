package com.asiainfo.crm.assist.app.sesame;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ISesameCreditSmo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by wenhy on 2017/7/31.
 * 芝麻信用购机第四步：签约
 */
@Component("vita.sesameCreditSign")
public class SesameCreditSign extends AbstractComponent {

    private static Logger logger = LoggerFactory.getLogger(SesameCreditSign.class);

    @Autowired
    private ISesameCreditSmo sesameCreditSmo;

    @Override
    public Map achieveData(Object... params) throws Exception {
        Map options = new HashMap();
        options.put("sesameSignUrl", AssistMDA.SESAME_SIGN_URL);
        return options;
    }

    public Map sesameCreditAction(String jsonStr) throws Exception {
        String sesameCreditStr = sesameCreditSmo.sesameCreditAction(jsonStr);
        String resultStr = (String)resolveResult(sesameCreditStr);
        Map resultObjectMap = jsonConverter.toBean(resultStr, Map.class);
        return resultObjectMap;
    }
}