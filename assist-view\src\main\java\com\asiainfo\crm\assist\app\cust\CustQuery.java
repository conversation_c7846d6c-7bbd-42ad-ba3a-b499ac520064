package com.asiainfo.crm.assist.app.cust;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.service.intf.IStaffInfoQuerySMO;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
* 查询客户
*/
@Component("vita.custQuery")
public class CustQuery extends AbstractComponent {

	@Autowired
	private ICustMultiSMO custMultiSMO;
	
	@Autowired
	private IStaffInfoQuerySMO staffSMO;

	public Map achieveData(Object... params) throws Exception {
		return null;
	}

	public List qryCustomerList(String params) throws Exception {
		String resString = custMultiSMO.qryCustomerListForMulti(params);
		List partys = new ArrayList();
		Map<String, List> resultObject = (Map) resolveResult(resString);
        List<Map> customers = resultObject.get("customers");
        if(customers != null && !customers.isEmpty()){
        	return customers;
        }
		return partys;
	}
	
	public List queryAuthenticDataRange(String jsonString) throws Exception {
		// String resString = staffSMO.queryAuthenticDataRange(jsonString);
		String resString = "{\n" +
                " \"resultCode\":\"0\",\n" +
                " \"resultMsg\":\"处理成功!\",\n" +
                " \"resultObject\":{\n" +
                "  \"dataRanges\":[\n" +
                "   {\n" +
                "    \"dataDimensionName\":\"接入号码\",\n" +
                "    \"dataDimensionCode\":\"accNbr\",\n" +
                "    \"dimensionValue\":\"\",\n" +
                "    \"rangeId\":\"14654\"\n" +
                "   },\n" +
                "   {\n" +
                "    \"dataDimensionName\":\"订单流水号\",\n" +
                "    \"dataDimensionCode\":\"coNbr\",\n" +
                "    \"dimensionValue\":\"\",\n" +
                "    \"rangeId\":\"14654\"\n" +
                "   },\n" +
                "   {\n" +
                "    \"dataDimensionName\":\"组织机构代码\",\n" +
                "    \"dataDimensionCode\":\"identity\",\n" +
                "    \"dimensionValue\":\"11\",\n" +
                "    \"rangeId\":\"14657\"\n" +
                "   },\n" +
                "   {\n" +
                "    \"dataDimensionName\":\"护照\",\n" +
                "    \"dataDimensionCode\":\"identity\",\n" +
                "    \"dimensionValue\":\"9\",\n" +
                "    \"rangeId\":\"14656\"\n" +
                "   },\n" +
                "   {\n" +
                "    \"dataDimensionName\":\"身份证\",\n" +
                "    \"dataDimensionCode\":\"identity\",\n" +
                "    \"dimensionValue\":\"1\",\n" +
                "    \"rangeId\":\"14653\"\n" +
                "   },\n" +
                "   {\n" +
                "    \"dataDimensionName\":\"军人证\",\n" +
                "    \"dataDimensionCode\":\"identity\",\n" +
                "    \"dimensionValue\":\"2\",\n" +
                "    \"rangeId\":\"14654\"\n" +
                "   }\n" +
                "  ],\n" +
                "  \"resultCode\":\"0\",\n" +
                "  \"resultMsg\":\"成功\"\n" +
                " }\n" +
                "}";

		Map dataRangeMap = jsonConverter.toBean(resString, Map.class);
		List dataRanges = new ArrayList();
		String resultCode = MapUtils.getString(dataRangeMap, "resultCode");
		if (!StringUtil.isEmpty(resultCode) && resultCode.equals(MDA.RESULT_SUCCESS.toString())) {
			Map resultObject = (Map) dataRangeMap.get("resultObject");
			dataRanges = (List) resultObject.get("dataRanges");
		}
		return dataRanges;
	}
	
	

}