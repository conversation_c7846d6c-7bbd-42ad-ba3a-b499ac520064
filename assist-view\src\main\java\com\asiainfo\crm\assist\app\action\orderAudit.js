(function (vita) {
    var orderAudit = vita.Backbone.BizView.extend({
        events: {
            "click #btn-query": "_orderAuditListQuery",// 查询
            "click [name=orderAuditBtn]": "_orderAuditBtnClick", //稽核点击事件
            "click #qryTimeQuantum button": "_qryTimeQuantum",//日历控件
            "click #regionIdBtn": "_chooseArea",
            "click [name=orderAuditHis]": "_orderAuditHisClick" //审核历史查询
        },
        _initialize: function () {
            var widget = this, element = $(widget.el);
            // 时间控件类型初始化
            var datetime = widget.require("datetime");
            var nowDate = new Date();
            var beginDate = element.find("input[name='beginDate']");
            var endDate = element.find("input[name='endDate']");
            var startYear = nowDate.getFullYear() - 5, endYear = nowDate.getFullYear();
            if (beginDate.length) {
                datetime.register(beginDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear: endYear
                });
            }
            if (endDate.length) {
                datetime.register(endDate, {
                    preset: widget.global.preset,
                    dateFormat: "yy-mm-dd",
                    endYear: endYear
                });
            }
            //日期控件设置默认时间
            widget.global.defaultBeginTime = widget._currentDate();
            widget.global.defaultEndTime = widget._currentDate();
            widget._setDefaultDateTime();
            var gSession = widget.gSession;
            element.find("#regionId").val(gSession.staffRegionName).attr("value", gSession.staffRegionId);
        },
        global: {
            pageIndex: 1,
            pageSize: 5,
            preset: "date",
            defaultBeginTime: "",
            defaultEndTime: "",
            currentDayCount: 0,
            oneDayCount: -1,
            lastMonthDayCount: -29,
            orderAuditDetailUrl: "../action/orderAuditDetail",
            chooseArea: "../comm/chooseArea",
            orderAuditHisUrl: "../query/orderAuditHis"
        },
        _orderAuditListQuery: function () {
            var widget = this;
            var params = widget._getConds();
            if (params) {
                widget.refreshPart("queryOrderAuditList", JSON.stringify(params), "#orderListResult", function (res) {
                    var paging = widget.require("paging"), r = $(res);
                    var totalNumber = r.find("#showPageInfo").data("totalNumber");
                    if (totalNumber > 0) {
                        var e = paging.new({
                            recordNumber: widget.global.pageSize,
                            total: totalNumber,
                            pageIndex: widget.global.pageIndex,
                            callback: function (_pageIndex, _recordNumber) {
                                params.pageInfo.pageIndex = _pageIndex;
                                params.pageInfo.pageSize = _recordNumber;
                                if (_pageIndex > 1 && !params.pageInfo.rowCount) {
                                    params.pageInfo.rowCount = totalNumber;
                                }
                                if (widget._getConds()) {
                                    widget.refreshPart("queryOrderAuditList", JSON.stringify(params), "#orderList");
                                }
                            }
                        });
                        r.find("#showPageInfo").append(e.getElement());
                    }

                }, {
                    async: false
                });
            }
        },
        _getConds: function () {
            var widget = this;
            var element = $(widget.el);
            var custOrderNbr = $.trim(element.find("#custOrderNbr").val());
            var beginDate = element.find("#beginDate").val();
            var endDate = element.find("#endDate").val();
            beginDate = beginDate ? beginDate + " 00:00:00" : beginDate;
            endDate = endDate ? endDate + " 23:59:59" : endDate;
            var select = element.find("select[name='serviceOffers']");
            var option = select.find("option:selected");
            var serviceOfferId = option.val();
            var statusMenu = element.find("select[name='statusMenu']");
            var statusOption = statusMenu.find("option:selected");
            var status = statusOption.val();
            var params = {
                pageInfo: {
                    pageIndex: widget.global.pageIndex,
                    pageSize: widget.global.pageSize
                },
                auditResult: status
            };
            if (!widget._isNullStr(beginDate) && !widget._isNullStr(endDate)) {
                if (beginDate >= endDate) {
                    widget.popup("结束时间应大于开始时间!");
                    return false;
                }
            }
            if (!widget._isNullStr(beginDate) || !widget._isNullStr(endDate)) {
                params.acceptDateScope = {};
                if (!widget._isNullStr(beginDate)) {
                    params.acceptDateScope.beginDate = beginDate;
                }
                if (!widget._isNullStr(endDate)) {
                    params.acceptDateScope.endDate = endDate;
                }
            }
            if (!widget._isNullStr(custOrderNbr)) {
                params.custOrderNbr = custOrderNbr;
            }
            if (!widget._isNullStr(serviceOfferId) && serviceOfferId != "-1") {
                params.serviceOfferId = serviceOfferId;
            }
            var regionId = element.find("#regionId").attr("value");
            if (widget._isNullStr(regionId)) {
                widget.popup("请选择地区！");
                return false;
            } else {
                params.regionId = regionId;
            }
            return params;
        },
        _orderAuditBtnClick: function (e) {
            var widget = this, element = $(widget.el);
            var btn = $(e.currentTarget);
            var clickType = btn.data("auditResult");
            var custOrderId = btn.data("custOrderId");
            var custOrderNbr = btn.data("custOrderNbr");
            var serviceOfferId = btn.data("serviceOfferId");
            var accNum = btn.data("accNum");
            var btnData = widget.model.get(custOrderId + serviceOfferId);
            if (btnData) {
                clickType = btnData.auditResult;
            }
            if (clickType == "0") {
                //抢单
                var params = {
                    "custOrderId": custOrderId,
                    "custOrderNbr": custOrderNbr,
                    "glbSessionId": widget.gSession.glbSessionId
                };
                widget.callService("grabOrder", JSON.stringify(params), function (ret) {
                    if (ret.code == "0") {
                        widget.popup(ret.desc);
                        btnData = {"auditResult": ret.auditResult, "auditResultName": ret.auditResultName};
                        widget.model.set(custOrderId + serviceOfferId, btnData);
                        var tr = btn.closest("tr");
                        tr.find("td[name='auditorName']").text(widget.gSession.staffName);
                        tr.find("td[name='auditResultName']").text(ret.auditResultName);
                        btn.find("span[name='orderAuditBtnName']").text("审核");
                        tr.find("td[name='statusDate']").text(ret.statusDate);
                        return false;
                    } else {
                        widget.popup(ret.desc);
                        return false;
                    }
                });
            } else {
                //进行审核或查看审核结果
                var orderAuditDetail = {
                    "custOrderNbr": custOrderNbr,
                    "custOrderId": custOrderId,
                    "serviceOfferId": serviceOfferId,
                    "accNum": accNum
                };
                var option = {
                    url: widget.global.orderAuditDetailUrl,
                    params: orderAuditDetail,
                    destroy: true,
                    onClose: function () {
                        widget.callService("queryOrderAuditList", JSON.stringify(orderAuditDetail), function (ret) {
                            if (ret.auditOrderInfos && ret.auditOrderInfos[0]) {
                                var tempInfo = ret.auditOrderInfos[0];
                                if (clickType != tempInfo.auditResult) {
                                    btnData = {
                                        "auditResult": tempInfo.auditResult,
                                        "auditResultName": tempInfo.auditResultName
                                    };
                                    widget.model.set(custOrderId + serviceOfferId, btnData);
                                    btn.find("span[name='orderAuditBtnName']").text("查看");
                                    var tr = btn.closest("tr");
                                    tr.find("td[name='auditResultName']").text(tempInfo.auditResultName);
                                    tr.find("td[name='statusDate']").text(tempInfo.statusDate);
                                }
                            }
                        });
                    }
                };
                widget.dialog(option);
            }
        },
        _isNullStr: function (str) {
            if (str == null || str == "") {
                return true;
            }
            return false;
        },
        _qryTimeQuantum: function (e) {
            var widget = this, element = $(widget.el),
                timeQuantum = $(e.currentTarget).attr("name"),
                dayCount;
            timeQuantum = timeQuantum.substring(timeQuantum.indexOf("_") + 1);
            switch (timeQuantum) {
                case "1":
                    dayCount = widget.global.currentDayCount;
                    break;
                default:
                    dayCount = widget.global.currentDayCount;
            }
            widget._changeTimeQuanTum(dayCount);
        },
        _changeTimeQuanTum: function (dayCount) {
            var widget = this, element = $(widget.el), beginDate = element.find('#beginDate'),
                endDate = element.find('#endDate');
            if (dayCount == widget.global.currentDayCount) {
                beginDate.val(widget._currentDate());
            } else if (dayCount == widget.global.lastMonthDayCount) {
                beginDate.val(widget._lastMonthDate());
            } else {
                beginDate.val(widget._getFormatDate(dayCount));
            }
            endDate.val(widget._getFormatDate(widget.global.currentDayCount));
        },
        //当天从0点开始
        _currentDate: function () {
            var widget = this, el = $(widget.el);
            var nowDate = widget._getFormatDate(widget.global.currentDayCount);
            return nowDate;
        },
        //设置默认时间
        _setDefaultDateTime: function () {
            var widget = this, element = $(widget.el),
                beginDate = element.find("#beginDate"),
                endDate = element.find("#endDate");
            beginDate.val(widget.global.defaultBeginTime);
            endDate.val(widget.global.defaultEndTime);
        },
        //时间控件开发方法
        _dateTimeSwitch: function (flag) {
            var widget = this, element = $(widget.el),
                beginDate = element.find("#beginDate"),
                endDate = element.find("#endDate");
            if (flag === "on") {
                widget._setDefaultDateTime();
                beginDate.prop("disabled", false);
                endDate.prop("disabled", false);
                element.find("#qryTimeQuantum").find('button').removeAttr('disabled');
            } else {
                beginDate.val("");
                endDate.val("");
                element.find("#qryTimeQuantum").find('button').attr('disabled', 'disabled');
                beginDate.prop("disabled", true);
                endDate.prop("disabled", true);
            }
        },
        //获取上个月时间
        _lastMonthDate: function () {
            var Nowdate = new Date();
            var vYear = Nowdate.getFullYear();
            var vMon = Nowdate.getMonth() + 1;
            var vDay = Nowdate.getDate();
            //每个月的最后一天日期（为了使用月份便于查找，数组第一位设为0）
            var daysInMonth = new Array(0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31);
            if (vMon == 1) {
                vYear = Nowdate.getFullYear() - 1;
                vMon = 12;
            } else {
                vMon = vMon - 1;
            }
            //若是闰年，二月最后一天是29号
            if (vYear % 4 == 0 && vYear % 100 != 0 || vYear % 400 == 0) {
                daysInMonth[2] = 29;
            }
            if (daysInMonth[vMon] < vDay) {
                vDay = daysInMonth[vMon];
            }
            if (vDay < 10) {
                vDay = "0" + vDay;
            }
            if (vMon < 10) {
                vMon = "0" + vMon;
            }
            var date = vYear + "-" + vMon + "-" + vDay;
            return date;
        },
        _getFormatDate: function (days) {
            var date = new Date();
            var yesterday_milliseconds = date.getTime() + days * 1000 * 60 * 60 * 24;
            var yesterday = new Date();
            yesterday.setTime(yesterday_milliseconds);
            var strYear = yesterday.getFullYear();
            var strDay = yesterday.getDate();
            var strMonth = yesterday.getMonth() + 1;
            if (strMonth < 10) {
                strMonth = "0" + strMonth;
            }
            if (strDay < 10) {
                strDay = "0" + strDay;
            }
            var datastr = strYear + "-" + strMonth + "-" + strDay;
            return datastr;
        },
        _chooseArea: function () {
            var widget = this, element = $(widget.el);
            var dialogId = "chooseAreaDialog";
            widget.dialog({
                id: dialogId,
                url: widget.global.chooseArea,
                params: {},
                onClose: function (res) {
                    var chooseArea = $(res).closest("[data-widget=chooseArea]");
                    if (chooseArea.length) {
                        var data = chooseArea.chooseArea("getValue");
                        if (!data || !data.validate) {
                            return false;
                        }
                        element.find("#regionId").val(data.regionName).attr("value", data.commonRegionId);
                    }
                }
            });
        },
        _orderAuditHisClick: function (e) {
            var widget = this, element = $(widget.el);
            var btn = $(e.currentTarget);
            var custOrderId = btn.data("custOrderId");
            var custOrderNbr = btn.data("custOrderNbr");
            //查看审核历史记录
            var orderAuditHis = {
                "custOrderId": custOrderId,
                "custOrderNbr": custOrderNbr
            };
            var option = {
                url: widget.global.orderAuditHisUrl,
                params: orderAuditHis,
                destroy: true,
                onClose: function () {
                    //DO NOTHING
                }
            };
            widget.dialog(option);
        }
    });
    vita.widget.register("orderAudit", orderAudit, true);
})(window.vita);