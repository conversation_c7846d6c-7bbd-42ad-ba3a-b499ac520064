package com.asiainfo.crm.assist.app.query;

import com.al.common.utils.StringUtil;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.common.MDA;
import com.asiainfo.crm.service.intf.ICommQuerySMO;
import com.asiainfo.crm.service.intf.ICustSMO;
import com.asiainfo.crm.util.center.OrderUtil;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zhangyj on 2019/9/23.
 */
@Component("vita.disputeWorkOrder")
public class DisputeWorkOrder extends AbstractComponent {
    @Autowired
     private ICustSMO custSMO;
    @Autowired
    private ICommQuerySMO commQuerySMO;
    @Override
    public Map achieveData(Object... objects) throws Exception {
        Map options = new HashMap();
        options.put("disputeUploadSwitch", MDA.DISPUTE_UPLOAD_SWITCH);
        return options;
    }
    public Map  qrydisputeWorkOrderLogs (String jsonString)throws Exception{
        //获取证件类型
        Map attrRetMap=jsonConverter.toBean(commQuerySMO.qryAttrValuesById("PTY-0004"),Map.class);
        String attrResultCode=MapUtils.getString(attrRetMap,"resultCode");
        List<Map> lstAttrValues=null;
        //Map<String,String> certType = null;
        if(!StringUtil.isEmpty(attrResultCode)&&attrResultCode.equals(com.asiainfo.crm.common.MDA.RESULT_SUCCESS.toString())){
            lstAttrValues = (List<Map>) attrRetMap.get("resultObject");
        }
        //获取争议单
        String disputeWorkOrderStr=custSMO.qryDisputeWorkOrderLog(jsonString);
        Map retMap = jsonConverter.toBean(disputeWorkOrderStr, Map.class);
        String resultCode = MapUtils.getString(retMap, "resultCode");
        List<Map> lstRows;
        if (!StringUtil.isEmpty(resultCode) && resultCode.equals(com.asiainfo.crm.common.MDA.RESULT_SUCCESS.toString())) {
            Map resultObjectMap = (Map) retMap.get("resultObject");
            lstRows= (List<Map>) resultObjectMap.get("disputeWorkOrderLogs");
            for (Map map:lstRows){
                //脱敏
            	if (!OrderUtil.isMarkSensitive("disputeWorkOrder")) {
            		String custName=(String)map.get("custName");
                    if(custName!=null){
                        map.put("custName",custName.substring(0,1)+"****");
                    }
                    String certNum=(String)map.get("certNum");
                    if(certNum!=null&&certNum.length()>=4){
                        map.put("certNum",certNum.substring(0,3)+"********");
                    }
            	}
            	//处理日期
                String createDate=(String)map.get("createDate");
                if(createDate!=null&&createDate.length()>10){
                    map.put("createDate",createDate.substring(0,10));
                }
                String updateDate=(String)map.get("updateDate");
                if(updateDate!=null&&updateDate.length()>10){
                    map.put("updateDate",updateDate.substring(0,10));
                }
                //加上证件名称
                for(Map attr:lstAttrValues)
                    if(map.get("certType").equals(attr.get("attrValue"))){
                        map.put("certName",attr.get("attrValueName"));
                    }

            }
            retMap.put("disputeWorkOrders", lstRows);
            Map pageInfoMap=(Map)resultObjectMap.get("pageInfo");
            retMap.put("totalNumber",  pageInfoMap.get("rowCount"));
        }
        joinOtherRetMap(retMap);

        return retMap;
    }

    /**
     * 内蒙开放查询结果retMap其他参数填入
     * @param retMap
     */
    public void joinOtherRetMap(Map retMap){
    }

}
