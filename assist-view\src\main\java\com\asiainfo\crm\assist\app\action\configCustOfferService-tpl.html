<script type="text/javascript" src="../bundles/assist/common/jquery-form.js"></script>

<div data-widget="configCustOfferService" style="height:100%">

    <div class="pace-done wrapper mini-rightmax no-minright"> <!--控制测边栏mini-rightmax--><!--控制底部按钮nobuttnfood-->

        <div class="box-maincont">
            <div class="homenofood nopagenav">
                <div class="page_main">
                    <!--填单start-->
                    <div class="col-lg-12">
                        <div class="box-item">
                            <div class="container-fluid row">
                                <div class="wmin_content row mart15">
                                    <form class="form-bordered">
                                      <!--  <div class="form-group col-md-5">
                                            <label class="col-md-4 control-label lablep">地区</label>
                                            <div class="col-md-8">
                                                <div class="input-group">
                                                    <input id="regionId" type="text" class="form-control" placeholder=""
                                                           readonly="readonly">
                                                    <div class="input-group-btn">
                                                        <button id="regionIdChooseBtn" class="btn btn-green" type="button">
                                                            选择
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>-->
                                        <div class="form-group col-md-5">
                                            <label class="col-md-4 control-label lablep"> 客户证件类型</label>
                                            <div class="col-md-8">
                                                <select id="partyTypeQry" class="form-control ">
                                                    <option value="1">居民身份证</option>
                                                    <option value="3">外国公民护照</option>
                                                    <option value="4">港澳居民来往内地通行证</option>
                                                    <option value="12">户口簿</option>
                                                    <option value="42">台湾居民来往内地通行证</option>
                                                    <option value="51">现役军人居民身份证</option>
                                                    <option value="52">人民武装警察居民身份证</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group col-md-5">
                                            <label class="col-md-4 control-label lablep">客户证件号码</label>
                                            <div class="col-md-8">
                                                <input id="custIdentityNum" type="text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-2">
                                            <div class="col-md-8 text-right">
                                                <button id="btn-queryCust" type="button" class="btn btn-primary">查询
                                                </button>
                                            </div>
                                        </div>

                                    </form>
                                </div>
                            </div>

                            <div class="container-fluid row">
                                <div class="form_title">
                                </div>
                                <div class="wmin_content">
                                    <div class="col-lg-12">
                                        <a class="btn btn-gray btn-outline" id="deleteCustService">
                                            <i class="glyphicon fa-stale text18"> </i> 删除
                                        </a>
                                    </div>
                                    <div class="col-lg-12 mart10" id="configCustOfferServiceResult">
                                        <table class="table table-hover" id="custServLimitCfgs">
                                            <thead>
                                            <tr>
                                                <th>选择</th>
                                                <th>客户名称</th>
                                                <th>客户证件类型</th>
                                                <th>客户证件号码</th>
                                                <th>限制业务动作ID</th>
                                                <th>限制业务动作名称</th>
                                                <th>产品规格</th>
                                                <!--<th>地区</th>-->
                                                <th>备注</th>
                                                <th>操作员编号</th>
                                                <th>类型名称</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            #if($options != "null" && $options.custServLimitCfgs &&
                                            $options.custServLimitCfgs !=
                                            "null"
                                            &&
                                            $options.custServLimitCfgs.size() > 0)
                                            #foreach($custServLimitCfg in $options.custServLimitCfgs)
                                            <tr name="trClick">
                                                <td>
                                                    <label class="wu-radio full absolute" data-scope="">
                                                        <input id="checkModify" type="checkbox" name="payment">
                                                        <p class="vita-data">{"data":$custServLimitCfg}</p>
                                                    </label>


                                                </td>
                                                <td>$!{custServLimitCfg.custName}</td>
                                                <td>$!{custServLimitCfg.certType}</td>
                                                <td>$!{custServLimitCfg.certNum}</td>
                                                <td>$!{custServLimitCfg.serviceOfferId}</td>
                                                <td>$!{custServLimitCfg.serviceOfferName}</td>
                                                <td>$!{custServLimitCfg.prodId}</td>
                                                <!--<td>$!{custServLimitCfg.regionName}</td>-->
                                                <td>$!{custServLimitCfg.remark}</td>
                                                <td>$!{custServLimitCfg.statusStaff}</td>
                                                <td>$!{custServLimitCfg.typeName}</td>
                                            </tr>
                                            #end
                                            #elseif($options != "null" && $options.custServLimitCfgs &&
                                            $options.custServLimitCfgs
                                            !=
                                            "null" &&
                                            $options.custServLimitCfgs.size() == 0)
                                            <tr>
                                                <td align='center' colspan='7'>未查询到数据！
                                                <td>
                                            </tr>
                                            #end
                                            </tbody>
                                        </table>
                                        <div id="showPageInfo">
                                        </div>
                                        #if($options.totalNumber && $options.totalNumber != "null")
                                        <p class="vita-data">{"totalNumber":$options.totalNumber}</p>
                                        #end
                                        <!--翻页end -->

                                    </div>
                                        <!--填单end-->
                                        <!--填单1start-->
                                        <div class="col-lg-12">
                                            <div class="box-item">
                                                <div class="container-fluid row">
                                                    <div class="wmin_content row mart15">
                                                        <form class="form-bordered">
                                                            <div class="form-group col-md-5">
                                                                <label class="col-md-4 control-label lablep"> 客户证件类型</label>
                                                                <div class="col-md-8">
                                                                    <select id="partyType" class="form-control partySelect" autocomplete="off">
                                                                        <option value="1">居民身份证</option>
                                                                        <option value="3">外国公民护照</option>
                                                                        <option value="4">港澳居民来往内地通行证</option>
                                                                        <option value="12">户口簿</option>
                                                                        <option value="42">台湾居民来往内地通行证</option>
                                                                        <option value="51">现役军人居民身份证</option>
                                                                        <option value="52">人民武装警察居民身份证</option>
                                                                    </select>
                                                                </div>
                                                            </div>

                                                            <div class="form-group col-md-5">
                                                                <label class="col-md-4 control-label lablep">客户证件号码</label>
                                                                <div class="col-md-8">

                                                                    <input id="custIdentity" name="configNum" type="text" value="$!userNum"class="form-control">
                                                                </div>
                                                            </div>

                                                            <div class="form-group col-md-5">
                                                                <label class="col-md-4 control-label lablep">动作名称</label>
                                                                <div class="col-md-8">
                                                                    <select id="addBoType" class="form-control">
                                                                        #if($options != "null" && $options.serviceOfferIdsQuery &&
                                                                        $options.serviceOfferIdsQuery !=
                                                                        "null"
                                                                        && $options.serviceOfferIdsQuery.size() > 0)
                                                                        #foreach($itme in $options.serviceOfferIdsQuery)

                                                                        <option serviceOfferId="${itme.parServiceOfferId}"
                                                                                prodInstId="$!{itme.serviceOfferId}">
                                                                            $!{itme.serviceOfferName}
                                                                        </option>
                                                                        #end
                                                                        #end
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="form-group col-md-5">
                                                                <label class="col-md-4 control-label lablep">产品规格</label>
                                                                <div class="col-md-8">
                                                                    <select id="prodId" class="form-control">
                                                                        <option value="">请选择</option>
                                                                        <option value="2">普通电话</option>
                                                                        <option value="9">宽带</option>
                                                                        <option value="69">固话类</option>
                                                                        <option value="379">手机</option>
                                                                        <option value="650020001">iTV账号</option>

                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="form-group col-md-5">
                                                                <label class="col-md-4 control-label lablep">备注</label>
                                                                <div class="col-md-8">
                                                                    <input id="remarks" type="text" class="form-control"
                                                                           placeholder="">
                                                                </div>
                                                            </div>

                                                            <div class="form-group col-md-5">
                                                                <label class="col-md-4 control-label lablep"></label>
                                                                <div class="col-md-8 ">
                                                                    <button id="btn-addCustService"  type="button" class="btn btn-primary">新增动作关系
                                                                    </button>
                                                                </div>
                                                            </div>


                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>



                                </div>
                            </div>
                            <div class="container-fluid row">
                                <div class="form_title">导入EXCEL文件批量功能</div>

                                <div class="form_title"></div>
                                <div class="wmin_content ">
                                    <div class="col-lg-12">
                                        <form id="a_form" action="../action/uploadConfigExcels" method="post"
                                              enctype="multipart/form-data">
                                            <input type="file" class="m_file" name="data"  accept="xls">
                                            <br>
                                            <input class="route_input" type="text" value="" name="jsonStr"
                                                   style="display: none"/>
                                            <button id="subbutton" type="button" class="btn btn-primary">提交</button>
                                            <a href="javascript:;" id="btn-expResults" class="btn btn-primary">导出操作结果信息</a>
                                            <a href="javascript:;" id="upLoadTemplet" class="btn btn-primary">批量上传文件模板下载</a>
                                        </form>
                                    </div>

                                    <div class="col-lg-12 mart10">
                                        <p><strong>注意：</strong></p>
                                        <p>
                                            1、证件类型说明：1:居民身份证，3:外国公民护照，4:港澳居民来往内地通行证，12：户口簿，
                                                42:台湾居民来往内地通行证，51:现役军人居民身份证,52:人民武装警察居民身份证<br>
                                            2、操作类型：ADD表示增加，DEL表示删除<br>
                                            3、一次不超过500条<br>
                                            4、客户证件类型，客户证件号码，禁止业务id，操作类型为必填<br>
                                            5、产品规格说明：2:普通电话，9:宽带，69:固话类，379：手机，650020001:iTV账号<br>
                                            6、文件内容参考下面列表
                                        </p>
                                        <table width="55%" cellspacing="0" cellpadding="0" border="0"
                                               class="list_table_inner">
                                            <tbody>
                                            <tr>
                                                <th class="h_td">A</th>
                                                <th class="h_td">B</th>
                                                <th class="h_td">D</th>
                                                <th class="h_td">E</th>
                                                <th class="h_td">F</th>
                                            </tr>
                                            <tr>
                                                <td class="td_content_mid">客户证件类型</td>
                                                <td class="td_content_mid">客户证件号码</td>
                                                <td class="td_content_mid">禁止业务id</td>
                                                <td class="td_content_mid">产品规格</td>
                                                <td class="td_content_mid">操作类型</td>
                                                <td class="td_content_mid">备注</td>
                                            </tr>
                                            <tr>
                                                <td class="td_content_mid">1</td>
                                                <td class="td_content_mid">511111196910071039</td>
                                                <td class="td_content_mid">3020400001</td>
                                                <td class="td_content_mid">379</td>
                                                <td class="td_content_mid">ADD</td>
                                                <td class="td_content_mid">测试新增</td>
                                            </tr>
                                            <tr>
                                                <td class="td_content_mid">1</td>
                                                <td class="td_content_mid">51113219680503502X</td>
                                                <td class="td_content_mid">4040400001</td>
                                                <td class="td_content_mid">379</td>
                                                <td class="td_content_mid">ADD</td>
                                                <td class="td_content_mid">测试新增</td>
                                            </tr>
                                            <tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>


                            </div>
                        </div>
                        <!--填单1end-->

                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
