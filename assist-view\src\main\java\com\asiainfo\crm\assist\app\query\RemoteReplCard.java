package com.asiainfo.crm.assist.app.query;

import com.asiainfo.crm.assist.AssistMDA;
import com.asiainfo.crm.common.AbstractComponent;
import com.asiainfo.crm.service.intf.ICertNumFiveQuerySMO;
import com.asiainfo.crm.service.intf.ICustMultiSMO;
import com.asiainfo.crm.service.intf.IMenuCollectionSMO;
import com.asiainfo.crm.util.SmLogUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by fengjie on 2018/6/22.
 */
@Component("vita.remoteReplCard")
public class RemoteReplCard extends AbstractComponent {

    private static final Logger logger = LoggerFactory.getLogger(RemoteReplCard.class);

	@Autowired
	private ICustMultiSMO custMultiSMO;

    @Override
    public Map achieveData(Object... params) throws Exception {
        return null;
    }

    /**
     * 查询证号关系
     * */
    public Map<String, Object> qryCrtfctNmbr(String jsonStr)  throws Exception {
        Map<String, Object> options = Maps.newHashMap();
        logger.debug(jsonStr);
        jsonStr = SmLogUtil.supplyOneFivePatams(jsonStr);
        String res = custMultiSMO.queryCrtfctNmbr(jsonStr);
        Map<String, Object> resultObjectMap = (Map<String, Object>) resolveResult(res);
        if (resultObjectMap == null  ) {
            options.put("crtfctNmbrList", null);
            return options;
        }
        List<Map<String, Object>> crtfctNmbrList = new ArrayList<Map<String, Object>>();
        crtfctNmbrList = (List<Map<String, Object>>) MapUtils .getObject(resultObjectMap, "crtfctNmbrList");

        Map<String, Object> pageInfoMap = MapUtils.getMap(resultObjectMap, "pageInfo");

        int totalCount = MapUtils.getIntValue(pageInfoMap, "totalCount");
        int pageIndex = MapUtils.getIntValue(pageInfoMap, "pageIndex");
        int pageSize = MapUtils.getIntValue(pageInfoMap, "pageSize");
        options.put("pageCount",pageSize );
        options.put("pageIndex", pageIndex);
        options.put("totalNumber", totalCount);
        options.put("crtfctNmbrList", crtfctNmbrList);
        return options;
    }
}
