(function (vita) {
    var chooseChannel = vita.Backbone.BizView.extend({
        events: {
            "click #channelDiv a": "_chooseChannel",
            "click .pagination li": "_pageClick",
            "click #closeBtn": "_closeBtnClick"
        },
        global: {
            pageSize: 8
        },
        _initialize: function () {
            var widget = this,
                el = $(widget.el);
            var totalNumber = parseInt(el.find("#channelDiv").data("totalNumber")) || 0;
            if (parseInt(totalNumber) > 8) {
                var datas = el.data("data");
                if(datas && datas.channelList && datas.channelList.length){
                    var channelList = datas.channelList;
                    var paging = widget.require("paging");
                    var e = paging.new({
                        recordNumber : widget.global.pageSize,
                        total : totalNumber,
                        pageIndex : 1,
                        callback : function (pageIndex, recordNumber) {
                            widget.refreshPart("queryChannelList", [JSON.stringify(channelList), (pageIndex-1)*recordNumber + 1, pageIndex*recordNumber], "#channelDiv");
                        }
                    });
                    el.find("#showPageInfo").append(e.getElement());
                }
            }
        },
        _chooseChannel: function (e) {
            var widget = this,
                gSession = widget.gSession;
            var channelEl = $(e.target);
            var data = channelEl.data("data");
            var datas = $(widget.el).data("data");
            var hasChange = true;
            if (data.orgId == gSession.channelId) {
                hasChange = false;
            }
            widget.model.set("hasChange", hasChange);
            widget.model.set(data);
            widget._closeDialog();
        },

        _closeBtnClick: function (e) {
            
            var widget = this;
            widget.model.set("hasChange", false);
            this._closeDialog(e);
        },
        /**
         * 弹出框关闭事件
         * @param e
         * @private
         */
        _closeDialog: function (e) {
            var widget = this,
                element = $(widget.el);
            var dialog = element.closest("[data-widgetfullname=vita-dialog]");
            if (dialog.length) {
                dialog.dialog("close");
            }
        },
        getValue: function () {
            return this.model.toJSON();
        }

    });
    vita.widget.register("chooseChannel", chooseChannel, true);
})(window.vita);